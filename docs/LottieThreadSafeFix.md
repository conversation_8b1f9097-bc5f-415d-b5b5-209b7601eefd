# Lottie 线程安全修复方案

## 问题描述

应用出现 `ViewRootImpl$CalledFromWrongThreadException` 崩溃，错误信息：
```
Only the original thread that created a view hierarchy can touch its views.
```

根据堆栈信息，问题出现在 Lottie 动画库中：
- `LottieAnimationView.setComposition()`
- `LottieDrawable.setComposition()`
- `LottieTask.notifySuccessListeners()`

## 根本原因

Lottie 动画在异步加载完成后，通过回调在非UI线程中设置 composition，导致 UI 操作在错误的线程中执行。

## 修复方案

### 1. 创建线程安全的 WaitDialog

**文件**: `LibComm/src/main/java/com/zczy/comm/ui/SafeWaitDialog.java`

- 继承 `AlertDialog`
- 使用 `Handler(Looper.getMainLooper())` 确保所有 UI 操作在主线程执行
- 设置 Lottie 渲染模式为 `SOFTWARE`，避免硬件加速问题

### 2. 创建 Lottie 线程安全工具类

**文件**: `LibComm/src/main/java/com/zczy/comm/utils/LottieThreadSafeUtil.java`

提供以下线程安全方法：
- `setAnimationSafely()` - 安全设置动画
- `cancelAnimationSafely()` - 安全取消动画
- `pauseAnimationSafely()` - 安全暂停动画
- `resumeAnimationSafely()` - 安全恢复动画
- `runOnMainThread()` - 在主线程执行任务

### 3. 创建全局异常处理器

**文件**: `LibComm/src/main/java/com/zczy/comm/utils/ThreadSafeExceptionHandler.java`

- 捕获 `ViewRootImpl$CalledFromWrongThreadException` 异常
- 捕获 Lottie 相关的线程异常
- 记录异常信息但不崩溃应用

### 4. 修改 React Native 中的 Lottie 使用

**文件**: `PluginReact/page/widget/UIWaitLoading.tsx`

添加以下属性确保线程安全：
```typescript
<Lottie 
    source={require('../../src/main/assets/loading.json')} 
    autoPlay={true} 
    style={styles.lottie}
    renderMode="SOFTWARE"  // 软件渲染
    loop={true}
    speed={1.0}
    resizeMode="contain"
/>
```

### 5. 更新 UIShowDialog 使用安全版本

**文件**: `PluginReact/src/main/java/com/zczy/pluginreact/action/UIShowDialog.java`

- 替换 `WaitDialog` 为 `SafeWaitDialog`
- 添加 `hideWaitDialog()` 方法

### 6. 在应用启动时安装异常处理器

**文件**: `app/src/main/java/com/zczy/AppMainContext.java`

在 `onCreate()` 方法中添加：
```java
// 安装线程安全异常处理器，防止 Lottie 等组件的线程异常导致应用崩溃
ThreadSafeExceptionHandler.install();
```

## 修复效果

1. **防止崩溃**: 捕获并处理线程异常，避免应用崩溃
2. **线程安全**: 所有 Lottie 操作都在主线程中执行
3. **向后兼容**: 不影响现有功能，只是增加了安全保护
4. **日志记录**: 记录异常信息便于调试和监控

## 测试建议

1. 在多线程环境下测试 Lottie 动画
2. 测试等待对话框的显示和隐藏
3. 测试 React Native 页面中的 Loading 动画
4. 验证异常处理器是否正确捕获线程异常

## 注意事项

1. 异常处理器只处理特定的线程异常，其他异常仍会正常抛出
2. 软件渲染模式可能会略微影响动画性能，但提高了稳定性
3. 建议在生产环境中监控异常日志，确保修复效果

## 相关文件

- `LibComm/src/main/java/com/zczy/comm/ui/SafeWaitDialog.java`
- `LibComm/src/main/java/com/zczy/comm/utils/LottieThreadSafeUtil.java`
- `LibComm/src/main/java/com/zczy/comm/utils/ThreadSafeExceptionHandler.java`
- `PluginReact/page/widget/UIWaitLoading.tsx`
- `PluginReact/src/main/java/com/zczy/pluginreact/action/UIShowDialog.java`
- `app/src/main/java/com/zczy/AppMainContext.java`
