package com.zczy.comm.utils;

import android.os.Handler;
import android.os.Looper;

import com.airbnb.lottie.LottieAnimationView;
import com.airbnb.lottie.RenderMode;

/**
 * Lottie 线程安全工具类
 * 修复 LottieAnimationView 在非UI线程操作导致的崩溃问题
 * 
 * <AUTHOR>
 * @date 2025/08/04
 */
public class LottieThreadSafeUtil {
    
    private static final Handler MAIN_HANDLER = new Handler(Looper.getMainLooper());
    
    /**
     * 线程安全地设置 Lottie 动画
     * 
     * @param lottieView Lottie 动画视图
     * @param animationFileName 动画文件名
     */
    public static void setAnimationSafely(LottieAnimationView lottieView, String animationFileName) {
        if (lottieView == null) {
            return;
        }
        
        Runnable task = () -> {
            try {
                // 设置渲染模式为软件渲染，避免硬件加速问题
                lottieView.setRenderMode(RenderMode.SOFTWARE);
                
                // 设置动画文件
                lottieView.setAnimation(animationFileName);
                
                // 设置循环播放
                lottieView.setRepeatCount(com.airbnb.lottie.LottieDrawable.INFINITE);
                
                // 开始播放动画
                lottieView.playAnimation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            task.run();
        } else {
            MAIN_HANDLER.post(task);
        }
    }
    
    /**
     * 线程安全地停止 Lottie 动画
     * 
     * @param lottieView Lottie 动画视图
     */
    public static void cancelAnimationSafely(LottieAnimationView lottieView) {
        if (lottieView == null) {
            return;
        }
        
        Runnable task = () -> {
            try {
                if (lottieView.isAnimating()) {
                    lottieView.cancelAnimation();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            task.run();
        } else {
            MAIN_HANDLER.post(task);
        }
    }
    
    /**
     * 线程安全地暂停 Lottie 动画
     * 
     * @param lottieView Lottie 动画视图
     */
    public static void pauseAnimationSafely(LottieAnimationView lottieView) {
        if (lottieView == null) {
            return;
        }
        
        Runnable task = () -> {
            try {
                if (lottieView.isAnimating()) {
                    lottieView.pauseAnimation();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            task.run();
        } else {
            MAIN_HANDLER.post(task);
        }
    }
    
    /**
     * 线程安全地恢复 Lottie 动画
     * 
     * @param lottieView Lottie 动画视图
     */
    public static void resumeAnimationSafely(LottieAnimationView lottieView) {
        if (lottieView == null) {
            return;
        }
        
        Runnable task = () -> {
            try {
                if (!lottieView.isAnimating()) {
                    lottieView.resumeAnimation();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        };
        
        if (Looper.myLooper() == Looper.getMainLooper()) {
            task.run();
        } else {
            MAIN_HANDLER.post(task);
        }
    }
    
    /**
     * 检查当前是否在主线程
     * 
     * @return true 如果在主线程，false 否则
     */
    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
    
    /**
     * 在主线程中执行任务
     * 
     * @param task 要执行的任务
     */
    public static void runOnMainThread(Runnable task) {
        if (task == null) {
            return;
        }
        
        if (isMainThread()) {
            task.run();
        } else {
            MAIN_HANDLER.post(task);
        }
    }
}
