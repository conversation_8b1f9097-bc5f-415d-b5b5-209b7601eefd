package com.zczy.comm.utils;

import android.util.Log;

/**
 * 线程安全异常处理器
 * 专门处理 ViewRootImpl$CalledFromWrongThreadException 等线程相关异常
 * 
 * <AUTHOR>
 * @date 2025/08/04
 */
public class ThreadSafeExceptionHandler implements Thread.UncaughtExceptionHandler {
    
    private static final String TAG = "ThreadSafeExceptionHandler";
    private final Thread.UncaughtExceptionHandler defaultHandler;
    
    public ThreadSafeExceptionHandler() {
        this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
    }
    
    @Override
    public void uncaughtException(Thread thread, Throwable throwable) {
        // 检查是否是 ViewRootImpl 线程异常
        if (isViewRootImplThreadException(throwable)) {
            Log.e(TAG, "捕获到 ViewRootImpl 线程异常，已处理: " + throwable.getMessage(), throwable);
            
            // 记录异常信息但不崩溃
            logThreadException(throwable);
            
            // 不调用默认处理器，避免应用崩溃
            return;
        }
        
        // 检查是否是 Lottie 相关的线程异常
        if (isLottieThreadException(throwable)) {
            Log.e(TAG, "捕获到 Lottie 线程异常，已处理: " + throwable.getMessage(), throwable);
            
            // 记录异常信息但不崩溃
            logThreadException(throwable);
            
            // 不调用默认处理器，避免应用崩溃
            return;
        }
        
        // 其他异常交给默认处理器
        if (defaultHandler != null) {
            defaultHandler.uncaughtException(thread, throwable);
        }
    }
    
    /**
     * 检查是否是 ViewRootImpl 线程异常
     */
    private boolean isViewRootImplThreadException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        String message = throwable.getMessage();
        String className = throwable.getClass().getName();
        
        return className.contains("ViewRootImpl$CalledFromWrongThreadException") ||
               (message != null && message.contains("Only the original thread that created a view hierarchy can touch its views"));
    }
    
    /**
     * 检查是否是 Lottie 相关的线程异常
     */
    private boolean isLottieThreadException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        // 检查堆栈信息是否包含 Lottie 相关的类
        StackTraceElement[] stackTrace = throwable.getStackTrace();
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            if (className.contains("com.airbnb.lottie") || 
                className.contains("LottieAnimationView") ||
                className.contains("LottieDrawable") ||
                className.contains("LottieTask")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 记录线程异常信息
     */
    private void logThreadException(Throwable throwable) {
        try {
            Log.e(TAG, "=== 线程异常详细信息 ===");
            Log.e(TAG, "异常类型: " + throwable.getClass().getName());
            Log.e(TAG, "异常消息: " + throwable.getMessage());
            Log.e(TAG, "当前线程: " + Thread.currentThread().getName());
            
            // 记录堆栈信息
            StackTraceElement[] stackTrace = throwable.getStackTrace();
            for (int i = 0; i < Math.min(stackTrace.length, 10); i++) {
                Log.e(TAG, "堆栈[" + i + "]: " + stackTrace[i].toString());
            }
            
            Log.e(TAG, "=== 线程异常信息结束 ===");
        } catch (Exception e) {
            Log.e(TAG, "记录异常信息时发生错误", e);
        }
    }
    
    /**
     * 安装异常处理器
     */
    public static void install() {
        Thread.setDefaultUncaughtExceptionHandler(new ThreadSafeExceptionHandler());
        Log.i(TAG, "线程安全异常处理器已安装");
    }
}
