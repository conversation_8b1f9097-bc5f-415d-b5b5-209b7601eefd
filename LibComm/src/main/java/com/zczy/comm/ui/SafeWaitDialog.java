package com.zczy.comm.ui;

import android.app.AlertDialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;

import com.airbnb.lottie.LottieAnimationView;
import com.zczy.comm.R;
import com.zczy.comm.utils.LottieThreadSafeUtil;

/**
 * 线程安全的等待对话框
 * 修复 LottieAnimationView 在非UI线程操作导致的崩溃问题
 * 
 * <AUTHOR>
 * @date 2025/08/04
 */
public class SafeWaitDialog extends AlertDialog {
    
    private LottieAnimationView lottieAnimationView;
    private Handler mainHandler;
    
    public static SafeWaitDialog newToastDialog(Context context) {
        return new SafeWaitDialog(context);
    }
    
    protected SafeWaitDialog(Context context) {
        super(context, R.style.dialogToast);
        mainHandler = new Handler(Looper.getMainLooper());
        initView();
    }
    
    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.base_wait_dialog, null);
        setContentView(view);
        
        lottieAnimationView = view.findViewById(R.id.progressBar);
        
        // 使用线程安全工具类设置 Lottie 动画
        if (lottieAnimationView != null) {
            LottieThreadSafeUtil.setAnimationSafely(lottieAnimationView, "loading.json");
        }
        
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }
    
    @Override
    public void show() {
        // 确保在主线程中显示对话框
        if (Looper.myLooper() == Looper.getMainLooper()) {
            super.show();
        } else {
            mainHandler.post(() -> {
                try {
                    if (!isShowing()) {
                        super.show();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }
    
    @Override
    public void dismiss() {
        // 确保在主线程中关闭对话框
        if (Looper.myLooper() == Looper.getMainLooper()) {
            dismissInternal();
        } else {
            mainHandler.post(this::dismissInternal);
        }
    }
    
    private void dismissInternal() {
        try {
            if (isShowing()) {
                // 使用线程安全工具类停止动画
                LottieThreadSafeUtil.cancelAnimationSafely(lottieAnimationView);
                super.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        // 使用线程安全工具类停止动画以释放资源
        LottieThreadSafeUtil.cancelAnimationSafely(lottieAnimationView);
    }
}
