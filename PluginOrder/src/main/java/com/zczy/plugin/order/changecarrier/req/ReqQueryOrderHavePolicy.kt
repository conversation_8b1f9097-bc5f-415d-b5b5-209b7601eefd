package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 *ZCZY-8163 变更查询该运单是否有人身安全险
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=50348556
 */
data class ReqQueryOrderHavePolicy(
    var orderId: String? = "", // 订单id
    var sourceId: String? = "", // 货源id
) : BaseOrderRequest<BaseRsp<RspOrderHavePolicy>>("oms-app/orderChange/queryOrderHavePolicy")

class RspOrderHavePolicy(
    val safePolicyIsShow: String = "",//"是否有人身安全险 1有 0 没有
) : ResultData()