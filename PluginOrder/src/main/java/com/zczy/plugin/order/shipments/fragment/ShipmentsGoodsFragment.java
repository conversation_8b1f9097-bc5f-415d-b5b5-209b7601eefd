//package com.zczy.plugin.order.shipments.fragment;
//
//import android.text.Editable;
//import android.text.TextUtils;
//import android.text.TextWatcher;
//import android.view.View;
//import android.widget.LinearLayout;
//
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.entity.EGoodInfo;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
//import com.zczy.plugin.order.shipments.model.ShipmentsGoodsModel;
//import com.zczy.plugin.order.shipments.view.LoadAndUnloadAskView;
//import com.zczy.plugin.order.shipments.view.SelectTimeView;
//import com.zczy.plugin.order.shipments.view.ShipmentsGoodsItemView;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 功能描述:确认发货，货物明细+实际发货量
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2018/12/20
// */
//public class ShipmentsGoodsFragment extends ShipmentsBaseFragment<ShipmentsGoodsModel> implements TextWatcher {
//
//    public static ShipmentsGoodsFragment newFragment() {
//
//        return new ShipmentsGoodsFragment();
//    }
//
//    public interface GoodInfoTextWatcher {
//
//        /***
//         * 所有有输入数据货物明细计划
//         * @param data
//         */
//        void advanceMoney(List<EGoodInfo> data);
//    }
//
//    private LinearLayout ll_content;
//
//    private List<EGoodInfo> data;
//    //默认可编辑输入框内容
//    private boolean editInput = true;
//
//    private List<ShipmentsGoodsItemView> shipmentsGoodsInfoViews;
//
//    private GoodInfoTextWatcher goodInfoTextWatcher;
//
//    private boolean uploadGrossAndTare;
//
//    private ShipmentsEGoodInfo goodInfo;
//
//    private SelectTimeView timeView;
//
//    //自定义数据类型 1 : 预付申请场景进入 ,2：发货后预付申请
//    private int senceAdvance;
//
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_goods_fragment_view;
//    }
//
//    @Override
//    public void initData(View view) {
//
//        ll_content = view.findViewById(R.id.ll_content);
//        if (data != null) {
//            this.shipmentsGoodsInfoViews = new ArrayList<>(data.size());
//            for (EGoodInfo detail : data) {
//                ShipmentsGoodsItemView goodsInfoView = new ShipmentsGoodsItemView(this.getContext());
//                goodsInfoView.getEditTextAccount().setEnabled(this.editInput);
//                goodsInfoView.getEditTextAccount().addTextChangedListener(this);
//                this.ll_content.addView(goodsInfoView);
//                this.shipmentsGoodsInfoViews.add(goodsInfoView);
//                goodsInfoView.setInfo(detail, uploadGrossAndTare);
//
//                if (TextUtils.equals("1", goodInfo.getLTCantUpdate())) {
//                    //龙腾特钢 过磅后的重量就传 1，app端吨位不能修改
//                    goodsInfoView.getEditTextAccount().setEnabled(false);
//                    goodsInfoView.getEditTextAccount().setText(goodInfo.getLTTotalMoney());
//                }
//            }
//
//            if (2 != senceAdvance && goodInfo.isJDcustom()) {
//                //非货后预付申请 && ZCZY-7729 冀东定制化需求
//                timeView = new SelectTimeView(getContext());
//                this.ll_content.addView(timeView);
//                timeView.setTxt("发货磅单出场时间", "请选择时间", goodInfo.getOutStageTime());
//            }
//
//            if (TextUtils.equals("1",goodInfo.getPromptFlag())){
//
//                LoadAndUnloadAskView andUnloadAskView = new LoadAndUnloadAskView(getContext());
//                this.ll_content.addView(andUnloadAskView);
//                //货主发单时的装卸货要求
//                String consignorPrompt = goodInfo.getConsignorPrompt();
//                if (!TextUtils.isEmpty(consignorPrompt)){
//                    andUnloadAskView.setTvToast1(consignorPrompt);
//                }
//
//                //平台货物的装卸货要求
//                String platFormCargoPrompt = goodInfo.getPlatFormCargoPrompt();
//                if (!TextUtils.isEmpty(platFormCargoPrompt)) {
//                    andUnloadAskView.setTvToast2(platFormCargoPrompt);
//                }
//            }
//        }
//    }
//
//    public void setGoodInfoTextWatcher(GoodInfoTextWatcher goodInfoTextWatcher) {
//
//        this.goodInfoTextWatcher = goodInfoTextWatcher;
//    }
//
//    public void setEGoodInfoList(ShipmentsEGoodInfo goodInfo, boolean uploadGrossAndTare) {
//        this.goodInfo = goodInfo;
//        this.data = goodInfo.getRootArray();
//        this.uploadGrossAndTare = uploadGrossAndTare;
//    }
//
//    public void setSenceAdvance(int senceAdvance) {
//        this.senceAdvance = senceAdvance;
//    }
//
//    public void setEditInput(boolean editInput) {
//
//        this.editInput = editInput;
//    }
//
//
//    /***
//     * 检查输入内容
//     * @return
//     */
//    @Override
//    public boolean checkParams(ShipmentUI shipmentUI) {
//
//        //这里做确认发货的请求
//        int childSize = this.shipmentsGoodsInfoViews.size();
//        if (childSize == 0) {
//            return false;
//        }
//        List<EGoodInfo> goodInfos = new ArrayList<>(3);
//        for (int i = 0; i < childSize; i++) {
//
//            final ShipmentsGoodsItemView itemView = this.shipmentsGoodsInfoViews.get(i);
//            final EGoodInfo data = itemView.getData();
//            final String input = data.getBeforeDeliverCargoWeight();
//
//            if (itemView.getEditTextAccount().isEnabled() && TextUtils.isEmpty(input)) {
//                this.showToast("请输入[" + data.getCargoName() + "]货物重量");
//                return false;
//            }
//
//            if (Double.valueOf(input) <= 0.0) {
//                this.showToast("[" + data.getCargoName() + "]货物,货物计量不能小于等于0，请重新确认！");
//                return false;
//            }
//            if (TextUtils.equals("1",data.getUnit()) && !TextUtils.isEmpty(data.getWeightOrientationLimit())){
//                //(重)
//                if ( Double.valueOf(input) > Double.valueOf(data.getWeightOrientationLimit())){
//                    //zczy-13915_发货吨位提示错误
//                    this.showToast("" + data.getCargoName() + "货物，货物计量不能大于"+data.getWeightOrientationLimit()+"，请重新确认");
//                    return false;
//                }
//            }else  if (TextUtils.equals("2",data.getUnit()) && !TextUtils.isEmpty(data.getShippingOrientationMargin())){
//                //(重)
//                if ( Double.valueOf(input) > Double.valueOf(data.getShippingOrientationMargin())){
//                    //zczy-13915_发货吨位提示错误
//                    this.showToast("" + data.getCargoName() + "货物，货物计量不能大于"+data.getShippingOrientationMargin()+"，请重新确认");
//                    return false;
//                }
//            }
//
//            goodInfos.add(data);
//        }
//        if (timeView != null) {
//            if (TextUtils.isEmpty(timeView.getTime())) {
//                this.showToast("请选择发货磅单出场时间!");
//                return false;
//            }
//            //货物出厂时间
//            shipmentUI.leaveFactoryTime = timeView.getTime();
//        }
//        //货物ID:输入吨位
//        shipmentUI.goodInfos = goodInfos;
//        //是否需要上传皮毛重
//        shipmentUI.uploadGrossAndTare = uploadGrossAndTare;
//        return true;
//    }
//
//
//    @Override
//    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
//
//    }
//
//    @Override
//    public void onTextChanged(CharSequence s, int start, int before, int count) {
//
//    }
//
//    @Override
//    public void afterTextChanged(Editable s) {
//
//
//        if (goodInfoTextWatcher != null) {
//            List<EGoodInfo> goodsList = new ArrayList<>();
//            for (ShipmentsGoodsItemView itemView : shipmentsGoodsInfoViews) {
//                EGoodInfo goodInfo = itemView.getData();
//                if (TextUtils.isEmpty(goodInfo.getBeforeDeliverCargoWeight())) {
//                    continue;
//                }
//                goodsList.add(goodInfo);
//            }
//            goodInfoTextWatcher.advanceMoney(goodsList);
//        }
////        if (this.listener != null) {
////            if (TextUtils.isEmpty(s.toString())) {
////                this.listener.touchInteraction(this.flag, false);
////                return;
////            }
////
////            for (ShipmentsGoodsItemView infoView : this.shipmentsGoodsInfoViews) {
////                if (TextUtils.isEmpty(infoView.getData().getBeforeDeliverCargoWeight())) {
////                    this.listener.touchInteraction(this.flag, false);
////                    return;
////                }
////            }
////            this.listener.touchInteraction(this.flag, true);
////        }
//    }
//}
