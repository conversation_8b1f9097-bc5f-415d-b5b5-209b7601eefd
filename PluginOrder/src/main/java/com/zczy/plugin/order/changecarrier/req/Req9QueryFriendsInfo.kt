package com.zczy.plugin.order.changecarrier.req

import com.example.libaes.aesUtils.AESUtils
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.BaseOrderRequest
import com.zczy.plugin.order.OrderConstant
import java.io.Reader
import java.io.StringReader
import java.lang.reflect.Type

/**
 * PS: 9.变更前查询好友信息
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355528
 * Created by sdx on 2019/2/22.
 */
class Req9QueryFriendsInfo(
    var orderId: String? = null,
    var sourceId: String? = null,
    var consignorUserId: String? = null,
    var nowPage: Int = 1,
    var pageSize: Int = 10,
    var contacter: String = "", // 好友姓名
    var contacterPhone: String = "" // 好友手机号
) : BaseOrderRequest<BaseRsp<PageList<RspFriendData>>>("oms-app/orderChange/queryFriendsInfo") {
    override fun <T : Any?> parseResult(reader: Reader?, cls: Type?): T {
        val content = reader?.readText()
        val decrypt = AESUtils.decrypt(content, OrderConstant.HZ_AES_MOBILE_DECRYPT)
        return super.parseResult(StringReader(decrypt), cls)
    }
}


data class RspFriendData(
    var customerName: String = "",//滕宝红
    var friendId: String = "", // 用户名
    var consProvince: String = "",
    var mobile: String = "", // 手机号
    var headUrl: String = "", // 头像
    var id: String = "", //
//        var userName: String = "", //
    var userId: String = "", //
    var consCity: String = "", //
    var mFriendId: String = "",
    var groupdId: String = "",
    var carrierFinalPass: String = ""//   司机是否终审通过 0否1是
)

fun RspFriendData.hidePhoneNum(): String? {
    val length = mobile.length
    return if (length == 11)
        mobile.replaceRange(length - 8, length - 4, "****")
    else
        mobile
}