package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:授权码
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import android.text.TextUtils;

public enum CodeType {

    A("5a8c8b6aa3874614ba80b894f33b1adb5a8c8b6aa3874614ba80b894f33b1adb", "32921", "21","中储智运科技股份有限公司淮安分公司"),
    B("42f8654d98544074a39ef9495d4df7a142f8654d98544074a39ef9495d4df7a1", "32920", "35","中储智运科技股份有限公司金湖分公司"),
    C("d223d87c81b241a68cf0f3d22f86fc33d223d87c81b241a68cf0f3d22f86fc33", "32922", "31","中储智运科技股份有限公司徐州分公司"),
    D("94523472ffb14cddaba76ee359aaa19294523472ffb14cddaba76ee359aaa192", "32041", "1","中储智运科技股份有限公司"),
    E("1d19037d7eda4c6b9c18e52922d6d4dc2ae308abafb3489dac48d7ea5bbfb5e51sInIahgokwxhrWY", "62ETUXxnYun88sQtjaKu", "38","中储智运科技股份有限公司临夏分公司"),
    F("c4d3578267f54f2582a5792c900fbf282461bd6264544697aa349cfcddb71e57Mb63K8k2NjsFKede", "12QmLfKXqNEu0r7tGiPW", "53","中储智运科技股份有限公司天津分公司"),
    G("16f512bd90494b4088aedc8b7aa247ee2f17fa419e734e3bb5978b032092e2f7a72823db87c043e98e9e23e25a30d2c5735513eb2c99421a868902a48666130a", "3400000323", "52","安徽中储智慧物流科技有限公司"),
    H("6bb6b871157645dab310629c154f699e6bb6b871157645dab310629c154f699e", "32237", "2","江苏中储智运物流有限公司");
    public String APPID = "com.tiema.zhwl_android";
    //安全码
    public String appSecurity;
    //发送码
    public String enterpriseSenderCode;
    public String consignorSubsidiaryId;
    public String company;

    @Override
    public String toString() {
        return "CodeType{" +
                "APPID='" + APPID + '\'' +
                ", appSecurity='" + appSecurity + '\'' +
                ", enterpriseSenderCode='" + enterpriseSenderCode + '\'' +
                ", consignorSubsidiaryId='" + consignorSubsidiaryId + '\'' +
                ", company='" + company + '\'' +
                '}';
    }

    CodeType(String appSecurity, String enterpriseSenderCode, String consignorSubsidiaryId, String company) {
        this.appSecurity = appSecurity;
        this.enterpriseSenderCode = enterpriseSenderCode;
        this.consignorSubsidiaryId = consignorSubsidiaryId;
        this.company = company;
    }

    public static CodeType getCodeType(String consignorSubsidiaryId){
        for ( CodeType type: CodeType.values()){
            if (TextUtils.equals(type.consignorSubsidiaryId,consignorSubsidiaryId)){
                return type;
            }
        }
        return null;
    }

}
