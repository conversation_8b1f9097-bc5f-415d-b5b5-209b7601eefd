package com.zczy.plugin.order.changecarrier.dialog

import android.os.Bundle
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.zczy.comm.ui.BaseDialog
import com.zczy.plugin.order.R

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/5/31
 */
class OrderChangeRejectDialog : BaseDialog() {
    override fun getDialogLayout(): Int {
        return R.layout.order_change_reject_dialog
    }

    override fun getDialogTag(): String {
        return "OrderChangeRejectDialog"
    }

    override fun bindView(view: View, bundle: Bundle?) {
        val tvReject = view.findViewById<TextView>(R.id.tv_reject)
        val tvOk = view.findViewById<TextView>(R.id.tv_ok)
        val ivClose = view.findViewById<ImageView>(R.id.iv_close)
        val noteTv = view.findViewById<EditText>(R.id.noteTv)
        val sizeTv = view.findViewById<TextView>(R.id.sizeTv)
        noteTv.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(200))
        noteTv.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable) {
                if (s.length > 200) {
                    return
                }
                sizeTv.setText("(" + s.length + "/200)")
            }
        })
        ivClose.setOnClickListener { this.dismiss() }
        tvReject.setOnClickListener {
            listener?.reject()
            this.dismiss()
        }
        tvOk.setOnClickListener {
            listener?.agree(noteTv.text.toString().trim())
            this.dismiss()
        }

    }

    var listener: OrderChargeListener? = null

    interface OrderChargeListener {
        fun reject()
        fun agree(reason: String)
    }

    fun setServiceChargeListener(listener: OrderChargeListener): OrderChangeRejectDialog {
        this.listener = listener
        return this
    }
}