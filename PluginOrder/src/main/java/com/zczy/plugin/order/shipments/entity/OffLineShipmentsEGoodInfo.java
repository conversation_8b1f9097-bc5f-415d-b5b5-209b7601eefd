//package com.zczy.plugin.order.shipments.entity;
//
//import android.text.TextUtils;
//
//import com.zczy.comm.http.entity.PageList;
//import com.zczy.comm.http.entity.ResultData;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 功能描述:确认发货时查询货物信息
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public class OffLineShipmentsEGoodInfo extends ResultData {
//
//    /*** 订单ID*/
//    String orderId;
//    //货物明细
//    String allCargoName;
//    /**
//     * 货物单位：1：吨，2：m3,
//     */
//    String unit;
//    //	货物列表
//    List<EGoodInfo> cargoList;
//    String  LTTotalMoney;//	总重量（龙腾特钢运单）
//
//    String  LTCantUpdate;//司机不能修改重量（龙腾特钢运单）不能修改返回 1
//
//    public String getLTTotalMoney() {
//        return LTTotalMoney;
//    }
//
//    public void setLTTotalMoney(String LTTotalMoney) {
//        this.LTTotalMoney = LTTotalMoney;
//    }
//
//    public String getLTCantUpdate() {
//        return LTCantUpdate;
//    }
//
//    public void setLTCantUpdate(String LTCantUpdate) {
//        this.LTCantUpdate = LTCantUpdate;
//    }
//
//    public String getOrderId() {
//        return orderId;
//    }
//
//    public void setOrderId(String orderId) {
//        this.orderId = orderId;
//    }
//
//    public String getAllCargoName() {
//        return allCargoName;
//    }
//
//    public void setAllCargoName(String allCargoName) {
//        this.allCargoName = allCargoName;
//    }
//
//    public String getUnit() {
//        return unit;
//    }
//
//    public void setUnit(String unit) {
//        this.unit = unit;
//    }
//
//    public List<EGoodInfo> getCargoList() {
//        return cargoList;
//    }
//
//    public void setCargoList(List<EGoodInfo> cargoList) {
//        this.cargoList = cargoList;
//    }
//}
