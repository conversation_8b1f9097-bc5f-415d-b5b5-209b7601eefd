package com.zczy.plugin.order.changecarrier.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.req.RspFriendData
import com.zczy.plugin.order.changecarrier.req.hidePhoneNum

class OrderChangeCarrierPeopleAdapter
    : BaseQuickAdapter<RspFriendData, BaseViewHolder>(R.layout.order_change_carrier_people_list_item) {
    private var showMobile: Boolean = false
    override fun convert(helper: BaseViewHolder, item: RspFriendData) {
        helper
                // 名字
                .setText(R.id.tv_name, item.customerName)
                // 手机号
                .setText(R.id.tv_mobile, item.mobile)

        // 手机号
        val mobile = item.mobile
        if (showMobile) {
            helper.setText(R.id.tv_mobile, mobile)
        } else {
            //隐藏中间手机号
            helper.setText(R.id.tv_mobile, item.hidePhoneNum())
        }
    }


    fun hideMobilePart() {
        this.showMobile = !showMobile
        notifyDataSetChanged()
    }

    fun getShowMobile(): String {
        return if (showMobile) {
            "隐藏号码"
        } else {
            "显示号码"
        }
    }
}


