package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 4.新承运人处理变更运单
 * Created by sdx on 2019/2/22.
 */
data class Req4decideChange(
    var carrierState: String = "", // 变更决定 0不同意，  1同意
    var changeId: String? = "", // 变更单Id
    var carrierContent: String? = "", // 变更人意见
    var money: String = "", // 冻结金额
    var dispatchOperationFlag: String = "0", // 加盟动力变更标识  0:不是 1;是
    var esignFlag: String = "0", // 0 不需要电子签  1 只签合同  2 签约合同和承诺函
    var changeOrderSource: String? = null, // （新增 0 网货运单 2 TMS代开）
) : BaseOrderRequest<BaseRsp<RspDecideChange>>("oms-app/orderChange/decideChange")

data class RspDecideChange(
    //道路运输许可证状态  -1：未上传 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    //resultCode:2003
    var licenseState: String? = null,
    var vehicleId: String? = null,
    var plateNumber: String? = null,
) : ResultData()