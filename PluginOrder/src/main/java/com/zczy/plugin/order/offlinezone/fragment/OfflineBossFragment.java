package com.zczy.plugin.order.offlinezone.fragment;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/11/8
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.permission.PermissionCallBack;
import com.zczy.comm.permission.PermissionUtil;
import com.zczy.comm.utils.LocationUtil;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.bill.OffLineBillNormalActivity;
import com.zczy.plugin.order.offlinezone.OfflineZoneListModel;
import com.zczy.plugin.order.offlinezone.OfflineZoneWebDetailActivity;
import com.zczy.plugin.order.offlinezone.adapter.OfflineZoneListAdapter;
import com.zczy.plugin.order.shipments.OffLineShipmentsActivity;
import com.zczy.plugin.order.shipments.entity.RxEventShipmentsBillSuccess;
import com.zczy.plugin.order.source.pick.PickSourceTools;
import com.zczy.plugin.order.source.pick.entity.RxEventPickOffer;
import com.zczy.user.offlinezone.bean.CarrierListCountNum;
import com.zczy.user.offlinezone.bean.OfflineZoneListRes;

import io.reactivex.disposables.Disposable;

public class OfflineBossFragment extends AbstractLifecycleFragment<OfflineZoneListModel> {

    public static OfflineBossFragment newInstance(String orderState) {
        Bundle args = new Bundle();
        args.putString("orderState", orderState);
        OfflineBossFragment fragment = new OfflineBossFragment();
        fragment.setArguments(args);
        return fragment;
    }

    String orderState = "";
    SwipeRefreshMoreLayout swipeRefreshMoreLayout;

    @Override
    public int getLayout() {
        return R.layout.offline_zone_list_fragment;
    }

    @Override
    public void initData(View view) {
        orderState = getArguments().getString("orderState");

        swipeRefreshMoreLayout = view.findViewById(R.id.refreshMoreLayout);
        OfflineZoneListAdapter adapter = new OfflineZoneListAdapter();
        adapter.setUserType("2");
        swipeRefreshMoreLayout.setAdapter(adapter, true);
        swipeRefreshMoreLayout.addItemDecorationSize(15);
        swipeRefreshMoreLayout.addOnItemChildClickListener(new BaseQuickAdapter.OnItemChildClickListener() {
            @Override
            public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
                OfflineZoneListRes itemBean = (OfflineZoneListRes) adapter.getItem(position);
                if (view.getId() == R.id.btnChange) {
                    pickOrOffer(JsonUtil.toJson(itemBean), PickSourceTools.ACTION_OFFER_AGAIN);
                } else if (view.getId() == R.id.btnQuote) {
                    String text = ((Button) view).getText().toString();
                    if (TextUtils.equals(OfflineZoneListAdapter.WANT_TO_QUOTE, text)) {
                        pickOrOffer(JsonUtil.toJson(itemBean), PickSourceTools.ACTION_OFFER);
                    } else if (TextUtils.equals(OfflineZoneListAdapter.RENEW_QUOTE, text)) {
                        pickOrOffer(JsonUtil.toJson(itemBean), PickSourceTools.ACTION_OFFER_CANCLE_AGAIN);
                    } else if (TextUtils.equals(OfflineZoneListAdapter.CANCEL_QUOTE, text)) {
                        getViewModel(OfflineZoneListModel.class).cancelBid(itemBean.getExpectId());
                    }

                } else if (view.getId() == R.id.tvCopy) {
                    UtilTool.setCopyText(getContext(), "运单号", itemBean.getOrderId());

                } else if (view.getId() == R.id.bt_deliver_goods) {
                    //    确认发货
                    shipment(itemBean);
                } else if (view.getId() == R.id.bt_receive_goods) {
                    //确认收货
                    confirmUnload(itemBean);
                }else if (view.getId() == R.id.bt_cancel_waybill) {
                    //取消运单
                    cancelOrderTender(itemBean.getOrderId());
                }
            }
        });
        swipeRefreshMoreLayout.addOnItemListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                OfflineZoneListRes itemBean = (OfflineZoneListRes) adapter.getItem(position);
                // h5 详情
                OfflineZoneWebDetailActivity.start(
                        getContext(), itemBean.getOrderId()
                );
            }
        });
        swipeRefreshMoreLayout.setOnLoadListener2(new OnLoadingListener2() {
            @Override
            public void onLoadUI(int nowPage) {
                getViewModel(OfflineZoneListModel.class).getOfflineZoneListBoss(nowPage, orderState);
                if (nowPage == 1) {
                    //下拉刷新
                    postEvent(new CarrierListCountNum(0, 0));
                }
            }
        });
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(getActivity()));
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    Disposable offerOrPickPrice;

    private void pickOrOffer(String data, String action) {
        if (offerOrPickPrice != null) {
            offerOrPickPrice.dispose();
        }
        offerOrPickPrice = new PickSourceTools().inScene(PickSourceTools.SCENE_WAYBILL).setData(data).setAction(action).start(getContext());
        if (offerOrPickPrice != null){
            putDisposable(offerOrPickPrice);
        }
    }

    private void cancelOrderTender(String orderId) {
        getViewModel(OfflineZoneListModel.class).cancelOrderTender(orderId);
    }

    private void shipment(OfflineZoneListRes res) {
        if (!LocationUtil.isOpenGPS(getActivity())) {
            //GPS 未打开
            showOPenGPS(getActivity());
            return;
        }

        PermissionUtil.location(
                getActivity(),
                new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        OffLineShipmentsActivity.start(getActivity(), res.getOrderId(), res.getDetailId());
                    }
                }
        );
    }

    private void confirmUnload(OfflineZoneListRes res) {
        if (!LocationUtil.isOpenGPS(getActivity())) {
            //GPS 未打开
            showOPenGPS(getActivity());
            return;
        }
        PermissionUtil.location(
                getActivity(),
                new PermissionCallBack() {
                    @Override
                    public void onHasPermission() {
                        OffLineBillNormalActivity.startContentUI(getActivity(), res.getOrderId(), res.getDetailId());
                    }
                }
        );

    }

    private void showOPenGPS(Activity activity) {
        DialogBuilder dialog = new DialogBuilder();
        dialog.setMessage("定位功能未打开,会影响到APP的功能使用.请到【设置】>【定位】中打开开关");
        dialog.setHideCancel(true);
        dialog.setOKTextListener("去设置", new DialogBuilder.DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
                dialogInterface.dismiss();
                LocationUtil.openGPS(activity);
            }
        });
        showDialog(dialog);
    }


    @LiveDataMatch
    public void getOfflineZoneList(PageList<OfflineZoneListRes> res) {
        swipeRefreshMoreLayout.onRefreshCompale(res);
    }

    @LiveDataMatch
    public void cancelOrderTenderSuccess() {
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void cancelBid() {
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @RxBusEvent(from = "摘单议价成功")
    public void onEven(RxEventPickOffer pickOffer) {
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @RxBusEvent(from = "收货成功-》待结算【菜单】")
    public void onEventBillSuccess(RxEventShipmentsBillSuccess success) {
        if (success.type == 3) {
            //待结算
            swipeRefreshMoreLayout.onAutoRefresh();
        }
    }

    @RxBusEvent(from = " 发货成功 -》待卸货【菜单】")
    public void onEventClose(RxEventShipmentsBillSuccess success) {
        //0: 发货
        if (success.type == 2) {
            swipeRefreshMoreLayout.onAutoRefresh();
        }
    }
}
