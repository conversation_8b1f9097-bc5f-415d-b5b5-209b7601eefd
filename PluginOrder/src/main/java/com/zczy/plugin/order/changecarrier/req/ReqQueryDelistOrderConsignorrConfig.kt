package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 货主配置车辆排放类型
 * */
data class ReqQueryDelistOrderConsignorrConfig(
    var orderId: String? = null, // 订单id
    var setId: String? = null, //集合Id
    var fuelType: String? = null, //燃料类型
    var newEnergyType: String? = null, //燃料类型
) : BaseOrderRequest<BaseRsp<ReqEDelistOrderConsignorrConfig>>("oms-app/carrier/common/queryDelistOrderConsignorConfig")

class ReqEDelistOrderConsignorrConfig(
    val allowDieselCarWithSpecifiedStandardsForTransport: String = "",//货主配置车辆排放类型 6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
) : ResultData()