package com.zczy.plugin.order.offlinezone.bean;



import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.plugin.order.waybill.entity.ContractToPdf;

/**
 * 功能描述: 根据orderId查询合同Pdf
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/13
 */
public class ReqContractOfflineToPdf extends BaseNewRequest<BaseRsp<ContractToPdf>> {

    String orderId;
    public ReqContractOfflineToPdf(String orderId) {
        super("/oms-app/tender/downloadAppTenderContractToPdf" );
        this.orderId = orderId;
        setMethod(POST);
        setEncryption(false);
    }
}
