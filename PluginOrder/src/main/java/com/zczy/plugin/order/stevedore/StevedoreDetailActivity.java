package com.zczy.plugin.order.stevedore;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.config.HttpURLConfig;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.widget.ListViewForScrollView;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.adapter.ItemDetailAdapter;
import com.zczy.plugin.order.stevedore.model.ESteveDoreDeatil;
import com.zczy.plugin.order.stevedore.model.StevedoreModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:详情
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
public class StevedoreDetailActivity extends AbstractLifecycleActivity<StevedoreModel> {


    public static void startContentUI(Context context, String detailId) {

        Intent intent = new Intent (context, StevedoreDetailActivity.class);
        intent.putExtra ("detailId", detailId);
        context.startActivity (intent);
    }

    private ListViewForScrollView llContent;

    private ImageSelectProgressView billImageSelectView;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate (savedInstanceState);
        this.setContentView (R.layout.order_stevedore_detail_activity);
        UtilStatus.initStatus (this, ContextCompat.getColor (this,R.color.comm_title_bg));
        this.llContent = findViewById (R.id.ll_content);
        this.billImageSelectView = findViewById (R.id.bill_image_select_view);
        this.billImageSelectView.setDelete (false);
        this.billImageSelectView.setOnItemSelectListener(new ImageSelectProgressView.OnItemSelectListener() {
            @Override
            public void onSelectImageClick(int surplus) {

            }

            @Override
            public void onUpImageClick(String file) {

            }

            @Override
            public void onLookImageClick(List<EProcessFile> file, int position) {
                //查看大图
                List<EImage> list = new ArrayList<>(file.size());
                for (EProcessFile processFile : file) {
                    EImage image = new EImage();
                    image.setNetUrl(HttpURLConfig.getUrlImage(processFile.getImagUrl()));
                    list.add(image);
                }
                ImagePreviewActivity.start(StevedoreDetailActivity.this, list, position);
            }

            @Override
            public void onDelateClick(int position) {

            }
        });
        String detailId = getIntent ().getStringExtra ("detailId");

        this.getViewModel ().queryHandlingChargesDetail (detailId);
    }

    @LiveDataMatch(tag = "装卸费详情")
    public void onDetailSuccess(ESteveDoreDeatil doreDeatil) {

        if (doreDeatil != null) {
            llContent.setAdapter (new ItemDetailAdapter (this, doreDeatil));

            if (doreDeatil.getUrlArr () != null && !doreDeatil.getUrlArr ().isEmpty ()) {
                List<EProcessFile> data = new ArrayList<> (doreDeatil.getUrlArr ().size ());
                for (String url : doreDeatil.getUrlArr ()) {
                    EProcessFile processFile = new EProcessFile ();
                    processFile.setSuccess ();
                    processFile.setImagUrl (url);
                    data.add (processFile);
                }
                this.billImageSelectView.setShowSize(data.size(),4);
                this.billImageSelectView.setNewData (data);
            }

        }
    }

}
