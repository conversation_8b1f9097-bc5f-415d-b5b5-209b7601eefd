package com.zczy.plugin.order.shipments;

import android.content.Context;
import android.text.TextUtils;
import com.zczy.comm.config.RouterConfig;
import com.zczy.comm.utils.JsonUtil;
import com.zczy.comm.utils.RouterUtils;
import com.zczy.plugin.order.waybill.entity.EWaybill;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:确认发货
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
public class ShipmentsActivity
//        extends AbstractLifecycleActivity<ShipmentsModel>
{

    /***
     *
     * @param fragment
     * @param data
     */
    public static void start(Context fragment, EWaybill data) {

        //startNactive( fragment,  data);
        //区分 零担单个。批量零担 正常
        Map<String, Object> params = new HashMap<>();

        if (TextUtils.equals("1", data.getZeroType())) {
            //单个零担(发货)
            params.put("page", "ShipmentsZeroPage");

        } else if (TextUtils.equals("2", data.getZeroType())) {
            //批量零担(发货)
            params.put("page", "ShipmentsZeroBatchPage");

        } else if (!TextUtils.isEmpty(data.getSourceId())) {
            //ms运单Id 通过货主TMS-APP发布的运单才会有值
            params.put("page", "ShipmentsTrnPage");

        } else {
            //正常货源发货
            params.put("page", "ShipmentsPage");
        }

        params.put("data", JsonUtil.toJson(data));
        RouterUtils.skipRouter(RouterConfig.ReactRouter.URI, params);
    }

    /***
     *
     * @param fragment
     * @param data
     */
    public static void startNactive(Context fragment, EWaybill data) {

//        Intent intent = new Intent(fragment, ShipmentsActivity.class);
//        intent.putExtra("orderId", data.getOrderId());
//        intent.putExtra("detailId", data.getDetailId());
//        intent.putExtra("advanceState", data.getAdvanceState());
//        fragment.startActivity(intent);

    }
//
//    /***货主预付费*/
//    private TextView tvMoney;
//    private TextView mTvOk;
//    private String orderId;
//    private TextView tv_nonpase;
//
//    private String detailId;
//
//    /*** 预付款状态0:未确认，1:已确认，2:货主打回*/
//    private String advanceState;
//
//    private ShipmentsEGoodInfo shipmentsEGoodInfo;
//    private EAdvanceInfo mAdvanceInfo;
//    private SparseArray<ShipmentsBaseFragment> fragments = new SparseArray<>(5);
//    private ShipmenstIntegrityFragment integrity_fragment;
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate(savedInstanceState);
//        this.setContentView(R.layout.order_shipments_activity);
//
//        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
//        this.orderId = getIntent().getStringExtra("orderId");
//        this.detailId = getIntent().getStringExtra("detailId");
//        this.advanceState = getIntent().getStringExtra("advanceState");
//
//        TextView tv_orderId = this.findViewById(R.id.tv_orderId);
//        AppToolber appToolber = findViewById(R.id.appToolber);
//        tv_orderId.setText("运单编号   " + this.orderId);
//        //打回原因
//        this.tv_nonpase = this.findViewById(R.id.tv_nonpase);
//
//        this.tvMoney = findViewById(R.id.tv_money);
//        this.mTvOk = findViewById(R.id.tv_ok);
//        this.putDisposable(UtilRxView.clicks(mTvOk, 1000).subscribe((Object o) -> {
//
//            if (shipmentsEGoodInfo.isAuthorize()) {
//                //ZCZY-6636 【互联互通】兰鑫钢铁系统对接
//                new ToastDialog().setOnClickListener(new View.OnClickListener() {
//                            @Override
//                            public void onClick(View v) {
//                                ShipmentsActivity.this.postShipments();
//                            }
//                        }).setTitle("温馨提示")
//                        .setBtnText("同意")
//                        .setCon("    平台将通过系统对接形式提供您的身份证号给该货主，便于您在该货主场区顺利作业。如您充分理解上述信息的收集、使用场景并且同意，请点击“同意”。")
//                        .show(ShipmentsActivity.this);
//
//            } else {
//                ShipmentsActivity.this.postShipments();
//            }
//        }));
//        appToolber.setLeftOnClickListener(v -> {
//                    ZStatistics.onViewClick(this, "send_detail_back");
//                    finish();
//                }
//        );
//        //协议
//        integrity_fragment = (ShipmenstIntegrityFragment) getSupportFragmentManager().findFragmentById(R.id.integrity_fragment);
//
//        //查询货物信息
//        this.getViewModel().openSence(orderId, detailId);
//
//        ZStatistics.putBusinessKV(this, new OnAddBusinessKV() {
//            @Override
//            public void put(Map<String, Object> map) throws Exception {
//                Map<String, String> requestParam = new HashMap<>(1);
//                requestParam.put("orderId", orderId);
//                map.put("requestParam", requestParam);
//            }
//        });
//
//    }
//
//
//    @LiveDataMatch(tag = "查询货物信息")
//    public void onDeliverCargoQuerySuccess(final ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo) {
//
//        if (advanceInfo == null || shipmentsEGoodInfo == null) {
//            showDialogToast("未查询到相关货物信息");
//            return;
//        }
//        if (TextUtils.equals("1", shipmentsEGoodInfo.getOrderPolicyFlag())) {
//            //保障服务
//            TextView tv_instruction = findViewById(R.id.tv_instruction);
//            tv_instruction.setVisibility(View.VISIBLE);
//            tv_instruction.setText(String.format("温馨提示：此单已购买保障服务，预计扣除%s元货物保障 服务费", shipmentsEGoodInfo.getOrderPolicyMoney()));
//        }
//        this.mAdvanceInfo = advanceInfo;
//        this.shipmentsEGoodInfo = shipmentsEGoodInfo;
//        this.shipmentsEGoodInfo.setDetailId(detailId);
//
//        if (TextUtils.equals("2", this.advanceState) && !TextUtils.isEmpty(this.shipmentsEGoodInfo.getAdvanceReason())) {
//            tv_nonpase.setVisibility(View.VISIBLE);
//            //打回原因
//            SpannableString sp = new SpannableString(" 未通过原因：" + this.shipmentsEGoodInfo.getAdvanceReason());
//            //获取一张图片
//            Drawable drawable = ContextCompat.getDrawable(this, R.drawable.base_message_black_ic);
//            drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight());
//            //居中对齐imageSpan
//            CenterAlignImageSpan imageSpan = new CenterAlignImageSpan(drawable);
//            sp.setSpan(imageSpan, 0, 1, ImageSpan.ALIGN_BASELINE);
//            tv_nonpase.setText(sp);
//        }
//
//        integrity_fragment.setData(detailId, shipmentsEGoodInfo, advanceInfo);
//
//        if (TextUtils.equals("1", shipmentsEGoodInfo.getGoodsSource())) {
//            //ZCZY-7768 批量货按箱发布（针对广州中物储）(界面优先判断集装箱)
//            this.containerUI(this.shipmentsEGoodInfo, advanceInfo);
//        } else {
//            switch (advanceInfo.getAdvanceType()) {
//                case "1": {
//                    //个体司机指定订单
//                    if (advanceInfo.isAdvance()) {
//                        // => 走货主预付款
//                        tvMoney.setVisibility(View.VISIBLE);
//                        this.normallUI(advanceInfo.getOrderImgObj(), advanceInfo.getPeopleVehicleImgObj(), shipmentsEGoodInfo, advanceInfo, EAgreement.Query.SHIP);
//                    } else {
//                        // => 走普通发货
//                        this.normallUI(shipmentsEGoodInfo.getOrderImgObj(), shipmentsEGoodInfo.getPeopleVehicleImgObj(), shipmentsEGoodInfo, advanceInfo, EAgreement.Query.SHIP);
//                    }
//                    break;
//                }
//                case "2":
//                case "4": {
//                    //2.个体司机非指定订单 => 走平台预付
//                    // 4.个体司机关联车老板模式 => 走平台预付
//                    this.terracePayUI(this.shipmentsEGoodInfo, advanceInfo);
//                    break;
//                }
//                case "3": {
//                    // 车老板自己摘牌订单 => 车老板预付
//                    if (advanceInfo.isAdvance()) {
//                        this.normallUI(advanceInfo.getOrderImgObj(), advanceInfo.getPeopleVehicleImgObj(), shipmentsEGoodInfo, advanceInfo, EAgreement.Query.INTEGRITY, EAgreement.Query.SHIP);
//                    } else {
//                        this.normallUI(shipmentsEGoodInfo.getOrderImgObj(), shipmentsEGoodInfo.getPeopleVehicleImgObj(), shipmentsEGoodInfo, advanceInfo, EAgreement.Query.SHIP);
//                    }
//                    break;
//                }
//                case "5": {
//                    //承运商指派订单 => 走普通发货
//                    this.normallUI(shipmentsEGoodInfo.getOrderImgObj(), shipmentsEGoodInfo.getPeopleVehicleImgObj(), shipmentsEGoodInfo, advanceInfo, EAgreement.Query.SHIP);
//                    break;
//                }
//                default:
//            }
//        }
//    }
//
//
//    /* ------------------------------ 平台预付-------------------------------------------------*/
//    private void terracePayUI(final ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo) {
//        // 【平台预付】
//        if (TextUtils.equals("2", this.advanceState)) {
//            //打回
//            mTvOk.setText("重新确认发货");
//        }
//        tvMoney.setVisibility(View.GONE);
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//        // 预付费开关-优惠券【展示】
//        ShipmentsTerracePayFragment terrace = ShipmentsTerracePayFragment.newFragment();
//        terrace.setData(0, this.advanceState, shipmentsEGoodInfo, advanceInfo, openColse -> ShipmentsActivity.this.openColseFragment(openColse));
//        this.fragments.put(ShipmentUI.TAG_OFF_ON.hashCode(), terrace);
//        transaction.add(R.id.ll_content, terrace, ShipmentUI.TAG_OFF_ON);
//
//        //货物明细【展示】
//        ShipmentsGoodsFragment goods = ShipmentsGoodsFragment.newFragment();
//        goods.setEGoodInfoList(shipmentsEGoodInfo, shipmentsEGoodInfo.uploadGrossAndTareWeightFlag());
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goods);
//        transaction.add(R.id.ll_content, goods, ShipmentUI.TAG_GOODINFO);
//
//        transaction.commitAllowingStateLoss();
//    }
//
//    private void openColseFragment(boolean openColse) {
//
//        FragmentManager fragmentManager = this.getSupportFragmentManager();
//        FragmentTransaction transaction = fragmentManager.beginTransaction();
//
//        //1.单据照片
//        Fragment waybill = fragmentManager.findFragmentByTag(ShipmentUI.TAG_IMAGE_WAYBILL);
//        if (waybill == null) {
//            waybill = this.fragments.get(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode());
//        }
//
//        ShipmentsImgObj orderImgObj = openColse ? mAdvanceInfo.getOrderImgObj() : shipmentsEGoodInfo.getOrderImgObj();
//        if (orderImgObj != null && orderImgObj.isShowUpload()) {
//            //【显示】
//            if (waybill == null) {
//                ShipMentFileFragment fileFragment = ShipMentFileFragment.newFragment(orderId, this.shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//                fileFragment.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//                transaction.add(R.id.ll_content, fileFragment, ShipmentUI.TAG_IMAGE_WAYBILL);
//                this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), fileFragment);
//                waybill = fileFragment;
//            }
//
//            if (waybill instanceof ShipMentFileFragment) {
//                ShipMentFileFragment fileFragment = (ShipMentFileFragment) waybill;
//                this.setCheckListener(fileFragment, orderImgObj);
//            }
//
//        } else if (waybill != null) {
//            //【不显示】
//            transaction.remove(waybill);
//            this.fragments.remove(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode());
//        }
//
//        //2.人车货合影
//        ShipmentsImgObj peopleVehicleImgObj = openColse ? mAdvanceInfo.getPeopleVehicleImgObj() : shipmentsEGoodInfo.getPeopleVehicleImgObj();
//        Fragment person = fragmentManager.findFragmentByTag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//        if (person == null) {
//            person = this.fragments.get(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode());
//        }
//        if (peopleVehicleImgObj != null && peopleVehicleImgObj.isShowUpload()) {
//            //【显示】
//            if (person == null) {
//                ShipMentFileFragment personFragment = ShipMentFileFragment.newFragment(orderId, this.shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//                personFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//                transaction.add(R.id.ll_content, personFragment, ShipmentUI.TAG_IMAGE_PERSONCAR);
//                this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), personFragment);
//                person = personFragment;
//            }
//
//            if (person instanceof ShipMentFileFragment) {
//                ShipMentFileFragment fileFragment = (ShipMentFileFragment) person;
//                //预付开启时，指定拍照,必传，加水印
//                this.setCheckListener(fileFragment, peopleVehicleImgObj);
//            }
//        } else if (person != null) {
//            //【不显示】
//            transaction.remove(person);
//            this.fragments.remove(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode());
//        }
//
//        if (openColse) {
//            //必需显示内容 协议 显示：（ 本地固定预付服务说明,预付类协议，确认发货协议）
//            integrity_fragment.setShowKey(EAgreement.Query.LOCAL, EAgreement.Query.INTEGRITY, EAgreement.Query.SHIP);
//            this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//        } else {
//            //必需显示内容 协议 显示：（ 确认发货协议）
//            integrity_fragment.setShowKey(EAgreement.Query.SHIP);
//            this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//        }
//        transaction.commitAllowingStateLoss();
//
//        ZStatistics.onViewClick(this, openColse ? "openFragment" : "colseFragment");
//    }
//
//    /* ------------------------------ 【正常发货】-------------------------------------------------*/
//    private void normallUI(ShipmentsImgObj orderImgObj, ShipmentsImgObj peopleVehicleImgObj, ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo, EAgreement.Query... queries) {
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//        //1.货物明细【显示】
//        ShipmentsGoodsFragment goodsFragment = ShipmentsGoodsFragment.newFragment();
//        goodsFragment.setEGoodInfoList(shipmentsEGoodInfo, shipmentsEGoodInfo.uploadGrossAndTareWeightFlag());
//        goodsFragment.setGoodInfoTextWatcher(new ShipmentsGoodsFragment.GoodInfoTextWatcher() {
//            @Override
//            public void advanceMoney(List<EGoodInfo> data) {
//
//                if (TextUtils.equals("1", advanceInfo.getAdvanceType()) && advanceInfo.isAdvance()) {
//                    //计算货主预付费用 【货主预付发货】
//                    String money = "¥" + shipmentsEGoodInfo.calculationAdvanceMoney(mAdvanceInfo.getAdvanceRatio(), data);
//                    SpannableStringBuilder builder = new SpannableStringBuilder("预付运费 ");
//                    SpannableString text = new SpannableString(money);
//                    text.setSpan(new ForegroundColorSpan(Color.parseColor("#FB6B40")), 0, money.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
//                    builder.append(text);
//                    tvMoney.setText(builder);
//                }
//            }
//        });
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goodsFragment);
//        transaction.add(R.id.ll_content, goodsFragment, ShipmentUI.TAG_GOODINFO);
//
//        //2. 单据照片
//        if (orderImgObj != null && orderImgObj.isShowUpload()) {
//            //【显示】
//            ShipMentFileFragment shipmentBill = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//            shipmentBill.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.setCheckListener(shipmentBill, orderImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), shipmentBill);
//            transaction.add(R.id.ll_content, shipmentBill);
//        }
//
//        //3.人车货合影照片
//        if (peopleVehicleImgObj != null && peopleVehicleImgObj.isShowUpload()) {
//            //【显示】
//            ShipMentFileFragment personCarFileFragment = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//            personCarFileFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.setCheckListener(personCarFileFragment, peopleVehicleImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), personCarFileFragment);
//            transaction.add(R.id.ll_content, personCarFileFragment);
//        }
//
//        //必需显示内容 协议 显示：（ 确认发货协议）
//        integrity_fragment.setShowKey(queries);
//        this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//
//        transaction.commitAllowingStateLoss();
//
//    }
//
//    private void setCheckListener(ShipMentFileFragment fileFragment, ShipmentsImgObj imgObj) {
//        //是否加水印
//        fileFragment.setWaterMarkFlag(imgObj.getWaterMarkFlag());
//        //指定拍照(图片可拍照，可选择相册)
//        fileFragment.setTakePhotoListener(imgObj.isTakePhoto());
//        //红色* 号提示必传/非必传
//        fileFragment.setShowReadToastIcon(imgObj.isUpload());
//        //设置校验检查
//        fileFragment.setNoCheckFileListener(list -> !imgObj.isUpload());
//    }
//
//    /* ------------------------------ 【批量货按箱发货】-------------------------------------------------*/
//    private void containerUI(ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo) {
//        //【批量货按箱发货】
//        tvMoney.setVisibility(View.GONE);
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//        //1.货物明细【显示】
//        ShipmentsContainerGoodsFragment goodsFragment = ShipmentsContainerGoodsFragment.newFragment();
//        goodsFragment.setEGoodInfoList(shipmentsEGoodInfo, advanceInfo);
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goodsFragment);
//        transaction.add(R.id.ll_content, goodsFragment, ShipmentUI.TAG_GOODINFO);
//
//        //2.单据照片
//        ShipmentsImgObj orderImgObj = shipmentsEGoodInfo.getOrderImgObj();
//        if (orderImgObj.isShowUpload()) {
//            //【显示】
//            ShipMentFileFragment shipmentBill = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//            shipmentBill.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.setCheckListener(shipmentBill, orderImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), shipmentBill);
//            transaction.add(R.id.ll_content, shipmentBill);
//        }
//
//        //3.人车货合影照片
//        ShipmentsImgObj peopleVehicleImgObj = shipmentsEGoodInfo.getPeopleVehicleImgObj();
//        if (peopleVehicleImgObj.isShowUpload()) {
//            //【显示】（必传）
//            ShipMentFileFragment personCarFileFragment = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//            personCarFileFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.setCheckListener(personCarFileFragment, peopleVehicleImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), personCarFileFragment);
//            transaction.add(R.id.ll_content, personCarFileFragment);
//        }
//
//        //必需显示内容 协议 显示：（ 确认发货协议）
//        integrity_fragment.setShowKey(EAgreement.Query.SHIP);
//        this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//
//        transaction.commitAllowingStateLoss();
//
//    }
//
//    /* ------------------------------ 确认发货-------------------------------------------------*/
//    private void postShipments() {
//
//        //确认发货
//        UtilSoftKeyboard.hide(mTvOk);
//        //ZCZY-4491 司机确认收发货时获取APP定位优化
//        if (!LocationUtil.isOpenGPS(this)) {
//            //GPS 未打开
//            LocationUtil.openGPS(this);
//            return;
//        }
//
//        PermissionUtil.location(ShipmentsActivity.this, new PermissionCallBack() {
//            @Override
//            public void onHasPermission() {
//                shipments();
//            }
//        });
//    }
//
//    private void shipments() {
//        final int size = fragments.size();
//        if (size == 0) {
//            return;
//        }
//        ShipmentUI shipmentUI = new ShipmentUI();
//        shipmentUI.goodsSource = shipmentsEGoodInfo.getGoodsSource();
//        shipmentUI.orderId = orderId;
//        shipmentUI.detailId = detailId;
//        shipmentUI.creditPoint = mAdvanceInfo.getCreditPoint();
//        shipmentUI.sdkInfoObj = shipmentsEGoodInfo.getSdkInfoObj();
//        shipmentUI.haveOpenSdk = shipmentsEGoodInfo.isHaveOpenSdk();
//        shipmentUI.deliverTrackPersonImgFlag = shipmentsEGoodInfo.getDeliverTrackPersonImgFlag();
//        shipmentUI.coordinateFlag = shipmentsEGoodInfo.getDeliverPicConfig();
//
//        if (TextUtils.equals("2", this.advanceState)) {
//            //重新发货，则不需要再次上传省部平台2.0 订单数据
//            shipmentUI.haveOpenSdk = false;
//        }
//        ShipmentsTerracePayFragment terracePayFragment = null;
//
//        for (int i = 0; i < size; i++) {
//            ShipmentsBaseFragment fragment = fragments.valueAt(i);
//            if (fragment instanceof ShipmentsTerracePayFragment) {
//                terracePayFragment = (ShipmentsTerracePayFragment) fragment;
//            }
//            if (!fragment.checkParams(shipmentUI)) {
//                return;
//            }
//        }
//        if (TextUtils.equals("1", shipmentUI.isAdvanceButtonOn)) {
//            //预付款
//            ShipmentsTerracePayFragment finalTerracePayFragment = terracePayFragment;
//
//            if (TextUtils.equals("2", mAdvanceInfo.getAdvanceType())) {
//                //2.个体司机非指定订单
//                ReqQueryAdvanceInfoBeforeCommit infoBeforeCommit = new ReqQueryAdvanceInfoBeforeCommit();
//                infoBeforeCommit.setAdvanceType(shipmentUI.advanceWay);
//                infoBeforeCommit.setOrderId(shipmentUI.orderId);
//                infoBeforeCommit.setWeightDetails(shipmentUI.getCargoIdWeightData());
//                infoBeforeCommit.setAdvanceOilRatio(shipmentUI.oilRatio);
//                this.getViewModel(ShipmentsModel.class).execute(true, infoBeforeCommit, new IResult<BaseRsp<EAdvanceInfoBeforeCommit>>() {
//                    @Override
//                    public void onFail(HandleException e) {
//                        showDialogToast(e.getMsg());
//                    }
//
//                    @Override
//                    public void onSuccess(BaseRsp<EAdvanceInfoBeforeCommit> rsp) throws Exception {
//                        if (rsp.success()) {
//                            DialogBuilder dialogBuilder = new DialogBuilder();
//                            dialogBuilder.setTitle("提交预付款审核");
//                            if (TextUtils.equals("1", rsp.getData().getTipFlag())) {
//                                String attributeMsg = rsp.getData().getAttributeMsg();
//                                String totalMsg = rsp.getData().getTotalMsg();
//                                int startIndex = totalMsg.indexOf(attributeMsg);
//
//                                SpannableString stringBuilder = new SpannableString(totalMsg);
//                                stringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#F42424")), startIndex, startIndex + attributeMsg.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                                dialogBuilder.setMessage(stringBuilder);
//
//                            } else {
//                                dialogBuilder.setMessage(rsp.getData().getTotalMsg());
//                            }
//                            dialogBuilder.setCancelListener((dialog, which) -> {
//                                dialog.dismiss();
//
//                                if (TextUtils.equals("1", rsp.getData().getChangRatioFlag())) {
//                                    //预付比例是否变更 1:变更,变动界面比例
//                                    if (finalTerracePayFragment != null) {
//                                        finalTerracePayFragment.onChangeAdvanceRatio(rsp.getData().getAdvanceRatio());
//                                    }
//                                }
//                            });
//                            dialogBuilder.setOkListener((dialog, which) -> {
//                                dialog.dismiss();
//                                ZStatistics.onViewClick(ShipmentsActivity.this, "deliver_advance");
//                                getViewModel(ShipmentsModel.class).confrimOrderDeliver(shipmentUI);
//
//                            });
//                            showDialog(dialogBuilder);
//
//                        } else {
//                            showDialogToast(rsp.getMsg());
//                        }
//                    }
//                });
//            } else {
//
//                //预付款
//                DialogBuilder dialogBuilder = new DialogBuilder();
//                dialogBuilder.setTitle("提交预付款审核");
//                String msg = "";
//                if (TextUtils.equals("4", mAdvanceInfo.getAdvanceType())) {
//
//                    //"提交后平台会进行审核，审核通过后会将预付运费打款至您关联的车老板智运宝账户"
//                    msg = TextUtils.equals("2", shipmentUI.advanceWay) ?
//                            "提交后平台会进行审核，审核通过会将预付运费打款至您关联的车老板智运油卡账户" :
//                            TextUtils.equals("3", shipmentUI.advanceWay) ?
//                                    "提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本和智运油卡账户中进行查询" : "提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本中进行查询";
//
//                } else {
//                    //1:预付现金;2:预付油品;3:预付油品+现金
//                    //
//                    msg = TextUtils.equals("2", shipmentUI.advanceWay) ?
//                            "提交后平台会进行审核，审核通过会将预付运费打款至您的智运油卡账户" :
//                            TextUtils.equals("3", shipmentUI.advanceWay) ?
//                                    "提交后平台会进行审核，审核通过后您可在智运账本和智运油卡账户中进行查询" : "提交后平台会进行审核，审核通过后您可在智运账本中进行查询";
//                }
//                dialogBuilder.setMessage(msg);
//
//                dialogBuilder.setOkListener((dialog, which) -> {
//                    dialog.dismiss();
//                    ZStatistics.onViewClick(ShipmentsActivity.this, "deliver_advance");
//                    getViewModel().confrimOrderDeliver(shipmentUI);
//                });
//                this.showDialog(dialogBuilder);
//            }
//        } else {
//            this.getViewModel(ShipmentsModel.class).confrimOrderDeliver(shipmentUI);
//        }
//
//        ZStatistics.onViewClick(this, "send_detail_deliver");
//    }
//
//    @LiveDataMatch(tag = "发货成功")
//    public void onShipmentSuccess(boolean payTerrace, EShipmentsSuccess data, ShipmentUI shipmentUI) {
//
//        //发货确认提示框
//        ShipmentsSuccessActivity.start(this, payTerrace ? "1" : "0", shipmentUI.orderId, shipmentUI.advanceWay, shipmentsEGoodInfo, mAdvanceInfo);
//        this.finish();
//
//    }
//
//
//    @LiveDataMatch(tag = "客服电话")
//    public void onShowLineServerPhone() {
//        PhoneUtil.callPhone(this, Const.PHONE_SERVER_400);
//    }


}
