package com.zczy.plugin.order.navigation.req

import android.os.Parcel
import android.os.Parcelable
import com.amap.api.services.core.LatLonPoint
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import java.io.Serializable

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/28queryTmsAddressInfoBySourceId
 */
class ReqOrderCoordinate(
    var orderId: String = ""
) : BaseNewRequest<BaseRsp<OrderCoordinate>>("oms-app/order/common/queryOrderCoordinate")

class ReqQueryTmsAddressInfoBySourceId(
    var sourceId: String = "",
    var consignorUserId: String = ""
) : BaseNewRequest<BaseRsp<OrderCoordinate>>("oms-app/order/common/queryTmsAddressInfoBySourceId")

data class OrderCoordinate(
    var despatchCoordinateX: String = "",
    var despatchCoordinateY: String = "",
    var deliverCoordinateX: String = "",
    var deliverCoordinateY: String = "",
    var despatchProCityDisPlace: String = "", // 启运地省市区详细地址
    var deliverProCityDisPlace: String = "" // 目的地省市区详细地址
) : ResultData(), Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: ""
    ) {
    }


    fun point1(): LatLonPoint {
        return LatLonPoint(despatchCoordinateY.toDouble(), despatchCoordinateX.toDouble())
    }

    fun point2(): LatLonPoint {
        return LatLonPoint(deliverCoordinateY.toDouble(), deliverCoordinateX.toDouble())
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(despatchCoordinateX)
        parcel.writeString(despatchCoordinateY)
        parcel.writeString(deliverCoordinateX)
        parcel.writeString(deliverCoordinateY)
        parcel.writeString(despatchProCityDisPlace)
        parcel.writeString(deliverProCityDisPlace)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<OrderCoordinate> {
        override fun createFromParcel(parcel: Parcel): OrderCoordinate {
            return OrderCoordinate(parcel)
        }

        override fun newArray(size: Int): Array<OrderCoordinate?> {
            return arrayOfNulls(size)
        }
    }
}
