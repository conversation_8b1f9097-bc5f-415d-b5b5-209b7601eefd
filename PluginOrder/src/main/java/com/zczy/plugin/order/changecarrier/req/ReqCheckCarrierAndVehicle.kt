package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：ZCZY-14730 证件过期管控优化-业务部分
 * 时间：2023/12/12 0012 19:25
 * 作者：郭翰林
 */
class ReqCheckCarrierAndVehicle(
    var carrierUserId: String = "", //变更人ID
    var vehicleId: String = "",//车辆ID
    var plateNumber: String = "",//车牌号
    var changeType: String = ""//1 人 2 车 必传
) :
    BaseNewRequest<BaseRsp<RspCheckCarrierAndVehicle>>("oms-app/orderChange/checkCarrierAndVehicle")


class RspCheckCarrierAndVehicle(
    var carrierLicenseState: String,//0 不提示 1 提示 不拦截  2 提示 拦截
    var updateLicenseFlag: String,// 0 不能更新证件  1 可以更新证件
) : ResultData()