package com.zczy.plugin.order.supervise;
/*=============================================================================================
 * 功能描述:省平台监管
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/16
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import android.content.Context;

import androidx.work.WorkManager;

import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.Constants;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.sfh.lib.AppCacheManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.supervise.action.InitAction;
import com.zczy.plugin.order.supervise.action.PauseAction;
import com.zczy.plugin.order.supervise.action.StartAction;
import com.zczy.plugin.order.supervise.action.StopAction;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;


public class SuperviseManager {

    private static final class Hondler {
        static final SuperviseManager sInit = new SuperviseManager();
    }

    public static SuperviseManager getInstance() {
        return Hondler.sInit;
    }

    public void initApp() {
        new InitAction(AppCacheManager.getApplication()).start();
    }

    public void postOrderInfo(ActionType type, String orderId, ESDKInfoObj sdkInfoObj) {
        switch (type){
            case Start:{
                sdkInfoObj.setOrderId(orderId);
                new StartAction(sdkInfoObj).start();
                break;
            }
            case Stop:{
                sdkInfoObj.setOrderId(orderId);
                new StopAction(sdkInfoObj).start();
                break;
            }
            default:{

            }
        }
    }

    public void logout() {
       new PauseAction().start();
    }



}
