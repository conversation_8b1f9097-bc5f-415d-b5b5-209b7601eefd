package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 6. 历史变更记录列表
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=12355400
 * Created by sdx on 2019/2/22.
 */
data class Req6QueryChangeOrderInfoPage(
        var currentPage: Int = 1,
        var pageSize: Int = 10,
        /** 搜索条件 */
        var title: String = "",
        /**
         * 1 同意列表  ,
         * 2 历史 ,
         * 3 变更历史下的 变更生效 ,
         * 4 变更历史下的 变更未生效
         */
        var selectType: String = ""// 1（查询变更同意列表） 2（查询变更历史列表）
) : BaseOrderRequest<BaseRsp<PageList<RspOrderChangeData>>>("oms-app/orderChange/queryChangeOrderInfoPage")

data class RspOrderChangeData(
    var orderId: String = "",// 订单id
    /**
         * 1变更成功
         * 2变更失败
         * 3拒绝变更
         * */
    var changeResult: String? = "",
    var changeRecordId: String = "",//变更主键
    var dispatchOperationFlag: String = "",// 是否加盟运力变更 0 否 1 是
    var changeTimeStr: String = "",//申请时间
    var consignorUserId: String = "",//  货主userId
    var consignorMobile: String = "", //货主手机号
    var carrierHandleUserId: String = "", // 变更人ID
    var detailList: List<OrderChangesDetailDto>,
    var isLessThanOrder: String = "", //1 表示零担
    var changeOrderSource: String = "", //（新增 0 网货运单 2 TMS代开）
    var modeType: String = "", //业务模式：0：网货专票 1：代开专票  2：不要票
    var changeSourceType: String = "", //[1-货主 2-承运方 3-后台 4-默认]',
)

class OrderChangesDetailDto {
    /**
     *
    1-车牌号变更 2-货物名称变更 3-发货单位变更 4-发货人变更 5-发货人联系电话变更 6-启运地变更
    7-具体发货地址变更 8-收货单位变更 9-收货人变更
    10-收货人联系电话变更 11-目的地变更 12-具体收货地址变更 13-变更承运人
     */
    var orderId: String = ""// 订单id
    var changeType: String = ""//变更类型
    var oldValue: String = "" //变更前
    var newValue: String = "" //变更后
    var effectTimeStr: String = ""  //生效时间
    var changeTypeStr: String = "" // changeTypeStr变更类型中文
}

fun RspOrderChangeData.formatChangeResult(): String {
    /**
     * 1变更成功
     * 2变更失败
     * 3拒绝变更
     * */
    return when (changeResult) {
        "1" -> "变更成功"
        "2" -> "变更失败"
        "3" -> "拒绝变更"
        "4" -> "取消变更"
        else -> ""
    }
}

