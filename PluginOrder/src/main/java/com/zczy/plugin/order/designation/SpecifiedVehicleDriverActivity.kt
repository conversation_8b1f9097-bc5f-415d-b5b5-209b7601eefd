package com.zczy.plugin.order.designation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.ui.BaseActivity
import com.zczy.overdue.boss.BossExpiredCertificateManagementActivity
import com.zczy.overdue.cyr.CyrExpiredCertificateManagementActivity
import com.zczy.overdue.cys.CysExpiredCertificateManagementActivity
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.RxEventPointCar
import com.zczy.plugin.order.changecarrier.req.ReqQueryDelistUserElectronicSignState
import com.zczy.plugin.order.designation.model.DesignationVehicleDriverModel
import com.zczy.plugin.order.designation.req.ReqCarrierLicenseTransitionPeriod
import com.zczy.plugin.order.designation.req.ReqQueryUserAndVehicleDelistRiskStates
import com.zczy.plugin.order.designation.req.ReqSpecidiedVehichleDriver
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.source.pick.CheckUserAndVehicleStates
import com.zczy.plugin.order.source.pick.OrderDriverListActivity
import com.zczy.plugin.order.source.pick.PickAntiDraudAction
import com.zczy.plugin.order.source.pick.entity.DelistPrePayStatus
import com.zczy.plugin.order.source.pick.entity.EDriverUser
import com.zczy.plugin.order.source.pick.entity.EVehicle
import com.zczy.plugin.order.source.pick.fragment.OnUserAndVehicleStates
import com.zczy.plugin.order.source.pick.fragment.OrderPickOfferCarFragmnetV1
import com.zczy.plugin.order.source.pick.model.request.ReqBossDelistPrePayStatus
import com.zczy.plugin.order.source.pick.wight.PrePaidDiaolog


/**
 *    author : Ssp
 *    date   : 2019/7/110:20
 *    desc   :已指定车辆和司机
 *    version: 1.0
 */
class SpecifiedVehicleDriverActivity : BaseActivity<DesignationVehicleDriverModel>(),
    View.OnClickListener {

    /**
     * 服务费金额
     */
    private val tvMoney by lazy { findViewById<TextView>(R.id.tv_money) }

    /**
     * 预付开关
     */
    private val rbOpen by lazy { findViewById<CheckBox>(R.id.rb_open) }

    /**
     * 协议
     */
    private val cbXieYi by lazy { findViewById<CheckBox>(R.id.cb_xieyi) }

    /**
     * 选择司机
     */
    private val tvSelectDriver by lazy { findViewById<TextView>(R.id.tv_select_driver) }

    /**
     * 派单
     */
    private val tvCommit by lazy { findViewById<TextView>(R.id.tv_commit) }

    /**
     * 协议内容入口
     */
    private val tvRead by lazy { findViewById<TextView>(R.id.tv_read) }

    /**
     * 预付服务布局
     */
    private val clMoney by lazy { findViewById<ConstraintLayout>(R.id.cl_money) }

    /**
     * 协议布局
     */
    private val clXieYi by lazy { findViewById<ConstraintLayout>(R.id.cl_xieyi) }

    /**
     * 选择车辆
     */
    private lateinit var pickCarFragment: OrderPickOfferCarFragmnetV1
    private var mData: DelistPrePayStatus = DelistPrePayStatus()
    private var orderId: String = ""
    private var setId: String = ""
    private var checkPersonCar: OnUserAndVehicleStates? = null
    private var esignFlag: String = "0"
    private var driverUser: EDriverUser? = null
    private var selectedCar: EVehicle? = null
    private var mReqSpecidiedVehichleDriver = ReqSpecidiedVehichleDriver()

    companion object {
        @JvmStatic
        fun startContentUI(
            context: Context,
            orderId: String,
            orderType: String,
            setId: String? = ""
        ) {
            val intent = Intent(context, SpecifiedVehicleDriverActivity::class.java)
            intent.putExtra("orderId", orderId)
            intent.putExtra("orderType", orderType)
            intent.putExtra("setId", setId ?: "")
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.specified_vehicle_driver_activity
    }

    override fun initData() {

        orderId = intent.getStringExtra("orderId") ?: ""
        setId = intent.getStringExtra("setId") ?: ""
        val orderType = intent.getStringExtra("orderType") ?: ""

        // 查询车老板预付
        val req = ReqBossDelistPrePayStatus()
        req.orderId = orderId
        req.orderType = if (TextUtils.isEmpty(orderType)) "1" else "2"
        viewModel?.queryBossDelistPrePayStatus(req)
        pickCarFragment.queryCarrierBossRelationList(orderId)

        checkPersonCar = CheckUserAndVehicleStates(
            this@SpecifiedVehicleDriverActivity,
            viewModel,
            orderId,
            setId
        )
        pickCarFragment.setOnUserAndVehicleStates(checkPersonCar)
        pickCarFragment.setOrderId(orderId)

        //ZCZY-18042  司机摘单时选择确认收货地址
        PickAntiDraudAction(
            this@SpecifiedVehicleDriverActivity,
            getViewModel(DesignationVehicleDriverModel::class.java),
            PickAntiDraudAction.NODE_ASSIGN
        )
            .setOrderId(orderId)
            .setSetId(setId)
            .showAntiDraudDialog {

            }
    }

    override fun bindView(bundle: Bundle?) {
        tvCommit.setOnClickListener(this)
        tvSelectDriver.setOnClickListener(this)
        tvRead.setOnClickListener(this)
        pickCarFragment =
            supportFragmentManager.findFragmentById(R.id.select_car) as OrderPickOfferCarFragmnetV1
        pickCarFragment.setOnEnableSlect(object : OrderPickOfferCarFragmnetV1.OnEnableSlect {
            override fun isOK(): Boolean {
                if (driverUser == null || TextUtils.isEmpty(driverUser?.driverId)) {
                    showToast("请先选择司机")
                    return false
                }
                return true
            }
        })
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.tv_commit -> {
                //确认派单
                commitData()
            }
            R.id.tv_select_driver -> {
                //选择司机
                //打开选择司机UI
                OrderDriverListActivity.startContentUI(this)
            }
            R.id.tv_read -> {
                //预付服务说明
                if (mData != null) {
                    PrePaidDiaolog()
                        .setData(mData)
                        .show(this)
                }
            }
        }
    }

    /**
     * 注释：ZCZY-14730 证件过期管控优化-业务部分
     * 时间：2023/12/12 0012 14:20
     * 作者：郭翰林
     */
    private fun queryCarrierLicenseTransitionPeriod(checkDelistType: String) {
        val request = ReqCarrierLicenseTransitionPeriod()
        request.vehicleId = selectedCar?.vehicleId ?: ""
        request.driverUserId = driverUser?.driverId ?: ""
        request.checkDelistType = checkDelistType
        queryCarrierLicenseTransitionPeriod(this@SpecifiedVehicleDriverActivity, request)
    }

    /**
     * 派单
     */
    fun commitData() {
        mReqSpecidiedVehichleDriver.orderId = orderId
        val carId = pickCarFragment.getVehicleInfo()
        if (TextUtils.isEmpty(carId.vehicleId)) {
            return
        }
        mReqSpecidiedVehichleDriver.vehicleId = carId.vehicleId
        if (driverUser == null || TextUtils.isEmpty(driverUser?.driverId)) {
            showDialogToast("请选择司机!")
            return
        }
        mReqSpecidiedVehichleDriver.driverUserId = driverUser?.driverId ?: ""
        if (rbOpen.isChecked) {
            if (!cbXieYi.isChecked) {
                this.showDialogToast("请勾选《预付服务说明》")
                return
            }
            mReqSpecidiedVehichleDriver.isAdvance = "1"
        } else {
            //后台服务已改成0 qq信息陈泰确认
            mReqSpecidiedVehichleDriver.isAdvance = "0"
        }

        getViewModel(BaseViewModel::class.java).execute(
            true,
            ReqQueryDelistUserElectronicSignState(
                orderId = orderId,
            )
        ) { t ->
            //{"msg":"请求成功","code":"200","data":{"resultCode":"0000","electronicSignState":"0","cycleState":"0","resultMsg":"查询成功"}}
            if (t.success()) {

                t.data?.let {
                    var urlType = "#/carBoss?isSign=1"
                    var userType = "10"

                    if (TextUtils.equals("1", it.electronicSignState) && TextUtils.equals(
                            "1",
                            it.cycleState
                        )
                    ) {
                        esignFlag = "2"
                        X5WebNoToolBarActivity.startContentUI(
                            this,
                            HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/cycleCommitmentLetter?userType=" + userType + "&biddingMoney=&plateNumber=" + selectedCar?.plateNumber + "&heavy=&orderId=" + orderId,
                            "3"
                        )
                    } else {
                        if (TextUtils.equals(
                                "0",
                                t.data!!.electronicSignState
                            ) && TextUtils.equals(
                                "0",
                                t.data!!.cycleState
                            )
                        ) {
                            esignFlag = "0"
                        } else {
                            esignFlag = "1"
                        }
                        X5WebNoToolBarActivity.startContentUI(
                            this,
                            HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + urlType + "&biddingMoney=&plateNumber=" + selectedCar?.plateNumber + "&heavy=&orderId=" + orderId,
                            "3"
                        )
                    }
                }

            } else {
                showDialogToast(t.msg)
            }
        }
    }

    @RxBusEvent(from = "来自司机选择")
    open fun onSelectDriverSuccess(data: EDriverUser) {
        driverUser = data
        tvSelectDriver.text = data.driverName
        checkPersonCar?.onSelectPerson(data)
        queryCarrierLicenseTransitionPeriod("0")
    }

    @RxBusEvent(from = "选择车辆回调")
    open fun onSelectCarSuccess(data: EVehicle) {
        selectedCar = data
        queryCarrierLicenseTransitionPeriod("1")
    }

    @LiveDataMatch(tag = "派单成功")
    open fun specifiedSuccess(msg: String) {

        showToast(msg)
        finish()
    }

    @LiveDataMatch(tag = "查询是否支持预付款服务")
    open fun onBossDelistPrePayStatus(data: DelistPrePayStatus) {
        mData = data
        tvMoney.text = "服务费预估金额" + mData.serviceMoney + "元"
        when {
            TextUtils.equals(data.preStatus, "0") -> {
                clMoney.visibility = View.GONE
                clXieYi.visibility = View.GONE
            }
            TextUtils.equals(data.preStatus, "2") -> {
                clMoney.visibility = View.VISIBLE
                rbOpen.setOnCheckedChangeListener(null)
                rbOpen.isChecked = true
                this.clXieYi.visibility = View.VISIBLE
                rbOpen.setOnCheckedChangeListener(listener)
            }
            TextUtils.equals(mData.preStatus, "1") -> {
                clMoney.visibility = View.VISIBLE
                clXieYi.visibility = View.VISIBLE
                rbOpen.isChecked = true
            }
        }
    }

    // 预付说明开关
    val listener: CompoundButton.OnCheckedChangeListener =
        CompoundButton.OnCheckedChangeListener { compoundButton, checked ->
            when {
                checked -> clXieYi.visibility = View.VISIBLE
                else -> {
                    val alertTemple = DialogBuilder()
                    alertTemple.isCancelable = false
                    alertTemple.title = "提示"
                    alertTemple.message = "放弃使用运费预付服务,将无法收到预付运费"
                    alertTemple.cancelText = "放弃使用"
                    alertTemple.setOKText("继续使用")
                    alertTemple.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, which ->
                            clXieYi.visibility = View.VISIBLE
                            dialog.dismiss()
                        }
                    alertTemple.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, which ->
                            rbOpen.isChecked = true
                            dialog.dismiss()
                        }
                    showDialog(alertTemple)
                }
            }
        }

    @RxBusEvent(from = "电子签车老板发起指派")
    open fun onRxEventBusH5(type: RxEventPointCar) {
        mReqSpecidiedVehichleDriver.esignFlag = esignFlag
        viewModel?.getNetInfo(mReqSpecidiedVehichleDriver)
    }

    /**
     * 注释：ZCZY-14730 证件过期管控优化-业务部分
     * 时间：2023/12/12 0012 14:33
     * 作者：郭翰林
     */
    fun queryCarrierLicenseTransitionPeriod(context: Context, req: ReqCarrierLicenseTransitionPeriod) {
        viewModel.execute(
            true, req
        ) {
            if (it.success()) {
                if (!TextUtils.equals("0", it.data?.carrierLicenseState)) {
                    if (TextUtils.equals("1", it.data?.carrierLicenseState)) {
                        //1 提示 不拦截
                        if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = it.data?.resultMsg
                            dialogBuilder.isCancelable = false
                            dialogBuilder.cancelText = "知道了"
                            dialogBuilder.setCancelListener { dialog, _ ->
                                dialog.dismiss()
                                if (!TextUtils.isEmpty(selectedCar?.vehicleId)) {
                                    //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                                    queryCheckCarrierAndVehicleCertificate(selectedCar?.vehicleId ?: "")
                                }
                            }
                            dialogBuilder.setOKText("立即更新")
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                                //过期管理
                                val relation = CommServer.getUserServer().login.relation

                                if (relation.isCarrier) {
                                    CyrExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isBoss) {
                                    BossExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isCys) {
                                    CysExpiredCertificateManagementActivity.jumpPage(context)
                                }
                            }
                            showDialog(dialogBuilder)
                        } else {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = it.data?.resultMsg
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.isCancelable = false
                            dialogBuilder.setOKText("我知道了")
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()

                                if (!TextUtils.isEmpty(selectedCar?.vehicleId)) {
                                    //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                                    queryCheckCarrierAndVehicleCertificate(selectedCar?.vehicleId ?: "")
                                }
                            }
                            showDialog(dialogBuilder)
                        }
                    } else {
                        //2 提示 拦截
                        //限制成交,清空选择车辆与人
                        driverUser = null
                        selectedCar = null
                        pickCarFragment?.onClear()

                        tvSelectDriver.text = ""
                        req.driverUserId = ""
                        req.vehicleId = ""

                        if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = it.data?.resultMsg
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("立即更新")
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                                //过期管理
                                val relation = CommServer.getUserServer().login.relation
                                if (relation.isCarrier) {
                                    CyrExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isBoss) {
                                    BossExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isCys) {
                                    CysExpiredCertificateManagementActivity.jumpPage(context)
                                }
                            }
                            showDialog(dialogBuilder)
                        } else {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = it.data?.resultMsg
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("我知道了")
                            showDialog(dialogBuilder)
                        }
                    }
                } else {
                    if (!TextUtils.isEmpty(selectedCar?.vehicleId)) {
                        //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                        queryCheckCarrierAndVehicleCertificate(selectedCar?.vehicleId ?: "")
                    }
                }
            } else {
                showDialogToast(it.msg)
            }
        }
    }

    /**
     * WLHY-9279	【安卓】【汽运】证件风险通过规则调整
     */
    private fun queryCheckCarrierAndVehicleCertificate(
        vehicleId: String
    ) {
        val reqCheck = ReqQueryUserAndVehicleDelistRiskStates()
        reqCheck.orderId = orderId;
        reqCheck.vehicleId = vehicleId
        reqCheck.driverUserId = driverUser?.driverId ?: ""

        viewModel.execute(reqCheck) {
            it.data?.let {

                if (TextUtils.equals("1", it.userAndVehicleRiskStates)) {

                    var dailog = DialogBuilder()
                    dailog.message = it.resultMsg

                    if (TextUtils.equals("1", it.riskLimitationDelistFlag)) {
                        //限制成交,清空选择车辆与人
                        driverUser = null
                        selectedCar = null
                        pickCarFragment?.onClear()

                        tvSelectDriver.text = ""
                        mReqSpecidiedVehichleDriver.driverUserId = ""
                        mReqSpecidiedVehichleDriver.vehicleId = ""

                        if (TextUtils.equals("1", it.isOperation)) {
                            //完善资料
                            dailog.cancelText = "完善资料"
                            dailog.setCancelListener { dialog, which ->
                                dialog.dismiss()
                                gotoExpiredCertificateManagementActivity()
                            }
                            dailog.setOKText("我知道了")
                        } else {
                            dailog.isHideCancel = true
                            dailog.setOKText("我知道了")
                        }
                    } else {
                        if (TextUtils.equals("1", it.isOperation)) {
                            //完善资料
                            dailog.cancelText = "完善资料"
                            dailog.setCancelListener { dialog, which ->
                                dialog.dismiss()
                                gotoExpiredCertificateManagementActivity()
                            }
                            dailog.setOKText("继续提交")
                        } else {
                            dailog.cancelText = "我知道了"
                            dailog.setOKText("继续提交")
                        }
                    }
                    showDialog(dailog)
                }
            }
        }
    }

    fun gotoExpiredCertificateManagementActivity() {
        //过期管理
        val relation = CommServer.getUserServer().login.relation
        if (relation.isCarrier) {
            CyrExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverActivity)
        } else if (relation.isBoss) {
            BossExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverActivity)
        } else if (relation.isCys) {
            CysExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverActivity)
        }
    }
}