package com.zczy.plugin.order

import android.graphics.Color
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.certificate.Utils
import com.zczy.comm.CommServer
import com.zczy.comm.ui.BaseDialog
import com.zczy.plugin.order.designation.req.*
import kotlinx.android.synthetic.main.elect_sign_contract_dialog.view.*
import layout.ElectSignContractAdapter

/**
 * 签署运输业务合同
 * */
open class ElectSignContractDialog(data: RspDataCheckEletcSignContract, isPick: Boolean, viewModel: BaseViewModel, callback: (req: ReqEletcSignContract) -> Unit = {}, onClosePage: () -> Unit = {}) :
    BaseDialog() {
    var res: RspDataCheckEletcSignContract = data
    var isPick: Boolean = isPick
    val login = CommServer.getUserServer().login
    val adapter = ElectSignContractAdapter()
    val viewModel: BaseViewModel = viewModel
    val mCallback = callback
    val onClosePage = onClosePage
    override fun bindView(view: View, bundle: Bundle?) {
        //空白部分不可点击
        isCancelable = false
        if (res.userList == null) {
            res.userList = mutableListOf(ElectUserInfo(userRealName = "自己签署"))
        } else {
            res.userList?.add(0, ElectUserInfo(userRealName = "自己签署"))
        }
        view.et_phone.requestFocus()
        view.tv_no.setOnClickListener {
            when (view.tv_no.text) {
                "关闭", "取消" -> {
                    this.dismiss()
                    if (onClosePage != null) {
                        onClosePage()
                    }
                }
                "重新发起" -> {
                    if (TextUtils.equals("0", res.customerSignState)) {
                        //未认证
                        view.tv_no.text = "关闭"
                        view.tv_ok.text = "发起签署"
                        view.ll_input_mobile.visibility = View.VISIBLE
                        view.ll_assigned.visibility = View.GONE
                        view.ll_assign_list.visibility = View.GONE
                        view.et_phone.setText(res.signUserMobile ?: "")
                    } else {
                        //已认证
                        view.ll_input_mobile.visibility = View.GONE
                        view.ll_assigned.visibility = View.GONE
                        view.ll_assign_list.visibility = View.VISIBLE
                        view.tv_no.text = "取消"
                        view.tv_ok.text = "发起签署"
                    }
                }
            }
        }
        view.tv_ok.setOnClickListener {
            when (view.tv_ok.text) {
                "发起签署" -> {
                    val req = ReqEletcSignContract()
                    if (view.ll_input_mobile.visibility == View.VISIBLE) {
                        if (TextUtils.isEmpty(view.et_phone.text)) {
                            viewModel.showToast("请输入手机号")
                            return@setOnClickListener
                        }
                        if (!isMobileNO(view.et_phone.text.toString())) {
                            viewModel.showToast("请输入正确的手机号码!")
                            return@setOnClickListener
                        }
                        req.carrierMobile = view.et_phone.text.toString()
                    } else if (view.ll_assign_list.visibility == View.VISIBLE) {
                        if (adapter.selectItem == null) {
                            viewModel.showToast("请指定签署人")
                            return@setOnClickListener
                        }
                        req.carrierMobile = if (TextUtils.equals("自己签署", adapter.selectItem?.userRealName)) {
                            login.mobile
                        } else {
                            adapter.selectItem?.mobile
                        }
                    }
                    mCallback(req)
                }
                "我知道了" -> {
                    this.dismiss()
                    if (onClosePage != null) {
                        onClosePage()
                    }
                }
            }

        }

        if (TextUtils.equals("2", res.underWayContractSignState) && !isPick) {
            view.tv_desc.text = "您的运输业务合同临近超期，请及时续签!到期未续签会影响摘单。"
        } else {
            view.tv_desc.text = "为确保您与本平台双方的合法权益，请您在正式开展业务前完成《运输业务合同》的签署。"
        }
        if (TextUtils.equals("0", res.customerSignState) && TextUtils.isEmpty(res.signUserMobile)) {
            //未认证未指定
            view.ll_input_mobile.visibility = View.VISIBLE
            view.ll_assigned.visibility = View.GONE
            view.ll_assign_list.visibility = View.GONE
            view.tv_no.text = "关闭"
            view.tv_ok.text = "发起签署"
            view.et_phone.setText(login.mobile)
        } else if (!TextUtils.isEmpty(res.signUserMobile)) {
            //已指定
            view.ll_input_mobile.visibility = View.GONE
            view.ll_assigned.visibility = View.VISIBLE
            view.ll_assign_list.visibility = View.GONE
            view.tv_no.text = "重新发起"
            view.tv_ok.text = "我知道了"
            view.tv_assign_driver.text = Utils.hideMobile(res.signUserMobile) + " " + res.signUserName
            view.tv_status.text = res.getCustomerSignStateStr()
            view.tv_status.setTextColor(Color.parseColor(res.getCustomerSignStateStrColor()))
        } else {
            //未指定
            view.ll_input_mobile.visibility = View.GONE
            view.ll_assigned.visibility = View.GONE
            view.ll_assign_list.visibility = View.VISIBLE
            view.tv_no.text = "取消"
            view.tv_ok.text = "发起签署"
        }
        view.recycler_view.layoutManager = LinearLayoutManager(this.context)
        view.recycler_view.adapter = adapter.apply {
            setNewData(res.userList)
            setSelect(res.userList?.get(0))
            setOnItemClickListener { adapter, _, position ->
                val mAdapter = adapter as ElectSignContractAdapter
                val item = mAdapter.getItem(position) as ElectUserInfo
                mAdapter.setSelect(item)
            }
        }
    }

    open fun isMobileNO(mobiles: String): Boolean {
        /*
         * 移动：134、135、136、137、138、139、150、151、157(TD)、158、159、187、188
         * 联通：130、131、132、152、155、156、185、186 电信：133、153、180、189、（1349卫通） 177
         * 总结起来就是第一位必定为1，第二位必定为3或5或8，其他位置的可以为0-9
         */
        /*
         * 手机号放开第二位限制
         *
         * */
        val telRegex = "[1]\\d{10}" // "[1]"代表第1位为数字1，"[3578]"代表第二位可以为3、5、7,8中的一个，"\\d{9}"代表后面是可以是0～9的数字，有9位。
        return if (TextUtils.isEmpty(mobiles)) {
            false
        } else {
            mobiles.matches(telRegex.toRegex())
        }
    }

    override fun getDialogTag(): String {
        return "ElectSignContractDialog"
    }

    override fun getDialogLayout(): Int {
        return R.layout.elect_sign_contract_dialog
    }
}