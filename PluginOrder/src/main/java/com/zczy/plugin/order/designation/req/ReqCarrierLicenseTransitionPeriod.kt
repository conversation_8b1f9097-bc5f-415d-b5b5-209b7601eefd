package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 注释：ZCZY-14730 证件过期管控优化-业务部分
 * 时间：2023/12/12 0012 14:14
 * 作者：郭翰林
 */
class ReqCarrierLicenseTransitionPeriod(
    var orderId: String = "",
    var driverUserId: String = "",
    var vehicleId: String = "",
    var plateNumber: String = "",
    var checkDelistType: String = "",
) :
    BaseOrderRequest<BaseRsp<RspLicenseTransitionPeriod>>("/oms-app/carrier/common/queryCarrierLicenseTransitionPeriod")

class RspLicenseTransitionPeriod(
    //0 不提示 1 提示 不拦截  2 提示 拦截
    var carrierLicenseState: String,
    // 0 不能更新证件  1 可以更新证件
    var updateLicenseFlag: String
) : ResultData()