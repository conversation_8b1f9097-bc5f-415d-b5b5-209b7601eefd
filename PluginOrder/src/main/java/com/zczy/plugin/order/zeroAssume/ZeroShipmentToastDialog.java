package com.zczy.plugin.order.zeroAssume;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.zczy.comm.ui.BaseDialog;
import com.zczy.plugin.order.R;


/**
 * 合并发货提示
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 */
public class ZeroShipmentToastDialog extends BaseDialog implements View.OnClickListener {


    DialogInterface.OnClickListener onClickListener;

    String msg;
    String title;

    @Override
    protected String getDialogTag() {
        return "ZeroShipmentToastDialog";
    }

    @Override
    protected int getDialogLayout() {
        return R.layout.order_zero_shipments_pay_dialog;
    }


    @Override
    protected void bindView(View view, @Nullable Bundle bundle) {
        view.findViewById(R.id.tv_left).setOnClickListener(this);
        view.findViewById(R.id.tv_right).setOnClickListener(this);
        TextView tv =view.findViewById(R.id.tv_content);
        tv.setText(msg);

        TextView tv_colse =view.findViewById(R.id.tv_colse);
        tv_colse.setText(title);
    }

    public ZeroShipmentToastDialog setTitle(String title) {
        this.title = title; return this;
    }

    public ZeroShipmentToastDialog setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public ZeroShipmentToastDialog setOnClickListener(DialogInterface.OnClickListener onClickListener) {
        this.onClickListener = onClickListener;
        return this;
    }

    @Override
    public void onClick(View view) {
        dismissAllowingStateLoss();
        if (view.getId() == R.id.tv_left){
            onClickListener.onClick(this.getDialog(),0);
        }else {
            onClickListener.onClick(this.getDialog(),1);
        }

    }
}
