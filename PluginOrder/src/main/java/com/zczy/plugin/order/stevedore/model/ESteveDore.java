package com.zczy.plugin.order.stevedore.model;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ESteveDore {

    String lastUptTime;

    String orderId;

    /*** 发货明细ID*/
    String detailId;

    String remark;

    String consignorCompany;

    String deleteFlag;

    String createdTime;

    String examineState;

    String state;

    String examineBy;

    String driverUserId;

    String examineTime;

    String plateNo;

    String examineStreamNo;

    String pictureUrl;

    String plateNumber;

    String uploadTime;

    String userId;

    String carrierCustomerName;

    String transportFilesBeans;

    String loadId;

    String money;

    String createdBy;

    String stateText;

    String exTime;

    String examineOpinion;

    String LTLOrderFlag;

    public String getLTLOrderFlag() {
        return LTLOrderFlag;
    }


    public String getLastUptTime() {

        return lastUptTime;
    }

    public String getOrderId() {

        return orderId;
    }

    public String getDetailId() {

        return detailId;
    }

    public String getRemark() {

        return remark;
    }

    public String getConsignorCompany() {

        return consignorCompany;
    }

    public String getDeleteFlag() {

        return deleteFlag;
    }

    public String getCreatedTime() {

        return createdTime;
    }

    public String getExamineState() {

        return examineState;
    }

    public String getState() {

        return state;
    }

    public String getExamineBy() {

        return examineBy;
    }

    public String getDriverUserId() {

        return driverUserId;
    }

    public String getExamineTime() {

        return examineTime;
    }


    public String getPlateNo() {

        return plateNo;
    }

    public String getExamineStreamNo() {

        return examineStreamNo;
    }

    public String getPictureUrl() {

        return pictureUrl;
    }


    public String getPlateNumber() {

        return plateNumber;
    }

    public String getUploadTime() {

        return uploadTime;
    }

    public String getUserId() {

        return userId;
    }

    public String getCarrierCustomerName() {

        return carrierCustomerName;
    }

    public String getTransportFilesBeans() {

        return transportFilesBeans;
    }

    public String getLoadId() {

        return loadId;
    }

    public String getMoney() {

        return money;
    }

    public String getCreatedBy() {

        return createdBy;
    }

    public String getStateText() {

        return stateText;
    }

    public String getExTime() {

        return exTime;
    }

    public String getExamineOpinion() {

        return examineOpinion;
    }
}
