//package com.zczy.plugin.order.shipments.fragment;
//
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.LinearLayout;
//
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.entity.EGoodInfo;
//import com.zczy.plugin.order.shipments.entity.EShipmentsEditGoodInfo;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.model.ShipmentsGoodsModel;
//import com.zczy.plugin.order.shipments.view.ShipmentsGoodsItemView;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 功能描述:发货单修改，货物明细+实际发货量
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2018/12/20
// */
//public class ShipmentsEditGoodsFragment extends ShipmentsBaseFragment<ShipmentsGoodsModel> {
//
//    public static ShipmentsEditGoodsFragment newFragment() {
//
//        return new ShipmentsEditGoodsFragment();
//    }
//
//    private LinearLayout ll_content;
//
//    private List<EGoodInfo> data;
//
//    private List<ShipmentsGoodsItemView> shipmentsGoodsInfoViews;
//
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_goods_fragment_view;
//    }
//
//    @Override
//    public void initData(View view) {
//
//        ll_content = view.findViewById(R.id.ll_content);
//        if (data != null) {
//            this.shipmentsGoodsInfoViews = new ArrayList<>(data.size());
//            for (EGoodInfo detail : data) {
//                ShipmentsGoodsItemView goodsInfoView = new ShipmentsGoodsItemView(this.getContext());
//                this.ll_content.addView(goodsInfoView);
//                this.shipmentsGoodsInfoViews.add(goodsInfoView);
//                goodsInfoView.setInfo(detail,false);
//            }
//        }
//    }
//
//
//    public void setEGoodInfoList(EShipmentsEditGoodInfo goodInfo) {
//        this.data = goodInfo.getRootArray();
//    }
//
//    /***
//     * 检查输入内容
//     * @return
//     */
//    @Override
//    public boolean checkParams(ShipmentUI shipmentUI) {
//
//        //这里做确认发货的请求
//        int childSize = this.shipmentsGoodsInfoViews.size();
//        if (childSize == 0) {
//            return false;
//        }
//        List<EGoodInfo> goodInfos = new ArrayList<>(3);
//        for (int i = 0; i < childSize; i++) {
//
//            final ShipmentsGoodsItemView itemView = this.shipmentsGoodsInfoViews.get(i);
//            final EGoodInfo data = itemView.getData();
//            final String input = data.getBeforeDeliverCargoWeight();
//
//            if (TextUtils.isEmpty(input)) {
//                this.showToast("请输入[" + data.getCargoName() + "]货物重量");
//                return false;
//            }
//            if (Double.valueOf(input) <= 0.0) {
//                this.showToast("[" + data.getCargoName() + "]货物,货物计量不能小于等于0，请重新确认！");
//                return false;
//            }
//
//            if (TextUtils.equals("1",data.getUnit()) && !TextUtils.isEmpty(data.getWeightOrientationLimit())){
//                //(重)
//                if ( Double.valueOf(input) > Double.valueOf(data.getWeightOrientationLimit())){
//                    //zczy-13915_发货吨位提示错误
//                    this.showToast("货物计量不能大于"+data.getWeightOrientationLimit()+"，请重新确认");
//                    return false;
//                }
//            }else  if (TextUtils.equals("2",data.getUnit()) && !TextUtils.isEmpty(data.getShippingOrientationMargin())){
//                //(泡)
//                if ( Double.valueOf(input) > Double.valueOf(data.getShippingOrientationMargin())){
//                    //zczy-13915_发货吨位提示错误
//                    this.showToast("货物计量不能大于"+data.getShippingOrientationMargin()+"，请重新确认");
//                    return false;
//                }
//            }
//            goodInfos.add(data);
//        }
//        //货物ID:输入吨位
//        shipmentUI.goodInfos = goodInfos;
//        return true;
//    }
//
//}
