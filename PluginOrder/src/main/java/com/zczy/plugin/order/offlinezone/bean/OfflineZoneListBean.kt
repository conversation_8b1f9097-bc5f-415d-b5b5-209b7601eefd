package com.zczy.user.offlinezone.bean

import android.text.TextUtils
import com.google.gson.annotations.SerializedName
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData


data class OfflineZoneListCarrierReq(
    val nowPage: Int,
    val pageSize: Int = 10,
    var orderState: String,
    val queryType: Int = 2
) : BaseNewRequest<BaseRsp<PageList<OfflineZoneListRes>>>("oms-app/tender/carrier/querySeniorCarrierTenderOrderList")

data class OfflineZoneListCysReq(
    val nowPage: Int,
    val pageSize: Int = 10,
    var orderState: String,
    val queryType: Int = 2
) : BaseNewRequest<BaseRsp<PageList<OfflineZoneListRes>>>("oms-app/tender/carrier/queryBigCarrierTenderOrderList")

data class OfflineZoneListBossReq(
    val nowPage: Int,
    val pageSize: Int = 10,
    var orderState: String,
    val queryType: Int = 2
) : BaseNewRequest<BaseRsp<PageList<OfflineZoneListRes>>>("oms-app/tender/carrier/queryBossCarrierTenderOrderList")

class QueryCarrierListCountNum :
    BaseNewRequest<BaseRsp<CarrierListCountNum>>("/oms-app/tender/carrier/queryCarrierListCountNum")

data class CarrierListCountNum(
    var loadingNum: Int,//待装货
    var unloadingNum: Int//待卸货
) : ResultData()

data class OfflineZoneListRes(
    val orderId: String?, // 订单ID
    var detailId: String?,
    val consignorUserName: String?,
    val consignorCompany: String?, // 货主公司名称
    val consignorMobile: String?,
    val allCargoName: String?, // 货物名称
    val freightType: String?, // 费用类型：0 包车价,1 单价
    val consignorState: String?,
    val orderModel: String?, // 订单类型：0 抢单,1 竞价
    val cargoCategory: String?,
    val dealFlag: String?,
    val despatchStart: String?, // 装货开始时间
    val despatchEnd: String?, // 装货结束时间
    val validityTime: String?,
    val expectValidityTime: String?,
    val expectPeriod: String?,
    val displayMoney: String?, // 展示报价或成交金额
    @SerializedName(value = "blockMoney", alternate = ["displayBlockMoney"])
    val blockMoney: String?, // 展示拦标金额
    val orderQueryType: String?,
    val orderStateStr: String?, // 运单显示状态 0. (无状态) 1.已成交 2.已被他人成交 3.报价已结束 4.报价中 5.取消报价
    val comparsionLevel: String?, // 报价排名
    val despatchPro: String?,
    val despatchDis: String?,
    val despatchCity: String?,
    val despatchPlace: String?,
    val deliverPro: String?,
    val deliverDis: String?,
    val deliverCity: String?,
    val deliverPlace: String?,
    val vehicleLength: String?,
    val vehicleType: String?,
    val validityPeriod: String?,
    val goodsDescription: String?,
    val tenderFlag: String?, // 线下 0 否 1 是
    val tenderButtonDto: TenderButton?,
    val expectId: String?,
    val delistUserType: String?//单子属于谁 1 司机 2 车老板 3承运商 null或者空字符串代表没有成交人
) {
    fun getStartAddress(): String {
        //开始位置
        if (!TextUtils.isEmpty(despatchCity)) {
            if (despatchCity?.contains("市辖区") ?: false) {
                return despatchPro + despatchDis
            } else {
                return despatchCity + despatchDis
            }
        }
        return despatchPro + despatchCity + despatchDis
    }

    fun getEndAddress(): String {
        //开始位置
        if (!TextUtils.isEmpty(deliverCity)) {
            if (deliverCity?.contains("市辖区") ?: false) {
                return deliverPro + deliverDis
            } else {
                return deliverCity + deliverDis
            }
        }
        return deliverPro + deliverCity + deliverDis
    }
}

data class TenderButton(
    val quotePrice: Boolean,
    val cancelPrice: Boolean,
    val reHangPrice: Boolean,
    val modifyPrice: Boolean,
    val deliverGoods: Boolean,//确认发货(文字)
    val receiveGoods: Boolean,// 确认收货
    val cancelRelease: Boolean // 取消运单
)