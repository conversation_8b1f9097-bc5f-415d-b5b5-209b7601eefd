package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 查询是否需要签署电子签
 */
class ReqQueryDelistUserElectronicSignState(
    var orderId: String?
) :
    BaseOrderRequest<BaseRsp<RspQueryDelistUserElectronicSignState>>("oms-app/carrier/common/queryDelistUserElectronicSignState")

data class RspQueryDelistUserElectronicSignState(
    var electronicSignState: String? = "",//电子签状态 0 不需要 1 需要
    var cycleState: String? = "",//周期签署状态  0 不需要 1 需要
) : ResultData()