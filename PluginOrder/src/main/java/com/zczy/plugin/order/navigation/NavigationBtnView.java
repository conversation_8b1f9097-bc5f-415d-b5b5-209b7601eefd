package com.zczy.plugin.order.navigation;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zczy.plugin.order.R;

/***
 * 车老板名下无可用车辆
 */
public class NavigationBtnView extends PopupWindow {
    public NavigationBtnView(Context context, String flag) {
        super(context);
        View view = View.inflate(context, R.layout.layout_navigation_btn, null);
        setContentView(view);
        ColorDrawable bg = new ColorDrawable();
        bg.setAlpha(100);
        this.setBackgroundDrawable(bg);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);

        TextView tv_navigation_btn = view.findViewById(R.id.tv_navigation_btn);
        tv_navigation_btn.setText(flag.equals("1") ? "确认发货" : "确认收货");

        tv_navigation_btn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    public void show(Activity activity) {
        Rect rect = new Rect();
        View parent = activity.getWindow().getDecorView();
        parent.getWindowVisibleDisplayFrame(rect);
        int winHeight = parent.getHeight();
        super.showAtLocation(parent, Gravity.BOTTOM, 0, winHeight - rect.bottom);
    }
}
