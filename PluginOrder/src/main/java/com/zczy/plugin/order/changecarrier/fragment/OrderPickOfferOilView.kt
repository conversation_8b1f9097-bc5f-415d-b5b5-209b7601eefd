package com.zczy.plugin.order.changecarrier.fragment

import android.content.Context
import android.graphics.Color
import android.text.TextUtils
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.utils.ex.setVisible
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.req.*
import io.reactivex.Observable
import io.reactivex.Observer
import kotlinx.android.synthetic.main.order_offer_pickup_oil_v2_include.view.*

/**
 * 功能描述:【议价/摘单】油卡配置
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/1/22
 */
class OrderPickOfferOilView : ConstraintLayout {

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init()
    }

    private fun init() {
        inflate(context, R.layout.order_offer_pickup_oil_v2_include, this)
    }


    open fun setReqOilData(req: Req3ChangeOne) {
        req.sdOilCardFlag = sdOilCardFlag
        req.oilCalculateType = oilCalculateType
        req.oilFixedCredit = oilFixedCredit
        req.sdOilCardRatio = sdOilCardRatio
        req.sdGasCardRatio = sdGasCardRatio
        req.gasFixedCredit = gasFixedCredit
        req.oilCardOrGas = oilCardOrGas
    }

    /***
     * 切换车辆
     */
    open fun changeCar(
        vm: BaseViewModel,
        orderId: String,
        fuelTypeTxt: String,
        fuelType: String
    ) {

        //先清理,数据
        clear()
        visibility = GONE

        vm.execute(Observable.just(ReqQueryChangeOrderOilCard(orderId))
            .map {
                var rsp = it.sendRequest()
                var ok = false
                var oil = RspChangeOrderOil()
                var config = RspFuelTypeOilGas()

                if (rsp.success()) {
                    rsp.data?.let {
                        ok = true
                        oil = it
                        try {
                            var configRsp = ReqQueryFuelTypeOilGasControl(fuelTypeTxt).sendRequest()
                            if (configRsp.success()) {
                                configRsp.data?.let {
                                    config = it
                                }
                            }
                        } catch (e: Exception) {
                        }
                    }
                }

                Pair(ok, Pair(oil, config))

            }) {
            if (it.first) {
                if (TextUtils.equals("1", it.second.second?.fuelTypeOilGasState)) {
                    //燃料类型是否限制使用 1 是(不支持使用油气品)
                    showSupportOil(false)
                } else {
                    changeOil(fuelType, it.second.first)
                }
            }
        }
    }

    //	油卡类型 0没有油卡 1可选油卡 2强制油卡 3承运方强制油卡
    var sdOilCardFlag: String = ""
    //	油卡种类 1.比例 2.固额
    var oilCalculateType: String = ""
    //	油卡金额
    var oilFixedCredit: String = ""
    //	油卡比例
    var sdOilCardRatio: String = ""
    //	气卡比例
    var sdGasCardRatio: String = ""
    //	汽品固额
    var gasFixedCredit: String = ""
    // 1 油卡 2 气卡
    var oilCardOrGas: String = ""

    fun changeOil(fuelType: String, orderOil: RspChangeOrderOil) {

        orderOil?.let {

            //默认不支持
            var haveSupportOil = false;

            if (TextUtils.equals("1", it.dlOilCardFlag) && (TextUtils.equals(
                    "1",
                    it.pbOilCardType
                )||TextUtils.equals(
                    "0",
                    it.pbOilCardType
                ))
            ) {
                // 成交油品订单 && 可选油品时 直接取转值()
                if (TextUtils.equals("2", fuelType)) {
                    //当前是油车
                    if (TextUtils.equals("1", it.dlOilCalculateType)) {
                        //成交是比例
                        sdOilCardFlag = it.pbOilCardType
                        oilCardOrGas = "1"
                        oilCalculateType = "1"
                        //油气转职
                        sdOilCardRatio = if (TextUtils.isEmpty(it.dlOilCardRatio)) it.dlGasCardRatio else it.dlOilCardRatio

                        tv_oil_ratio_title.text = "兑换油品比例"
                        tv_oil_ratio.text = sdOilCardRatio + "%"

                        //支持
                        haveSupportOil = true

                    } else   if (TextUtils.equals("2", it.dlOilCalculateType)) {
                        //固额
                        sdOilCardFlag = it.pbOilCardType
                        oilCardOrGas = "1"
                        oilCalculateType = "2"
                        //油气转职
                        oilFixedCredit = if (TextUtils.isEmpty(it.dlOilCardMoney)) it.dlGasCardMoney else it.dlOilCardMoney

                        tv_oil_ratio_title.text = "兑换油品额度"
                        tv_oil_ratio.text = oilFixedCredit + "元"

                        //支持
                        haveSupportOil = true
                    }

                } else if (TextUtils.equals("1", fuelType)) {
                    //当前是气车
                    if (TextUtils.equals("1", it.dlOilCalculateType)) {
                        //成交是比例
                        sdOilCardFlag = it.pbOilCardType
                        oilCardOrGas = "2"
                        oilCalculateType = "1"
                        //油气转职
                        sdGasCardRatio =
                            if (TextUtils.isEmpty(it.dlGasCardRatio)) it.dlOilCardRatio else it.dlGasCardRatio

                        tv_oil_ratio_title.text = "兑换气品比例"
                        tv_oil_ratio.text = sdGasCardRatio + "%"

                        //支持
                        haveSupportOil = true

                    } else   if (TextUtils.equals("2", it.dlOilCalculateType)) {
                        //固额
                        sdOilCardFlag = it.pbOilCardType
                        oilCardOrGas = "2"
                        oilCalculateType = "2"
                        gasFixedCredit =
                            if (TextUtils.isEmpty(it.dlGasCardMoney)) it.dlOilCardMoney else it.dlGasCardMoney

                        tv_oil_ratio_title.text = "兑换气品额度"
                        tv_oil_ratio.text = gasFixedCredit + "元"

                        //支持
                        haveSupportOil = true
                    }
                }

            } else if (TextUtils.equals("2", it.pbOilCardType)) {
                //发布类型：强制油品
                if (TextUtils.equals("2", fuelType)) {
                    //当前是油车
                    if (TextUtils.equals("1", it.oilCardOrGas) || TextUtils.equals("3", it.oilCardOrGas)) {
                        //油品
                        if (TextUtils.equals("1", it.oilCalculateType) || TextUtils.equals(
                                "3",
                                it.oilCalculateType
                            )
                        ) {
                            //按比例计算油品
                            sdOilCardFlag = it.pbOilCardType
                            oilCardOrGas = "1"
                            oilCalculateType = "1"
                            sdOilCardRatio = it.pbOilCardRatio

                            tv_oil_ratio_title.text = "兑换油品比例"
                            tv_oil_ratio.text = sdOilCardRatio + "%"

                            //支持
                            haveSupportOil = true

                        } else if (TextUtils.equals("2", it.oilCalculateType)) {
                            //按固定额度计算油品
                            sdOilCardFlag = it.pbOilCardType
                            oilCardOrGas = "1"
                            oilCalculateType = "2"
                            oilFixedCredit = it.pbOilCardMoney

                            tv_oil_ratio_title.text = "兑换油品额度"
                            tv_oil_ratio.text = oilFixedCredit + "元"

                            //支持
                            haveSupportOil = true

                        }
                    }
                } else if (TextUtils.equals("1", fuelType)) {
                    //当前是气车
                    if (TextUtils.equals(
                            "2",
                            it.oilCardOrGas
                        ) || TextUtils.equals("3", it.oilCardOrGas)){
                        if (TextUtils.equals("1", it.oilCalculateType) || TextUtils.equals(
                                "3",
                                it.oilCalculateType
                            )
                        ) {
                            //按比例计算
                            sdOilCardFlag = it.pbOilCardType
                            oilCardOrGas = "2"
                            oilCalculateType = "1"
                            sdGasCardRatio = it.pbGasCardRatio

                            tv_oil_ratio_title.text = "兑换气品比例"
                            tv_oil_ratio.text = sdGasCardRatio + "%"
                            //支持
                            haveSupportOil = true

                        } else if (TextUtils.equals("2", it.oilCalculateType)) {
                            //按固定额度计算
                            sdOilCardFlag = it.pbOilCardType
                            oilCardOrGas = "2"
                            oilCalculateType = "2"
                            gasFixedCredit = it.pbGasCardMoney

                            tv_oil_ratio_title.text = "兑换气品额度"
                            tv_oil_ratio.text = gasFixedCredit + "元"
                            //支持
                            haveSupportOil = true
                        }
                    }
                }
            }

            //是否支持油品
            showSupportOil(haveSupportOil)
        }

    }

    fun showSupportOil(state: Boolean) {
        visibility = VISIBLE

        cb_user.setVisible(state)
        tv_oil_ratio_title.setVisible(state)
        tv_oil_ratio.setVisible(state)

        tv_oil_toast.text = if (state) "部分运费以油/气卡形式体现，加油时可享受优惠" else "此车辆燃料类型不支持配置油气品"
        tv_oil_toast.setTextColor(
            if (state) Color.parseColor("#979797") else Color.parseColor(
                "#FF8725"
            )
        );
    }

    fun clear() {
        sdOilCardFlag = ""
        oilCalculateType = ""
        oilFixedCredit = ""
        sdOilCardRatio = ""
        sdGasCardRatio = ""
        gasFixedCredit = ""
        oilCardOrGas = ""
    }

}