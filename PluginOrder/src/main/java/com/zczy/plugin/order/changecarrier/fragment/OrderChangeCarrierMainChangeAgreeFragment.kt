package com.zczy.plugin.order.changecarrier.fragment

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.utils.UtilTool
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.data.ERNToNativeEvent
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.pluginserver.AWisdomServer
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.PhoneUtil
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.adapter.OrderChangeCarrierMainChangeAgreeAdapter
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeAgreeCar
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeRefuseCar
import com.zczy.plugin.order.changecarrier.dialog.OrderChangeRejectDialog
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierMainChangeHistoryModel
import com.zczy.plugin.order.changecarrier.req.ReqQueryCheckBindBankCard
import com.zczy.plugin.order.changecarrier.req.ReqQueryDelistUserElectronicSignState
import com.zczy.plugin.order.changecarrier.req.ReqQueryMemberSilentSignState
import com.zczy.plugin.order.changecarrier.req.RspOrderChangeData
import com.zczy.plugin.order.source.bean.req.RspQueryHigCargoMoneyOrderRiskControl
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.waybill.WaybillCommDetailActivity

class OrderChangeCarrierMainChangeAgreeFragment : BaseFragment<OrderChangeCarrierMainChangeHistoryModel>() {

    private var swipeRefreshMoreLayout: SwipeRefreshMoreLayout? = null

    private var mItem: RspOrderChangeData? = null
    private var tItem: RspOrderChangeData? = null
    private var esignFlag: String = "0"
    override fun getLayout(): Int = R.layout.order_common_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout = view.findViewById(R.id.swipe_refresh_more_layout)

        val adapter = OrderChangeCarrierMainChangeAgreeAdapter()
        val emptyView = CommEmptyView.creatorDef(context)
        swipeRefreshMoreLayout?.apply {
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            addOnItemChildClickListener(onItemChildClickListener)
            setOnLoadListener2 { nowPage ->
                viewModel?.getNetInfo(nowPage, "1")
            }
        }
    }

    override fun initData() {
        swipeRefreshMoreLayout?.onAutoRefresh()
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        }
    }

    private val onItemChildClickListener = BaseQuickAdapter.OnItemChildClickListener { adapter, view, position ->
        val item: RspOrderChangeData = adapter.getItem(position) as RspOrderChangeData
        when (view.id) {
            R.id.tv_ok -> {
                tItem = item
                if (CommServer.getUserServer().login.relation.isCys) {
                    //承运商 && 变更他人 不走电子签逻辑
                    onAgreeChange(item)
                    return@OnItemChildClickListener
                }

                if (TextUtils.equals("2", item.changeOrderSource)) {
                    var flag = false
                    for (i in item.detailList) {
                        if (TextUtils.equals("13", i.changeType)) {
                            flag = true
                        }
                    }
                    if (flag) {
                        viewModel?.checkHaveAccountState(context, item.orderId, item.consignorUserId)
                    } else {
                        onAgreeV2(item)
                    }

                } else {
                    //查询是否需要静默签
                    getViewModel(BaseViewModel::class.java).execute(
                        true, ReqQueryMemberSilentSignState(userId = CommServer.getUserServer().login.userId)
                    ) { t ->
                        if (t.success()) {
                            if (TextUtils.equals("1", t.data!!.silentSignState)) {
                                var dialog = DialogBuilder()
                                dialog.title = "温馨提示"
                                dialog.message = "您尚未完成电签认证不可进行业务，请优先完成电签认证"
                                dialog.isHideCancel = true
                                dialog.setOKTextListener("前往电签认证") { dialog: DialogBuilder.DialogInterface, _: Int ->
                                    dialog.dismiss()
                                    AMainServer.getPluginServer().changeMenuToEvent(
                                        activity,
                                        AMainServer.MENU_USER,
                                        500,
                                        ERNToNativeEvent(type = "openElectorn")
                                    )
                                }
                                showDialog(dialog)
                            } else {
                                onAgreeChange(item)
                            }
                        } else {
                            showToast(t.msg)
                        }
                    }

                }

            }

            R.id.tv_no -> {
                OrderChangeRejectDialog().also {
                    it.setServiceChargeListener(object :
                        OrderChangeRejectDialog.OrderChargeListener {
                        override fun agree(reason: String) {
                            viewModel?.decideChangeReject(item.changeRecordId, reason, item.changeOrderSource)
                        }

                        override fun reject() {
                        }

                    }).show(this)
                }
            }

            R.id.tv_copy -> {
                //复制order
                UtilTool.setCopyText(requireContext(), "运单号", item.orderId)
                showToast("复制成功")
            }

            R.id.iv_contact_shippers -> {
                // 用intent启动拨打电话
                PhoneUtil.callPhone(activity, item.consignorMobile)
            }

            R.id.tv_check -> {
                // 查看运单详情
                WaybillCommDetailActivity.start(activity, item.orderId)
            }
        }
    }

    fun onAgreeChange(item: RspOrderChangeData) {

        getViewModel(OrderChangeCarrierMainChangeHistoryModel::class.java)
            .execute(true, ReqQueryCheckBindBankCard(item.orderId)) {
                if (it.success()) {
                    //0 不需要绑定 1 需要绑定本人卡 2 需要绑定亲人卡  如果是1或者2的时候提示resultMsg
                    if (TextUtils.equals("0", it.data?.bindBankCardState)) {
                        val relation = CommServer.getUserServer().login.relation
                        if (relation.isCys) {
                            viewModel?.decideChangeAgree(
                                activity,
                                item.orderId,
                                item.carrierHandleUserId,
                                item.changeRecordId,
                                "",
                                "0",
                                item.changeOrderSource
                            )
                        } else {
                            mItem = item
                            viewModel?.queryHigCargoMoneyOrderRiskControl(item)
                        }
                    } else if (TextUtils.equals("1", it.data?.bindBankCardState) && TextUtils.equals("1", it.data?.operationFlag)) {
                        // 需要绑定本人卡
                        var dilaog = DialogBuilder()
                        dilaog.title = "温馨提示"
                        dilaog.message = it.data?.resultMsg ?: ""
                        dilaog.setOKText("去绑银行卡")
                        dilaog.setOkListener { dialog, which ->
                            dialog.dismiss()
                            AWisdomServer.getPluginServer().openAddBank(context, "0")
                        }
                        dilaog.isHideCancel = false
                        dilaog.cancelText = "联系在线客服"
                        dilaog.setCancelListener { dialog, _ ->
                            dialog.dismiss()
                            val mainServer = AMainServer.getPluginServer()
                            mainServer?.openLineServer(context)
                        }
                        showDialog(dilaog)

                    } else if (TextUtils.equals("2", it.data?.bindBankCardState) && TextUtils.equals("1", it.data?.operationFlag)) {
                        //需要绑定亲人卡
                        var dilaog = DialogBuilder()
                        dilaog.title = "温馨提示"
                        dilaog.message = it.data?.resultMsg ?: ""
                        dilaog.setOKText("去绑银行卡")
                        dilaog.setOkListener { dialog, which ->
                            dialog.dismiss()
                            AWisdomServer.getPluginServer().openAddBank(context, "1")
                        }
                        dilaog.isHideCancel = false
                        dilaog.cancelText = "联系在线客服"
                        dilaog.setCancelListener { dialog, _ ->
                            dialog.dismiss()
                            val mainServer = AMainServer.getPluginServer()
                            mainServer?.openLineServer(context)
                        }
                        showDialog(dilaog)

                    } else {
                        showContactCustomerDialog(it.msg)
                    }

                } else {
                    showContactCustomerDialog(it.msg)
                }
            }

    }

    fun onAgreeV2(item: RspOrderChangeData?) {
        //易找车
        item?.let {
            if (TextUtils.equals("1", item?.modeType)) {
                //代开票
                X5WebNoToolBarActivity.startContentUI(
                    activity,
                    HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/tmsContract?sourceId=" + item?.orderId + "&isChange=2&consignorUserId=" + item?.consignorUserId,
                    "onAgreeChangelist"
                )
            } else {
                viewModel?.decideChangeTms(
                    activity,
                    item?.changeRecordId ?: "",
                    "",
                    esignFlag,
                    item?.changeOrderSource ?: "",
                )
            }
        }

    }

    private fun showContactCustomerDialog(msg: String) {
        var dialog = DialogBuilder()
        dialog.title = "温馨提示"
        dialog.message = msg
        dialog.isHideCancel = false
        dialog.setOKTextListener("联系在线客服") { dialog, _ ->
            dialog.dismiss()
            val mainServer = AMainServer.getPluginServer()
            mainServer?.openLineServer(context)
        }
        dialog.cancelText = "关闭"
        dialog.setCancelListener { dialog, _ ->
            dialog.dismiss()
        }
        showDialog(dialog)
    }

    @LiveDataMatch(tag = "高风险校验")
    open fun queryHigCargoMoneyOrderRiskControlSuccess(
        data: RspQueryHigCargoMoneyOrderRiskControl,
        item: RspOrderChangeData
    ) {
        if (data.riskFlag.isTrue) {

            var dialog = DialogBuilder()
            dialog.message = "您当前摘取的运单需进行人脸识别验证！"
            dialog.isHideCancel = true
            dialog.setOKTextListener("去处理") { dialog, _ ->
                dialog.dismiss()
                openUserAuthent()
            }
            showDialog(dialog)

        } else {
            getViewModel(BaseViewModel::class.java).execute(
                true,
                ReqQueryDelistUserElectronicSignState(
                    orderId = item.orderId,
                )
            ) { t ->
                if (t.success() && t.data != null
                ) {
                    var urlType = ""
                    var userType = ""
                    //（电子签）
                    if (CommServer.getUserServer().login.relation.isCarrier) {
                        urlType = "#/driver?platform=XXX&isSign=1"
                        userType = "2"
                    } else {
                        urlType = "#/carBoss?isSign=1"
                        userType = "10"
                    }

                    var plateNumber = ""
                    for (i in item.detailList) {
                        if (TextUtils.equals("1", i.changeType)) {
                            plateNumber = i.newValue
                        }
                    }

                    if (TextUtils.equals("1", t.data!!.electronicSignState) && TextUtils.equals(
                            "1",
                            t.data!!.cycleState
                        )
                    ) {
                        esignFlag = "2"
                        X5WebNoToolBarActivity.startContentUI(
                            context,
                            HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/cycleCommitmentLetter?userType=" + userType + "&biddingMoney=&plateNumber=" + plateNumber + "&heavy=&orderId=" + item.orderId + "&changeFlag=2&changeId=" + item.changeRecordId,
                            "onAgreeChangelist"
                        )
                    } else {
                        if (TextUtils.equals("0", t.data!!.electronicSignState) && TextUtils.equals(
                                "0",
                                t.data!!.cycleState
                            )
                        ) {
                            esignFlag = "0"
                        } else {
                            esignFlag = "1"
                        }
                        X5WebNoToolBarActivity.startContentUI(
                            context,
                            HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + urlType + "&biddingMoney=&plateNumber=" + plateNumber + "&heavy=&orderId=" + item.orderId + "&changeFlag=2&changeId=" + item.changeRecordId,
                            "onAgreeChangelist"
                        )
                    }
                } else {
                    showToast(t.msg)
                }
            }
        }
    }

    @LiveDataMatch(tag = "高风险校验失败")
    open fun queryHigCargoMoneyOrderRiskControlError(
        data: RspQueryHigCargoMoneyOrderRiskControl,
        item: RspOrderChangeData
    ) {
        if (data.memberAuditState.isTrue) {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.setTitle("提示")
            dialogBuilder.setMessage(data.resultMsg)
            dialogBuilder.setCancelText("取消")
            dialogBuilder.setCancelListener { dialog: DialogBuilder.DialogInterface, _: Int -> dialog.dismiss() }
            dialogBuilder.setOKText("去完善")
            dialogBuilder.setOkListener { dialog: DialogBuilder.DialogInterface?, _: Int ->
                dialog?.dismiss()
                AMainServer.getPluginServer().userAuthent(context)
            }
            showDialog(dialogBuilder)
        } else {
            val dialogBuilder = DialogBuilder()
            dialogBuilder.setTitle("提示")
            dialogBuilder.setMessage(data.resultMsg)
            dialogBuilder.isHideCancel = true
            dialogBuilder.setCancelListener { dialog: DialogBuilder.DialogInterface, _: Int -> dialog.dismiss() }
            dialogBuilder.setOKText("确定")
            dialogBuilder.setOkListener { dialog: DialogBuilder.DialogInterface?, _: Int ->
                dialog?.dismiss()
            }
            showDialog(dialogBuilder)
        }
    }

    @LiveDataMatch
    open fun onPlayMoney(earnestMoney: String) {
        //支付诚意金
        AWisdomServer.getPluginServer().startWisdomEarnesrBuyActivity(context, earnestMoney);
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: PageList<RspOrderChangeData>?) {
        swipeRefreshMoreLayout?.onRefreshCompale(data)
    }


    @LiveDataMatch
    open fun onDecideChange() {
        swipeRefreshMoreLayout?.onAutoRefresh()
    }

    @LiveDataMatch
    open fun openUserAuthent() {
        // ZCZY-18025 会员强控手机号三要素--业务配合
        AMainServer.getPluginServer().changeMenuToEvent(
            activity,
            AMainServer.MENU_USER,
            500,
            ERNToNativeEvent(type = "openUserAuthent")
        )

    }

    @RxBusEvent(from = "电子签后发起变更")
    open fun onRxEventBusH5(data: RxEventChangeAgreeCar) {
        tItem?.let { //同意变更
            if (TextUtils.equals("2", it.changeOrderSource)) {
                viewModel?.decideChangeTms(
                    activity,
                    it.changeRecordId,
                    "",
                    esignFlag,
                    it.changeOrderSource,
                )
            } else {
                viewModel?.decideChangeAgree(
                    activity,
                    it.orderId,
                    it.carrierHandleUserId,
                    it.changeRecordId,
                    "",
                    esignFlag,
                    it.changeOrderSource,
                )
            }
        }
    }

    @RxBusEvent(from = "电子签后发起变更")
    open fun onRefuseRxEventBusH5(data: RxEventChangeRefuseCar) {
        val dialog = DialogBuilder()
        dialog.title = "提示"
        dialog.message = "拒绝签署则视为不同意运单变更，请确认继续操作么？"
        dialog.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, _: Int ->
            dialog.dismiss()
            viewModel?.decideChangeReject(tItem?.changeRecordId ?: "", "", tItem?.changeOrderSource)
        }
        showDialog(dialog)
    }

    @LiveDataMatch
    open fun onCheckHaveAccountState() {
        onAgreeV2(tItem)
    }

    @LiveDataMatch
    open fun onOpenAccount() {
        onAgreeV2(tItem)
    }
}
