package com.zczy.plugin.order.stevedore.model.request;

import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;
import com.zczy.plugin.order.BaseOrderRequest;
import com.zczy.plugin.order.stevedore.model.ESearchOrder;

/**
 * 功能描述:订单列表
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ReqStevedoreOrderList extends BaseOrderRequest<BaseRsp<PageList<ESearchOrder>>> {


    int nowPage;
    int pageSize = 10;
    String orderId;

    public ReqStevedoreOrderList(int nowPage,String content) {

        super ("oms-app/order/loadpay/selectDriverLoadPayList");
        this.nowPage = nowPage;
        this.orderId = content;
    }

    public int getNowPage() {
        return nowPage;
    }

    public void setNowPage(int nowPage) {
        this.nowPage = nowPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
