//package com.zczy.plugin.order.shipments.entity;
//
//import com.zczy.comm.http.entity.ResultData;
//
//public class EAdvanceInfoBeforeCommit extends ResultData {
//
//    //     最新预付比例
//    String advanceRatio;
//    //是否提示标识;1:提醒;0:不提醒
//    String tipFlag;
//    //    总提示信息
//    String totalMsg;
//    //      富文本文本信息
//    String attributeMsg;
//    //    预付比例是否变更 1:变更 0:未变更
//    String changRatioFlag;
//
//    public String getAdvanceRatio() {
//        return advanceRatio;
//    }
//
//    public String getTipFlag() {
//        return tipFlag;
//    }
//
//    public String getTotalMsg() {
//        return totalMsg;
//    }
//
//    public String getAttributeMsg() {
//        return attributeMsg;
//    }
//
//    public String getChangRatioFlag() {
//        return changRatioFlag;
//    }
//}
