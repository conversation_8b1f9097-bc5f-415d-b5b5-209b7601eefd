package com.zczy.plugin.order;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.ColorDrawable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ReplacementSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.zczy.comm.config.HttpURLConfig;
import com.zczy.comm.utils.ex.ImageViewKt;
import com.zczy.plugin.order.bill.entity.EBank;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:选择拍照还是相册提示对话框
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2017/11/20
 */
public class SelectTakePhotoDialog extends PopupWindow implements View.OnClickListener{

    public interface IOnItemClick {

        /***
         * 选择拍照
         * @param dialog
         */
        void openTakePhoto(SelectTakePhotoDialog dialog);

        /***
         * 选择相册
         * @param dialog
         */
        void openAlbum(SelectTakePhotoDialog dialog);

    }


    private IOnItemClick onItemClick;


    public SelectTakePhotoDialog(Context context, IOnItemClick onItemClick) {

        super(context);
        View view = LayoutInflater.from(context).inflate(R.layout.order_select_take_photo_dialog, null, false);
        setContentView(view);

        ColorDrawable background = new ColorDrawable();
        background.setAlpha(100);
        this.setBackgroundDrawable(background);
        this.setFocusable(true);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setOutsideTouchable(true);
        this.setAnimationStyle(R.style.take_photo_anim);

        this.onItemClick = onItemClick;
        view.findViewById(R.id.ivColse).setOnClickListener(this);
        view.findViewById(R.id.tv_cancel).setOnClickListener(this);
        view.findViewById(R.id.tv_take).setOnClickListener(this);
        view.findViewById(R.id.tv_ablum).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {

        this.dismiss();
        if (v.getId() == R.id.tv_ablum) {
            onItemClick.openAlbum(this);
        } else if (v.getId() == R.id.tv_take) {
            onItemClick.openTakePhoto(this);
        }
    }

    public void show(View parent) {

        if (isShowing()) {
            return;
        }
        super.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
    }

}

