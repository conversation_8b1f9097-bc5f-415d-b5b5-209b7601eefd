package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:省货物平台系统2.0 【发货】
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2020/10/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnResultListener;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.supervise.req.ReqAddSdkRecord;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.functions.Function;

public class StartAction extends BaseActionServer implements OnResultListener, Function<StartAction, Boolean> {


    private ESDKInfoObj sdkInfoObj;

    public StartAction(ESDKInfoObj sdkInfoObj) {
        this.sdkInfoObj = sdkInfoObj;
    }

    @Override
    public void start() {
        //授权
         RetrofitManager.executeSigin(Observable.just(StartAction.this).map(StartAction.this), new EmptyResult<>());
    }

    @Override
    public Boolean apply(@NonNull StartAction shippingNoteInfoServer) throws Exception {
        this.auth(sdkInfoObj.getConsignorSubsidiaryId(), new OnAuthListener() {
            @Override
            public void onAuthFailure(String code, String msg) {
                StartAction.this.onFailure(code, msg);
            }

            @Override
            public void onAuthSuccess(List<ShippingNoteInfo> list) {
                StartAction.this.post();
            }
        });

        return true;
    }

    private void post() {
        out(String.format("省货物平台系统2.0=Start=>发货[构建数据] sdkInfoObj:%s", sdkInfoObj));

        ShippingNoteInfo[] shippingNoteNumbers = new ShippingNoteInfo[]{sdkInfoObj.getShippingNoteInfo()};;
        //发货
        LocationOpenApi.start(AppCacheManager.getApplication(), sdkInfoObj.getVehicleNumber(), sdkInfoObj.getDriverName(), "", shippingNoteNumbers, StartAction.this);
    }

    @Override
    public void onSuccess(List<ShippingNoteInfo> list) {

        out(String.format("省货物平台系统2.0=Start=>发货[成功] sdkInfoObj:%s,size:%s", sdkInfoObj,(list!=null?list.size():0)));
        if (list != null && (list.size() > 0)) {
            //定位间隔时间(单位 ms)毫秒
            sdkInfoObj.setInterval(list.get(0).getInterval());
            new TimeWorkerAction.Build().setInfo(sdkInfoObj).build();

        }
        //收货-发货，上报公司平台信息
        new ReqAddSdkRecord(sdkInfoObj.getOrderId(), "code:0, msg:省平台start成功, type:Android").setSuccessFlag("1").buildStart();

    }

    @Override
    public void onFailure(String code, String msg) {

        out(String.format("省货物平台系统2.0=Start=>发货[失败]  sdkInfoObj:%s,code:%s,msg:%s", sdkInfoObj, code, msg));
        //收货-发货，上报公司平台信息
       new ReqAddSdkRecord(sdkInfoObj.getOrderId(), String.format("code:%s, msg:%s, type:Android", code, msg)).buildStart();

    }


}
