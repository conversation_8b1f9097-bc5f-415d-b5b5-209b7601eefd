package com.zczy.plugin.order.zeroAssume.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.waybill.entity.EWaybill
import com.zczy.plugin.order.waybill.model.request.ReqWaybillPage
import com.zczy.plugin.order.zeroAssume.model.request.*

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ZeroAssumeModel : BaseViewModel() {
    /**
     * 零担拼车单列表
     */
    fun getOrderCarpoolingList(nowPage: Int, queryType: String = "") {
        execute(
            ReqZeroAssumeList(
                currentPage = nowPage,
                queryType = queryType
            ), object : IResult<BaseRsp<PageList<RspZeroAssumeList>>> {
                override fun onSuccess(p0: BaseRsp<PageList<RspZeroAssumeList>>) {
                    if (p0.success()) {
                        setValue("getOrderCarpoolingList", p0.data)
                    } else {
                        showDialogToast(p0.msg)
                        setValue("getOrderCarpoolingList", null)
                    }
                }

                override fun onFail(p0: HandleException) {
                    showDialogToast(p0.msg)
                    setValue("getOrderCarpoolingList", null)
                }
            })
    }

    /**
     * 零担拼车单子单列表
     */
    fun getOrderCarpoolingChildList(
        orderCarpoolingId: String?,
        queryType: String,
        block: (list: PageList<EWaybill>?) -> Unit
    ) {
        execute(false, ReqZeroAssumeChildList(
            orderCarpoolingId = orderCarpoolingId,
            queryType = queryType
        ), object : IResult<BaseRsp<PageList<EWaybill>>> {
            override fun onSuccess(p0: BaseRsp<PageList<EWaybill>>) {
                if (p0.success()) {
                    block(p0.data)
                } else {
                    showDialogToast(p0.msg)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
                setValue("getOrderCarpoolingChildList", null)
            }
        })
    }

    fun queryPageWaybill(req: ReqWaybillPage) {
        req.path = "oms-app/display/serniorCarrierOrderList"
        execute(false, req, object : IResult<BaseRsp<PageList<EWaybill>>> {
            override fun onSuccess(p0: BaseRsp<PageList<EWaybill>>) {
                if (p0.success()) {
                    setValue("getOrderCarpoolingChildList", p0.data)
                } else {
                    showDialogToast(p0.msg)
                    setValue("getOrderCarpoolingChildList", null)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
                setValue("getOrderCarpoolingChildList", null)
            }
        })
    }


    /**
     * 零担-合并发货检查
     */
    fun queryMergeByOrderId(waybill: EWaybill?,processFlag:String?) {

        execute(true,ReqMergeByOrderId(
            orderId = waybill?.orderId,
            processFlag = processFlag,
            orderCarpoolingId = waybill?.orderCarpoolingId
        ), object : IResult<BaseRsp<RspMergeByOrderId>> {
            override fun onSuccess(p0: BaseRsp<RspMergeByOrderId>) {
                if (p0.success()) {
                    if (TextUtils.equals("1", p0.data?.firstFlag) && TextUtils.equals("1",processFlag)) {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = "运单确认卸货后将无法拼入其他零担运单，请确认已摘取所有承运运单"
                        dialogBuilder.setCancelTextListener("返回") { dialog, _ ->
                            dialog.dismiss()
                        }
                        dialogBuilder.setOKTextListener("确认卸货") { dialog, _ ->
                            dialog.dismiss()
                            setValue("onQueyMergeBill", TextUtils.equals("1",p0.data?.contMoreFlag),waybill)

                        }
                        showDialog(dialogBuilder)
                    } else {
                        //true 标识：可以合并出现合并弹框选择，false:只能单个发货
                        if (TextUtils.equals("1",processFlag)){
                            //  收货为1
                            setValue("onQueyMergeBill", TextUtils.equals("1",p0.data?.contMoreFlag),waybill)
                        }else{
                            //发货传0
                            setValue("onQueyMergeShipment", TextUtils.equals("1",p0.data?.contMoreFlag),waybill)
                        }
                    }

                } else {
                    showDialogToast(p0.msg)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
            }
        })
    }
    /**
     * 零担-确认发货前查询是否首次发货
     */
    fun queryOrderCarpoolingListCorner(queryType: String?) {
        execute(ReqQueryOrderCarpoolingListCorner(
            queryType = queryType,
        ), object : IResult<BaseRsp<RspOrderCarpoolingListCorner>> {
            override fun onSuccess(p0: BaseRsp<RspOrderCarpoolingListCorner>) {
                if (p0.success()) {
                    setValue("countNum", p0.data?.orderCarpoolingTransportCount)
                }
            }

            override fun onFail(p0: HandleException) {
                showToast(p0.msg)
            }
        })
    }

}