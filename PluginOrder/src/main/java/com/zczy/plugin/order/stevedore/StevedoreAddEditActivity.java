package com.zczy.plugin.order.stevedore;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.jakewharton.rxbinding2.widget.RxTextView;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.comm.config.HttpURLConfig;
import com.zczy.comm.data.entity.EImage;
import com.zczy.comm.data.entity.EProcessFile;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.JsonUtil;
import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
import com.zczy.comm.utils.imageselector.ImageSelector;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.ListViewForScrollView;
import com.zczy.comm.widget.inputv2.InputViewClick;
import com.zczy.comm.widget.inputv2.InputViewEdit;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.shipments.ShipmentsActivity;
import com.zczy.plugin.order.stevedore.adapter.ItemNameAdapter;
import com.zczy.plugin.order.stevedore.model.ESteveDoreDeatil;
import com.zczy.plugin.order.stevedore.model.StevedoreModel;
import com.zczy.plugin.order.stevedore.model.request.ReqCarrierAddLoadPay;
import com.zczy.plugin.order.waybill.entity.EWaybill;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * 功能描述:录入装卸费(发货成功直接录入)
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class StevedoreAddEditActivity extends AbstractLifecycleActivity<StevedoreModel> implements View.OnClickListener, ImageSelectProgressView.OnItemSelectListener {


    /***
     *
     * @param context
     * @param type 1: 卸货成功进入  2:录入管理进入新增 3:录入管理进入修改
     * @param data  type == 1,data为运单Id,type == 1,data为发货明细id
     */
    public static void startContentUI(Context context, int type, String data) {

//        Intent intent = new Intent (context, StevedoreAddEditActivity.class);
//        intent.putExtra ("data", data);
//        intent.putExtra ("type", type);
//        context.startActivity (intent);
        start(context, type, data);
    }

    /***
     *
     * @param fragment
     */
    private static void start(Context fragment, int type, String data) {

        AMainServer mainServer = AMainServer.getPluginServer();
        Map<String,Object> params=new HashMap<>();
        params.put("orderId", data);
        params.put("type", type);
        mainServer.openReactNativeActivity(fragment, "StevedoreAddEditPage",JsonUtil.toJson(params));
    }


    private ImageView ivClose;

    private InputViewClick inputOrderId;

    private InputViewEdit inputMoney;

    private ImageSelectProgressView billImageSelectView;

    private EditText etDescription;

    private TextView tvMoreSize;

    private Button btnCommit;

    private String detailId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate (savedInstanceState);
        setContentView (R.layout.order_stevedore_add_activity);
        UtilStatus.initStatus (this, ContextCompat.getColor (this,R.color.comm_title_bg));
        initView ();
    }

    private void initView() {
        AppToolber appToolbar = findViewById(R.id.app_toolbar);
        this.ivClose = findViewById (R.id.iv_close);
        this.inputOrderId = findViewById (R.id.input_orderId);
        this.inputMoney = findViewById (R.id.input_money);
        this.billImageSelectView = findViewById (R.id.bill_image_select_view);

        this.etDescription = findViewById (R.id.et_description);
        this.tvMoreSize = findViewById (R.id.tv_more_size);
        this.btnCommit = findViewById (R.id.btn_commit);

        this.billImageSelectView.setOnItemSelectListener (this);
        this.billImageSelectView.setShowSize (5, 4);

        this.ivClose.setOnClickListener (this);
        this.btnCommit.setOnClickListener (this);
        UtilTool.setEditTextInputSize (this.inputMoney.getEditText (), 2);
        Disposable disposable = RxTextView.textChanges (etDescription).subscribe (new Consumer<CharSequence> () {

            @Override
            public void accept(CharSequence charSequence) throws Exception {

                tvMoreSize.setText (charSequence.length () + "/200");
            }
        });
        this.putDisposable (disposable);

        String data = getIntent ().getStringExtra ("data");

        int type = getIntent ().getIntExtra ("type", 1);
        // 1: 卸货成功进入  2:录入管理进入新增 3:录入管理进入修改
        if (type == 1) {
            this.inputOrderId.setContent (data);
            this.getViewModel ().queryCargoList (data);

        } else if (type == 3) {
            this.detailId = data;
            this.inputOrderId.setVisibility (View.VISIBLE);
            this.getViewModel ().queryHandlingChargesDetail (this.detailId);
        } else {
            this.inputOrderId.setVisibility (View.VISIBLE);
        }

        appToolbar.setLeftOnClickListener(v -> {
            ZStatistics.onViewClick(this, "LoadingFee_back");
            finish();
        });

        inputOrderId.setListener(new InputViewClick.Listener() {
            @Override
            public void onClick(int i, @NonNull InputViewClick inputViewClick, @NonNull String s) {
                SearchOrderListActivity.start(StevedoreAddEditActivity.this,10001);
            }
        });
    }


    @Override
    public void onClick(View v) {

        if (v == btnCommit) {
            String orderId = inputOrderId.getContent ();
            if (TextUtils.isEmpty (orderId)) {
                showToast ("请输入运单号");
                return;
            }
            String moeny = inputMoney.getContent ();
            if (TextUtils.isEmpty (moeny)) {
                showToast ("请输入装卸费金额");
                return;
            }
            List<EProcessFile> files = billImageSelectView.getDataList ();
            if (files == null || files.isEmpty ()) {
                this.showToast ("请上传收据照片");
                return;
            }
            List<String> url = new ArrayList<> (files.size());
            for (EProcessFile processFile : files) {
                url.add (processFile.getImagUrl ());
            }

            ReqCarrierAddLoadPay req = new ReqCarrierAddLoadPay ();
            req.setOrderId (orderId);
            req.setDetailId (this.detailId);
            req.setMoney (moeny);
            req.setPictureUrlJsonArray(url);
            req.setRemark (this.etDescription.getText ().toString ());

            this.getViewModel ().carrierAddLoadPay (req);
            ZStatistics.onViewClick(this, "LoadingFee_confirm");
        } else if (v == ivClose) {
            this.findViewById (R.id.cl_toast).setVisibility (View.GONE);
        }
    }

    @LiveDataMatch(tag = "录入装修费成功")
    public void onAddLoadPaySuccess() {

        StevedoreSuccessActivity.startContentUI (this);
        finish ();
    }

    /*-------------------------------货物明细列表 start---------------------------------------*/
    @LiveDataMatch(tag = "货物明细列表")
    public void onCargoListSuccess(List<String> list) {

        ListViewForScrollView lvGoods = findViewById (R.id.lv_goods);
        lvGoods.setVisibility (View.VISIBLE);
        ItemNameAdapter adapter = new ItemNameAdapter (this, list);
        lvGoods.setAdapter (adapter);
    }

    @LiveDataMatch(tag = "装卸费详情")
    public void onDetailSuccess(ESteveDoreDeatil doreDeatil) {

        if (doreDeatil != null) {
            this.inputOrderId.setContent (doreDeatil.getOrderId ());
            this.inputMoney.setContent (doreDeatil.getMoney ());
            this.etDescription.setText (doreDeatil.getRemark ());

            if (doreDeatil.getUrlArr () != null && !doreDeatil.getUrlArr ().isEmpty ()) {
                List<EProcessFile> data = new ArrayList<> (doreDeatil.getUrlArr ().size ());
                for (String url : doreDeatil.getUrlArr ()) {
                    EProcessFile processFile = new EProcessFile ();
                    processFile.setSuccess ();
                    processFile.setImagUrl (url);
                    data.add (processFile);
                }
                this.billImageSelectView.setNewData (data);
            }

        }
    }


    /*-------------------------------图片上传 start---------------------------------------*/
    private static final int REQUESTCODE = 0x18;

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {

        super.onActivityResult (requestCode, resultCode, data);
        if (REQUESTCODE == requestCode && resultCode == Activity.RESULT_OK) {
            List<String> file = ImageSelector.obtainPathResult (data);
            this.billImageSelectView.onUpLoadStart (file);
            this.getViewModel ().upFile (file);
        }else if (requestCode == 10001  && resultCode == Activity.RESULT_OK ){
            inputOrderId.setContent(data.getStringExtra("orderId"));
        }
    }

    @Override
    public void onLookImageClick(List<EProcessFile> file, int position) {

        //查看大图
        List<EImage> list = new ArrayList<> (file.size ());
        for (EProcessFile processFile : file) {
            EImage image = new EImage ();
            image.setNetUrl (HttpURLConfig.getUrlImage (processFile.getImagUrl ()));
            list.add (image);
        }
        ImagePreviewActivity.start (this, list, position);
    }

    @Override
    public void onSelectImageClick(int surplus) {

        ImageSelector.open (this, surplus, true, REQUESTCODE);
    }

    @Override
    public void onUpImageClick(String file) {

        this.getViewModel ().upFile (file);
    }

    @Override
    public void onDelateClick(final int position) {

        DialogBuilder dialogBuilder = new DialogBuilder ();
        dialogBuilder.setMessage ("确定删除当前图片吗？");
        dialogBuilder.setOkListener ((DialogBuilder.DialogInterface dialogInterface, int i) -> {

            dialogInterface.dismiss ();
            billImageSelectView.deleteImage (position);
        });
        this.showDialog (dialogBuilder);
    }

    @LiveDataMatch(tag = "上传文件成功")
    public void onFileSuccess(File tag, String url) {

        billImageSelectView.onUpLoadFileSuccess (tag.getAbsolutePath (), url);
    }

    @LiveDataMatch(tag = "上传文件失败")
    public void onFileFailure(File tag, String error) {

        billImageSelectView.onUpLoadFileError (tag.getAbsolutePath ());
    }


}
