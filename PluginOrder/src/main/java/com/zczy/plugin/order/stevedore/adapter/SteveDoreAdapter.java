package com.zczy.plugin.order.stevedore.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.model.ESteveDore;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class SteveDoreAdapter extends BaseQuickAdapter<ESteveDore, BaseViewHolder> {

    public SteveDoreAdapter() {

        super(R.layout.order_stevedore_list_adapter);
    }

    @Override
    protected void convert(BaseViewHolder helper, ESteveDore item) {

        helper.setText(R.id.tv_orderId, "运单号：" + item.getOrderId());
        helper.setText(R.id.tv_car, "车牌号：" + item.getPlateNumber());
        helper.setText(R.id.tv_money, "装卸费金额：" + item.getMoney() + "元");
        helper.setText(R.id.tv_time, item.getCreatedTime());

        helper.addOnClickListener(R.id.tv_look_detail).addOnClickListener(R.id.tv_edit);
        helper.setGone(R.id.tv_edit, false);
        helper.setGone(R.id.iv_lingdan, TextUtils.equals("1", item.getLTLOrderFlag()));

        //状态:1:未审核,2:审核通过,3:已驳回'
        if (TextUtils.equals("1", item.getState())) {
            helper.setText(R.id.tv_status, "未审核");
        } else if (TextUtils.equals("2", item.getState())) {
            helper.setText(R.id.tv_status, "审核通过");
        } else if (TextUtils.equals("3", item.getState())) {
            helper.setText(R.id.tv_status, "已驳回");
            helper.setGone(R.id.tv_edit, true);
        } else {
            helper.setText(R.id.tv_status, TextUtils.isEmpty(item.getState()) ? "" : item.getState());
        }
    }
}
