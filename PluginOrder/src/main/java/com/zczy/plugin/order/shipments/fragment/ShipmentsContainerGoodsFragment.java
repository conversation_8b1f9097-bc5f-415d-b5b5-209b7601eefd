//package com.zczy.plugin.order.shipments.fragment;
//
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.LinearLayout;
//import android.widget.TextView;
//
//import com.sfh.lib.mvvm.annotation.LiveDataMatch;
//import com.zczy.comm.http.entity.PageList;
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.entity.EAdvanceInfo;
//import com.zczy.plugin.order.shipments.entity.EContainer;
//import com.zczy.plugin.order.shipments.entity.EGoodInfo;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
//import com.zczy.plugin.order.shipments.model.ShipmentsGoodsModel;
//import com.zczy.plugin.order.shipments.view.SelectContainerDialog;
//import com.zczy.plugin.order.shipments.view.ShipmentsContainerGoodsItemView;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 功能描述:【集装箱】确认发货，货物明细+实际发货量
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2018/12/20
// */
//public class ShipmentsContainerGoodsFragment extends ShipmentsBaseFragment<ShipmentsGoodsModel>  {
//
//    public static ShipmentsContainerGoodsFragment newFragment() {
//
//        return new ShipmentsContainerGoodsFragment();
//    }
//
//
//    private LinearLayout ll_content;
//    private ShipmentsEGoodInfo goodInfos;
//    private EAdvanceInfo advanceInfo;
//    private List<ShipmentsContainerGoodsItemView> shipmentsGoodsInfoViews;
//    private List<EGoodInfo> data;
//    private ShipmentsContainerGoodsItemView goodsItemView;
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_container_goods_fragment_view;
//    }
//
//    @Override
//    public void initData(View view) {
//
//        ll_content = view.findViewById(R.id.ll_content);
//        if (data != null) {
//            this.shipmentsGoodsInfoViews = new ArrayList<>(data.size());
//
//            for (EGoodInfo detail : data) {
//                ShipmentsContainerGoodsItemView goodsInfoView = new ShipmentsContainerGoodsItemView(this.getContext());
//                goodsInfoView.setOnClick(new ShipmentsContainerGoodsItemView.OnClick() {
//                    @Override
//                    public void onClick(ShipmentsContainerGoodsItemView view, EGoodInfo data) {
//                        goodsItemView = view;
//                        getViewModel(ShipmentsGoodsModel.class).queryContainerDetailList(goodInfos.getOrderId(), 1, 100);
//                    }
//                });
//
//                this.ll_content.addView(goodsInfoView);
//                this.shipmentsGoodsInfoViews.add(goodsInfoView);
//                goodsInfoView.setInfo(goodInfos, detail,advanceInfo);
//            }
//        }
//    }
//
//    public void setEGoodInfoList(ShipmentsEGoodInfo goodInfo,EAdvanceInfo advanceInfo) {
//        this.goodInfos = goodInfo;
//        this.data = goodInfo.getRootArray();
//        this.advanceInfo = advanceInfo;
//    }
//
//    /***
//     * 检查输入内容
//     * @return
//     */
//    @Override
//    public boolean checkParams(ShipmentUI shipmentUI) {
//
//        //这里做确认发货的请求
//        int childSize = this.shipmentsGoodsInfoViews.size();
//        if (childSize == 0) {
//            return false;
//        }
//        List<EGoodInfo> goodInfos = new ArrayList<>(3);
//        for (int i = 0; i < childSize; i++) {
//
//            final ShipmentsContainerGoodsItemView itemView = this.shipmentsGoodsInfoViews.get(i);
//            final EGoodInfo data = itemView.getData();
//            final String input = data.getBeforeDeliverCargoWeight();
//            if (data.getTempContainerSize() == null || data.getTempContainerSize().isEmpty()) {
//                this.showToast("请选择集装箱箱号！");
//                return false;
//            }
//            if (TextUtils.isEmpty(input)) {
//                this.showToast("请输入[" + data.getCargoName() + "]货物重量");
//                return false;
//            }
//            goodInfos.add(data);
//        }
//        //货物ID:输入吨位
//        shipmentUI.goodInfos = goodInfos;
//        return true;
//    }
//
//
//
//    @LiveDataMatch
//    public void onQueryContainerDetailListSuccess(PageList<EContainer> page) {
//        if (page == null) {
//            return;
//        }
//        new SelectContainerDialog().setListener(new SelectContainerDialog.SelectContainerDialogListener() {
//            @Override
//            public void selectItem(ArrayList<EContainer> list) {
//                goodsItemView.setContainers(list);
//            }
//
//            @Override
//            public void selectError() {
//                showToast("最多只能选择"+ goodsItemView.getWeight() + "箱");
//            }
//        }).setData(page,goodsItemView.getCargoName(),goodsItemView.getMomey(),goodsItemView.getWeight(),goodInfos.getCargoCategory()).show(getActivity());
//    }
//}
