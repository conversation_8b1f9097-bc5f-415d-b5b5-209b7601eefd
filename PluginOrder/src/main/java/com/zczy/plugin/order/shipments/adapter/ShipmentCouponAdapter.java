package com.zczy.plugin.order.shipments.adapter;

import android.graphics.Color;
import android.text.TextUtils;
import android.util.SparseArray;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.comm.utils.ResUtil;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.source.pick.entity.EPickUserCoupon;

/**
 * 功能描述:发货优惠券
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/20
 */
public class ShipmentCouponAdapter extends BaseQuickAdapter<EPickUserCoupon, BaseViewHolder> {

    SparseArray<EPickUserCoupon> mSelectArray = new SparseArray<>();
    int color50 = Color.parseColor("#5088fc");
    int color66 = ResUtil.getResColor(R.color.text_66);

    public ShipmentCouponAdapter() {

        super(R.layout.order_pick_coupon_adapter);
    }

    boolean user = false;

    public void setUser(boolean user) {
        this.user = user;
    }

    @Override
    protected void convert(BaseViewHolder helper, EPickUserCoupon item) {

        // 优惠券面额
        helper.setText(R.id.tv_coupon_money, item.getCouponUnitMoney())
                // 优惠券类型名称
                .setText(R.id.tv_coupon_type_name, item.getCouponTypeIdUI())
                // 满多少可用
                .setText(R.id.tv_min_money, "满" + item.getMinMoney() + "元可用")
                // 优惠券有效期
                .setText(R.id.tv_validity_time, item.getValidityTime())
                //不可与已勾选卷叠加使用
                .setGone(R.id.tv_toast, false)
                //选择框
                .setGone(R.id.cb_select, false)

                .addOnClickListener(R.id.cb_select);

        if (TextUtils.equals("1", item.getCouponTypeId())) {
            helper.setVisible(R.id.moneyIcon, true);
            helper.setVisible(R.id.tv_min_money, true);

        } else if (TextUtils.equals(item.getCouponTypeId(), "2")) {

            if (TextUtils.equals(item.getDiscountType(), "1")) {
                    // 1 抵扣金额
                helper.setVisible(R.id.moneyIcon, true);
                helper.setGone(R.id.tv_min_money, !TextUtils.isEmpty(item.getUseThreshold() )).setText(R.id.tv_min_money, "满" + item.getUseThreshold() + "元可用");

            } else if (TextUtils.equals(item.getDiscountType(), "0")) {
                //0 抵扣比例
                if (TextUtils.equals(item.getDiscountRatio(), "10.0")) {
                    helper.setText(R.id.tv_coupon_money, "全额抵用");
                } else {
                    helper.setText(R.id.tv_coupon_money, item.getDiscountRatio() + "折");
                }
                helper.setVisible(R.id.moneyIcon, false);

                helper .setGone(R.id.tv_min_money, !TextUtils.isEmpty(item.getDiscountMoneyTop() )).setText(R.id.tv_min_money, "最多抵用" + item.getDiscountMoneyTop() + "元");

            }

        }

        if (TextUtils.equals("3", item.getCouponType())) {
            helper.setGone(R.id.tv_toast, true).setText(R.id.tv_toast, "订单不满足使用优惠条件");
        }

        //可以优惠卷列表
        if (this.user) {

            helper.setImageResource(R.id.iv_coupon_bg, R.drawable.order_coupon_main_item_unused_bg);
            helper.setTextColor(R.id.tv_coupon_money, color50);
            helper.setTextColor(R.id.tv_coupon_type_name, color50);

            if (mSelectArray.get(item.getKey()) != null) {
                helper.setGone(R.id.cb_select, true).setChecked(R.id.cb_select, true);
            } else {
                helper.setGone(R.id.cb_select, true).setChecked(R.id.cb_select, false);
            }
        } else {
            //不可以优惠卷列表
            helper.setTextColor(R.id.tv_coupon_money, color66);
            helper.setTextColor(R.id.tv_coupon_type_name, color66);
            helper.setImageResource(R.id.iv_coupon_bg, R.drawable.order_coupon_main_item_used_bg);
            helper.setGone(R.id.cb_select, false);
        }

    }

    public void setSelect(int position) {

        EPickUserCoupon item = this.getItem(position);
        this.setSelect(item);
    }

    public void setSelect( EPickUserCoupon item) {

        if (mSelectArray.get(item.getKey()) != null) {
            mSelectArray.remove(item.getKey());
        } else {
            // 发货优惠抵用券只能选一个
            mSelectArray.clear();
            mSelectArray.put(item.getKey(), item);
        }

        this.notifyDataSetChanged();
    }
    public SparseArray<EPickUserCoupon> getSelectArray() {
        return mSelectArray;
    }
}
