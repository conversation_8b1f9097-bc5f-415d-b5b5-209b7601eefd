package com.zczy.plugin.order.designation.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.designation.req.*
import com.zczy.plugin.order.source.pick.entity.DelistPrePayStatus
import com.zczy.plugin.order.source.pick.entity.RxEventSpecified
import com.zczy.plugin.order.source.pick.model.request.ReqBossDelistPrePayStatus


/**
 *    author : Ssp
 *    date   : 2019/7/2 14:07
 *    desc   : 指定司机和车辆
 */
class DesignationVehicleDriverModel : BaseViewModel() {
    /**
     * 确认派单
     */
    fun getNetInfo(req: ReqSpecidiedVehichleDriver) {
        execute(true, req, object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(t: BaseRsp<ResultData>) {
                if (t.success()) {
                    postEvent(RxEventSpecified(true))
                    setValue("specifiedSuccess", t.data?.resultMsg)
                } else {
                    showDialogToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }


    /**
     * 查询预付功能
     */
    fun queryBossDelistPrePayStatus(req: ReqBossDelistPrePayStatus) {
        this.execute(true, req, object : IResult<BaseRsp<DelistPrePayStatus>> {
            override fun onFail(e: HandleException) {
                hideLoading()
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(rsp: BaseRsp<DelistPrePayStatus>) {
                hideLoading()
                if (rsp.success()) {
                    setValue("onBossDelistPrePayStatus", rsp.data)
                } else {
                    showDialogToast(rsp.data?.resultMsg)
                }
            }
        })
    }

    /**
     * 确认派单
     */
    fun bigAssignDriverVehicle(req: ReqBigAssignDriverVehicle) {
        execute(true, req, object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(t: BaseRsp<ResultData>) {
                if (t.success()) {
                    postEvent(RxEventSpecified(true))
                    setValue("bigAssignDriverVehicleSuccess", t.data?.resultMsg)
                } else {
                    showDialogToast(t.msg)
                }
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    /**
     * 电子合同签署前置检验接口
     */
    fun checkEletcSignContract() {
        this.execute(true, ReqCheckEletcSignContract()) { rsp ->
            if (rsp.success()) {
                setValue("onCheckEletcSignContractSuccess", rsp.data)
            }
        }
    }

    /**
     * 发起电子合同签署
     */
    fun eletcSignContract(req: ReqEletcSignContract) {
        this.execute(true, req) { rsp ->
            if (rsp.success()) {
                setValue("onEletcSignContract", rsp.data)
            } else {
                showDialogToast(rsp.msg)
            }
        }
    }

    /**
     * 获取临时token
     */
    fun getTempToken(url: String?) {
        this.execute(true, ReqGetTempToken()) { rsp ->
            if (rsp.success()) {
                setValue("onGetTempToken", rsp.data, url)
            } else {
                showDialogToast(rsp.msg)
            }
        }
    }

}