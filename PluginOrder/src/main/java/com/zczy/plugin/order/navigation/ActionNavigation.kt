package com.zczy.plugin.order.navigation

import android.content.Context
import android.content.Intent
import android.text.TextUtils

import com.google.gson.Gson
import com.sfh.lib.mvvm.service.BaseViewModel
import com.zczy.comm.CommServer
import com.zczy.comm.tencent.TencetMavigatorMapActivity
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.plugin.order.navigation.req.OrderCoordinate
import com.zczy.plugin.order.navigation.req.ReqOrderCoordinate
import com.zczy.plugin.order.navigation.req.ReqQueryTmsAddressInfoBySourceId
import com.zczy.plugin.order.waybill.entity.EWaybill

/** 功能描述: 高德导航
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/11/20
 */
class ActionNavigation{
    private var sourceId: String = ""
    private var mOrderId: String = ""
    private var context: Context? = null
    private var mData: EWaybill? = null
    private var  consignorUserId: String = ""
    // 1 待装货、枪单成功 2 待卸货 、发货成功
    private var mFlag: String = ""

    fun setEWaybillData(data: EWaybill): ActionNavigation {
        this.mData = data
        return this
    }

//    fun setSourceId(sourceId: String): ActionNavigation {
//        this.sourceId = sourceId
//        return this
//    }
    fun setSourceId(sourceId: String?): ActionNavigation { // 修改参数类型为 String? (可空)
        if (sourceId == null) {
            this.sourceId = "" // 或者设置为默认值，例如空字符串
        } else {
            this.sourceId = sourceId
        }
        return this
    }
    fun setOrderId(orderId: String): ActionNavigation {
        this.mOrderId = orderId
        return this
    }
    fun setConsignorUserId(consignorUserId: String): ActionNavigation {
        this.consignorUserId = consignorUserId
        return this
    }

    fun setFlag(flag: String): ActionNavigation {
        mFlag = flag
        return this
    }

    fun setData(context: Context, data: String): ActionNavigation {
        this.context = context
        val orderCoordinate = data.toJsonObject(OrderCoordinate::class.java)
        lookRouteSearch(orderCoordinate, null)
        return this
    }

    fun start(context: Context, model: BaseViewModel) {
        this.context = context
        model.execute(true,if(TextUtils.isEmpty(this.sourceId)) ReqOrderCoordinate(mOrderId) else ReqQueryTmsAddressInfoBySourceId(sourceId,consignorUserId)) { baseRsp ->
            if (baseRsp.success()) {
                lookRouteSearch(baseRsp.data, null)
            } else {
                model.showDialogToast(baseRsp.data?.resultMsg)
            }
        }
    }

    fun start(context: Context, model: BaseViewModel, callback: Runnable) {
        this.context = context
        model.execute(true, ReqOrderCoordinate(mOrderId)) { baseRsp ->
            if (baseRsp.success()) {
                lookRouteSearch(baseRsp.data, callback)
            } else {
                model.showDialogToast(baseRsp.data?.resultMsg)
            }
        }
    }

    fun lookRouteSearch(data: OrderCoordinate?, callback: Runnable?) {
        data?.let {
           //起运地
            val despatchProCityDisPlace = it.despatchProCityDisPlace
            val despatchLat: Double = it.despatchCoordinateY.toDoubleOrNull() ?: 0.0
            val despatchLon: Double = it.despatchCoordinateX.toDoubleOrNull() ?: 0.0
            //目的地
            val deliverProCityDisPlace = it.deliverProCityDisPlace
            val deliverLat: Double = it.deliverCoordinateY.toDoubleOrNull() ?: 0.0
            val deliverLon: Double = it.deliverCoordinateX.toDoubleOrNull() ?: 0.0
            if(TextUtils.equals(mFlag, "1")){
                TencetMavigatorMapActivity.start(context, 0.0, 0.0,"", despatchLat, despatchLon,despatchProCityDisPlace,mFlag,mData?.toJson())
            }else{
                TencetMavigatorMapActivity.start(context, 0.0, 0.0,"", deliverLat, deliverLon,deliverProCityDisPlace,mFlag,mData?.toJson())
            }

            callback?.run()

        }
    }


}
