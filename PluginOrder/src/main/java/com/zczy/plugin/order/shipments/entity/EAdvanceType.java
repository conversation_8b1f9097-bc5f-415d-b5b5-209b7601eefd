package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import androidx.annotation.NonNull;

/***
 * 预付支付方式
 */
public class EAdvanceType {
    //1:预付现金;2:预付油品;3:预付油品+现金
    String type;
    String desc;
    //是否默认选择 1是 0 否
    String defaultAdvanceWayFlag;

    public String getDefaultAdvanceWayFlag() {
        return defaultAdvanceWayFlag;
    }

    public boolean isDefaultAdvanceWayFlag() {
      return TextUtils.equals("1",defaultAdvanceWayFlag);
    }

    /***
     * 判断是否为预付现金
     * @return
     */
    public boolean isPayMoney(){
        return TextUtils.equals("1",type);
    }
    /***
     * 判断是否为预付油品
     * @return
     */
    public boolean isPayOil(){
        return TextUtils.equals("2",type);
    }
    /***
     * 判断是否为预付油品+现金
     * @return
     */
    public boolean isPayMoenyAndOil(){
        return TextUtils.equals("3",type);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @NonNull
    @Override
    public String toString() {
        return desc;
    }
}
