package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:省平台2.0 定时任务
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import android.content.Context;
import androidx.annotation.NonNull;
import android.text.TextUtils;

import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import com.google.gson.Gson;
import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnSendResultListener;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;

import java.util.List;
import java.util.concurrent.TimeUnit;

/***
 * 定时任务
 */
public class TimeWorkerAction extends Worker implements OnSendResultListener {

    public static class Build {
        ESDKInfoObj infoObj;

        public Build setInfo(ESDKInfoObj info) {
            this.infoObj = info;
            return this;
        }

        public void build() {
            String json = new Gson().toJson(infoObj);

            Data data = new Data.Builder()
                    .putString("json", json)
                    .putLong("intervalTime", infoObj.getInterval())
                    .build();
            if (infoObj.getInterval() <= 0) {
                infoObj.setInterval(17 * 1000);
            }
            OneTimeWorkRequest request = new OneTimeWorkRequest.Builder(TimeWorkerAction.class)
                    .setInitialDelay(infoObj.getInterval(), TimeUnit.MILLISECONDS)
                    .setInputData(data)
                    .build();

            WorkManager.getInstance().enqueueUniqueWork(infoObj.getOrderId(), ExistingWorkPolicy.REPLACE, request);

            BaseActionServer.out(String.format("省货物平台系统2.0=TimeWorker=>定时[开始] json:%s", infoObj));
        }
    }

    private ESDKInfoObj infoObj;

    public TimeWorkerAction(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }


    @NonNull
    @Override
    public Result doWork() {
        String json = this.getInputData().getString("json");
        if (TextUtils.isEmpty(json)) {
            return Result.success();
        }

        this.infoObj = new Gson().fromJson(json, ESDKInfoObj.class);
        BaseActionServer.out(String.format("省货物平台系统2.0=TimeWorker=>定时[执行] infoObj:%s", infoObj));

        ShippingNoteInfo[] shippingNoteNumbers = new ShippingNoteInfo[]{infoObj.getShippingNoteInfo()};

        LocationOpenApi.send(getApplicationContext(), this.infoObj.getVehicleNumber(), this.infoObj.getVehicleNumber(), "", shippingNoteNumbers, this);

        return Result.success();
    }

    @Override
    public void onFailure(String code, String s1, List<ShippingNoteInfo> list) {

        BaseActionServer.out(String.format("省货物平台系统2.0=TimeWorker=>定时[失败] infoObj:%s,code:%s,msg:%s", infoObj, code, s1));
        if (TextUtils.equals("888884", code)) {
            //未开始的运单
            new ReStartAction(infoObj, "").start();
        } else if (TextUtils.equals("110001", code)) {
            //定位失败：网络连接异常 请到http://lbs.amap.com/api/android-location-sdk/guide/utilities/errorcode/查看错误码说明,错误详细信息:网络异常，未连接到网络，请连接网络#0401
            new Build().setInfo(infoObj).build();
        }
    }

    @Override
    public void onSuccess(List<ShippingNoteInfo> list) {


        if (list != null && list.size() > 0) {
            BaseActionServer.out(String.format("省货物平台系统2.0=TimeWorker=>定时[成功] infoObj:%s,size:%s,time:%s", infoObj, list.size(), list.get(0).getInterval()));
            //定位间隔时间(单位 ms)毫秒
            infoObj.setInterval(list.get(0).getInterval());
            new Build().setInfo(infoObj).build();
        }
    }

}
