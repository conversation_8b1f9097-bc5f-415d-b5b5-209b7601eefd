package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 查询变更费
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=50332000
 */
class ReqGetChangeFee() : BaseOrderRequest<BaseRsp<RspChangeFee>>("oms-app/orderChange/getChangeFee")

data class RspChangeFee(
        var changeFee: Double = 0.0//变更费
) : ResultData()