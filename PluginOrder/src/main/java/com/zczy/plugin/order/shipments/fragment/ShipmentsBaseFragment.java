//package com.zczy.plugin.order.shipments.fragment;
//
//import com.sfh.lib.mvvm.service.BaseViewModel;
//import com.sfh.lib.ui.AbstractLifecycleFragment;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//
///**
// * 功能描述:确认
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public abstract class ShipmentsBaseFragment<VM extends BaseViewModel> extends AbstractLifecycleFragment<VM> {
//
//    protected String flag = ShipmentsBaseFragment.class.getSimpleName();
//
////    /***
////     * 满足条件动态
////     * @param listener
////     */
////    public void setMeetIfListener(MeetInteractionListener listener) {
////        this.listener = listener;
////    }
////
////    /***
////     * 满足条件动态
////     * @param listener
////     */
////    public void setMeetIfListener(String flag, MeetInteractionListener listener) {
////        this.flag = flag;
////        this.listener = listener;
////    }
//
//    public void setFlag(String flag) {
//
//        this.flag = flag;
//    }
//
//    /***
//     * 检查是否满足条件，赋值
//     * @return
//     */
//    public abstract boolean checkParams(ShipmentUI data);
//
//}
