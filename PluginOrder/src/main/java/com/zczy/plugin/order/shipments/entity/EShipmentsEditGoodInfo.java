package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import com.zczy.comm.http.entity.ResultData;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:确认发货后修改发货信息时查询运单信息
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/23
 */
public class EShipmentsEditGoodInfo extends ResultData {

    /*** 订单ID*/
    String orderId;

    /*** 货物总量*/
    String weight;

    /*** 费用类型 0：包车价 1：单价*/
    String freightType;

    /*** 货物类别 1：重货,2 泡货*/
    String cargoCategory;

    /*** 货主发货吨位限制 */
    String shippingOrientationMargin;

    //纸质单据
    ArrayList<EWater> pageImageJsonObjArr;

    //发货照片
    ArrayList<EWater>  deliverImageJsonObjArr;

    private List<EGoodInfo> rootArray;


    /*** 上传发货单配置项 1必填 0 非必填（不展示上传模块） 2非必填（展示上传回单模块 司机可以不上传）*/
    private  String uploadInvoiceConfig;


    /***结算依据 1:确认发货吨位结算  2：确认收货吨位结算  3: 按收发货榜单较小值结算*/
    private  String settleBasisType;

    /***1:指定 非指定 */
    private String specifyFlag;
  //是否临夏订单-- 临夏的比传发货人车合影  1 是
    private String deliverTrackPersonImgFlag;
    //[天津] 人车货合影 0 不必填 1 必填
    String deliverPicConfig;

    //非预付情况下运单照片展示逻辑
    ShipmentsImgObj orderImgObj;
    //非预付情况下人车货照片展示逻辑
    ShipmentsImgObj peopleVehicleImgObj;

    public ShipmentsImgObj getOrderImgObj() {
        return orderImgObj;
    }

    public ShipmentsImgObj getPeopleVehicleImgObj() {
        return peopleVehicleImgObj;
    }


    public String getDeliverPicConfig() {
        return deliverPicConfig;
    }

    public String getSpecifyFlag() {
        return specifyFlag;
    }

    public void setSpecifyFlag(String specifyFlag) {
        this.specifyFlag = specifyFlag;
    }

    public String getDeliverTrackPersonImgFlag() {
        return deliverTrackPersonImgFlag;
    }

    public void setDeliverTrackPersonImgFlag(String deliverTrackPersonImgFlag) {
        this.deliverTrackPersonImgFlag = deliverTrackPersonImgFlag;
    }

    public boolean noCheckFile() {
        //true 不必传 ， false 必传
        if (TextUtils.equals("1", deliverTrackPersonImgFlag)){
           // 宁夏必传
            return false;
        }
        //!上传发货单配置项 1必填 && ！(结算依据[1:确认发货吨位结算,3: 按收发货榜单较小值结算])
        return (!TextUtils.equals("1", uploadInvoiceConfig))
                &&
                (!(TextUtils.equals("1", settleBasisType) || TextUtils.equals("3",settleBasisType)));

    }


    /***
     * 人车照片(显示状态)
     * true ： 显示 false 不显示
     */
    public  boolean perCarImageShow(){
        if (TextUtils.equals("1", deliverTrackPersonImgFlag ) || TextUtils.equals("1",deliverPicConfig)){
            //  融通平台 （必传） || 宁夏的运单 （必传）||  必传配置
            return  true;
        }
        return  false;
    }

    public String getUploadInvoiceConfig() {
        return uploadInvoiceConfig;
    }

    public void setUploadInvoiceConfig(String uploadInvoiceConfig) {
        this.uploadInvoiceConfig = uploadInvoiceConfig;
    }

    public String getSettleBasisType() {
        return settleBasisType;
    }

    public void setSettleBasisType(String settleBasisType) {
        this.settleBasisType = settleBasisType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getFreightType() {
        return freightType;
    }

    public void setFreightType(String freightType) {
        this.freightType = freightType;
    }

    public String getCargoCategory() {
        return cargoCategory;
    }

    public void setCargoCategory(String cargoCategory) {
        this.cargoCategory = cargoCategory;
    }

    public String getShippingOrientationMargin() {
        return shippingOrientationMargin;
    }

    public void setShippingOrientationMargin(String shippingOrientationMargin) {
        this.shippingOrientationMargin = shippingOrientationMargin;
    }

    public ArrayList<EWater> getPageImageJsonObjArr() {
        return pageImageJsonObjArr;
    }

    public void setPageImageJsonObjArr(ArrayList<EWater> pageImageJsonObjArr) {
        this.pageImageJsonObjArr = pageImageJsonObjArr;
    }

    public ArrayList<EWater> getDeliverImageJsonObjArr() {
        return deliverImageJsonObjArr;
    }

    public void setDeliverImageJsonObjArr(ArrayList<EWater> deliverImageJsonObjArr) {
        this.deliverImageJsonObjArr = deliverImageJsonObjArr;
    }

    public List<EGoodInfo> getRootArray() {
        return rootArray;
    }

    public void setRootArray(List<EGoodInfo> rootArray) {
        this.rootArray = rootArray;
    }
    public boolean isDeliverNoEmpty() {
        //发货
        return deliverImageJsonObjArr != null && !deliverImageJsonObjArr.isEmpty();
    }

    public boolean isPageImageJsonObjArrNoEmpty() {
        //发货运单
        return pageImageJsonObjArr != null && !pageImageJsonObjArr.isEmpty();
    }

}
