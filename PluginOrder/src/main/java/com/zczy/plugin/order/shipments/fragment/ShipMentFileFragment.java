//package com.zczy.plugin.order.shipments.fragment;
//
//import android.app.Activity;
//import android.content.Intent;
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.TextView;
//
//import com.sfh.lib.mvvm.annotation.LiveDataMatch;
//import com.sfh.lib.ui.dialog.DialogBuilder;
//import com.zczy.comm.data.entity.EImage;
//import com.zczy.comm.data.entity.EProcessFile;
//import com.zczy.comm.utils.imageselector.ImagePreviewActivity;
//import com.zczy.comm.utils.imageselector.ImageSelectProgressView;
//import com.zczy.comm.utils.imageselector.ImageSelector;
//import com.zczy.comm.utils.json.JsonUtil;
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.ZczyWaterActivity;
//import com.zczy.plugin.order.bill.BillDocumentImageDemoDialog;
//import com.zczy.plugin.order.shipments.entity.EWater;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.entity.ShipmentsImgObj;
//import com.zczy.plugin.order.shipments.model.ShipMentFileModel;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 功能描述:发货-图片上传
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2018/12/24
// */
//public class ShipMentFileFragment extends ShipmentsBaseFragment<ShipMentFileModel> implements ImageSelectProgressView.OnItemSelectListener {
//
//
//    public static ShipMentFileFragment newFragment(String orderId, ArrayList<EWater> image, String title, String toast, int maxsize) {
//
//        Bundle bundle = new Bundle();
//        bundle.putString("orderId", orderId);
//        bundle.putString("title", title);
//        bundle.putString("toast", toast);
//        bundle.putInt("maxsize", maxsize);
//        bundle.putString("images", image != null ? JsonUtil.toJson(image) : "");
//        ShipMentFileFragment fragmentView = new ShipMentFileFragment();
//        fragmentView.setArguments(bundle);
//        return fragmentView;
//    }
//
//    public static ShipMentFileFragment newFragment(String orderId, ArrayList<EWater> image, String title, String toast, int maxsize, String warning) {
//
//        Bundle bundle = new Bundle();
//        bundle.putString("orderId", orderId);
//        bundle.putString("title", title);
//        bundle.putString("toast", toast);
//        bundle.putString("warning", warning);
//        bundle.putInt("maxsize", maxsize);
//        bundle.putString("images", image != null ? JsonUtil.toJson(image) : "");
//        ShipMentFileFragment fragmentView = new ShipMentFileFragment();
//        fragmentView.setArguments(bundle);
//        return fragmentView;
//    }
//    public static ShipMentFileFragment newFragment(String orderId, ArrayList<EWater> image, ShipmentsImgObj imgObj) {
//
//        Bundle bundle = new Bundle();
//        bundle.putString("orderId", orderId);
//        bundle.putString("title", imgObj.getTitle());
//        bundle.putString("toast", String.format("最多上传%s张",imgObj.getLimitCount()));
//        bundle.putString("warning", imgObj.getWarningMsg());
//        bundle.putInt("maxsize", imgObj.getLimitCount());
//        bundle.putString("images", image != null ? JsonUtil.toJson(image) : "");
//        ShipMentFileFragment fragmentView = new ShipMentFileFragment();
//        fragmentView.setArguments(bundle);
//        return fragmentView;
//    }
//    public interface NoCheckFileListener {
//        /***
//         *  校验数据 true 不校验数据直接取值  false: 校验之后再取值
//         * @param list
//         * @return true  不校验数据直接取值  false: 校验之后再取值
//         */
//        boolean noCheck(List<EProcessFile> list);
//    }
//
//    private static final int REQUESTCODE = 0x18;
//    private static final int REQUESTCODE_X = 0x19;
//
//    /*** 纸质运单照片*/
//    private TextView mTvTitle;
//
//    /*** 最多可上传3张照片*/
//    private TextView mTvFileToast;
//
//    private TextView tvWarning;
//
//    private TextView tv_icon;
//
//    private ImageSelectProgressView mBillImageSelectView;
//
//    private NoCheckFileListener mCheckFileListener;
//
//    private String address = "";
//    private String latLon = "";
//    private String orderId;
//    //只拍照
//    private boolean mTakePhotoListener;
//    //是否上传水印 1:是;0:不是
//    String waterMarkFlag;
//    //默认红色*标签显示
//    private boolean showReadToastIcon = true;
//    //默认 可以编辑
//    private boolean edit = true;
//
//    public void setEdit(boolean edit) {
//        this.edit = edit;
//    }
//
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_file_fragment_view;
//    }
//
//
//    @Override
//    public void initData(View view) {
//
//        this.initView(view);
//    }
//
//    public void initView(View view) {
//        tv_icon = view.findViewById(R.id.tv_icon);
//        this.mTvTitle = view.findViewById(R.id.tv_title);
//        this.mTvFileToast = view.findViewById(R.id.tv_file_toast);
//        this.mBillImageSelectView = view.findViewById(R.id.bill_image_select_view);
//        this.mBillImageSelectView.setOnItemSelectListener(this);
//        this.tvWarning = view.findViewById(R.id.tv_warning);
//        Bundle bundle = getArguments();
//        if (bundle != null) {
//            String imagesJson = bundle.getString("images");
//            String title = bundle.getString("title");
//            String toast = bundle.getString("toast");
//            int maxsize = bundle.getInt("maxsize", 8);
//            orderId = bundle.getString("orderId", "");
//            this.mTvTitle.setText(title);
//            this.mTvFileToast.setText(toast);
//            this.mBillImageSelectView.setShowSize(maxsize, 4);
//            if (!TextUtils.isEmpty(imagesJson)) {
//
//                List<EWater> images = JsonUtil.toJsonArray(imagesJson, EWater.class);
//
//                List<EProcessFile> data = new ArrayList<>(maxsize);
//                for (EWater water : images) {
//                    EProcessFile processFile = new EProcessFile();
//                    processFile.setSuccess();
//                    processFile.setImagUrl(water.picUrl);
//                    processFile.setImage2Url(water.picOrgUrl);
//                    data.add(processFile);
//                }
//                this.mBillImageSelectView.setDelete(edit);
//                this.mBillImageSelectView.setNewData(data);
//            }
//
//            String warning = bundle.getString("warning");
//            if (!TextUtils.isEmpty(warning)) {
//                this.tvWarning.setVisibility(View.VISIBLE);
//                this.tvWarning.setText(warning);
//            }
//        }
//        //提示红标
//        tv_icon.setVisibility(showReadToastIcon ? View.VISIBLE : View.GONE);
//
//        View tv_demo = view.findViewById(R.id.tv_demo);
//        if (edit) {
//            tv_demo.setVisibility(View.VISIBLE);
//            tv_demo.setOnClickListener(v -> {
//                //查看示例
//                String demo = TextUtils.equals(ShipmentUI.TAG_IMAGE_WAYBILL, flag)? "1" : "2";
//                new BillDocumentImageDemoDialog().setWaybillImage(demo).show(getActivity());
//            });
//        }
//
//        getViewModel(ShipMentFileModel.class).openLocation();
//    }
//
//    long lastTime = -1;
//
//    @Override
//    public void onSelectImageClick(int surplus) {
//
//        long time = System.currentTimeMillis();
//        if (time - this.lastTime >= 1000) {
//            this.lastTime = time;
//
//            if (TextUtils.equals("1", waterMarkFlag)) {
//                if (TextUtils.isEmpty(address)) {
//                    getViewModel(ShipMentFileModel.class).openLocation();
//                }
//                //加水印只能每次一张
//                surplus = 1;
//            }
//
//            if (mTakePhotoListener) {
//                ImageSelector.openCamera(ShipMentFileFragment.this, REQUESTCODE);
//
//            } else {
//                ImageSelector.open(ShipMentFileFragment.this, surplus, true, REQUESTCODE);
//            }
//        }
//    }
//
//    @Override
//    public void onActivityResult(int requestCode, int resultCode, Intent data) {
//
//        try {
//            super.onActivityResult(requestCode, resultCode, data);
//            if (resultCode == Activity.RESULT_OK) {
//
//                List<EProcessFile> eProcessFiles = new ArrayList<>();
//
//                if (REQUESTCODE == requestCode) {
//                    List<String> files = ImageSelector.obtainPathResult(data);
//                    if (files == null || files.isEmpty()){
//                        showToast("图片损坏或图片不存在,请重新上传");
//                        return;
//                    }
//                    for (String path : files) {
//                        //#872955 java.lang.NullPointerException
//                        //com.zczy.plugin.order.shipments.fragment.ShipMentFileFragment.onActivityResult(ShipMentFileFragment.java:128)
//                        if (TextUtils.isEmpty(path)) {
//                            showToast("图片损坏或图片不存在,请重新上传");
//                            return;
//                        }
//                        File select = new File(path);
//                        if (!select.exists() || !select.canRead() || select.length() <= 1 * 1024) {
//                            showToast("图片损坏或图片不存在,请重新上传");
//                            return;
//                        }
//                        EProcessFile processFile = new EProcessFile();
//                        processFile.setTag(path);
//                        eProcessFiles.add(processFile);
//                    }
//                    if (TextUtils.equals("1", waterMarkFlag)) {
//                        //进入加水印界面
//                        ZczyWaterActivity.start(ShipMentFileFragment.this,
//                                orderId,
//                                address,
//                                latLon,
//                                files.get(0),
//                                REQUESTCODE_X);
//                    } else {
//                        this.mBillImageSelectView.onUpLoadStartList(eProcessFiles);
//                        this.getViewModel(ShipMentFileModel.class).upFile(eProcessFiles, true);
//                    }
//
//                } else {
//
//                    String image = data.getStringExtra(ZczyWaterActivity.KEY_IMAGE_PATH);
//                    String imageWater = data.getStringExtra(ZczyWaterActivity.KEY_IMAGE_PATH_WATERMASK);
//                    if (TextUtils.isEmpty(image) || TextUtils.isEmpty(imageWater)) {
//                        showToast("图片损坏或图片不存在,请重新上传");
//                        return;
//                    }
//                    EProcessFile processFile = new EProcessFile();
//                    processFile.setTag(imageWater);//水印图
//                    processFile.setImage2Tag(image);//原图
//                    eProcessFiles.add(processFile);
//
//                    this.mBillImageSelectView.onUpLoadStartList(eProcessFiles);
//                    this.getViewModel(ShipMentFileModel.class).upFile(eProcessFiles, false);
//                }
//            }
//        }catch (Exception e){
//
//        }
//
//    }
//
//    public void setShowReadToastIcon(boolean showReadToastIcon) {
//        this.showReadToastIcon = showReadToastIcon;
//        if (tv_icon != null) {
//            tv_icon.setVisibility(showReadToastIcon ? View.VISIBLE : View.GONE);
//        }
//    }
//
//    public void setNoCheckFileListener(NoCheckFileListener mCheckFileListener) {
//        this.mCheckFileListener = mCheckFileListener;
//    }
//
//    @Override
//    public boolean checkParams(ShipmentUI shipmentUI) {
//
//        List<EProcessFile> list = this.mBillImageSelectView.getDataList();
//        if (list.isEmpty()) {
//            //不校验数据直接取值
//            if (mCheckFileListener != null && mCheckFileListener.noCheck(list)) {
//                return true;
//            }
//            String content = "请选择" + mTvTitle.getText().toString();
//            this.showToast(content);
//            return false;
//        }
//        List<EWater> images = new ArrayList<>();
//        for (EProcessFile file : list) {
//            //第2张是原图，第一张是水印图
//            images.add(new EWater(file.getImage2Url(), file.getImagUrl()));
//        }
//
//        shipmentUI.images.put(this.flag.hashCode(), images);
//        return true;
//    }
//
//
//    /***
//     *  指定拍照
//     * @param takePhotoListener
//     */
//    public void setTakePhotoListener(boolean takePhotoListener) {
//        this.mTakePhotoListener = takePhotoListener;
//    }
//
//    @Override
//    public void onUpImageClick2(EProcessFile file) {
//        //水印的不压缩
//        this.getViewModel(ShipMentFileModel.class).upFile(file, !TextUtils.equals("1", waterMarkFlag));
//    }
//
//    @Override
//    public void onUpImageClick(String file) {
//
//    }
//
//    @Override
//    public void onLookImageClick(List<EProcessFile> file, int position) {
//
//        List<EImage> images = new ArrayList<>(file.size());
//        for (EProcessFile processFile : file) {
//            EImage image = new EImage();
//            image.setImageId(processFile.getImagUrl());
//            images.add(image);
//        }
//        ImagePreviewActivity.start(this, images, position);
//    }
//
//    @Override
//    public void onDelateClick(final int position) {
//
//        DialogBuilder dialogBuilder = new DialogBuilder();
//        dialogBuilder.setMessage("确定删除当前图片吗？");
//        dialogBuilder.setOkListener((DialogBuilder.DialogInterface dialogInterface, int i) -> {
//
//            dialogInterface.dismiss();
//            mBillImageSelectView.deleteImage(position);
//        });
//        this.showDialog(dialogBuilder);
//    }
//
//    @LiveDataMatch(tag = "上传文件成功")
//    public synchronized void onFileSuccess(File tag, String url) {
//
//        this.mBillImageSelectView.onUpLoadFileSuccess(tag.getAbsolutePath(), url);
//    }
//
//    @LiveDataMatch(tag = "上传文件2成功")
//    public synchronized void onFileSuccess2(File tag, String url) {
//
//        this.mBillImageSelectView.onUpdateImage2Status(tag.getAbsolutePath(), url);
//    }
//
//    @LiveDataMatch(tag = "上传文件失败")
//    public void onFileFailure(File tag, String error) {
//        this.showToast(error);
//        this.mBillImageSelectView.onUpLoadFileError(tag.getAbsolutePath());
//    }
//
//    @LiveDataMatch(tag = "定位")
//    public void onAdressSuccess(String address, String lat, String lon) {
//        this.address = address;
//        this.latLon = lat + "," + lon;
//    }
//
//    public ShipMentFileFragment setWaterMarkFlag(String waterMarkFlag) {
//        this.waterMarkFlag = waterMarkFlag;
//        return this;
//    }
//
//}
