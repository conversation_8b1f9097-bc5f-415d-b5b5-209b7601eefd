package com.zczy.plugin.order.stevedore.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.model.ESteveDoreDeatil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 功能描述:详情
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ItemDetailAdapter extends BaseAdapter {
    Context context;

    List<Item> list;

    public ItemDetailAdapter(Context context, ESteveDoreDeatil doreDeatil) {

        super ();
        this.context = context;
        this.list = new ArrayList<> (7);
        this.list.add (new Item ("运单号：",doreDeatil.getOrderId ()));
        this.list.add (new Item ("车牌号：",doreDeatil.getPlateNo ()));
        this.list.add (new Item ("提交时间： ",doreDeatil.getCreatedTime ()));

        //状态:1:未审核,2:审核通过,3:已驳回'
        if (TextUtils.equals ("1", doreDeatil.getState ())) {
            this.list.add (new Item ("提交状态","未审核"));
        } else if (TextUtils.equals ("2", doreDeatil.getState ())) {
            this.list.add (new Item ("提交状态","审核通过"));
        } else if (TextUtils.equals ("3", doreDeatil.getState ())) {
            this.list.add (new Item ("提交状态","已驳回"));
        } else {
            this.list.add (new Item ("提交状态",doreDeatil.getState ()));
        }
        this.list.add (new Item ("装卸费金额：",doreDeatil.getMoney ()));
        this.list.add (new Item ("备注：",doreDeatil.getRemark ()));
        this.list.add (new Item ("审核意见：",doreDeatil.getExamineOpinion ()));
    }

    @Override
    public int getCount() {

        return this.list.size ();
    }

    @Override
    public Item getItem(int position) {

        return this.list.get (position);
    }

    @Override
    public long getItemId(int position) {

        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        convertView = View.inflate (this.context, R.layout.order_stevedore_detail_adapter, null);
        TextView tv_left = convertView.findViewById (R.id.tv_left);
        TextView tv_info = convertView.findViewById (R.id.tv_info);
        tv_left.setText (getItem (position).title);
        tv_info.setText (getItem (position).value);
        return convertView;
    }

    class Item{

        Item(String title,String value){
            this.title = title;
            this.value = value;
        }
        String title;
        String value;
    }
}
