package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：查询成交用户是否支付接口
 * 时间：2025/3/24 11:15
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=79922116
 * 对接：陈泰
 * */
class ReqQueryProcessOrderPay(
    var consignorUserId: String? = null, // 货主用户id
    var sourceId: String? = null, // 货源ID
) : BaseNewRequest<BaseRsp<RspQueryProcessOrderPay>>("oms-app/carrier/common/queryProcessOrderPay") {
}

data class RspQueryProcessOrderPay(
    var payStatus: String? = "", //  是否支付成功 0 否 1 是
) : ResultData()
