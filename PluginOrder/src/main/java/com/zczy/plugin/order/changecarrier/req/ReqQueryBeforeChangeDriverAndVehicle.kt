package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：变更人和车的时候查询车辆和司机
 * 时间：2025/4/29 8:57
 * @author：王家辉
 * wiki：
 * 对接：黄琎
 * */
class ReqQueryBeforeChangeDriverAndVehicle(
    var orderId: String? = null, //订单号
) : BaseNewRequest<BaseRsp<RspQueryBeforeChangeDriverAndVehicle>>("oms-app/orderChange/queryBeforeChangeDriverAndVehicle")

data class RspQueryBeforeChangeDriverAndVehicle(
    var driverUserId: String? = null, // 司机id
    var driverMobile: String? = null, //司机联系电话
    var driverUserName: String? = null, //司机姓名
    var vehicleId: String? = null, //承运车辆id
    var plateNumber: String? = null, //承运车辆车牌号
) : ResultData()