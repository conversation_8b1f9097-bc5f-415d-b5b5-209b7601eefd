package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:省货物平台系统2.0 【清缓存运单和暂停全部运单】
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2020/10/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import androidx.work.WorkManager;

import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnResultListener;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.supervise.HookLocationOpenApi;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;

public class PauseAction extends BaseActionServer implements  Function<PauseAction, Boolean> {

    @Override
    public void start() {
        //授权
         RetrofitManager.executeSigin(Observable.just(PauseAction.this).map(PauseAction.this), new EmptyResult<>());
    }

    @Override
    public Boolean apply(@NonNull PauseAction pauseAction) throws Exception {

        HookLocationOpenApi hook =   new HookLocationOpenApi(AppCacheManager.getApplication());

        List<ShippingNoteInfo> list = hook.getShippingNoteList();
        if (list != null && !list.isEmpty()) {
            for (ShippingNoteInfo infoObj : list) {
                //取消定时任务
                WorkManager.getInstance().cancelUniqueWork(infoObj.getShippingNoteNumber());
            }
            hook.deleteShippingNotes(list);
        }
        return true;
    }

//    class OnAuthListener2 implements OnAuthListener,OnResultListener {
//
//        ESDKInfoObj sdkInfoObj;
//
//        OnAuthListener2(ESDKInfoObj infoObj) {
//            this.sdkInfoObj = infoObj;
//        }
//
//        @Override
//        public void onAuthFailure(String code, String msg) {
//            out(String.format("省货物平台系统2.0=>暂停运单[授权失败] sdkInfoObj:%s,code:%s,msg:%s", sdkInfoObj, code, msg));
//        }
//
//        @Override
//        public void onAuthSuccess(List<ShippingNoteInfo> list) {
//            out(String.format("省货物平台系统2.0=Pause=>暂停运单[授权成功] sdkInfoObj:%s,size:%s", sdkInfoObj, (list != null ? list.size() : 0)));
//            ShippingNoteInfo[] shippingNoteNumbers = putData(sdkInfoObj);
//            //运单列表中所有运单标记为暂停状态
//            LocationOpenApi.pause(AppCacheManager.getApplication(), sdkInfoObj.getVehicleNumber(), sdkInfoObj.getDriverName(), "", shippingNoteNumbers, OnAuthListener2.this);
//        }
//
//        @Override
//        public void onFailure(String code, String msg) {
//            out(String.format("省货物平台系统2.0=>暂停运单[失败] sdkInfoObj:%s,code:%s,msg:%s", sdkInfoObj,code,msg));
//        }
//
//        @Override
//        public void onSuccess(List<ShippingNoteInfo> list) {
//            out(String.format("省货物平台系统2.0=Pause=>暂停运单[成功] sdkInfoObj:%s,size:%s", sdkInfoObj,(list!=null?list.size():0)));
//        }
//    }


}
