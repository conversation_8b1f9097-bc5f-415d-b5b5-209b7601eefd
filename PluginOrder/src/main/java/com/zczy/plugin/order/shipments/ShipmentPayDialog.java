//package com.zczy.plugin.order.shipments;
//
//import android.os.Bundle;
//import androidx.annotation.Nullable;
//import android.view.View;
//
//import com.sfh.lib.AppCacheManager;
//import com.zczy.comm.ui.BaseDialog;
//import com.zczy.plugin.order.R;
//
//
///**
// * 油品预付更优惠
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// */
//public class ShipmentPayDialog extends BaseDialog implements View.OnClickListener {
//
//
//    @Override
//    protected String getDialogTag() {
//        return "ShipmentPayDialog";
//    }
//
//    @Override
//    protected int getDialogLayout() {
//        return R.layout.order_shipments_pay_dialog;
//    }
//
//
//    @Override
//    protected void bindView(View view, @Nullable Bundle bundle) {
//        view.findViewById(R.id.tv_colse).setOnClickListener(this);
//        view.findViewById(R.id.tv_ok).setOnClickListener(this);
//        AppCacheManager.putCache("ShipmentPayDialog_Key",false);
//    }
//
//    @Override
//    public void onClick(View view) {
//        dismissAllowingStateLoss();
//    }
//}
