package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 *   验证用户是否可以创建诚意金
 */
class ReqVerifyEarenstCanConfig(
) : BaseOrderRequest<BaseRsp<RspVerifyEarenstCanConfig>>("/pps-app/earnestManage/verifyEarnestCanConfig")

data class RspVerifyEarenstCanConfig(
        var type: String = "",
        var earnestMoney: String = ""
) : ResultData()