package com.zczy.plugin.order.designation;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.rx.IResultSuccess;
import com.sfh.lib.rx.ui.UtilRxView;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.pluginserver.AOrderServer;
import com.zczy.comm.utils.ResUtil;
import com.zczy.comm.utils.ex.StringUtil;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.changecarrier.dialog.OrderCarrierChooseCarDialog;
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck;
import com.zczy.plugin.order.designation.req.RspAssignVehicleTransportCheck;
import com.zczy.plugin.order.source.pick.OrderCarListActivityV1;
import com.zczy.plugin.order.source.pick.cyr.BossNoCarToastDialog;
import com.zczy.plugin.order.source.pick.entity.EVehicle;
import com.zczy.plugin.order.source.pick.entity.EVehiclePageList;
import com.zczy.plugin.order.source.pick.fragment.OnOverLoadListener;
import com.zczy.plugin.order.source.pick.fragment.OnUserAndVehicleStates;
import com.zczy.plugin.order.source.pick.model.OrderPickCarModel;
import com.zczy.plugin.order.source.pick.model.OrderPickDriverModel;
import com.zczy.plugin.order.source.pick.model.request.ReqCheckDelistOrderDocumentExpire;
import com.zczy.plugin.order.source.pick.model.request.RspCheckDelistOrderDocumentExpire;

import java.math.BigDecimal;
import java.util.Objects;

import io.reactivex.disposables.Disposable;

/**
 * 功能描述:指派选车辆查询承运方(个体司机，车老板，物流企业)名下车辆列表
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/1/22
 */
public class SpecifiedOrderCarFragmnet extends AbstractLifecycleFragment<OrderPickCarModel> {

    public interface OnEnableSlect {
        default boolean isOK() {
            return true;
        }
    }

    private TextView mTvLeft;

    private TextView mTvRight;
    /**
     * 超载提示
     */
    private TextView tvOverLoad;

    private TextView tvTips;

    private EVehicle selectEVehicle;
    private View input_select_person;
    private OnOverLoadListener mCallback;
    private String orderId;
    private BossNoCarToastDialog bossNoCarToastDialog;
    private OnUserAndVehicleStates onUserAndVehicleStates;
    private OnEnableSlect onEnableSlect = new OnEnableSlect() {
    };

    @Override
    public int getLayout() {

        return R.layout.order_picking_specified_car_fragment;
    }

    @Override
    public void initData(View view) {
        this.input_select_person = view.findViewById(R.id.input_select_person);
        this.mTvLeft = (TextView) view.findViewById(R.id.tv_left);
        this.mTvRight = (TextView) view.findViewById(R.id.tv_right);
        this.tvOverLoad = (TextView) view.findViewById(R.id.tv_over_load);
        this.tvTips = (TextView) view.findViewById(R.id.tv_tips);
        this.mTvLeft.setText("选择我的车辆");
        this.mTvRight.setHint("请选择");
        Disposable cilck = UtilRxView.clicks(mTvRight, 1000, new IResultSuccess<Object>() {
            @Override
            public void onSuccess(Object o) throws Exception {
                if (onEnableSlect.isOK()) {
                    getViewModel(OrderPickCarModel.class).queryVehicle(orderId, true);
                }
            }
        });
        this.putDisposable(cilck);
    }


    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }


    /**
     * 注释: 设置选中车辆回调
     * 时间: 2024/9/14 0014 11:36
     *
     * @param mCallback
     * <AUTHOR>
     */
    public void setSelectCarCallback(OnOverLoadListener mCallback) {
        this.mCallback = mCallback;
    }

    public void queryCarrierBossRelationList(String orderId) {
        this.setOrderId(orderId);
        // 承运人
        ELogin login = CommServer.getUserServer().getLogin();
        IRelation relation = login.getRelation();
        if (relation.isCarrier() && !login.relevanceBoss()) {
            //承运人 && 不关联车老板, 承运人只有一辆车需求
            this.getViewModel(OrderPickCarModel.class).queryVehicle(orderId, false);
        }
    }


    @LiveDataMatch(tag = "选择车辆列表成功")
    public void onQueryVehicleSuccess(boolean showSelectView, EVehiclePageList<EVehicle> data) {

        ELogin login = CommServer.getUserServer().getLogin();
        IRelation relation = login.getRelation();

        if (data == null || data.getRootArray() == null || data.getRootArray().isEmpty()) {
            if (relation.isCarrier() && login.relevanceBoss()) {
                //承运人 && 关联车老板, 车老板名下无可用车辆
                if (bossNoCarToastDialog == null) {
                    bossNoCarToastDialog = new BossNoCarToastDialog(getActivity());
                }
                if (!bossNoCarToastDialog.isShowing()) {
                    bossNoCarToastDialog.show(requireActivity());
                }

            } else {
                DialogBuilder dialogBuilder = new DialogBuilder();
                dialogBuilder.setTitle("提示");
                dialogBuilder.setMessage("无可用车辆，请先添加车辆！");
                dialogBuilder.setCancelText("关闭");
                dialogBuilder.setGravity(Gravity.CENTER);
                dialogBuilder.setOKTextListener("添加车辆", (dialog, which) -> {
                    dialog.dismiss();
                    AOrderServer server = AOrderServer.getPluginServer();
                    if (server != null) {
                        server.gotoVehicleManagement(getActivity());
                    }
                });
                showDialog(dialogBuilder);
            }
            return;
        }
        if (relation.isCarrier() && !login.relevanceBoss() && data.getRootArray().size() == 1) {
            //承运人 && 只有一辆车
            EVehicle vehicle = data.getRootArray().get(0);
            if (!vehicle.isScrapOrFrozen()) {
                //非冻结，到期车辆
                onSelectCarSuccess(true, data.getRootArray().get(0));
                return;
            }
        }

        if (showSelectView) {

            if (data.getRootArray().size() > 9) {
                OrderCarListActivityV1.start(SpecifiedOrderCarFragmnet.this, TextUtils.isEmpty(orderId) ? "" : orderId, TextUtils.isEmpty(data.getBossCarrierDelistState()) ? "" : data.getBossCarrierDelistState(), 0x01);
            } else {

                OrderCarrierChooseCarDialog
                        .instance(data.getRootArray(), true, true)
                        .setNoCheck(vehicle -> vehicle.isScrapOrFrozen()).setNoCheckTxt(vehicle -> vehicle.getTextScrapOrFrozen()).setSelectItem(this.selectEVehicle)
                        .setTitle("选择我的车辆")
                        .setChooseListener((s, dialog) -> {
                            dialog.dismiss();
                            onSelectCarSuccess(true, s);
                            return null;
                        })
                        .setToastText(TextUtils.equals("1", data.getBossCarrierDelistState()) ? "您本月交易车辆已超出平台当前角色管控要求，请选择可选车辆，如有疑问可咨询平台客服。" : "")
                        .setFlatShowLogo(eVehicle -> showExamineTypeIconV1(eVehicle))
                        .show(this);
            }
        }
    }

    private boolean showExamineTypeIconV1(EVehicle data) {
        ELogin login = CommServer.getUserServer().getLogin();
        if (login != null && !(TextUtils.equals(data.getExamineType(), "1") && TextUtils.equals(data.getExamineNewType(), "4"))) {
            IRelation relation = login.getRelation();
            //車老闆 or 关联车老板
            if (relation.isBoss() || login.relevanceBoss()) {
                return true;
            }
        }
        return false;
    }

    /***
     * 选择车辆
     * @param data
     */
    private void onSelectCarSuccess(boolean postEvent, EVehicle data) {
        if (data != null) {
            ReqAssignVehicleTransportCheck req = new ReqAssignVehicleTransportCheck();
            req.setPlateNumber(data.getPlateNumber());
            getViewModel(OrderPickCarModel.class).assignVehicleTransportCheck(req, data);
        }
    }

    @LiveDataMatch
    public void queryCarrierOrderCarpoolingSuccess(RspCheckDelistOrderDocumentExpire data) {
        if (StringUtil.isTrue(data.vehicleDocumentExpire)) {
            //用户证件过期
            DialogBuilder dialogBuilder = new DialogBuilder();
            dialogBuilder.setTitle("提示");
            dialogBuilder.setMessage(data.getResultMsg());
            dialogBuilder.setGravity(Gravity.CENTER);
            dialogBuilder.setCancelable(false);
            dialogBuilder.setHideCancel(true);
            dialogBuilder.setOKTextListener("我知道了", (dialog, which) -> {
                dialog.dismiss();
            });
            showDialog(dialogBuilder);
        }
    }

    /**
     * 注释: 获取选中车辆信息
     * 时间: 2024/9/14 0014 11:46
     *
     * @return
     * <AUTHOR>
     */
    public EVehicle getVehicleInfo() {
        if (this.selectEVehicle == null) {
            showToast("请选择车辆");
            return null;
        }
        return this.selectEVehicle;
    }

    /**
     * 进行重量校验
     *
     * @param actualWeight 货物吨位
     */
    @SuppressLint({"SetTextI18n"})
    public void setTvOverLoad(String actualWeight) {
        if (TextUtils.isEmpty(actualWeight) || selectEVehicle == null) {
            return;
        }
        String vehicleLoad = selectEVehicle.getVehicleLoad();
        if (TextUtils.isEmpty(vehicleLoad)) {
            return;
        }
        Double aDouble = Double.valueOf(actualWeight);
        Double bDouble = Double.valueOf(vehicleLoad);
        double cDouble = aDouble - bDouble;
        if (cDouble > 0) {
            this.tvOverLoad.setVisibility(View.VISIBLE);
            long divide = divide(cDouble, bDouble, 2);
            if (divide > 30) {
                this.tvOverLoad.setText("超载" + divide + "%");
                this.tvOverLoad.setTextColor(ResUtil.getResColor(R.color.color_fb4040));
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff4f8_rtg);
            } else {
                this.tvOverLoad.setText("超载" + divide + "%");
                this.tvOverLoad.setTextColor(ResUtil.getResColor(R.color.color_fb6b40));
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff3f1_rtg);
            }
        } else {
            this.tvOverLoad.setVisibility(View.GONE);
        }

    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    public long divide(double a, double b, int scale) {
        BigDecimal bd1 = new BigDecimal(Double.toString(a));
        BigDecimal bd2 = new BigDecimal(Double.toString(b));
        double bd3 = bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        return Math.round(bd3 * 100.00);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            switch (requestCode) {
                case 0x01:
                    String stringExtra = data.getStringExtra(OrderCarListActivityV1.EXTRA_SELECT_ITEM);
                    EVehicle vehicle = JsonUtil.toJsonObject(stringExtra, EVehicle.class);
                    onSelectCarSuccess(true, vehicle);
                    break;
            }
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        // 这是为了保证Activity容器实现了用以回调的接口。如果没有，它会抛出一个异常。
        try {
            mCallback = (OnOverLoadListener) context;
        } catch (Exception e) {
            Log.e("SelectCar", e.getMessage());
        }
    }

    /**
     * 零担-存在待装货的零担拼车单时，车牌号默认带出，切不可选择
     *
     * @param plateNumber
     */
    public void setPlantNumAndUnClick(String plateNumber) {
        mTvRight.setText(plateNumber);
        mTvRight.setOnClickListener(null);
    }


    public void setOnUserAndVehicleStates(OnUserAndVehicleStates onUserAndVehicleStates) {
        this.onUserAndVehicleStates = onUserAndVehicleStates;
    }

    /**
     * 清空数据
     */
    public void onClear() {

        this.selectEVehicle = null;
        this.mTvRight.setText("");

    }

    public void setOnEnableSlect(OnEnableSlect onEnableSlect) {
        this.onEnableSlect = onEnableSlect;
    }

    @LiveDataMatch
    public void onAssignVehicleTransportCheckSuccess(BaseRsp<RspAssignVehicleTransportCheck> it, EVehicle data) {
        if (TextUtils.equals("1", it.getData().isTransportFlag())) {
            DialogBuilder builder = new DialogBuilder();
            builder.setTitle("提示");
            builder.setMessage(it.getMsg());
            builder.setHideCancel(true);
            builder.setOKTextListener("我知道了", (dialog, which) -> {
                dialog.dismiss();
                onSelectCar(data);
            });
            showDialog(builder);
        } else {
            onSelectCar(data);
        }
    }

    @LiveDataMatch
    public void onAssignVehicleTransportCheckFail(BaseRsp<RspAssignVehicleTransportCheck> it, EVehicle data) {
        onSelectCar(data);
    }

    public void onSelectCar(EVehicle data) {
        if (this.showExamineTypeIconV1(data)) {
            DialogBuilder builder = new DialogBuilder();
            builder.setMessage("您选择的车辆还未审核通过，请尽快提交审核，以免影响运费结算，谢谢！");
            builder.setTitle("友好提醒");
            builder.setHideCancel(true);
            builder.setOKText("知道了");
            this.showDialog(builder);
        }
        this.selectEVehicle = data;
        this.mTvRight.setText(data.getPlateNumber());

        if (mCallback != null) {
            //通知Activity车辆信息发生变化
            mCallback.onOverLoadListener(data);
        }

        if (onUserAndVehicleStates != null) {
            onUserAndVehicleStates.onSelectCar(data);
        }
        ReqCheckDelistOrderDocumentExpire req = new ReqCheckDelistOrderDocumentExpire();
        req.setVehicleId(data.getVehicleId());
        getViewModel(OrderPickDriverModel.class).queryCarrierOrderCarpooling(req);
    }
}
