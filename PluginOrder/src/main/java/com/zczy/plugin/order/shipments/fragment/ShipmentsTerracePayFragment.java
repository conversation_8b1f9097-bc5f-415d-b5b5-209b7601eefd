//package com.zczy.plugin.order.shipments.fragment;
//
//import android.app.Activity;
//import android.content.Intent;
//import android.text.Html;
//import android.text.TextUtils;
//import android.view.Gravity;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.RadioGroup;
//import android.widget.TextView;
//
//import androidx.constraintlayout.widget.ConstraintLayout;
//
//import com.sfh.lib.mvvm.annotation.LiveDataMatch;
//import com.sfh.lib.ui.dialog.DialogBuilder;
//import com.zczy.comm.pluginserver.ADriverServer;
//import com.zczy.comm.utils.NumUtil;
//import com.zczy.comm.utils.SpannableHepler;
//import com.zczy.comm.widget.dialog.MenuDialogV1;
//import com.zczy.lib_zstatistics.sdk.ZStatistics;
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.ShipmentsCouponActivity;
//import com.zczy.plugin.order.shipments.ShipmentsTerracePayDialog;
//import com.zczy.plugin.order.shipments.ToastDialog;
//import com.zczy.plugin.order.shipments.entity.EAdvanceInfo;
//import com.zczy.plugin.order.shipments.entity.EAdvanceServiceMoney;
//import com.zczy.plugin.order.shipments.entity.EAdvanceType;
//import com.zczy.plugin.order.shipments.entity.EventOilCardRatio;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.entity.ShipmentsDiscountCard;
//import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
//import com.zczy.plugin.order.shipments.model.ShipmentsTerracePaymentModel;
//import com.zczy.plugin.order.shipments.view.SelectTerracePayView;
//import com.zczy.plugin.order.shipments.view.ShipmentsSelectCradView;
//import com.zczy.plugin.order.source.pick.entity.EPickUserCoupon;
//
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * 功能描述:[平台预付]
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public class ShipmentsTerracePayFragment extends ShipmentsBaseFragment<ShipmentsTerracePaymentModel> {
//
//    public static ShipmentsTerracePayFragment newFragment() {
//
//        return new ShipmentsTerracePayFragment();
//    }
//
//    /***
//     * 打开关闭预付服务
//     */
//    public interface OpenCloseListenter {
//
//        void openColseFragment(boolean openColse);
//    }
//
//
//    //预付布局
//    private View cl_play_ui;
//    private RadioGroup rbOpen;
//
//    //预付方式
//    private SelectTerracePayView selectTerracePayView;
//    //优惠券
//    private View ll_coupon;
//    private View ll_coupon_child;
//    private TextView tvPacket;
//    private TextView tv_new_money;
//    private ImageView tv_question_mark;
//
//    private TextView tv_paly_prepay_ratio;
//
//    //预付选择油气比例UI
//    private View cy_play_type_radio;
//    //油气比例
//    private View tv_oil_ratio_title;
//    private TextView tv_oil_ratio_value;
//    //现金比例
//    private View tv_ratio_money_title;
//    private TextView tv_ratio_money_value;
//
//    //合作加油
//    private ConstraintLayout clOil;
//    // 奖励
//    private TextView tv_award_tips1;
//    private TextView tv_award_tips2;
//
//    //智运卡
//    private ShipmentsSelectCradView selectCradView;
//    private TextView tv_prepay_tips;
//
//    private ShipmentsEGoodInfo shipmentsEGoodInfo;
//    private OpenCloseListenter openCloseListenter;
//    private String advanceState;
//    private EAdvanceInfo advanceInfo;
//    private String goodsSource;
//    private int senceAdvance;
//
//    /*** 预付服务费*/
//    private String serverMoney;
//    private String advanceWay;
//    //油比例
//    private String mOilRatio;
//
//    // //2.确定优惠方式初始化
//    private ShowDiscountHelper showDiscountHelper = new ShowDiscountHelper();
//
//    public void setData(int senceAdvance, String advanceState, ShipmentsEGoodInfo data, EAdvanceInfo advanceInfo, OpenCloseListenter listenter) {
//        this.senceAdvance = senceAdvance;
//        this.advanceInfo = advanceInfo;
//        this.advanceState = advanceState;
//        this.shipmentsEGoodInfo = data;
//        this.openCloseListenter = listenter;
//    }
//
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_terrace_payment_check_fragment;
//    }
//
//
//    @Override
//    public void initData(View view) {
//
//        this.selectCradView = view.findViewById(R.id.shipments_select_cradView);
//        this.selectCradView.findViewByIdLchy(view);
//
//        this.selectTerracePayView = view.findViewById(R.id.ll_play_type);
//        this.clOil = view.findViewById(R.id.clOil);
//        //预付布局
//        this.cl_play_ui = view.findViewById(R.id.cl_play_ui);
//        this.ll_coupon = view.findViewById(R.id.ll_coupon);
//        this.ll_coupon_child = view.findViewById(R.id.ll_coupon_child);
//        this.tv_new_money = view.findViewById(R.id.tv_new_money);
//        this.rbOpen = view.findViewById(R.id.rb_list);
//
//        this.tv_award_tips1 = view.findViewById(R.id.tv_award_tips1);
//        this.tv_award_tips2 = view.findViewById(R.id.tv_award_tips2);
//
//        this.initCommTxt(view);
//        this.setOilMoneyView(view);
//        this.initView(view);
//    }
//
//    private void initCommTxt(View view) {
//        //无业务，提示信息
//        tv_prepay_tips = view.findViewById(R.id.tv_prepay_tips);
//        tv_prepay_tips.setText(
//                new SpannableHepler()
//                        .append(new SpannableHepler.Txt("服务须知：", "#F65547"))
//                        .append("依据法律规定，各类骗取预付款的行为涉嫌诈骗，需承担相应的法律责任").builder());
//
//        TextView tvOil1 = view.findViewById(R.id.tvOil1);
//        tvOil1.setText(new SpannableHepler("本单平台为您规划了")
//                .append(new SpannableHepler.Txt("3", "#ff602e"))
//                .append("条线路，经过若干个合作油站。").builder());
//        //查看路线
//        view.findViewById(R.id.tvLook).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //查看路线
//                getViewModel(ShipmentsTerracePaymentModel.class).queryOrderCoordinate(shipmentsEGoodInfo.getOrderId(), data -> {
//                    ADriverServer pluginServer = ADriverServer.getPluginServer();
//                    if (pluginServer != null) {
//                        pluginServer.openOilMapDetailActivity(getContext(), data.point1(), data.point2(), "");
//                    }
//                });
//            }
//        });
//
//        //优惠券
//        this.tvPacket = view.findViewById(R.id.tv_packet);
//        this.tvPacket.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //选择优惠券
//                ZStatistics.onViewClick(getContext(), "send_detail_PrepayCoupon");
//                String userCouponIds = showDiscountHelper.getUserCouponIds();
//                ShipmentsCouponActivity.startContentUI(ShipmentsTerracePayFragment.this, shipmentsEGoodInfo.getOrderId(), shipmentsEGoodInfo.getPbCarrierMoney(), serverMoney, userCouponIds, 1000);
//            }
//        });
//        tv_question_mark = view.findViewById(R.id.tv_question_mark);
//        tv_question_mark.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                ToastDialog dialog = new ToastDialog();
//                dialog.setTitle("预付服务费");
//                dialog.setCon("对预付服务费优惠，若预付审核时调整预付比例，导致服务费变更，按提交时优惠金额抵扣。");
//                dialog.setBtnText("我知道了");
//                dialog.show(getActivity());
//            }
//        });
//    }
//
//    private void initView(View view) {
//        //java.lang.NullPointerException: Attempt to invoke virtual method 'java.lang.String com.zczy.plugin.order.shipments.entity.EAdvanceInfo.getAdvanceType()' on a null object reference
//        //at com.zczy.plugin.order.shipments.fragment.ShipmentsTerracePayFragment.initData(ShipmentsTerracePayFragment.java:159)
//        if (advanceInfo == null || shipmentsEGoodInfo == null) {
//            return;
//        }
//
//        // 零担无预付
//        if (TextUtils.equals("2", this.shipmentsEGoodInfo.getGoodsSource())) {
//            tv_prepay_tips.setVisibility(View.GONE);
//            view.findViewById(R.id.cl_run).setVisibility(View.GONE);
//        }
//        if (this.advanceInfo.isAdvance()) {
//
//            //1.预付服务费初始化
//            this.serverMoney = advanceInfo.getNewAdvanceServiceMoney();
//            this.advanceWay = shipmentsEGoodInfo.getAdvanceWay();
//
//            if (TextUtils.equals("2", advanceInfo.getAdvanceType())) {
//
//                //个体司机非指定订单,显示预付相关内容
//                //1.智运卡
//                this.selectCradView.initData(shipmentsEGoodInfo, new ShipmentsSelectCradView.OnItemSelect() {
//                    @Override
//                    public void onItem(ShipmentsSelectCradView view, ShipmentsDiscountCard card) {
//                        //点击取消买卡/买卡,刷新优惠方式
//                        showDiscountHelper.showTxt(selectTerracePayView, view);
//                    }
//
//                    @Override
//                    public String getServerMoney() {
//                        //实时的预付费
//                        return serverMoney;
//                    }
//                });
//                this.tv_paly_prepay_ratio.setVisibility(View.VISIBLE);
//
//                //2.预付方式初始化
//                this.selectTerracePayView.initData(advanceInfo, shipmentsEGoodInfo, senceAdvance, (payView, init, advanceType) -> {
//                    //切换预付方式回调
//                    advanceWay = advanceType.getType();
//                    setSelectAdvanceType(payView, advanceWay);
//
//                });
//                if (senceAdvance == 1) {
//                    //重新预付申请场景进入:不显示预付方式
//                    this.selectTerracePayView.setVisibility(View.GONE);
//                } else {
//                    this.selectTerracePayView.setVisibility(View.VISIBLE);
//                }
//
//
//                //ZCZY-12277 【加急】2023惊蛰（3.6）活动趣味转盘
//                selectTerracePayView.showInsects(advanceInfo);
//
//                if (TextUtils.equals("2", this.advanceState)) {
//                    //重新发货获取已用优惠券信息
//                    this.getViewModel(ShipmentsTerracePaymentModel.class).queryDeliverOrderCoupon(shipmentsEGoodInfo.getOrderId());
//                }
//
//                this.getViewModel(ShipmentsTerracePaymentModel.class).queryCarrierDiscounts(this.shipmentsEGoodInfo.getOrderId(), this.serverMoney, this.advanceWay);
//
//            } else {
//                this.selectTerracePayView.setVisibility(View.GONE);
//                this.tv_paly_prepay_ratio.setVisibility(View.GONE);
//            }
//
//            if (senceAdvance == 1 || senceAdvance == 2 || TextUtils.equals("2", this.shipmentsEGoodInfo.getGoodsSource())) {
//                //强制需要预付 ，不可选择（需要/不需要） || 零担货不展示
//                view.findViewById(R.id.cl_run).setVisibility(View.GONE);
//            } else {
//                //正常预付场景=>可选（需要/不需要）
//                view.findViewById(R.id.cl_run).setVisibility(View.VISIBLE);
//            }
//            //选中需要预付
//            this.rbOpen.setOnCheckedChangeListener(palyCheckedChangeListener);
//            this.rbOpen.check(R.id.rb_yeas);
//
//        } else {
//
//            //预付不可使用
//            view.findViewById(R.id.rb_yeas).setEnabled(false);
//            view.findViewById(R.id.rb_no).setEnabled(false);
//            this.rbOpen.check(R.id.rb_no);
//            this.showPayEnableCheck(false);
//
//            if (!TextUtils.isEmpty(advanceInfo.getNoAdvanceReason()) && !TextUtils.equals("1149", advanceInfo.getNoAdvanceCode())) {
//                //不能预付原因
//                this.rbOpen.setVisibility(View.GONE);
//
//                TextView tv_no_prepay_msg = view.findViewById(R.id.tv_no_prepay_msg);
//                tv_no_prepay_msg.setText(advanceInfo.getNoAdvanceReason());
//                TextView tv_no_prepay_on = view.findViewById(R.id.tv_no_prepay_on);
//                tv_no_prepay_on.setVisibility(View.VISIBLE);
//                tv_no_prepay_on.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        tv_no_prepay_msg.setVisibility(tv_no_prepay_msg.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
//                    }
//                });
//            }
//        }
//    }
//
//    private int mLastCheckedId;
//    private RadioGroup.OnCheckedChangeListener palyCheckedChangeListener = new RadioGroup.OnCheckedChangeListener() {
//        @Override
//        public void onCheckedChanged(RadioGroup group, int checkedId) {
//            if (mLastCheckedId == checkedId) {
//                //防止二次调用
//                return;
//            }
//            mLastCheckedId = checkedId;
//            ZStatistics.onViewClick(getActivity(), "send_detail_prepay");
//            if (checkedId == R.id.rb_yeas) {
//                // 需要预付
//                showPayEnableCheck(true);
//            } else {
//                // 不需要预付
//                String msg = "已有" + advanceInfo.getAdvanceOrderDriverCount() + "位司机使用预付，是否确定不使用？";
//                new ShipmentsTerracePayDialog().setListener(new ShipmentsTerracePayDialog.Listener() {
//                    @Override
//                    public void onListener(String clickType) {
//                        if ("1".equals(clickType)) {
//                            //放弃使用
//                            showPayEnableCheck(false);
//                            ZStatistics.onViewClick(ShipmentsTerracePayFragment.this, "tv_prepay_no");
//                        } else {
//                            // 继续使用, 之前已经打开过
//                            rbOpen.check(R.id.rb_yeas);
//                            ZStatistics.onViewClick(ShipmentsTerracePayFragment.this, "tv_prepay_yes");
//                        }
//                    }
//                }, msg, advanceWay, carrierDiscountsMsg).show(ShipmentsTerracePayFragment.this);
//
////                DialogBuilder alertTemple = new DialogBuilder();
////                alertTemple.setCancelable(false);
////                alertTemple.setTitle("提示");
////                alertTemple.setMessage(msg);
////                alertTemple.setCancelText("放弃使用");
////                alertTemple.setOKText("继续使用");
////                alertTemple.setCancelListener((DialogBuilder.DialogInterface dialog, int which) -> {
////                    //放弃使用
////                    dialog.dismiss();
////                    showPayEnableCheck(false);
////                    ZStatistics.onViewClick(ShipmentsTerracePayFragment.this, "tv_prepay_no");
////                });
////                alertTemple.setOkListener((DialogBuilder.DialogInterface dialog, int which) -> {
////                    dialog.dismiss();
////                    // 继续使用, 之前已经打开过
////                    rbOpen.check(R.id.rb_yeas);
////                    ZStatistics.onViewClick(ShipmentsTerracePayFragment.this, "tv_prepay_yes");
////
////                });
////                showDialog(alertTemple);
//            }
//        }
//    };
//
//    private void showPayEnableCheck(boolean checked) {
//
//        if (TextUtils.equals("2", advanceInfo.getAdvanceType())) {
//            //打开关闭预付UI =>预付UI(预付方式-优惠券-协议)
//            this.cl_play_ui.setVisibility(checked ? View.VISIBLE : View.GONE);
//            this.tv_paly_prepay_ratio.setVisibility(checked ? View.VISIBLE : View.GONE);
//
//            //使用智运卡优惠，选择买卡可以操作
//            if (TextUtils.equals("2", advanceWay)) {
//                //2:预付油品不使用
//                this.selectCradView.userCardView(false);
//            } else {
//                this.selectCradView.userCardView(checked);
//            }
//        } else {
//            this.cl_play_ui.setVisibility(View.GONE);
//            this.tv_paly_prepay_ratio.setVisibility(View.GONE);
//        }
//
//        //打开预付UI
//        this.openCloseListenter.openColseFragment(checked);
//    }
//
//    /***
//     * 切换预付方式
//     */
//    private void setSelectAdvanceType(SelectTerracePayView payView, String advanceWay) {
//
//        //通知一下，预付协议UI
//        this.notifyArgess();
//
//        if (TextUtils.equals("1", advanceInfo.getOilSelectBySelf())) {
//            //油气比例选择
//            cy_play_type_radio.setVisibility(View.VISIBLE);
//            this.showPlayTypeRadioUI(advanceWay);
//        } else {
//            cy_play_type_radio.setVisibility(View.GONE);
//        }
//
//        switch (advanceWay) {
//            case "1": {
//                //1:预付现金
//                this.clOil.setVisibility(View.GONE);
//                this.ll_coupon.setVisibility(View.VISIBLE);
//
//                this.tv_paly_prepay_ratio.setText("选择预付，装货后将提前收到一部分现金");
//                //预估服务费
//                this.serverMoney = this.advanceInfo.getNewAdvanceServiceMoney();
//                //优惠信息
//                this.showDiscountHelper.showTxt(payView, selectCradView);
//
//                if (!this.showDiscountHelper.isDefalutCouponDiscount()) {
//                    //不是默认优惠券模式， 查询可以优惠券可以使用数量
//                    this.getViewModel(ShipmentsTerracePaymentModel.class).getUseCouponSize(this.shipmentsEGoodInfo.getOrderId(), this.shipmentsEGoodInfo.getPbCarrierMoney(), this.serverMoney);
//                }
//                break;
//            }
//
//            case "2": {
//                //2:预付油品
//                this.clOil.setVisibility(View.VISIBLE);
//                this.ll_coupon.setVisibility(View.GONE);
//                this.tv_paly_prepay_ratio.setText("选择预付，装货后将提前收到一部分油费");
//                //优惠信息(可能默认就是油品预付，刷新一下优惠显示)
//                this.showDiscountHelper.showTxt(payView, selectCradView);
//
//                if (TextUtils.equals("1", advanceInfo.getOilSelectBySelf())) {
//                    // 新模式，判断要不要展示奖励说明提示
//                    getViewModel(ShipmentsTerracePaymentModel.class).queryAdvanceCashOilServiceMoney(this.shipmentsEGoodInfo.getOrderId(), this.mOilRatio, this.advanceWay);
//                }
//                break;
//            }
//
//            case "3": {
//                //3:预付油品+现金（需要重新获取预付费用）=>1：查询油气比例选择,2->显示优惠信息
//                this.clOil.setVisibility(View.VISIBLE);
//                this.ll_coupon.setVisibility(View.VISIBLE);
//                this.tv_paly_prepay_ratio.setText("选择预付，装货后将提前收到一部分现金和油费");
//                //优惠信息(先显示)
//                this.showDiscountHelper.showTxt(payView, selectCradView);
//
//                if (TextUtils.equals("1", advanceInfo.getOilSelectBySelf())) {
//                    // 新模式，有油气比例选择
//                    getViewModel(ShipmentsTerracePaymentModel.class).queryAdvanceCashOilServiceMoney(this.shipmentsEGoodInfo.getOrderId(), this.mOilRatio, this.advanceWay);
//                } else {
//                    // 老模式 重新查询一下，预付服务费
//                    getViewModel(ShipmentsTerracePaymentModel.class).queryNewAdvanceServiceMoney(this.shipmentsEGoodInfo.getOrderId(), this.shipmentsEGoodInfo.getDetailId(), this.advanceWay, this.mOilRatio);
//                }
//                break;
//            }
//        }
//
//    }
//
//    @LiveDataMatch(tag = "预付服务费提示-50804版本")
//    public void queryAdvanceCashOilServiceMoneySuccess(EAdvanceServiceMoney data) {
//
//        if (TextUtils.equals("1", data.getOilRewardRatioFlag())) {
//            //是否有油品奖励	0或者空：无 ;1:有
//            tv_award_tips1.setVisibility(View.VISIBLE);
//            tv_award_tips1.setText(Html.fromHtml("预计可获得" + "<font color=\"#FF602E\">" + data.getRewardOilRatio() + "%(柴油)奖励 </font>" + "（以实际到账奖励为准）"));
//        } else {
//            tv_award_tips1.setVisibility(View.GONE);
//        }
//        if (TextUtils.equals("1", data.getRewardOilCashMoneyFlag())) {
//            //是否展示油品金额
//            tv_award_tips2.setVisibility(View.VISIBLE);
//            tv_award_tips2.setText(Html.fromHtml("最高可奖励" + "<font color=\"#FB6B40\">" + data.getRewardOilCashMoney() + "</font>" + "元，最终依据结算运费结算您的油品现金奖励金额，并和运费一起支付到您的智运账本，请注意查收"));
//        } else {
//            tv_award_tips2.setVisibility(View.GONE);
//        }
//
//        this.opeTenrracePaymentDialog(data);
//    }
//
//    @LiveDataMatch(tag = "切换比例与切换预付方式：现金+油品，重新查询一下，预付服务费,")
//    public void opeTenrracePaymentDialog(EAdvanceServiceMoney serviceMoney) {
//
//        //选择【3.现金+油品预付方式】时预估服务费,可能优惠券不满足
//        this.showDiscountHelper.updateCouponSelectDiscount("", 0);
//        this.serverMoney = serviceMoney.getRealAdvanceServiceMoney();
//        //重新刷新优惠信息
//        this.showDiscountHelper.showTxt(selectTerracePayView, selectCradView);
//
//        if (!this.showDiscountHelper.isDefalutCouponDiscount()) {
//            //不是默认优惠券模式， 查询可以优惠券可以使用数量
//            this.getViewModel(ShipmentsTerracePaymentModel.class).getUseCouponSize(this.shipmentsEGoodInfo.getOrderId(), this.shipmentsEGoodInfo.getPbCarrierMoney(), this.serverMoney);
//        }
//        this.getViewModel(ShipmentsTerracePaymentModel.class).queryCarrierDiscounts(this.shipmentsEGoodInfo.getOrderId(), this.serverMoney, this.advanceWay);
//    }
//
//    @Override
//    public boolean checkParams(ShipmentUI data) {
//
//        if (senceAdvance == 1) {
//
//            data.advanceWay = advanceWay;
//            data.isAdvanceButtonOn = "1";
//            data.oilRatio = mOilRatio;
//
//            if (cl_play_ui.getVisibility() == View.VISIBLE) {
//
//                //重新预付申请场景进入（预付方式已固定）
//                this.showDiscountHelper.checkParams(data);
//                if (selectCradView.isExistCard()) {
//                    //智运卡卡号
//                    data.discountCardNum = selectCradView.getCardNum();
//                    if (selectCradView.getCardType() == 1) {
//                        //买卡
//                        data.discountCardBuyFlag = "1";
//                    } else {
//                        data.discountCardBuyFlag = "0";
//                    }
//                }
//                if (!TextUtils.equals("2", advanceWay) && TextUtils.equals("1", advanceInfo.getActivityShowFlag())) {
//                    //非油品预付 && 1展示
//                    data.activityId = advanceInfo.getActivityInfo().getActivityId();
//                }
//            }
//
//        } else {
//            if (rbOpen.getCheckedRadioButtonId() == R.id.rb_yeas) {
//
//                data.advanceWay = advanceWay;
//                data.isAdvanceButtonOn = "1";
//                data.oilRatio = mOilRatio;
//
//                if (cl_play_ui.getVisibility() == View.VISIBLE) {
//                    //可以见预付方式与优惠券
//                    this.showDiscountHelper.checkParams(data);
//
//                    if (selectCradView.isExistCard()) {
//                        //智运卡卡号
//                        data.discountCardNum = selectCradView.getCardNum();
//                        if (selectCradView.getCardType() == 1) {
//                            //买卡
//                            data.discountCardBuyFlag = "1";
//                        } else {
//                            data.discountCardBuyFlag = "0";
//                        }
//                    }
//
//                    if (this.selectTerracePayView.getVisibility() == View.VISIBLE) {
//                        if (TextUtils.isEmpty(advanceWay)) {
//                            showToast("请选择预付方式");
//                            return false;
//                        }
//                    }
//                    if (!TextUtils.equals("2", advanceWay) && TextUtils.equals("1", advanceInfo.getActivityShowFlag())) {
//                        //非油品预付 && 1展示
//                        data.activityId = advanceInfo.getActivityInfo().getActivityId();
//                    }
//                }
//
//            } else {
//                //没有选择预付
//                data.advanceWay = "";
//                data.isAdvanceButtonOn = "2";
//                data.oilRatio = "";
//                data.discountCardUseFlag = "";
//            }
//        }
//        return true;
//    }
//
//    /*------------------------------------------优惠券-----------------------------------------*/
//    @LiveDataMatch(tag = "优惠券可用数量")
//    public void onCouponSizeSuccess(int size) {
//        this.ll_coupon_child.setVisibility(View.GONE);
//        this.tvPacket.setText(size <= 0 ? "无可用红包" : size + "张可用");
//    }
//
//    @LiveDataMatch(tag = "预付驳回带出抵用券")
//    public void onHasCouponInfoSuccess(EPickUserCoupon data) {
//
//        if (!TextUtils.isEmpty(data.getUserCouponId())) {
//            //优惠方式：自选优惠券,不使用智运卡
//            double backMoney = TextUtils.isEmpty(data.getDiscountAmount()) ? 0.00 : Double.valueOf(data.getDiscountAmount());
//
//            this.showDiscountHelper.updateCouponSelectDiscount(data.getUserCouponId(), backMoney);
//            this.showDiscountHelper.showTxt(selectTerracePayView, selectCradView);
//        }
//    }
//
//    @Override
//    public void onActivityResult(int requestCode, int resultCode, Intent data) {
//        super.onActivityResult(requestCode, resultCode, data);
//
//        if (resultCode == Activity.RESULT_OK && requestCode == 1000) {
//
//            String userCouponIds = data.getStringExtra("ID");
//
//            if (TextUtils.isEmpty(userCouponIds)) {
//                //清空选择
//                this.getViewModel(ShipmentsTerracePaymentModel.class).getUseCouponSize(this.shipmentsEGoodInfo.getOrderId(), this.shipmentsEGoodInfo.getPbCarrierMoney(), this.serverMoney);
//                this.showDiscountHelper.updateCouponSelectDiscount("", 0);
//
//            } else {
//                //优惠方式：自选优惠券,不使用智运卡
//                tv_question_mark.setVisibility(View.VISIBLE);
//                Double backMoney = data.getDoubleExtra("MONEY", 0.00f);
//                this.showDiscountHelper.updateCouponSelectDiscount(userCouponIds, backMoney);
//            }
//            this.showDiscountHelper.showTxt(selectTerracePayView, selectCradView);
//        }
//
//    }
//
//    private void setOilMoneyView(View view) {
//        //油气比例+现金比例
//        cy_play_type_radio = view.findViewById(R.id.cy_play_type_radio);
//        tv_oil_ratio_title = view.findViewById(R.id.tv_oil_ratio_title);
//        tv_oil_ratio_value = view.findViewById(R.id.tv_oil_ratio_value);
//        tv_ratio_money_title = view.findViewById(R.id.tv_ratio_money_title);
//        tv_ratio_money_value = view.findViewById(R.id.tv_ratio_money_value);
//        tv_paly_prepay_ratio = view.findViewById(R.id.tv_paly_prepay_ratio);
//
//        tv_oil_ratio_value.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //选择油气比例
//                try {
//                    //由油气比例，计算现金比例(最高40，取最小值)
//                    double ratio = Math.min(Double.parseDouble(advanceInfo.getAdvanceRatio()), 40);
//                    final List<String> items = new LinkedList<>();
//                    for (int i = 1; i <= ratio; i++) {
//                        items.add(String.valueOf(i));
//                    }
//                    MenuDialogV1.instance(items).setTitle("请选择").setClick((item, index) -> {
//
//                        countOilRatio(item);
//
//                        //切换比例
//                        if (TextUtils.equals("1", advanceInfo.getOilSelectBySelf())) {
//                            // 新模式，有油气比例选择
//                            getViewModel(ShipmentsTerracePaymentModel.class).queryAdvanceCashOilServiceMoney(shipmentsEGoodInfo.getOrderId(), mOilRatio, advanceWay);
//                        } else {
//                            // 老模式，重新查询一下，预付服务费
//                            getViewModel(ShipmentsTerracePaymentModel.class).queryNewAdvanceServiceMoney(shipmentsEGoodInfo.getOrderId(), shipmentsEGoodInfo.getDetailId(), advanceWay, mOilRatio);
//                        }
//                        return null;
//
//                    }).show(getActivity());
//                } catch (Exception e) {
//                }
//            }
//        });
//    }
//
//
//    private void showPlayTypeRadioUI(String advanceWay) {
//        //比例可以见场景
//        switch (advanceWay) {
//            case "1": {
//                //*1:预付现金
//                tv_oil_ratio_title.setVisibility(View.GONE);
//                tv_oil_ratio_value.setVisibility(View.GONE);
//                tv_ratio_money_title.setVisibility(View.VISIBLE);
//                tv_ratio_money_value.setVisibility(View.VISIBLE);
//                //预付现金则无油品比例
//                this.countOilRatio("0");
//                break;
//            }
//            case "2": {
//                //2:预付油品
//                tv_ratio_money_title.setVisibility(View.GONE);
//                tv_ratio_money_value.setVisibility(View.GONE);
//                tv_oil_ratio_title.setVisibility(View.VISIBLE);
//                tv_oil_ratio_value.setVisibility(View.VISIBLE);
//                this.countOilRatio(this.advanceInfo.getOilDefaultRatio());
//                break;
//            }
//            case "3": {
//                //3:预付油品+现金
//                tv_oil_ratio_title.setVisibility(View.VISIBLE);
//                tv_oil_ratio_value.setVisibility(View.VISIBLE);
//                tv_ratio_money_title.setVisibility(View.VISIBLE);
//                tv_ratio_money_value.setVisibility(View.VISIBLE);
//                this.countOilRatio(this.advanceInfo.getOilDefaultRatio());
//                break;
//            }
//            default: {
//            }
//        }
//    }
//
//    private void countOilRatio(String ratio) {
//        try {
//            this.mOilRatio = ratio;
//
//            final String oilRatio = TextUtils.isEmpty(ratio) ? "0" : ratio;
//            //由油气比例，计算现金比例
//            double moneyRatio = Double.parseDouble(advanceInfo.getAdvanceRatio()) - Double.parseDouble(oilRatio);
//            //现金比例
//            tv_ratio_money_value.setText(moneyRatio + "%");
//            //油品比例
//            tv_oil_ratio_value.setText(oilRatio + "%");
//
//            tv_oil_ratio_value.postDelayed(() -> {
//                //发送数据给ShipmenstIntegrityFragment
//                postEvent(new EventOilCardRatio(oilRatio));
//            }, 200);
//
//        } catch (Exception e) {
//        }
//    }
//
//    /**
//     * 8
//     * 通知一下，预付协议UI，需要查询预付服务费使用
//     */
//    private void notifyArgess() {
//        if (!TextUtils.isEmpty(this.advanceWay)) {
//            mRoot.postDelayed(() -> {
//                //通知一下，预付协议UI
//                EAdvanceType advanceType = new EAdvanceType();
//                advanceType.setType(advanceWay);
//                postEvent(advanceType);
//            }, 1000);
//        }
//    }
//
//    /***
//     * 优惠方式
//     */
//    interface OnShowDiscount {
//
//        /***
//         * 使用优惠
//         * @param payView
//         * @param selectCradView
//         * @return 返回当前优惠方式
//         */
//        OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView);
//
//        /***
//         *优惠被使用设置数据
//         * @param data
//         */
//        void checkParams(ShipmentUI data);
//    }
//
//    class ShowDiscountHelper {
//
//        OnShowDiscount onFirstShowDiscount;
//        CouponSelectDiscount couponSelectDiscount;
//        //当前使用
//        OnShowDiscount useOnShowDiscount;
//
//        ShowDiscountHelper() {
//            //优惠使用顺序：自选优惠卷=>智运卡=>立减活动=>默认优惠卷=>无优惠
//            couponSelectDiscount = new CouponSelectDiscount(new CardDiscount(new CeduceDiscount(new DefalutCouponDiscount(new NullDiscount(null)))));
//            onFirstShowDiscount = couponSelectDiscount;
//            useOnShowDiscount = onFirstShowDiscount;
//        }
//
//        public void showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//            useOnShowDiscount = onFirstShowDiscount.showTxt(payView, selectCradView);
//        }
//
//        /***
//         * 更新自选优惠券优惠方式数据
//         * @param userCouponIds 优惠券ID
//         * @param couponMoney   优惠券金额
//         */
//        public void updateCouponSelectDiscount(String userCouponIds, double couponMoney) {
//
//            couponSelectDiscount.userCouponIds = userCouponIds;
//            couponSelectDiscount.couponMoney = couponMoney;
//        }
//
//        public String getUserCouponIds() {
//            if (useOnShowDiscount instanceof DefalutCouponDiscount) {
//                return ((DefalutCouponDiscount) useOnShowDiscount).userCouponIds;
//            }
//            if (useOnShowDiscount instanceof CouponSelectDiscount) {
//                return ((CouponSelectDiscount) useOnShowDiscount).userCouponIds;
//            }
//            return "";
//        }
//
//        /***
//         * 判断当前使用优惠方式是不是默认优惠券
//         * @return
//         */
//        public boolean isDefalutCouponDiscount() {
//            return useOnShowDiscount instanceof DefalutCouponDiscount;
//        }
//
//        public void checkParams(ShipmentUI data) {
//            if (useOnShowDiscount != null) {
//                useOnShowDiscount.checkParams(data);
//            }
//        }
//
//        /****
//         * 智运卡方式
//         */
//        class CardDiscount extends NullDiscount {
//
//            public CardDiscount(OnShowDiscount next) {
//                super(next);
//            }
//
//            @Override
//            public OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//
//                if (selectCradView.isExistCard()) {
//                    //2已购买了智运折扣卡 || 1买卡状态
//                    //预付费
//                    double serverMoney = this.getServerMoney();
//                    // 优先使用智运卡
//                    if (serverMoney <= 0) {
//                        payView.setTvAdvanceMoneyConTxt("服务费预估金额¥0元");
//                        payView.setTvAdvanceMoneyOilConTxt("服务费预估金额¥0元");
//                    } else {
//                        double discount = selectCradView.getCardMoney(serverMoney);
//                        serverMoney = serverMoney - discount;
//
//                        if (serverMoney <= 0) {
//                            payView.setTvAdvanceMoneyConTxt("服务费预估金额¥0元");
//                            payView.setTvAdvanceMoneyOilConTxt("服务费预估金额¥0元");
//
//                        } else {
//                            serverMoney = NumUtil.halfup(serverMoney);
//
//                            CharSequence txt = new SpannableHepler("服务费预估金额")
//                                    .append(new SpannableHepler.Txt("¥" + this.getServerMoney() + "元", true))
//                                    .append(new SpannableHepler.Txt("¥" + serverMoney + "元", "#FFFF602E")).builder();
//                            payView.setTvAdvanceMoneyConTxt(txt);
//                            payView.setTvAdvanceMoneyOilConTxt(txt);
//                        }
//                    }
//                    //使用智运卡
//                    payView.setIcon(R.drawable.equity_crad_gs_icon, "智运卡" + selectCradView.getDiscountRatio() + "折");
//
//                    if (TextUtils.equals("2", this.getAdvanceWay()) || rbOpen.getCheckedRadioButtonId() != R.id.rb_yeas) {
//                        //油品预付不使用有智运卡 or 不需要预付了
//                        selectCradView.userCardView(false);
//                    } else {
//                        selectCradView.userCardView(true);
//                    }
//                    return this;
//                }
//                //下一个优惠方式
//                return nextDiscount.showTxt(payView, selectCradView);
//            }
//
//            @Override
//            public void checkParams(ShipmentUI data) {
//                //使用智运卡优惠
//                data.discountCardNum = selectCradView.getCardNum();
//                if (TextUtils.equals("2", this.getAdvanceWay())) {
//                    //油品预付不使用
//                    data.discountCardUseFlag = "0";
//                } else {
//                    data.discountCardUseFlag = "1";
//                }
//            }
//        }
//
//        /***
//         * 立减优惠方式
//         */
//        class CeduceDiscount extends NullDiscount {
//
//            public CeduceDiscount(OnShowDiscount next) {
//                super(next);
//            }
//
//            @Override
//            public OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//
//                if (advanceInfo.isCouponShow()) {
//                    super.showTxt(payView, selectCradView);
//
//                    double serverMoney = this.getServerMoney();
//                    String advanceWay = this.getAdvanceWay();
//
//                    //获取立减之后预付金额
//                    double money = advanceInfo.getCouponToServerMoney(serverMoney);
//                    if (money >= 0) {
//                        CharSequence moneyTxt = new SpannableHepler()
//                                .append(new SpannableHepler.Txt(advanceInfo.getCouponContext(), "#FFFF602E"))
//                                .append("\n服务费预估金额\n")
//                                .append(new SpannableHepler.Txt("¥" + serverMoney, true))
//                                .append(new SpannableHepler.Txt("¥" + money, "#FFFF602E")).builder();
//
//                        if (TextUtils.equals("3", advanceWay)) {
//                            //当前选择是现金+油品预付，具体金额
//                            payView.setTvAdvanceMoneyConTxt(moneyTxt);
//                            payView.setTvAdvanceMoneyOilConTxt(moneyTxt);
//                        } else {
//                            CharSequence moneyOilTxt = new SpannableHepler()
//                                    .append(new SpannableHepler.Txt(advanceInfo.getCouponContext(), "#FFFF602E"))
//                                    .append("\n服务费预估金额\n")
//                                    .append(new SpannableHepler.Txt("¥0-¥" + serverMoney, true))
//                                    .append(new SpannableHepler.Txt("¥0-¥" + money, "#FFFF602E")).builder();
//
//                            payView.setTvAdvanceMoneyConTxt(moneyTxt);
//                            payView.setTvAdvanceMoneyOilConTxt(moneyOilTxt);
//                        }
//                        payView.setIcon(R.drawable.order_pick_offer_insurance_yes, "立减优惠");
//                    }
//                    //不使用智运卡
//                    selectCradView.userCardView(false);
//                    return this;
//                }
//                //下一个优惠方式
//                return nextDiscount.showTxt(payView, selectCradView);
//
//            }
//
//            @Override
//            public void checkParams(ShipmentUI data) {
//                //立减优惠
//                if (TextUtils.equals("2", this.getAdvanceWay())) {
//                    //油品预付不使用
//                    data.couponAmountType = "";
//                    data.cutAdvanceCouponId = "";
//                    data.couponMoney = "";
//                } else {
//                    data.couponAmountType = advanceInfo.getCouponAmountType();
//                    data.cutAdvanceCouponId = advanceInfo.getCutAdvanceCouponId();
//                    data.couponMoney = advanceInfo.getCouponMoney();
//                }
//            }
//        }
//
//        /***
//         * 默认优惠券
//         */
//        class DefalutCouponDiscount extends NullDiscount {
//            //优惠券ID
//            String userCouponIds;
//
//            public DefalutCouponDiscount(OnShowDiscount next) {
//                super(next);
//            }
//
//            @Override
//            public OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//
//                if (!TextUtils.isEmpty(advanceInfo.getUserCouponId()) && !TextUtils.equals("3", this.getAdvanceWay())) {
//                    //有默认优惠券 && 不是油品+现金预付方式，此方式预付费重新查询而且优惠券不一定适用
//                    super.showTxt(payView, selectCradView);
//                    payView.setIcon(R.drawable.order_pick_offer_insurance_yes, "优惠券折扣");
//                    this.userCouponIds = advanceInfo.getUserCouponId();
//
//                    double serverMoney = this.getServerMoney();
//                    double discountMoney = advanceInfo.getDiscountMoney(serverMoney);
//                    tvPacket.setText("-¥" + (serverMoney > discountMoney ? discountMoney : serverMoney) + "元");
//
//                    //使用优惠券，抵扣后预估金额
//                    double money = NumUtil.sub(serverMoney, discountMoney);
//                    money = money <= 0 ? 0.0 : money;
//
//                    CharSequence txt = new SpannableHepler()
//                            .append(new SpannableHepler.Txt("¥" + serverMoney, "#999999", true))
//                            .append(new SpannableHepler.Txt("¥" + money, "#e95d4f")).builder();
//                    tv_new_money.setText(txt);
//                    ll_coupon_child.setVisibility(View.VISIBLE);
//
//                    //不使用智运卡
//                    selectCradView.userCardView(false);
//                    return this;
//                }
//                //下一个优惠方式
//                return nextDiscount.showTxt(payView, selectCradView);
//            }
//
//            @Override
//            public void checkParams(ShipmentUI data) {
//                //优惠券
//                if (TextUtils.equals("2", this.getAdvanceWay())) {
//                    //油品预付不使用默认优惠券
//                    data.userCouponIds = "";
//                } else {
//                    data.userCouponIds = userCouponIds;
//                }
//            }
//        }
//
//        /***
//         * 自选优惠券优惠方式
//         */
//        class CouponSelectDiscount extends NullDiscount {
//
//            //优惠券ID
//            String userCouponIds;
//            //优惠券金额
//            double couponMoney;
//
//            public CouponSelectDiscount(OnShowDiscount next) {
//                super(next);
//            }
//
//            @Override
//            public OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//
//                if (!TextUtils.isEmpty(this.userCouponIds)) {
//
//                    super.showTxt(payView, selectCradView);
//                    payView.setIcon(R.drawable.order_pick_offer_insurance_yes, "优惠券折扣");
//
//                    double serverMoney = this.getServerMoney();
//                    tvPacket.setText("-¥" + (serverMoney > couponMoney ? couponMoney : serverMoney) + "元");
//
//                    //使用优惠券，抵扣后预估金额
//                    double money = NumUtil.sub(serverMoney, couponMoney);
//                    money = money <= 0 ? 0.0 : money;
//
//                    CharSequence txt = new SpannableHepler()
//                            .append(new SpannableHepler.Txt("¥" + serverMoney, "#999999", true))
//                            .append(new SpannableHepler.Txt("¥" + money, "#e95d4f")).builder();
//                    tv_new_money.setText(txt);
//                    ll_coupon_child.setVisibility(View.VISIBLE);
//
//                    //不使用智运卡
//                    selectCradView.userCardView(false);
//
//                    return this;
//                }
//                //下一个优惠方式
//                return nextDiscount.showTxt(payView, selectCradView);
//            }
//
//            @Override
//            public void checkParams(ShipmentUI data) {
//                //优惠券
//                if (TextUtils.equals("2", this.getAdvanceWay())) {
//                    //油品预付不使用默认优惠券
//                    data.userCouponIds = "";
//                } else {
//                    data.userCouponIds = userCouponIds;
//                }
//            }
//        }
//
//        /***
//         * 无优惠方式
//         */
//        class NullDiscount implements OnShowDiscount {
//
//            OnShowDiscount nextDiscount;
//
//            public NullDiscount(OnShowDiscount next) {
//                this.nextDiscount = next;
//            }
//
//            @Override
//            public OnShowDiscount showTxt(SelectTerracePayView payView, ShipmentsSelectCradView selectCradView) {
//                double serverMoney = this.getServerMoney();
//                payView.setIcon(-1, "");
//                payView.setTvAdvanceMoneyConTxt("服务费预估金额¥" + serverMoney + "元");
//                payView.setTvAdvanceMoneyOilConTxt("服务费预估金额¥" + serverMoney + "元");
//                return this;
//            }
//
//            public String getAdvanceWay() {
//                return advanceWay;
//            }
//
//            public double getServerMoney() {
//                return TextUtils.isEmpty(serverMoney) ? 0.0 : Double.parseDouble(serverMoney);
//            }
//
//            @Override
//            public void checkParams(ShipmentUI data) {
//
//            }
//        }
//    }
//
//    private String carrierDiscountsMsg;
//
//    @LiveDataMatch
//    public void onCarrierDiscounts(String msg) {
//        this.carrierDiscountsMsg = msg;
//    }
//
//
//    /***
//     * 预付比例有变动
//     */
//    public void onChangeAdvanceRatio(String advanceRatio) {
//
//        if (TextUtils.equals("1", advanceInfo.getOilSelectBySelf())) {
//            //有油气比例选择
//            this.advanceInfo.setAdvanceRatio(advanceRatio);
//            //油气比例最高40，取最小值
//            double oilRatio = Math.min(Double.parseDouble(advanceRatio), 40);
//
//            this.advanceInfo.setOilDefaultRatio(String.valueOf(oilRatio));
//
//            this.showPlayTypeRadioUI(advanceWay);
//        }
//
//    }
//}
