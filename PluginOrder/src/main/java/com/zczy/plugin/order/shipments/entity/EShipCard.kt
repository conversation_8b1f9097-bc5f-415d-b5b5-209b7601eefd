package com.zczy.plugin.order.shipments.entity

data class EShipCard(
    var discountCardFlag: String,//是否展示智运折扣卡;1:是;0否:",
    var discountCardBuyFlag: String,//是否购买了智运折扣卡;1:是;0否:,买过之后discountCardArray返回的是购买卡的信息",
    var discountCardArray: List<ShipmentsDiscountCard>,//返回的是购买卡的信息",
    var activityId:String = "",// 活动ID
)

data class ShipmentsDiscountCard(
    var orgPrice: String,//原始价格",
    var salePrice: String,//折扣后价格"
    var cardNum: String,//卡号",
    var discountTitle: String,//黄金9折卡",
    var subTips: String,//有效期30天",
    var discountRatio: Double,//0.9 比如打9折，就返回0.9",
    var cardType: String,//月卡;年卡"
)