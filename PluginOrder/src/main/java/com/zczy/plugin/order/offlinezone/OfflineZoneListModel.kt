package com.zczy.plugin.order.offlinezone

import com.sfh.lib.AppCacheManager
import com.sfh.lib.exception.HandleException
import com.sfh.lib.http.down.HttpDownHelper
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.offlinezone.bean.CancelOrderTenderReq
import com.zczy.plugin.order.offlinezone.bean.ReqContractOfflineToPdf
import com.zczy.plugin.order.waybill.entity.ContractToPdf
import com.zczy.user.offlinezone.bean.*
import io.reactivex.Observable
import java.io.File

/**
 * @description
 * @date 15:21 5/25/20
 * <AUTHOR>
 * @since 1.0
 **/
class OfflineZoneListModel : BaseViewModel() {

    fun getOfflineZoneListCyr(nowPage: Int,orderState: String="") {
        execute( OfflineZoneListCarrierReq(
            nowPage = nowPage,
            orderState = orderState), object : IResult<BaseRsp<PageList<OfflineZoneListRes>>> {
            override fun onSuccess(p0: BaseRsp<PageList<OfflineZoneListRes>>) {
                if (p0.success()) {
                    setValue("getOfflineZoneList", p0.data)
                } else {
                    showDialogToast(p0.msg)
                    setValue("getOfflineZoneList", null)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
                setValue("getOfflineZoneList", null)
            }
        })
    }
    fun getOfflineZoneListBoss(nowPage: Int,orderState: String="") {
        execute(OfflineZoneListBossReq(
            nowPage = nowPage,
            orderState = orderState), object : IResult<BaseRsp<PageList<OfflineZoneListRes>>> {
            override fun onSuccess(p0: BaseRsp<PageList<OfflineZoneListRes>>) {
                if (p0.success()) {
                    setValue("getOfflineZoneList", p0.data)
                } else {
                    showDialogToast(p0.msg)
                    setValue("getOfflineZoneList", null)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
                setValue("getOfflineZoneList", null)
            }
        })
    }
    fun getOfflineZoneListCys(nowPage: Int,orderState: String="") {
        execute(OfflineZoneListCysReq(
            nowPage = nowPage,
            orderState = orderState), object : IResult<BaseRsp<PageList<OfflineZoneListRes>>> {
            override fun onSuccess(p0: BaseRsp<PageList<OfflineZoneListRes>>) {
                if (p0.success()) {
                    setValue("getOfflineZoneList", p0.data)
                } else {
                    showDialogToast(p0.msg)
                    setValue("getOfflineZoneList", null)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
                setValue("getOfflineZoneList", null)
            }
        })
    }

    fun cancelBid(expectId: String) {
        execute(CancelQuoteReq(expectId), object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(p0: BaseRsp<ResultData>) {
                if (p0.success()) {
                    showToast(p0.msg)
                    setValue("cancelBid", p0.data)
                } else {
                    showDialogToast(p0.msg)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
            }
        })
    }

    fun queryCarrierListCountNum() {
        execute(QueryCarrierListCountNum(), object : IResult<BaseRsp<CarrierListCountNum>> {
            override fun onSuccess(p0: BaseRsp<CarrierListCountNum>) {
                if (p0.success()) {
                    setValue("countNum", p0.data)
                }
            }

            override fun onFail(p0: HandleException) {
            }
        })
    }

    /**
     * 查询下载合同地址
     * @return
     */
    fun addContract(orderId :String) {
        this.execute<BaseRsp<ContractToPdf>>(true, ReqContractOfflineToPdf(orderId), object :
            IResult<BaseRsp<ContractToPdf>> {
            @Throws(Exception::class)
            override fun onSuccess(rspBase: BaseRsp<ContractToPdf>) {
                if (rspBase.success()) {
                    setValue("addContractSuccess", rspBase.data)
                } else {
                    showDialogToast(rspBase.msg)
                }
            }
            override fun onFail(e: HandleException) {
                showDialogToast(e.msg)
            }
        })
    }

    fun loadFile(url: String, fileName: String) {
        this.execute(true, Observable.just(url).map<String> { s ->
            var file = File(AppCacheManager.getFileCache(), fileName)
            file = HttpDownHelper.Builder(s).setTagFile(file).start()
            file.absolutePath
        }, object : IResult<String> {
            @Throws(Exception::class)
            override fun onSuccess(path: String) {
                setValue("downLoadSuccess", path)
            }

            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }
        })
    }

    /**
     * 取消运单
     */
    fun cancelOrderTender(orderId: String) {
        execute(CancelOrderTenderReq(orderId), object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(p0: BaseRsp<ResultData>) {
                if (p0.success()) {
                    showToast(p0.msg)
                    setValue("cancelOrderTenderSuccess")
                } else {
                    showDialogToast(p0.msg)
                }
            }

            override fun onFail(p0: HandleException) {
                showDialogToast(p0.msg)
            }
        })
    }

}