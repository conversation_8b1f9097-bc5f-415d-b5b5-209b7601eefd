//package com.zczy.plugin.order.navigation
//
//import android.content.BroadcastReceiver
//import android.content.Context
//import android.content.Intent
//import android.content.IntentFilter
//import android.graphics.PixelFormat
//import android.os.Bundle
//import android.os.Handler
//import androidx.localbroadcastmanager.content.LocalBroadcastManager
//import android.text.TextUtils
//import android.view.Gravity
//import android.view.LayoutInflater
//import android.view.View
//import android.view.WindowManager
//import android.widget.TextView
//import com.amap.api.navi.AmapRouteActivity
//import com.google.gson.Gson
//import com.zczy.lib_zstatistics.sdk.ZStatistics
//import com.zczy.plugin.order.R
//import com.zczy.plugin.order.source.list.fragment.OrderMainListBatchFragment
//import com.zczy.plugin.order.waybill.cyr.WaybillListCyrFragment
//import com.zczy.plugin.order.waybill.entity.EWaybill
//import com.zczy.plugin.order.waybill.model.WaybillCyrModel
//
//class NaviActivity : AmapRouteActivity() {
//    private lateinit var intentFilter: IntentFilter
//    private lateinit var myReceiver: MyReceiver
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//
//        myReceiver = MyReceiver()
//        intentFilter = IntentFilter()
//        intentFilter.addAction("onArriveDestination")
////        registerReceiver(myReceiver, intentFilter)
//        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(this).registerReceiver(myReceiver,intentFilter)
//    }
//
//    private inner class MyReceiver : BroadcastReceiver() {
//        override fun onReceive(context: Context, intent: Intent) {
//            if (intent.action == "onArriveDestination") {
//                var flag: String = intent.getStringExtra("flag")?:""
//                var data: String = intent.getStringExtra("data")?:""
//                if(!TextUtils.isEmpty(data)){
//                    try {
//                        var dataEWaybill: EWaybill = Gson().fromJson(data, EWaybill::class.java)
//                        showActionBtn(flag, dataEWaybill)
//                    }catch (e:Exception){}
//                }
//            }
//        }
//    }
//
//
//    private fun showActionBtn(flag: String, dataEWaybill: EWaybill) {
//        Handler().postDelayed(Runnable {
//            val view: View = LayoutInflater.from(this).inflate(R.layout.layout_navigation_btn, null)
//            val windowManager = this?.getSystemService(WINDOW_SERVICE) as WindowManager
//            val layoutParams = WindowManager.LayoutParams()
//            layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_PANEL
//            layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
//            layoutParams.gravity = Gravity.BOTTOM
//            layoutParams.format = PixelFormat.TRANSLUCENT
//            layoutParams.width = WindowManager.LayoutParams.MATCH_PARENT
//            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
//
//            val tvNavigationBtn = view.findViewById<TextView>(R.id.tv_navigation_btn)
//            if (flag == "1") {
//                tvNavigationBtn.text = "确认发货"
//            } else {
//                tvNavigationBtn.text = "确认收货"
//            }
//
//            tvNavigationBtn.setOnClickListener {
//                WaybillCyrModel.tagData = dataEWaybill
//                WaybillCyrModel.tagFlag = flag
//
//                if (flag == "1") {
//                    WaybillCyrModel.tagNavi = 1
//
//                    ZStatistics.onViewClick(this, "confirmSend")
//                } else {
//                    WaybillCyrModel.tagNavi = 2
//
//                    ZStatistics.onViewClick(this, "confirmRecive")
//                }
//
//                finish()
//            }
//            windowManager.addView(view, layoutParams)
//        }, 100)
//    }
//
//    override fun onDestroy() {
//        super.onDestroy()
//        androidx.localbroadcastmanager.content.LocalBroadcastManager.getInstance(this).unregisterReceiver(myReceiver)
//
//    }
//}