package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import java.util.List;

/**
 * 功能描述:货物信息(发货）
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/1/28
 */
public class EGoodInfo {

    /**
     * 货物品类
     */
    String cargoType;

    /**
     * 货物类别：1：重货，2：泡货
     */
    String cargoCategory;

    /**
     * 货物ID
     */
    String cargoId;

    /**
     * 货物单位：1：吨，2：m3,
     */
    String unit;

    /**
     * 货物名称
     */
    String cargoName;

    /**
     * 运单号
     */
    String orderId;

    /**
     * 重量/体积
     */
    String weight;

    /***
     * 货主配置项发货方控制(泡)
     */
    String shippingOrientationMargin;

    /***
     * 货主配置项发货方控制(重)
     */
    String weightOrientationLimit;
    /**
     * 回单打回时之前输入数量 or 当前输入内容
     */
    private String beforeDeliverCargoWeight;


    /**
     * 回单打回时之前输入数量 or 当前输入内容(旧值用于对比数据是否发生修改,本地字段)
     */
    private String oldBeforeDeliverCargoWeight;

    /*** 之前发货的毛重 3.5.5-4716需求*/
    String beforeDeliverGrossWeight;

    /*** 之前发货的皮重 3.5.5-4716需求*/
    String beforeDeliverTareWeight;

    //【】当前选择的集装箱箱号
    List<EContainer> tempContainerSize;

    public List<EContainer> getTempContainerSize() {
        return tempContainerSize;
    }

    public String getWeightOrientationLimit() {
        return weightOrientationLimit;
    }

    public void setTempContainerSize(List<EContainer> tempContainerSize) {
        this.tempContainerSize = tempContainerSize;
    }

    public String getOldBeforeDeliverCargoWeight() {
        return oldBeforeDeliverCargoWeight;
    }

    public void setOldBeforeDeliverCargoWeight(String oldBeforeDeliverCargoWeight) {
        this.oldBeforeDeliverCargoWeight = oldBeforeDeliverCargoWeight;
    }

    public String getBeforeDeliverGrossWeight() {
        return beforeDeliverGrossWeight;
    }

    public String getBeforeDeliverTareWeight() {
        return beforeDeliverTareWeight;
    }

    public void setBeforeDeliverTareWeight(String beforeDeliverTareWeight) {
        this.beforeDeliverTareWeight = beforeDeliverTareWeight;
    }

    public void setBeforeDeliverGrossWeight(String beforeDeliverGrossWeight) {
        this.beforeDeliverGrossWeight = beforeDeliverGrossWeight;
    }

    public void setBeforeDeliverCargoWeight(String beforeDeliverCargoWeight) {

        this.beforeDeliverCargoWeight = beforeDeliverCargoWeight;
    }

    public String getBeforeDeliverCargoWeight() {

        return beforeDeliverCargoWeight;
    }

    public String getShippingOrientationMargin() {

        return shippingOrientationMargin;
    }

    public String getCargoType() {

        return cargoType;
    }

    public String getCargoCategory() {

        //1:吨  2:方
        return TextUtils.equals("1", cargoCategory) ? "吨" : TextUtils.equals("2", cargoCategory) ? "方" : "";
    }

    public String getCargoId() {

        return cargoId;
    }

    public String getUnit() {

        return unit;
    }

    public String getUnitText() {

        //1：吨，2：m3,
        return TextUtils.equals("1", unit) ? "吨" : TextUtils.equals("2", unit) ? "方" : TextUtils.equals("3", unit) ? "箱" : "";
    }

    public String getCargoName() {

        return cargoName;
    }

    public String getOrderId() {

        return orderId;
    }

    public String getWeight() {

        return weight;
    }

    public void setCargoType(String cargoType) {
        this.cargoType = cargoType;
    }

    public void setCargoCategory(String cargoCategory) {
        this.cargoCategory = cargoCategory;
    }

    public void setCargoId(String cargoId) {
        this.cargoId = cargoId;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setCargoName(String cargoName) {
        this.cargoName = cargoName;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public void setShippingOrientationMargin(String shippingOrientationMargin) {
        this.shippingOrientationMargin = shippingOrientationMargin;
    }
}
