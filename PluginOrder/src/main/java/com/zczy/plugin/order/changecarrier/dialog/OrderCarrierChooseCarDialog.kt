package com.zczy.plugin.order.changecarrier.dialog

import android.graphics.Color
import android.os.Bundle
import androidx.constraintlayout.widget.ConstraintLayout
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.zczy.certificate.vehiclemanage.carowner.CarOwnerVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.carrier.CarrierVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.enterprise.EnterPriseVehicleManagementActivityV1
import com.zczy.comm.CommServer
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.ex.setVisible
import com.zczy.plugin.order.R
import java.util.*

/**
 * 变更承运人 选择车牌
 * sdx
 */
@Suppress("unused")
class OrderCarrierChooseCarDialog<T> : BaseDialog() {
    companion object {
        @JvmStatic
        fun <T> instance(data: List<T>): OrderCarrierChooseCarDialog<T> {
            val dialog = OrderCarrierChooseCarDialog<T>()
            dialog.data = data
            return dialog
        }

        @JvmStatic
        fun <T> instance(data: List<T>, showPrompt: Boolean, showPromptBtn: Boolean): OrderCarrierChooseCarDialog<T> {
            val dialog = OrderCarrierChooseCarDialog<T>()
            dialog.data = data
            dialog.showPrompt = showPrompt
            dialog.showPromptBtn = showPromptBtn
            return dialog
        }
    }

    private var data: List<T> = ArrayList()
    private var chooseListener: (T, BaseDialog) -> Unit = { _, _ -> }
    private var selectItem: T? = null
    private var title = "选择变更车辆"
    private var flatMap: (T) -> String = { it.toString() }
    private var flatShowLogo: (T) -> Boolean = { false }
    private var noCheck: T.() -> Boolean = { false }
    private var noCheckTxt: T.() -> String = { "" }
    private var showPrompt: Boolean = false
    private var showPromptBtn: Boolean = false
    private var toastText =""
    override fun getDialogTag(): String = "OrderCarrierChooseCarDialog"
    override fun getDialogLayout(): Int = R.layout.order_carrier_choose_car_dialog

    override fun getDialogType(): DialogType = DialogType.bottom

    override fun bindView(view: View, bundle: Bundle?) {
        view.findViewById<View>(R.id.view_root).setOnClickListener {
            dismiss()
        }

        view.findViewById<TextView>(R.id.tv_title)
                .text = title

        view.findViewById<ImageView>(R.id.btn_close)
                .setOnClickListener { dismiss() }

        val chooseCarGirdView = view.findViewById<ChooseCarGirdView2<T>>(R.id.choose_car_gird_view)
        chooseCarGirdView.noCheck = noCheck
        chooseCarGirdView.noCheckTxt = noCheckTxt
        chooseCarGirdView.flatMap = flatMap
        chooseCarGirdView.flatShowLogo = flatShowLogo
        chooseCarGirdView.listener = object : ChooseCarGirdView2.RegisterChooseCarViewListener<T> {
            override fun onSelectCar(num: T) {
                chooseListener(num, this@OrderCarrierChooseCarDialog)
            }

            override fun onClickMore() {
            }
        }

        chooseCarGirdView.setNewData(data)
        selectItem.isNotNull {
            chooseCarGirdView.addSelectCar(it)
        }

        val clVehicleInfo = view.findViewById<ConstraintLayout>(R.id.clVehicleInfo)
        clVehicleInfo.setVisible(showPrompt)
        val tvVehicleInfo = view.findViewById<TextView>(R.id.tvVehicleInfo)
        val spannableStringBuilder = SpannableStringBuilder()
        val spannableString1 = SpannableString("报废、冻结、临牌过期车辆不可勾选，可至")
        val spannableString2 = SpannableString("车辆管理")
        spannableString2.setSpan(ForegroundColorSpan(Color.parseColor("#F75B1F")), 0, spannableString2.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        val spannableString3 = SpannableString("查看")
        spannableStringBuilder.append(spannableString1).append(spannableString2).append(spannableString3)
        tvVehicleInfo.text = spannableStringBuilder
        val tvLookVehicle = view.findViewById<TextView>(R.id.tvLookVehicle)
        tvLookVehicle.setVisible(showPromptBtn)
        tvLookVehicle.setOnClickListener {
            val login = CommServer.getUserServer().login
            login?.apply {
                when {
                    relation.isCarrier -> {
                        CarrierVehicleManagementActivity.start(activity)
                    }
                    relation.isCys -> {
                        EnterPriseVehicleManagementActivityV1.start(activity)
                    }
                    relation.isBoss -> {
                        CarOwnerVehicleManagementActivity.start(activity)
                    }
                }
            }
        }

        if (!TextUtils.isEmpty(toastText)){
            var tv_toast_text = view.findViewById<TextView>(R.id.tv_toast_text)
            tv_toast_text.visibility = View.VISIBLE
            tv_toast_text.setText(toastText)
        }
    }

    fun setTitle(title: String): OrderCarrierChooseCarDialog<T> {
        this.title = title
        return this
    }
    fun setToastText(toast: String): OrderCarrierChooseCarDialog<T> {
        this.toastText = toast
        return this
    }
    fun setFlatMap(flatMap: (T) -> String): OrderCarrierChooseCarDialog<T> {
        this.flatMap = flatMap
        return this
    }
    fun setNoCheck(noCheck: (T) -> Boolean): OrderCarrierChooseCarDialog<T> {
        this.noCheck = noCheck
        return this
    }

    fun setNoCheckTxt(noCheckTxt: (T) -> String): OrderCarrierChooseCarDialog<T> {
        this.noCheckTxt = noCheckTxt
        return this
    }

    fun setFlatShowLogo(flatShowLogo: (T) -> Boolean): OrderCarrierChooseCarDialog<T> {
        this.flatShowLogo = flatShowLogo
        return this
    }

    fun setSelectItem(content: T?): OrderCarrierChooseCarDialog<T> {
        if (content != null) {
            this.selectItem = content
        }
        return this
    }

    fun setChooseListener(func: (T, BaseDialog) -> Unit): OrderCarrierChooseCarDialog<T> {
        this.chooseListener = func
        return this
    }


}