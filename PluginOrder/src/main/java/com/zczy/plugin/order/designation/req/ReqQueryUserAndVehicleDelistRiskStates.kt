package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 注释：承运方查询司机车辆风险状态
 * 时间：2024/11/20 8:47
 * 作者：孙飞虎
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=74974130
 * 对接：陈泰
 */
 class ReqQueryUserAndVehicleDelistRiskStates(
        /**
         * 运单号
         */
        var orderId: String = "",
        /**
         *司机id
         */
        var driverUserId: String = "",
        /**
         *车辆id
         */
        var vehicleId: String = "",
) : BaseOrderRequest<BaseRsp<ReqUserAndVehicleStates>>("oms-app/carrier/common/queryUserAndVehicleDelistRiskStates")

data class ReqUserAndVehicleStates(
    var userAndVehicleRiskStates: String="", //	0 没有风险 1 有风险
    var riskLimitationDelistFlag: String="", //0 不限制 1 限制
    var isOperation: String="",//0 否 1是
    var handlePage: String="",//1-会员 2-车辆
):ResultData()

