package com.zczy.plugin.order.stevedore;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.core.content.ContextCompat;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.zczy.comm.config.RouterConfig;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.plugin.order.R;

/**
 * 功能描述:成功
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
@Route(path = RouterConfig.StevedoreSuccessRouter.PATH)
public class StevedoreSuccessActivity extends FragmentActivity {


    public static void startContentUI(Context context) {

        Intent intent = new Intent (context, StevedoreSuccessActivity.class);
        context.startActivity (intent);
    }


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate (savedInstanceState);
        this.setContentView (R.layout.order_stevedore_success_activity);
        UtilStatus.initStatus (this, ContextCompat.getColor (this,R.color.comm_title_bg));
        findViewById (R.id.tv_success_ok).setOnClickListener (new View.OnClickListener () {

            @Override
            public void onClick(View v) {
                finish ();
            }
        });
    }
}
