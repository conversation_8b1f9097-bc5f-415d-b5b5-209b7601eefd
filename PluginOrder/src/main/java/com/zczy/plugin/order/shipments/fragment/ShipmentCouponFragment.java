package com.zczy.plugin.order.shipments.fragment;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleFragment;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.shipments.adapter.ShipmentCouponAdapter;
import com.zczy.plugin.order.shipments.model.ShipmentsCouponModel;
import com.zczy.plugin.order.shipments.model.request.ReqQueryCarrierAdvanceCouponList;
import com.zczy.plugin.order.source.pick.entity.EPickUserCoupon;


/**
 * 功能描述:发货优惠卷选择
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/19
 */
public class ShipmentCouponFragment extends AbstractLifecycleFragment<ShipmentsCouponModel> implements View.OnClickListener, OnLoadingListener, BaseQuickAdapter.OnItemClickListener, BaseQuickAdapter.OnItemChildClickListener {


    /**
     * @param type
     * @param orderId  订单id
     * @param moneyDou 服务费金额
     * @return
     */
    public static ShipmentCouponFragment start(String type, String orderId, String userCouponIds,   String pbCarrierMoney, double moneyDou,String forcePayFlag) {

        ShipmentCouponFragment fragment = new ShipmentCouponFragment();
        Bundle data = new Bundle();
        data.putString("orderId", orderId);
        data.putString("userCouponIds", userCouponIds);
        data.putString("type", type);
        data.putString("pbCarrierMoney", pbCarrierMoney);
        data.putDouble("moneyDou", moneyDou);
        data.putString("forcePayFlag", forcePayFlag);
        fragment.setArguments(data);
        return fragment;
    }

    SwipeRefreshMoreLayout swipeRefreshMoreLayout;

    TextView tv_selectTtile;

    ShipmentCouponAdapter itemAdapter;

    Button btOk;

    String orderId;

    String type;
    //  服务费金额
    double moneyDou;
    /***上次选择的优惠券ID*/
    String userLastCheckCouponIds;
    String forcePayFlag;
    String pbCarrierMoney;

    @Override
    public int getLayout() {

        return R.layout.order_pick_coupon_fragment;
    }

    @Override
    public void initData(View view) {

        tv_selectTtile = view.findViewById(R.id.tv_selectTtile);
        swipeRefreshMoreLayout = view.findViewById(R.id.swipe_refresh_more_layout);
        btOk = view.findViewById(R.id.bt_ok);


        Bundle data = getArguments();
        if (data != null) {
            orderId = data.getString("orderId");
            type = data.getString("type");
            moneyDou = data.getDouble("moneyDou", 0.0);
            pbCarrierMoney  = data.getString("pbCarrierMoney");
            forcePayFlag = data.getString("forcePayFlag");

            itemAdapter = new ShipmentCouponAdapter();
            swipeRefreshMoreLayout.setAdapter(itemAdapter, true);
            swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creatorDef(getContext()));
            swipeRefreshMoreLayout.setOnLoadListener(this);

            if (TextUtils.equals(ReqQueryCarrierAdvanceCouponList.TYPE_C, type)) {
                //可以使用
                userLastCheckCouponIds = data.getString("userCouponIds");
                tv_selectTtile.setVisibility(View.VISIBLE);
                itemAdapter.setUser(true);
                swipeRefreshMoreLayout.addOnItemListener(this);
                swipeRefreshMoreLayout.addOnItemChildClickListener(this);
                btOk.setOnClickListener(this);
                btOk.setVisibility(View.VISIBLE);
            }
        }
    }

    @Override
    public void onResume() {

        super.onResume();
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch
    public void onQueryUserCouponListSuccess(PageList<EPickUserCoupon> page) {

        swipeRefreshMoreLayout.onRefreshCompale(page);
        if (TextUtils.equals(ReqQueryCarrierAdvanceCouponList.TYPE_C, type)&& !TextUtils.isEmpty(userLastCheckCouponIds)) {
            //可以使用
            for (EPickUserCoupon coupon : itemAdapter.getData()) {
                if (TextUtils.equals(coupon.getUserCouponId(), userLastCheckCouponIds)) {
                    itemAdapter.setSelect(coupon);
                    break;
                }
            }
        }
    }

    private void showSelectUI() {
        String userCouponId = this.plusmoney();
        if (TextUtils.isEmpty(userCouponId)) {
            tv_selectTtile.setText("请选择优惠券");
            tv_selectTtile.setBackgroundColor(Color.parseColor("#f0eff5"));
        } else {
            tv_selectTtile.setBackgroundColor(Color.parseColor("#f7f4e0"));
            tv_selectTtile.setText(Html.fromHtml("<font color=\"#3c75ed\">您已选中" + itemAdapter.getSelectArray().size() + "张，合计</font>" + "<font color=\"#fd382d\"> ¥ " + plusmoney + "</font>"));

        }
    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {

        this.onItemClick(adapter, view, position);
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
        userLastCheckCouponIds = "";
        itemAdapter.setSelect(position);
        this.showSelectUI();
    }


    @Override
    public void onRefreshUI(int nowPage) {

        this.onLoadMoreUI(nowPage);
    }

    @Override
    public void onLoadMoreUI(int nowPage) {

        getViewModel().queryCarrierAdvanceCouponList(nowPage, type,pbCarrierMoney, String.valueOf(moneyDou) ,userLastCheckCouponIds,forcePayFlag);
    }

    @Override
    public void onClick(View v) {

        Intent data = new Intent();
        String userCouponId = this.plusmoney();

        if (TextUtils.isEmpty(userCouponId)) {
            data.putExtra("ID", "");
            data.putExtra("MONEY", 0.0f);

        } else {
            data.putExtra("ID", userCouponId);
            data.putExtra("MONEY", plusmoney);
        }

        getActivity().setResult(Activity.RESULT_OK, data);
        getActivity().finish();
    }

    double plusmoney = 0.0f;

    private String plusmoney() {
        plusmoney = 0.0f;
        StringBuilder userCouponId = new StringBuilder(70);
        int size = itemAdapter.getSelectArray().size();
        for (int i = 0; i < size; i++) {
            EPickUserCoupon item = itemAdapter.getSelectArray().valueAt(i);
            plusmoney += item.computeMoney(moneyDou);
            userCouponId.append(item.getUserCouponId());
            if (i < size - 1) {
                userCouponId.append(",");
            }
        }
        return userCouponId.toString();
    }

}
