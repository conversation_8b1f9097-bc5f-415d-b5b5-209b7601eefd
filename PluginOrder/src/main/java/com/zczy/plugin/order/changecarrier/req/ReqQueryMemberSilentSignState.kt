package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 查询是否需要签署电子签
 */
class ReqQueryMemberSilentSignState(
    var userId: String?
) :
    BaseOrderRequest<BaseRsp<RspQueryMemberSilentSignState>>("mms-app/member/queryMemberSilentSignState")

data class RspQueryMemberSilentSignState(
    var silentSignState: String? = "",//电子签状态 0 不需要 1 需要
    var faceAuthState: String? = "",//人脸认证是否通过，1通过，0未通过
) : ResultData()