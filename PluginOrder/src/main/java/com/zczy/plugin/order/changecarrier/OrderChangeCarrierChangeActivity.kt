package com.zczy.plugin.order.changecarrier

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.certificate.UserAuthentActivity
import com.zczy.certificate.driver.DriverEmploymentRiskActivity
import com.zczy.certificate.vehiclemanage.carrier.CarrierCarRiskActivityV1
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.data.ERNToNativeEvent
import com.zczy.comm.data.help.getLoginInfo
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.pluginserver.AOrderServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.isNull
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.x5.X5WebActivity
import com.zczy.lib_zstatistics.sdk.ZStatistics
import com.zczy.overdue.boss.BossExpiredCertificateManagementActivity
import com.zczy.overdue.cyr.CyrExpiredCertificateManagementActivity
import com.zczy.overdue.cys.CysExpiredCertificateManagementActivity
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.RxEventChange
import com.zczy.plugin.order.changecarrier.dialog.OrderCarrierChooseCarDialog
import com.zczy.plugin.order.changecarrier.fragment.OrderPickOfferOilView
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierChangeModel
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck
import com.zczy.plugin.order.source.pick.model.request.ReqCheckDelistOrderDocumentExpire
import com.zczy.plugin.order.source.pick.model.request.RspCheckDelistOrderDocumentExpire
import com.zczy.plugin.order.violate.OrderViolateMainActivity
import io.reactivex.Observable
import kotlinx.android.synthetic.main.order_change_carrier_change_activity.*
import java.math.BigDecimal

/**
 * 变更承运 变更
 */
class OrderChangeCarrierChangeActivity : BaseActivity<OrderChangeCarrierChangeModel>() {

    private val mOrderId by lazy { intent.getStringExtra(EXTRA_ORDER_ID) ?: "" }
    private val consignorUserId by lazy { intent.getStringExtra("consignorUserId") ?: "" }
    private val req: Req3ChangeOne = Req3ChangeOne()
    private val reqVertifyCarType: VertifyCarTypeBeanReq = VertifyCarTypeBeanReq()

    private val mAppToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val viewHelp by lazy { findViewById<View>(R.id.view_help) }

    //    private val viewDivider by lazy { findViewById<View>(R.id.view_divider) }
    private val chooseCarrier by lazy { findViewById<InputViewClick>(R.id.choose_carrier) }
    private val chooseCar by lazy { findViewById<InputViewClick>(R.id.choose_car) }

    private val btnCommit by lazy { findViewById<TextView>(R.id.btn_commit) }
    private val tvOverLoad by lazy { findViewById<TextView>(R.id.tv_over_load) }

    private val viewHInt by lazy { findViewById<View>(R.id.view_hint) }

    private var rspChangeOverLoad: RspChangeOverLoad = RspChangeOverLoad()

    // 是否零担运单
    private val isZeroAssume by lazy { intent.getBooleanExtra("isZeroAssume", false) }

    private val ly_noChoose_carrier by lazy { findViewById<View>(R.id.ly_noChoose_carrier) }
    private val orderPickOfferOilView by lazy { findViewById<OrderPickOfferOilView>(R.id.orderPickOfferOilView) }

    private var standard: String? = ""
    private var esignFlag: String = "0"

    private var dialog: BaseDialog? = null

    var relation = CommServer.getUserServer().login.relation

    companion object {
        private const val EXTRA_ORDER_ID = "extra_order_id"
        private const val REQUEST_CARRIER = 0x32
        private const val REQUEST_CAR = 0x33

        @JvmStatic
        @JvmOverloads
        fun start(
            fragment: Fragment,
            orderId: String,
            consignorUserId: String,
            isZeroAssume: Boolean,
            requestCode: Int = -1
        ) {
            val intent = Intent(fragment.context, OrderChangeCarrierChangeActivity::class.java)
            intent.putExtra(EXTRA_ORDER_ID, orderId)
            intent.putExtra("consignorUserId", consignorUserId)
            intent.putExtra("isZeroAssume", isZeroAssume)
            fragment.startActivityForResult(intent, requestCode)
        }
    }


    override fun getLayout(): Int = R.layout.order_change_carrier_change_activity

    override fun bindView(bundle: Bundle?) {
        /*
        * 变更承运，初始状态配置
        * */
        changemine.isSelected = true
        changother.isSelected = false
        choose_carrier.visibility = View.GONE
        ly_noChoose_carrier.visibility = View.GONE
        tv_boss_select_driver.visibility = View.GONE
        val relation = CommServer.getUserServer()?.login?.relation
        if (relation != null) {
            viewHInt.setVisible(relation.isCarrier)
            if (isZeroAssume && relation.isCarrier) {
                changemine.isSelected = false
                changemine.setVisible(false)
            } else {
                changemine.setVisible(true)
            }
        } else {
            viewHInt.setVisible(false)
        }
        chooseCarrier.tvTitle.typeface = Typeface.DEFAULT_BOLD
        chooseCarrier.setListener(listener)
        chooseCar.tvTitle.typeface = Typeface.DEFAULT_BOLD
        chooseCar.setListener(listener)
        changemine.setOnClickListener {
            if (changemine.isSelected) {
                return@setOnClickListener
            } else {
                changemine.isSelected = true
                changother.isSelected = false
                choose_carrier.visibility = View.GONE
                ly_noChoose_carrier.visibility = View.GONE
                chooseCar.content = ""
                chooseCarrier.content = ""
                req.plateNumber = ""
                req.vehicleId = ""
                req.friendId = ""
                reqVertifyCarType.vehicleId = ""
            }
        }
        changother.setOnClickListener {
            if (changother.isSelected) {
                return@setOnClickListener
            } else {
                changemine.isSelected = false
                changother.isSelected = true
                choose_carrier.visibility = View.VISIBLE
                chooseCar.content = ""
                chooseCarrier.content = ""
                req.plateNumber = ""
                req.vehicleId = ""
                req.friendId = ""
                reqVertifyCarType.vehicleId = ""

                //变更查询该运单是否有人身安全险
                getViewModel(OrderChangeCarrierChangeModel::class.java).queryOrderHavePolicy(
                    mOrderId
                )
                viewModel.queryBeforeChangeDriverAndVehicle(mOrderId)
            }
            ZStatistics.onViewClick("ydybgcy", "ydybgcy_click01")
        }
        mAppToolber.setRightOnClickListener {
            //点击客服
            val mainServer = AMainServer.getPluginServer()
            mainServer?.openLineServerHaveParams(
                this@OrderChangeCarrierChangeActivity,
                "{\"customField12\":\"变更承运\"}"
            )
            ZStatistics.onViewClick(this@OrderChangeCarrierChangeActivity, "ChangeTransfer_service")

        }
        mAppToolber.setLeftOnClickListener {
            ZStatistics.onViewClick(this@OrderChangeCarrierChangeActivity, "ChangeTransfer_back")
            finish()
        }

        bindClickEvent(btnCommit)
        bindClickEvent(viewHelp)

        val changes1 = RxTextView.textChanges(chooseCarrier.contentView)
        val changes2 = RxTextView.textChanges(chooseCar.contentView)
        Observable
            .combineLatest(
                changes1, changes2
            ) { t1, t2 ->
                if (changother.isSelected) {
                    t1.isNotEmpty() && t2.isNotEmpty()
                } else {
                    t1.isNotEmpty() || t2.isNotEmpty()
                }
            }
            .subscribe { b ->

                btnCommit.isEnabled = b
            }
            .let {
                putDisposable(it)
            }
    }

    override fun initData() {
        req.orderId = mOrderId
        viewModel?.onChangeOverLoad(mOrderId)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.view_help -> {
                gotoHelp()
            }

            R.id.btn_commit -> {
                ZStatistics.onViewClick(
                    "ydybgcy",
                    "ydybgcy_click04"
                )
                orderPickOfferOilView?.setReqOilData(req)

                if (CommServer.getUserServer().login.relation.isCys || (CommServer.getUserServer().login.relation.isCarrier && changother.isSelected)) {
                    //承运商 && 司机变更他人 不走电子签逻辑
                    viewModel?.vertifyCarType(
                        mOrderId,
                        reqVertifyCarType.vehicleId,
                        reqVertifyCarType.driverUserId
                    )
                    return
                }
                //查询是否需要静默签
                getViewModel(BaseViewModel::class.java).execute(
                    true,
                    ReqQueryMemberSilentSignState(userId = CommServer.getUserServer().login.userId)
                ) { t ->
                    if (t.success()) {
                        if (TextUtils.equals("1", t.data!!.silentSignState)) {
                            val dialog = DialogBuilder()
                            dialog.title = "温馨提示"
                            dialog.message = "您尚未完成电签认证不可进行业务，请优先完成电签认证"
                            dialog.isHideCancel = true
                            dialog.setOKTextListener("前往电签认证") { dialog: DialogBuilder.DialogInterface, _: Int ->
                                dialog.dismiss()
                                AMainServer.getPluginServer().changeMenuToEvent(
                                    this@OrderChangeCarrierChangeActivity,
                                    AMainServer.MENU_USER,
                                    500,
                                    ERNToNativeEvent(type = "openElectorn")
                                )
                            }
                            showDialog(dialog)
                        } else {
                            viewModel?.vertifyCarType(
                                mOrderId,
                                reqVertifyCarType.vehicleId,
                                reqVertifyCarType.driverUserId
                            )
                        }
                    } else {
                        showToast(t.msg)
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_CARRIER -> {
                    val obtainData = OrderChangeCarrierPeopleListActivity.obtainData(data)
                    req.friendId = obtainData.friendId
//                    if (!TextUtils.equals(chooseCarrier.content, obtainData.customerName)) {
//                        chooseCar.content = ""
//                        req.plateNumber = ""
//                        req.vehicleId = ""
//                        reqVertifyCarType.vehicleId = ""
//                    }
                    chooseCarrier.content = obtainData.customerName
                    reqVertifyCarType.driverUserId = obtainData.friendId

                    //ZCZY-7178  车老板提现流程优化
                    val relation = CommServer.getUserServer().login.relation
                    val isBoss = relation.isBoss
                    if (isBoss && TextUtils.equals("0", obtainData.carrierFinalPass)) {
                        tv_boss_select_driver.setVisible(true)
                    } else {
                        tv_boss_select_driver.setVisible(false)
                    }
                    if (relation.isBoss || relation.isCys) {
                        viewModel?.queryCarrierOrderCarpooling(
                            req = ReqCheckDelistOrderDocumentExpire(
                                driverUserId = obtainData.friendId
                            )
                        )
                    }
                    //ZCZY-14730 证件过期管控优化-业务部分
                    queryCarrierLicenseTransitionPeriod(obtainData.friendId, "", "")
                    viewModel?.queryChangePageInfoV2(mOrderId, obtainData.friendId, 1)
                }

                REQUEST_CAR -> {
                    val obtainData = OrderChangeCarrierChooseAllCarActivity.obtainData(data)
                    chooseCar.content = obtainData?.plateNumber ?: ""
                    req.plateNumber = obtainData?.plateNumber ?: ""
                    req.vehicleId = obtainData?.vehicleId ?: ""
                    reqVertifyCarType.vehicleId = obtainData?.vehicleId ?: ""
                    if (isZeroAssume) {
                        //零担货
                        getViewModel(OrderChangeCarrierChangeModel::class.java).checkVehicleCarpooling(
                            req = ReqCheckVehicleCarpooling(
                                orderCarpoolingId = mOrderId,
                                plateNumber = obtainData?.plateNumber
                            )
                        ) {
                            runOnUiThread {
                                if (it.batchPromptFlag.isTrue) {
                                    val dialogBuilder = DialogBuilder()
                                    dialogBuilder.title = "提示"
                                    dialogBuilder.message = it.resultMsg
                                    dialogBuilder.gravity = Gravity.CENTER
                                    dialogBuilder.isHideCancel = true
                                    dialogBuilder.cancelText = "好的"
                                    dialogBuilder.setOKText("确定")
                                    dialogBuilder.okListener =
                                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                                            dialog?.dismiss()
                                        }
                                    showDialog(dialogBuilder)
                                } else {
                                    viewModel?.queryCarrierOrderCarpooling(
                                        req = ReqCheckDelistOrderDocumentExpire(
                                            vehicleId = obtainData?.vehicleId ?: ""
                                        )
                                    )
                                }
                            }
                        }
                    } else {
                        viewModel?.queryCarrierOrderCarpooling(
                            req = ReqCheckDelistOrderDocumentExpire(
                                vehicleId = obtainData?.vehicleId ?: ""
                            )
                        )
                        //切换车辆，油品匹配
                        orderPickOfferOilView?.changeCar(
                            viewModel,
                            mOrderId,
                            obtainData?.fuelType ?: "",
                            obtainData?.fuelTypeStr ?: ""
                        )
                    }
                    //ZCZY-14730 证件过期管控优化-业务部分
                    queryCarrierLicenseTransitionPeriod(
                        "",
                        obtainData?.vehicleId ?: "",
                        obtainData?.plateNumber ?: ""
                    )
                }
            }
        }
    }

    private fun gotoHelp() {
        val url =
            HttpURLConfig.getWebUrl() + "form_h5/order/index.html?_t=" + System.currentTimeMillis() + "#/changeCarrierHelp"
        X5WebActivity.start(this, url, "使用帮助")
    }


    private val listener = object : InputViewClick.Listener() {
        override fun onClick(viewId: Int, view: InputViewClick, content: String) {
            when (viewId) {
                R.id.choose_carrier -> {
                    OrderChangeCarrierPeopleListActivity.start(
                        this@OrderChangeCarrierChangeActivity,
                        orderId = mOrderId,
                        requestCode = REQUEST_CARRIER
                    )
                    ZStatistics.onViewClick(
                        "ydybgcy",
                        "ydybgcy_click02"
                    )
                }

                R.id.choose_car -> {

                    viewModel?.queryChangePageInfo(mOrderId, getFriendId(), 1)

                    ZStatistics.onViewClick(
                        "ydybgcy",
                        "ydybgcy_click03"
                    )
                }
            }
        }
    }

    @LiveDataMatch
    open fun queryCarrierOrderCarpoolingSuccess(data: RspCheckDelistOrderDocumentExpire) {
        if (data.vehicleDocumentExpire.isTrue || data.userDocumentExpire.isTrue) {
            //用户证件过期
            val dialogBuilder = DialogBuilder()
            dialogBuilder.title = "提示"
            dialogBuilder.message = data.resultMsg
            dialogBuilder.gravity = Gravity.CENTER
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKTextListener("我知道了") { dialog: DialogBuilder.DialogInterface, _: Int -> dialog.dismiss() }
            showDialog(dialogBuilder)
        }
    }

    @LiveDataMatch
    open fun onGetVehicleEmissionStandard(
        data: RspVehicleEmissionStandard?,
        vehicle: RspVehicleData?
    ) {

        dialog?.dismiss()
        chooseCar.content = vehicle?.plateNumber.toString()
        req.plateNumber = vehicle?.plateNumber.toString()
        req.vehicleId = vehicle?.vehicleId.toString()
        reqVertifyCarType.vehicleId = vehicle?.vehicleId.toString()
        vehicle?.vehicleLoad?.let { setTvOverLoad(it) }
        viewModel?.queryCarrierOrderCarpooling(
            req = ReqCheckDelistOrderDocumentExpire(
                vehicleId = vehicle?.vehicleId
            )
        )
        //ZCZY-14730 证件过期管控优化-业务部分
        vehicle?.let {
            queryCarrierLicenseTransitionPeriod(
                "",
                it.vehicleId,
                it.plateNumber
            )
        }
    }

    @LiveDataMatch
    open fun onQueryChangePageInfoV2(data: RspPageCar?) {
        if (data == null || data.rootArray == null) return
        val find = data.rootArray?.find { it.plateNumber == req.plateNumber }
        if (find != null) {
            req.vehicleId = find.vehicleId
            reqVertifyCarType.vehicleId = find.vehicleId
        } else {
            chooseCar.content = ""
            req.plateNumber = ""
            req.vehicleId = ""
            reqVertifyCarType.vehicleId = ""
        }
    }

    @LiveDataMatch
    open fun onQueryChangePageInfo(data: RspPageCar?) {
        data?.let { d ->
            if (d.rootArray.isNullOrEmpty()) {
                showAlerDialog()
                return
            }
            val find = d.rootArray.find { it.vehicleId == req.vehicleId }
            if (d.totalSize > 9) {
                OrderChangeCarrierChooseAllCarActivity.start(
                    this@OrderChangeCarrierChangeActivity,
                    mOrderId,
                    getFriendId(),
                    find,
                    REQUEST_CAR,
                    changemine.isSelected
                )
            } else {
                OrderCarrierChooseCarDialog
                    .instance(data.rootArray, true, true)
                    .setSelectItem(find)
                    .setFlatMap { it.plateNumber }
                    .setToastText(
                        if (TextUtils.equals("1", data.isNeedHandle)) {
                            "您本月交易车辆已超出平台当前角色管控要求，请选择可选车辆，如有疑问可咨询平台客服。"
                        } else {
                            ""
                        }
                    )
                    .setNoCheck { TextUtils.equals("0", it.isChoose) }
                    .setNoCheckTxt {
                        if (TextUtils.equals("0", it.isChoose)) {
                            "4"
                        } else {
                            ""
                        }
                    }
                    .setChooseListener { s, dialog ->
                        this.dialog = dialog
                        val req = ReqAssignVehicleTransportCheck()
                        req.plateNumber = s.plateNumber
                        viewModel.assignVehicleTransportCheck(req, {
                            if (TextUtils.equals("1", it.data?.isTransportFlag)) {
                                val dialog2 = DialogBuilder()
                                dialog2.title = "提示"
                                dialog2.message = it.msg
                                dialog2.isHideCancel = true
                                dialog2.setOKTextListener("我知道了") { dialog1: DialogBuilder.DialogInterface, which: Int ->
                                    dialog1.dismiss()
                                    selectCarStep0(s, dialog)
                                }
                                showDialog(dialog2)
                            } else {
                                selectCarStep0(s, dialog)
                            }
                        }, {
                            selectCarStep0(s, dialog)
                        })

                    }
                    .show(this@OrderChangeCarrierChangeActivity)
            }
        } ?: run {
            if (changother.isSelected) {
                //变更他人
                val dialogBuilder = DialogBuilder()
                dialogBuilder.setTitle("提示")
                dialogBuilder.setMessage("无可用车辆，请先联系对应承运方添加车辆！")
                dialogBuilder.setGravity(Gravity.CENTER)
                dialogBuilder.setHideCancel(true)
                dialogBuilder.setOKTextListener("我知道了") { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()
                }
                showDialog(dialogBuilder)
            }
            if (changemine.isSelected) {
                //自己承运变更车
                val dialogBuilder = DialogBuilder()
                dialogBuilder.setTitle("提示")
                dialogBuilder.setMessage("无可用车辆，请先添加车辆！")
                dialogBuilder.setCancelText("关闭")
                dialogBuilder.setGravity(Gravity.CENTER)
                dialogBuilder.setOKTextListener("添加车辆") { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()
                    val server = AOrderServer.getPluginServer()
                    server?.gotoVehicleManagement(this@OrderChangeCarrierChangeActivity)
                }
                showDialog(dialogBuilder)
            }
        }
    }

    private fun showAlerDialog() {
        if (changother.isSelected) {
            //变更他人
            val dialogBuilder = DialogBuilder()
            dialogBuilder.setTitle("提示")
            dialogBuilder.setMessage("无可用车辆，请先联系对应承运方添加车辆！")
            dialogBuilder.setGravity(Gravity.CENTER)
            dialogBuilder.setHideCancel(true)
            dialogBuilder.setOKTextListener("我知道了") { dialog: DialogBuilder.DialogInterface, which: Int ->
                dialog.dismiss()
            }
            showDialog(dialogBuilder)
        }
        if (changemine.isSelected) {
            //自己承运变更车
            val dialogBuilder = DialogBuilder()
            dialogBuilder.setTitle("提示")
            dialogBuilder.setMessage("无可用车辆，请先添加车辆！")
            dialogBuilder.setGravity(Gravity.CENTER)
            dialogBuilder.setCancelText("关闭")
            dialogBuilder.setOKTextListener("添加车辆") { dialog: DialogBuilder.DialogInterface, which: Int ->
                dialog.dismiss()
                val server = AOrderServer.getPluginServer()
                server?.gotoVehicleManagement(this@OrderChangeCarrierChangeActivity)
            }
            showDialog(dialogBuilder)
        }
    }

    private fun selectCarStep0(s: RspVehicleData, dialog: BaseDialog) {
        if (isZeroAssume) {
            //零担货
            getViewModel(OrderChangeCarrierChangeModel::class.java).checkVehicleCarpooling(
                req = ReqCheckVehicleCarpooling(
                    orderCarpoolingId = mOrderId,
                    plateNumber = s.plateNumber
                )
            ) {
                runOnUiThread {
                    if (it.batchPromptFlag.isTrue) {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = it.resultMsg
                        dialogBuilder.gravity = Gravity.CENTER
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.cancelText = "好的"
                        dialogBuilder.setOKText("确定")
                        dialogBuilder.okListener =
                            DialogBuilder.DialogInterface.OnClickListener { d, _ ->
                                d.dismiss()
                                queryEmissionStandard(s, dialog)
                            }
                        showDialog(dialogBuilder)
                    } else {
                        queryEmissionStandard(s, dialog)
                    }
                }
            }
        } else {
            queryEmissionStandard(s, dialog)
        }
    }

    private fun queryEmissionStandard(s: RspVehicleData, dialog: BaseDialog) {
        //货主配置车辆排放类型
        val req = ReqQueryDelistOrderConsignorrConfig(
            orderId = mOrderId,
            fuelType = s.fuelType,
            newEnergyType = s.newEnergyType,
        )
        viewModel.ReqQueryDelistOrderConsignorrConfig(req) {
            standard = it
            selectCarStep1(s, dialog)
        }
        //切换车辆，油品匹配
        orderPickOfferOilView?.changeCar(
            viewModel,
            mOrderId,
            s?.fuelType ?: "",
            s?.fuelTypeStr ?: ""
        )
    }

    private fun selectCarStep1(s: RspVehicleData, dialog: BaseDialog) {
        dialog.dismiss()
        chooseCar.content = s.plateNumber
        req.plateNumber = s.plateNumber
        req.vehicleId = s.vehicleId
        reqVertifyCarType.vehicleId = s.vehicleId
        setTvOverLoad(s.vehicleLoad)
        viewModel?.queryCarrierOrderCarpooling(
            req = ReqCheckDelistOrderDocumentExpire(
                vehicleId = s.vehicleId
            )
        )
        //ZCZY-14730 证件过期管控优化-业务部分
        queryCarrierLicenseTransitionPeriod(
            "",
            s.vehicleId,
            s.plateNumber
        )
    }

    @LiveDataMatch
    open fun onChangeOne() {
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onQueryDelistOrderConsignorrConfig(data: ReqEDelistOrderConsignorrConfig?) {
        if (data != null) {
            standard = data.allowDieselCarWithSpecifiedStandardsForTransport
        }
    }

    @LiveDataMatch
    open fun onChangeOneError(data: Rsp3ChangeOne?, msg: String) {
        data?.let {
            // "1"-待货主处理，"2"-待承运方处理，"3"-平台介入中，"4"-咨询单介入中
            when (it.orderBreachType) {
                "2" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("去处理")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "1" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("去取消")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "3" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("查看详情")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "4" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("我知道了")
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    showDialog(dialogBuilder)
                }

                else -> {
                    //是否展示上传从业资格证按钮 1 展示 0 不展示
                    when (it.isShowUploadFlag) {
                        "1" -> {
                            //提示 有按钮
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "温馨提示"
                            dialogBuilder.message = msg
                            dialogBuilder.gravity = Gravity.CENTER
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("上传从业资格证")
                            dialogBuilder.okListener =
                                DialogBuilder.DialogInterface.OnClickListener { dialog: DialogBuilder.DialogInterface, which: Int ->
                                    dialog.dismiss()
                                    // 上传从业资格证
                                    DriverEmploymentRiskActivity.start(this)
                                }
                            showDialog(dialogBuilder)
                        }

                        else -> {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = msg
                            dialogBuilder.gravity = Gravity.CENTER
                            dialogBuilder.isCancelable = false
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("我知道了")
                            dialogBuilder.okListener =
                                DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                            showDialog(dialogBuilder)
                        }
                    }
                }
            }

        }
    }

    @LiveDataMatch
    open fun onCheckErrorState(data: Rsp3ChangeOne, msg: String) {
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "提示"
        dialogBuilder.message = msg
        val userErrorState = data.userErrorState
        val vehicleErrorState = data.vehicleErrorState

        //司机
        when (userErrorState) {
            "1" -> {
                //提交材料按钮
                dialogBuilder.setOKTextListener(
                    "去完善"
                ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()

                    if (TextUtils.equals("2", data.userVehicleType)) {
                        DriverEmploymentRiskActivity.startUi(this@OrderChangeCarrierChangeActivity)
                    } else {
                        UserAuthentActivity.start(this@OrderChangeCarrierChangeActivity)
                    }
                }
                showDialog(dialogBuilder)
            }

            "2" -> {
                //联系客服
                dialogBuilder.setOKTextListener(
                    "联系客服"
                ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()
                    AMainServer.getPluginServer()
                        .openLineServer(this@OrderChangeCarrierChangeActivity)
                }
                showDialog(dialogBuilder)
            }

            else -> {
                // 车辆状态
                when (vehicleErrorState) {
                    "1" -> {
                        //提交材料按钮
                        dialogBuilder.setOKTextListener(
                            "去完善"
                        ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                            dialog.dismiss()
                            CarrierCarRiskActivityV1.jumpPage(
                                this@OrderChangeCarrierChangeActivity,
                                req.vehicleId
                            )
                        }
                        showDialog(dialogBuilder)
                    }

                    "2" -> {
                        //联系客服
                        dialogBuilder.setOKTextListener(
                            "联系客服"
                        ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                            dialog.dismiss()
                            AMainServer.getPluginServer()
                                .openLineServer(this@OrderChangeCarrierChangeActivity)
                        }
                        showDialog(dialogBuilder)
                    }

                    else -> {
                        dialogBuilder.isHideCancel = true
                        showDialog(dialogBuilder)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun vertifyCarTypeSuccess(resp: VertifyCarTypeChangeBeanResp) {

        if (TextUtils.equals("2", resp.checkVehicleType)) {
            showCommDialog(resp.resultMsg)
        } else {
            continueLogic()
        }
    }

    private fun showCommDialog(message: String) {
        val dialog = DialogBuilder()
        dialog.isCancelable = false
        dialog.isHideCancel = false
        dialog.title = "提示"
        dialog.message = message
        dialog.setMessageGravity(message, Gravity.CENTER)
        dialog.setOKText("确认车辆")
        dialog.setOkListener { dialog, _ ->
            dialog.dismiss()
            continueLogic()
        }
        dialog.cancelText = "重新选择"
        dialog.setCancelListener() { dialog, _ ->

            viewModel?.queryChangePageInfo(mOrderId, getFriendId(), 1)
            ZStatistics.onViewClick(
                this@OrderChangeCarrierChangeActivity,
                "ChangeTransfer_change2"
            )
            dialog.dismiss()
        }
        showDialog(dialog)
    }

    private fun getFriendId(): String {
        if (chooseCarrier.content.isEmpty()) {
            val loginInfo = getLoginInfo()
            return loginInfo?.userId ?: ""
        } else {
            return req.friendId;
        }
    }

    private fun continueLogic() {
        val eSignFlag = CommServer.getUserServer().login.relation.isCys || (CommServer.getUserServer().login.relation.isCarrier && changother.isSelected)
        // 1：变更人  2：变更车  3：变更人和车
        req.consignorUserId = consignorUserId
        when {
            chooseCarrier.content.isEmpty() && chooseCar.content.isEmpty() -> {
                showToast("必须输入一项")
                return
            }

            chooseCarrier.content.isEmpty() -> {
                req.changeType = "2"
            }

            chooseCar.content.isEmpty() -> {
                req.changeType = "1"
            }

            else -> {
                req.changeType = "3"
            }
        }

        if (TextUtils.equals("2", req.changeType)) {
            //只变更车辆时调用，备案卸货变更模块相关接口
            viewModel?.checkPlateNumberBeforeChange(
                this@OrderChangeCarrierChangeActivity,
                ReqCheckPlateNumberBeforeChange(
                    changeType = req.changeType,
                    plateNumber = req.plateNumber, orderId = req.orderId ?: "",
                ), req, eSignFlag
            )
        } else {
            if (eSignFlag) {
                viewModel.changeOne(this@OrderChangeCarrierChangeActivity, req)
            } else {
                viewModel?.esignCheck(this@OrderChangeCarrierChangeActivity, req)
            }
        }
        ZStatistics.onViewClick(this@OrderChangeCarrierChangeActivity, "ChangeTransfer_confirm")
    }


    @LiveDataMatch
    open fun onChangeOverLoad(data: RspChangeOverLoad) {
        rspChangeOverLoad = data
    }

    @LiveDataMatch
    open fun onClearCar() {
        chooseCar.content = ""
        req.plateNumber = ""
        req.vehicleId = ""
        reqVertifyCarType.vehicleId = ""
    }

    @LiveDataMatch
    open fun onSetEsignFlag(esignFlag: String?) {
        if (!TextUtils.isEmpty(esignFlag)) {
            this.esignFlag = esignFlag ?: "0"
        }
    }

    /**
     * 进行重量校验
     *
     * @param actualWeight 货物吨位
     */
    @SuppressLint("SetTextI18n")
    fun setTvOverLoad(vehicleLoad: String) {
        val cargoCategory = rspChangeOverLoad.cargoCategory
        if (TextUtils.isEmpty(cargoCategory)) {
            return
        }
        if (!TextUtils.equals("1", cargoCategory)) {
            return
        }
        val actualWeight = rspChangeOverLoad.weight
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(vehicleLoad)) {
            return
        }
        val aDouble = actualWeight.toDouble()
        val bDouble = vehicleLoad.toDouble()
        val cDouble = aDouble - bDouble
        if (cDouble > 0) {
            this.tvOverLoad.visibility = View.VISIBLE
            val divide = divide(cDouble, bDouble, 2)
            if (divide > 30) {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb4040))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff4f8_rtg)
            } else {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb6b40))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff3f1_rtg)
            }
        } else {
            this.tvOverLoad.visibility = View.GONE
        }
    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    private fun divide(a: Double, b: Double, scale: Int): Long {
        val bd1 = BigDecimal(a.toString())
        val bd2 = BigDecimal(b.toString())
        val bd3 = bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).toDouble()
        return Math.round(bd3 * 100.00)
    }

    @LiveDataMatch
    open fun onQueryOrderHavePolicy() {
        //此单已购买人身安全保障服务, 不支持变更承运人
        ly_noChoose_carrier.visibility = View.VISIBLE
        choose_carrier.visibility = View.GONE
    }

    @RxBusEvent(from = "电子签后发起变更")
    open fun onRxEventBusH5(data: RxEventChange) {
        req.esignFlag = this.esignFlag
        viewModel?.changeOne(this@OrderChangeCarrierChangeActivity, req)
    }

    /**
     * 注释：ZCZY-14730 证件过期管控优化-业务部分
     * 时间：2023/12/12 0012 17:17
     * 作者：郭翰林
     */
    private fun queryCarrierLicenseTransitionPeriod(
        driverUserId: String,
        vehicleId: String,
        plateNumber: String
    ) {
        val req = ReqCheckCarrierAndVehicle()
        if (!TextUtils.isEmpty(driverUserId)) {
            req.changeType = "1"
            req.carrierUserId = driverUserId
        } else {
            req.changeType = "2"
            req.vehicleId = vehicleId
            req.plateNumber = plateNumber
        }

        viewModel.execute(true, req) {
            if (it.success()) {
                if (!TextUtils.isEmpty(vehicleId)) {
                    //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                    queryCheckCarrierAndVehicleCertificate(vehicleId, plateNumber)
                }

            } else if (TextUtils.equals("2001", it.code) || TextUtils.equals("1113", it.code)) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "提示"
                dialogBuilder.message = it.data?.resultMsg
                dialogBuilder.isHideCancel = true
                dialogBuilder.setOKText("我知道了")
                dialogBuilder.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    if (!TextUtils.isEmpty(vehicleId)) {
                        //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                        queryCheckCarrierAndVehicleCertificate(vehicleId, plateNumber)
                    }
                }
                showDialog(dialogBuilder)

            }
        }
    }

    /**
     * WLHY-9279	【安卓】【汽运】证件风险通过规则调整
     */
    private fun queryCheckCarrierAndVehicleCertificate(
        vehicleId: String,
        plateNumber: String
    ) {
        val reqCheck = ReqCheckCarrierAndVehicleCertificate()
        reqCheck.orderId = mOrderId;
        if (TextUtils.isEmpty(req.friendId)) {
            //2 车
            reqCheck.changeType = "2"
            reqCheck.vehicleId = vehicleId
            reqCheck.plateNumber = plateNumber
        } else {
            reqCheck.friendId = req.friendId
            reqCheck.changeType = "3"
            reqCheck.vehicleId = vehicleId
            reqCheck.plateNumber = plateNumber
        }
        viewModel.execute(reqCheck) {
            if (it.success()) {
                //0000-成功 1111系统异常错误信息 （有弹窗都是1111）
            } else if (TextUtils.equals("1111", it.code)) {
                it.data?.let {

                    if (TextUtils.equals("1", it.userAndVehicleRiskStates)) {

                        var dailog = DialogBuilder()
                        dailog.message = it.resultMsg

                        if (TextUtils.equals("1", it.riskLimitationDelistFlag)) {
                            //限制成交,清空选择车辆与人
                            this.chooseCar.content = ""
                            this.chooseCarrier.content = ""
                            this.req.plateNumber = ""
                            this.req.vehicleId = ""
                            this.req.friendId = ""
                            this.reqVertifyCarType.vehicleId = ""

                            if (TextUtils.equals("1", it.isOperation)) {
                                //完善资料
                                dailog.cancelText = "完善资料"
                                dailog.setCancelListener { dialog, which ->
                                    dialog.dismiss()
                                    gotoExpiredCertificateManagementActivity()
                                }
                                dailog.setOKText("暂不提交")
                            } else {
                                dailog.isHideCancel = true
                                dailog.setOKText("我知道了")
                            }
                        } else {
                            if (TextUtils.equals("1", it.isOperation)) {
                                //完善资料
                                dailog.cancelText = "完善资料"
                                dailog.setCancelListener { dialog, which ->
                                    dialog.dismiss()
                                    gotoExpiredCertificateManagementActivity()
                                }
                                dailog.setOKText("继续提交")
                            } else {
                                dailog.cancelText = "我知道了"
                                dailog.setOKText("继续提交")
                            }
                        }
                        showDialog(dailog)
                    } else {
                        showDialogToast(it.resultMsg)
                    }
                }
            }
        }
    }

    fun gotoExpiredCertificateManagementActivity() {
        //过期管理
        val relation = CommServer.getUserServer().login.relation
        if (relation.isCarrier) {
            CyrExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeActivity)
        } else if (relation.isBoss) {
            BossExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeActivity)
        } else if (relation.isCys) {
            CysExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeActivity)
        }
    }

    @LiveDataMatch
    open fun onQueryBeforeChangeDriverAndVehicle(data: RspQueryBeforeChangeDriverAndVehicle?) {
        chooseCar.content = data?.plateNumber ?: ""
        req.plateNumber = data?.plateNumber ?: ""
        req.vehicleId = data?.vehicleId ?: ""
        reqVertifyCarType.vehicleId = data?.vehicleId ?: ""
    }
}
