package com.zczy.plugin.order.stevedore.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.zczy.plugin.order.R;

import java.util.Collections;
import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ItemNameAdapter extends BaseAdapter {
    Context context;

    List<String> list;

    public ItemNameAdapter(Context context, List<String> list) {

        super ();
        this.context = context;
        this.list = list;
        if (this.list == null) {
            this.list = Collections.emptyList ();
        }
    }

    @Override
    public int getCount() {

        return this.list.size ();
    }

    @Override
    public String getItem(int position) {

        return this.list.get (position);
    }

    @Override
    public long getItemId(int position) {

        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        convertView = View.inflate (this.context, R.layout.order_bill_goodsinfo_fragment_adapter, null);
        TextView tv_left = convertView.findViewById (R.id.tv_left);
        TextView tv_info = convertView.findViewById (R.id.tv_info);
        if (getCount () > 1) {
            tv_left.setText ("货物明细(" + (position + 1) + ")");
        } else {
            tv_left.setText ("货物明细");
        }
        tv_info.setText (getItem (position));
        return convertView;
    }
}
