//package com.zczy.plugin.order.shipments.fragment;
//
//import android.util.SparseArray;
//
///**
// * 功能描述:满足交互条件
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public abstract class MeetInteractionListener {
//
//    private SparseArray<Boolean> array = new SparseArray<> (5);
//
//    /***
//     * 触发交互
//     * @param status true 满足 false 不满足
//     */
//    public void touchInteraction(String flag, boolean status) {
//
//        this.array.put (flag.hashCode (), status);
//        this.run ();
//    }
//
//    /***
//     * 整体交互结果
//     * @param status
//     */
//    public abstract void lastInteraction(boolean status);
//
//    private void run() {
//
//        final int size = array.size ();
//        if (size == 0) {
//            this.lastInteraction (false);
//            return;
//        }
//        for (int i = 0; i < size; i++) {
//            if (!(array.valueAt (i))) {
//                this.lastInteraction (false);
//                return;
//            }
//        }
//        this.lastInteraction (true);
//    }
//}
