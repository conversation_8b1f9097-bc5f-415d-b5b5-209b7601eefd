package com.zczy.plugin.order.shipments;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.utils.RouterUtils;
import com.zczy.comm.utils.SpannableHepler;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.PublicWelfareActivitiesView;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.libstyle.LanguageChangeUtil;
import com.zczy.libstyle.LanguageLinster;
import com.zczy.libstyle.LanguageTxt;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.bill.BillSuccessActivity;
import com.zczy.plugin.order.navigation.ActionNavigation;
import com.zczy.plugin.order.shipments.entity.EActivityInfo;
import com.zczy.plugin.order.shipments.entity.EAdvanceInfo;
import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
import com.zczy.plugin.order.shipments.model.request.ReqQueryDictConfigScore;
import com.zczy.plugin.order.shipments.model.request.RspQueryDictConfigScore;
import com.zczy.plugin.order.waybill.entity.EWaybill;
import com.zczy.plugin.order.waybill.model.WaybillCyrModel;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:确认发货/预付款申请 成功
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
public class ShipmentsSuccessActivity//extends AbstractLifecycleActivity<BaseViewModel> implements View.OnClickListener {
{

//    private String orderId;
//    private String endAddress;
//    private EWaybill mData;
//    private PublicWelfareActivitiesView publicWelfareActivitiesView;
//    private TextView tv_success_toast2;
//    private TextView tv_step_1;
//    private boolean isPreOperate = false;
//    private ELogin eLogin = CommServer.getUserServer().getLogin();


    /***
     *
     * @param context
     * @param type 0  普通/预付，1平台预付
     */
    public static void start(Context context, String type, String orderId, String advanceWay, ShipmentsEGoodInfo info, EAdvanceInfo advanceInfo, String scene) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("orderId", orderId);
        params.put("detailId", info.getDetailId());
        params.put("promptFlag", info.getPromptFlag());
        params.put("consignorPrompt", info.getConsignorPrompt());
        params.put("platFormCargoPrompt", info.getPlatFormCargoPrompt());
        params.put("scene", scene);
        if (info.getAddressInfoObj() != null && !TextUtils.isEmpty(info.getAddressInfoObj().getEndAddress())) {
            params.put("endAddress", info.getAddressInfoObj().getEndAddress());
        }
        if (TextUtils.equals("1", type) && !TextUtils.equals("2", advanceWay) && TextUtils.equals("1", advanceInfo.getActivityShowFlag())) {
            //预付 &&非油品预付 && 存在活动
            params.put("activityShowFlag", "1");
            params.put("activityInfo", JsonUtil.toJson(advanceInfo.getActivityInfo()));
        }
        if (TextUtils.equals("1", type)) {
            params.put("isAdvance", true);
        } else {
            if (TextUtils.equals("1", advanceInfo.getAdvanceType()) || TextUtils.equals("3", advanceInfo.getAdvanceType())) {
                params.put("isAdvance", true);
            } else if (TextUtils.equals("2", advanceInfo.getAdvanceType()) || TextUtils.equals("4", advanceInfo.getAdvanceType())) {
                if (TextUtils.equals(type, "1")) {
                    params.put("isAdvance", true);
                } else {
                    params.put("isAdvance", false);
                }
            } else {
                params.put("isAdvance", false);
            }
        }
        AMainServer.getPluginServer().openReactNativeActivity(context, "ShipmentsSuccessPage", JsonUtil.toJson(params));
    }
//
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.order_shipments_success_activity);
//        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
//        orderId = getIntent().getStringExtra("orderId");
//        endAddress = getIntent().getStringExtra("endAddress");
//        initView();
//        getViewModel(WaybillCyrModel.class).queryOrderInfoForAppSkip2(orderId);
//
//    }
//
//    private void initView() {
//
//        ImageView ivSuccessImage = findViewById(R.id.iv_success_image);
//
//        TextView mTvProcessToast = findViewById(R.id.tv_process_toast);
//
//        findViewById(R.id.tv_navigation).setOnClickListener(this);
//        publicWelfareActivitiesView = this.findViewById(R.id.publicWelfareActivitiesView);
//        tv_step_1 = findViewById(R.id.tv_step_1);
//        isPreOperate = TextUtils.equals("1", getIntent().getStringExtra("scene")) || TextUtils.equals("2", getIntent().getStringExtra("scene"));
//
//        //0  普通/预付，1平台预付
//        String type = getIntent().getStringExtra("type");
//        boolean isAdvance = getIntent().getBooleanExtra("isAdvance", false);
//        if (isAdvance) {
//            publicWelfareActivitiesView.setData("5", orderId);
//        }
//
//        AppToolber appToolber = findViewById(R.id.appToolber);
//        appToolber.setLeftOnClickListener(v -> {
//            ZStatistics.onViewClick(ShipmentsSuccessActivity.this, "shipments_result&back");
//            finish();
//        });
//
//
//        String promptFlag = getIntent().getStringExtra("promptFlag");
//        if (TextUtils.equals("1", promptFlag)) {
//            findViewById(R.id.cl_load_unload).setVisibility(View.VISIBLE);
//            //货主发单时的装卸货要求
//            TextView tv_toast_1 = findViewById(R.id.tv_toast_1);
//            String consignorPrompt = getIntent().getStringExtra("consignorPrompt");
//            if (!TextUtils.isEmpty(consignorPrompt)) {
//                tv_toast_1.setVisibility(View.VISIBLE);
//                tv_toast_1.setText(consignorPrompt);
//            } else {
//                tv_toast_1.setVisibility(View.GONE);
//            }
//            //平台货物的装卸货要求
//            String platFormCargoPrompt = getIntent().getStringExtra("platFormCargoPrompt");
//            TextView tv_toast_2 = findViewById(R.id.tv_toast_2);
//            if (!TextUtils.isEmpty(platFormCargoPrompt)) {
//                tv_toast_2.setVisibility(View.VISIBLE);
//                tv_toast_2.setText(platFormCargoPrompt);
//            } else {
//                tv_toast_2.setVisibility(View.GONE);
//            }
//        } else {
//            findViewById(R.id.cl_load_unload).setVisibility(View.GONE);
//        }
//
//        //ZCZY-12277 【加急】2023惊蛰（3.6）活动趣味转盘
//        String activityShowFlag = getIntent().getStringExtra("activityShowFlag");
//        if (TextUtils.equals("1", activityShowFlag)) {
//            EActivityInfo activityInfo = JsonUtil.toJsonObject(getIntent().getStringExtra("activityInfo"), EActivityInfo.class);
//            ActionDialog.showDialogUI(this, activityInfo);
//        }
//
//        if (TextUtils.equals("0", type)) {
//            mTvProcessToast.setText(LanguageChangeUtil.getTxt(LanguageTxt.SHIPMENT_B));
//            ivSuccessImage.setImageResource(R.drawable.order_success_icon);
//        } else {
//            mTvProcessToast.setText(LanguageChangeUtil.getTxt(LanguageTxt.SHIPMENT_F));
//            ivSuccessImage.setImageResource(R.drawable.base_tip_success);
//        }
//
//        new LanguageLinster()
//                .putTxt(appToolber.getTvTitle(), isPreOperate ? LanguageTxt.ADVANCE_A : (eLogin.getRelation().isCarrier() ? LanguageTxt.SHIPMENT_G : LanguageTxt.SHIPMENT_A))
//                .putTxt(this, R.id.tv_navigation, LanguageTxt.SHIPMENT_I)
//                .showOnlyOne();
//
//        getViewModel(WaybillCyrModel.class).execute(new ReqQueryDictConfigScore(), new IResult<BaseRsp<RspQueryDictConfigScore>>() {
//            @Override
//            public void onFail(HandleException e) {
//                showZPTxt();
//            }
//
//            @Override
//            public void onSuccess(BaseRsp<RspQueryDictConfigScore> rspQueryDictConfigScoreBaseRsp) throws Exception {
//                if (rspQueryDictConfigScoreBaseRsp.success() && TextUtils.equals("0", rspQueryDictConfigScoreBaseRsp.getData().getValue())) {
//                    showNewTxt();
//                } else {
//                    showZPTxt();
//                }
//            }
//        });
//
//        tv_success_toast2 = findViewById(R.id.tv_success_toast2);
//        tv_success_toast2.setVisibility(!TextUtils.equals("0", type) ? View.VISIBLE : View.GONE);
//        tv_success_toast2.setOnClickListener(v -> {
//            //跳转小程序
//            RouterUtils.skipMiniProgram(ShipmentsSuccessActivity.this, "gh_171fe8ef4ec4", "/packageIndex/publicQRCode/publicQRCode");
//            ZStatistics.onViewClick(ShipmentsSuccessActivity.this, "skipMiniProgram#gh_171fe8ef4ec4#/packageIndex/publicQRCode/publicQRCode");
//        });
//        if (isPreOperate || eLogin.getRelation().isCarrier()) {
//            tv_step_1.setText("确认装货");
//        }
//    }
//
//    private void showZPTxt() {
//
//        TextView tv_success_toast = findViewById(R.id.tv_success_toast);
//        SpannableHepler hepler = new SpannableHepler();
//        if (TextUtils.isEmpty(endAddress)) {
//            hepler.append("1、运输过程中，如您被告知");
//        } else {
//            hepler.append("1、您的运输目的地为：")
//                    .append(new SpannableHepler.Txt(endAddress, "#FF3434"))
//                    .append(",运输过程中，如您被告知");
//        }
//        hepler.append(new SpannableHepler.Txt("变更卸货目的地", "#FF3434"))
//                .append("未在平台APP收到变更申请，请及时")
//                .append(new SpannableHepler.Txt("联系平台客服", "#FF3434"))
//                .append("核实确认（")
//                .append(new SpannableHepler.Txt("4000885566", "#FF3434"))
//                .append("），谨防诈骗分子")
//                .append(new SpannableHepler.Txt("冒充货主、收货人诈骗。", "#FF3434"))
//                .append("未按平台要求操作，由此产生的损失将由您自行承担。")
//                .append("\n2、为保障您的权益，请您在运输途中")
//                .append(new SpannableHepler.Txt("全程保持APP前台运行", "#5086FC"))
//                .append(",到达目的地卸货后请及时【确认卸货】，否则会触发异常影响结算，如遇货主金额不足导致无法卸货请联系货主。");
//        tv_success_toast.setText(hepler.builder());
//        tv_success_toast2.setText(Html.fromHtml("3、您的预付申请需要平台审核，若想及时了解预付审核进度信息，请关注微信公众号【中储智运红运驿站】。<font color=\"#5086FC\">一键关注</font>"));
//    }
//
//    private void showNewTxt() {
//
//        TextView tv_success_toast = findViewById(R.id.tv_success_toast);
//        SpannableHepler hepler = new SpannableHepler();
//        hepler.append("1、为保障您的权益，请您在运输途中")
//                .append(new SpannableHepler.Txt("全程保持APP前台运行", "#5086FC"))
//                .append(",到达目的地卸货后请及时【确认卸货】，否则会触发异常影响结算，如遇货主金额不足导致无法卸货请联系货主。");
//        tv_success_toast.setText(hepler.builder());
//        TextView tv_success_title = findViewById(R.id.tv_success_title);
//        tv_success_title.setText(eLogin.getRelation().isCarrier() ? "装货成功！" : "发货成功！");
//        tv_success_title.setTypeface(Typeface.DEFAULT_BOLD);
//        tv_success_title.setBackgroundColor(Color.WHITE);
//        tv_success_title.setTextColor(Color.parseColor("#5086FC"));
//        tv_success_title.setVisibility(isPreOperate ? View.GONE : View.VISIBLE);
//
//        tv_success_toast2.setText(Html.fromHtml("2、您的预付申请需要平台审核，若想及时了解预付审核进度信息，请关注微信公众号【中储智运红运驿站】。<font color=\"#5086FC\">一键关注</font>"));
//    }
//
//    @Override
//    public void onClick(View v) {
//
//        int id = v.getId();
//        if (id == R.id.tv_navigation) {
//
//            ZStatistics.onViewClick(this, "deliver_result&navigation");
//
//            //#887883 java.lang.IllegalArgumentException
//            //Parameter specified as non-null is null: method kotlin.jvm.internal.Intrinsics.checkParameterIsNotNull, parameter model
//            //com.zczy.plugin.order.navigation.ActionNavigation.start(Unknown Source:7)
//
//            BaseViewModel baseViewModel = getViewModel(BaseViewModel.class);
//            if (baseViewModel != null && !TextUtils.isEmpty(orderId)) {
//                if (mData != null) {
//                    new ActionNavigation()
//                            .setOrderId(orderId)
//                            .setEWaybillData(mData)
//                            .setFlag("2")
//                            .start(this, baseViewModel, new Runnable() {
//                                @Override
//                                public void run() {
//                                    finish();
//                                }
//                            });
//                } else {
//                    new ActionNavigation()
//                            .setOrderId(orderId)
//                            .setFlag("2").
//                            start(this, baseViewModel, new Runnable() {
//                                @Override
//                                public void run() {
//                                    finish();
//                                }
//                            });
//                }
//
//            } else {
//                finish();
//            }
//        }
//
//    }
//
//    @LiveDataMatch
//    public void onGotoShipments2(EWaybill data) {
//        mData = data;
//    }
}
