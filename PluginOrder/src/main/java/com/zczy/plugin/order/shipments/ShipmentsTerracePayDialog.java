//package com.zczy.plugin.order.shipments;
//
//import android.os.Bundle;
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import androidx.annotation.NonNull;
//import androidx.annotation.Nullable;
//
//import com.zczy.comm.ui.BaseDialog;
//import com.zczy.comm.utils.SpannableHepler;
//import com.zczy.lib_zstatistics.sdk.ZStatistics;
//import com.zczy.plugin.order.R;
//
///**
// * 功能描述: 预付挽留
// *
// * <AUTHOR>
// * @date 2022/7/16-9:55
// */
//
//public class ShipmentsTerracePayDialog extends BaseDialog {
//
//    private Listener onClickListener;
//    private String msg;
//    //优惠的金额
//    private String preferentialMoney;
//    private String advanceWay;
//
//    public ShipmentsTerracePayDialog setListener(Listener onClickListener, String msg,String advanceWay, String preferentialMoney) {
//        this.onClickListener = onClickListener;
//        this.preferentialMoney = preferentialMoney;
//        this.advanceWay = advanceWay;
//        this.msg = msg;
//        return this;
//    }
//
//
//    @Override
//    protected void bindView(@NonNull View view, @Nullable Bundle bundle) {
//        TextView tv_content = view.findViewById(R.id.tv_content);
//        ImageView ivLeft = view.findViewById(R.id.ivLeft);
//        ImageView ivRight = view.findViewById(R.id.ivRight);
//        tv_content.setText(msg);
//        ivLeft.setOnClickListener(v -> {
//            dismiss();
//            onClickListener.onListener("1");
//            ZStatistics.onViewClick(v);
//        });
//        ivRight.setOnClickListener(v -> {
//            dismiss();
//            onClickListener.onListener("2");
//            ZStatistics.onViewClick(v);
//        });
//
//        TextView tv_preferential = view.findViewById(R.id.tv_preferential);
//        if (TextUtils.isEmpty(preferentialMoney) || TextUtils.equals("2",advanceWay)) {
//            //预付油品不使用
//            tv_preferential.setVisibility(View.GONE);
//        } else {
//            tv_preferential.setVisibility(View.VISIBLE);
//            SpannableHepler.Txt txt = new SpannableHepler.Txt(preferentialMoney, "#ED5400");
//            txt.bold = true;
//            tv_preferential.setText(new SpannableHepler("仅限此单\n").append(txt).builder());
//        }
//    }
//
//    @Override
//    protected String getDialogTag() {
//        return null;
//    }
//
//    @Override
//    protected int getDialogLayout() {
//        return R.layout.shipments_terracepay_dialog;
//    }
//
//    @Override
//    protected int getDialogStyle() {
//        return R.style.custom_dialog2;
//    }
//
//    public interface Listener {
//        void onListener(String clickType);
//    }
//
//    @Override
//    public boolean isCancelable() {
//        return false;
//    }
//
//    @Override
//    protected boolean isCancelableOnTouchOutside() {
//        return false;
//    }
//}
