package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

class ReqQueryCheckBindBankCard(
    var orderId: String? = "",

    ) : BaseOrderRequest<BaseRsp<RspCheckBindBankCard>>("oms-app/carrier/common/queryCheckBindBankCard")

data class RspCheckBindBankCard(
    var bindBankCardState: String,//0 不需要绑定 1 需要绑定本人卡 2 需要绑定亲人卡  如果是1或者2的时候提示resultMsg
    var operationFlag: String, //是否可以操作 0 否 1 是 是否可以操作添加银行卡
) : ResultData()