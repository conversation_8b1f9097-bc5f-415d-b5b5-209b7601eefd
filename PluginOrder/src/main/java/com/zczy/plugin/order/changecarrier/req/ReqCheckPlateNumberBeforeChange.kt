package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 承运方操作变更时校验（只变更车） 备案卸货变更模块相关接口
 */
data class ReqCheckPlateNumberBeforeChange(
        var changeType: String = "2", // 变更方式 changeType:  1：变更人  2：变更车  3：变更人和车
        var plateNumber: String = "", // 新车牌号  变更车或者人和车时必传
        var orderId: String? = "", // 订单号
        var sourceId: String? = "", // 订货源号
        var consignorUserId: String? = "", // 货主id
) : BaseOrderRequest<BaseRsp<ResultData>>("oms-app/orderChange/checkPlateNumberBeforeChange")

