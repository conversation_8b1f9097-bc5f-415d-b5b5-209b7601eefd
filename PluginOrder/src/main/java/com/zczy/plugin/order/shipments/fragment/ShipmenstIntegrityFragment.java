//package com.zczy.plugin.order.shipments.fragment;
//
//import android.text.TextUtils;
//import android.view.View;
//import android.widget.CompoundButton;
//
//import com.sfh.lib.event.RxBusEvent;
//import com.sfh.lib.mvvm.annotation.LiveDataMatch;
//import com.sfh.lib.rx.IResultSuccess;
//import com.zczy.comm.data.entity.EAgreement;
//import com.zczy.comm.widget.AgreementView;
//import com.zczy.lib_zstatistics.sdk.ZStatistics;
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.entity.EAdvanceInfo;
//import com.zczy.plugin.order.shipments.entity.EAdvanceServiceMoney;
//import com.zczy.plugin.order.shipments.entity.EAdvanceType;
//import com.zczy.plugin.order.shipments.entity.EventOilCardRatio;
//import com.zczy.plugin.order.shipments.entity.RxUserPayMent;
//import com.zczy.plugin.order.shipments.entity.ShipmentUI;
//import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
//import com.zczy.plugin.order.shipments.model.ShipmentsTerracePaymentModel;
//import com.zczy.plugin.order.shipments.view.TerracePaymentDialog;
//
//import java.util.List;
//
///**
// * 功能描述:协议
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/26
// */
//public class ShipmenstIntegrityFragment extends ShipmentsBaseFragment<ShipmentsTerracePaymentModel> {
//
//    //协议
//    private AgreementView agreementView;
//
//    @Override
//    public boolean checkParams(ShipmentUI data) {
//
//        if (!agreementView.getCheckBox().isChecked()) {
//            showToast("必须勾选我已阅读，才能确认发货");
//            return false;
//        }
//        return true;
//    }
//
//    @Override
//    public int getLayout() {
//
//        return R.layout.order_shipments_integrity_include;
//    }
//
//    private ShipmentsEGoodInfo shipmentsEGoodInfo;
//    private String detailId;
//    private EAdvanceType mEAdvanceType;
//    private EAdvanceInfo advanceInfo;
//    private EAgreement.Query[] keys ={EAgreement.Query.SHIP, EAgreement.Query.INTEGRITY} ;
//    private String dlOilCardRatio;
//
//    public void setData(String detailId, ShipmentsEGoodInfo data, EAdvanceInfo advanceInfo) {
//        this.shipmentsEGoodInfo = data;
//        this.detailId = detailId;
//        this.advanceInfo = advanceInfo;
//    }
//
//    //0:预付服务说明协议（独立）,2:预付类, 4通用协议
//    public void setShowKey(EAgreement.Query... keys) {
//
//        if (agreementView != null) {
//
//            List<EAgreement> agreements = agreementView.getAgreementAll();
//
//            if (agreements.isEmpty()) {
//                this.query(keys);
//            } else {
//                agreementView.show(keys);
//            }
//        } else {
//            this.keys = keys;
//        }
//    }
//
//    @Override
//    public void initData(View view) {
//
//
//        agreementView = view.findViewById(R.id.agreement_view);
//        agreementView.setListener(new AgreementView.OnClickListener() {
//            @Override
//            public boolean intercept(EAgreement agreement) {
//
//                if (TextUtils.isEmpty(agreement.contentDescAlias)) {
//                    return false;
//                }
//                if (agreement.contentDescAlias.contains("预付服务说明") && EAgreement.Query.LOCAL == agreement.type) {
//                    //预付服务说明
//                    getViewModel().queryNewAdvanceServiceMoney(shipmentsEGoodInfo.getOrderId(), detailId, mEAdvanceType == null ? "" : mEAdvanceType.getType(),dlOilCardRatio);
//                    ZStatistics.onViewClick(getActivity(), "send_detail_PrepayClaim");
//
//                    return true;
//                }
//                return false;
//            }
//        });
//
//        agreementView.getCheckBox().setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
//            @Override
//            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                if (isChecked) {
//                    ZStatistics.onViewClick(getActivity(), "send_detail_read");
//                }
//            }
//        });
//
//        this.query(keys);
//
//    }
//
//    private void query(EAgreement.Query... keys) {
//        agreementView.queryAgreement(getViewModel(ShipmentsTerracePaymentModel.class), false, new IResultSuccess<List<EAgreement>>() {
//            @Override
//            public void onSuccess(List<EAgreement> eAgreements) throws Exception {
//
//                //本地固定协议=>[预付协议]
//                EAgreement agreement = new EAgreement();
//                agreement.setType(EAgreement.Query.LOCAL);
//                agreement.setContentDescAlias("预付服务说明");
//                eAgreements.add(agreement);
//            }
//        }, keys);
//
//    }
//
//    @LiveDataMatch(tag = "预付服务费提示")
//    public void opeTenrracePaymentDialog(EAdvanceServiceMoney serviceMoney) {
//        new TerracePaymentDialog(this.getActivity(), this.shipmentsEGoodInfo, serviceMoney, advanceInfo, userPaymentMoney).show();
//    }
//
//    @RxBusEvent(from = "预付方式选择")
//    public void eventEAdvanceType(EAdvanceType type) {
//        this.mEAdvanceType = type;
//    }
//
//    @RxBusEvent(from = "油气比例")
//    public void eventEventOilCardRatio(EventOilCardRatio oilCardRatio){
//        this.dlOilCardRatio = oilCardRatio.getOilCardRatio();
//    }
//
//    boolean userPaymentMoney = false;
//
//    @RxBusEvent(from = "立减")
//    public void eventPaymentMoney(RxUserPayMent userPayMent) {
//        this.userPaymentMoney = userPayMent.user;
//    }
//}
