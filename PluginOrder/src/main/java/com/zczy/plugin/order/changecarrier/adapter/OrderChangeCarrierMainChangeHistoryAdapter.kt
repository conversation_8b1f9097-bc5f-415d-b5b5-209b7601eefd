package com.zczy.plugin.order.changecarrier.adapter

import android.text.TextUtils
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.utils.getResColor
import com.zczy.comm.widget.itemdecoration.CommItemDecoration
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.req.RspOrderChangeData
import com.zczy.plugin.order.changecarrier.req.formatChangeResult

class OrderChangeCarrierMainChangeHistoryAdapter
    :
    BaseQuickAdapter<RspOrderChangeData, BaseViewHolder>(R.layout.order_change_carrier_main_change_history_item) {
    override fun convert(helper: BaseViewHolder, item: RspOrderChangeData) {

        helper
            .setText(R.id.tv_order, item.orderId)
            .setText(R.id.tv_apply_order_time, item.changeTimeStr)
            .setText(R.id.tv_change_result, item.formatChangeResult())
            .setGone(R.id.iv_lingdan, TextUtils.equals("1", item.isLessThanOrder))
        helper.addOnClickListener(R.id.tv_copy)
        val orderChangeAdapter = OrderChangeCarrierHistoryChildAdapter()
        val detailList = item.detailList
        val recyclerView =
            helper.getView<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerView)
        recyclerView.let {
            it.layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(mContext)
            it.isFocusable = false
            it.isNestedScrollingEnabled = false
            it.addItemDecoration(
                CommItemDecoration.createVertical(mContext, getResColor(R.color.comm_divider_e3), 1)
            )
            it.adapter = orderChangeAdapter
        }
        orderChangeAdapter.setNewData(detailList)
    }
}
