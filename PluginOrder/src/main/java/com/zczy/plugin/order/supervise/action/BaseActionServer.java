package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/13
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnResultListener;
import com.sfh.lib.AppCacheManager;
import com.zczy.comm.BuildConfig;
import com.zczy.comm.config.HttpURLConfig;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

abstract class BaseActionServer {

    public interface OnAuthListener {
        void onAuthFailure(String code, String msg);

        void onAuthSuccess(List<ShippingNoteInfo> list);
    }

    public static ConcurrentHashMap<String, Long> AUTH_TIME = new ConcurrentHashMap<>();


    public abstract void start();


    /***
     * 指定公司授权
     * @param consignorSubsidiaryId
     * @param listener
     * @return
     */
    public BaseActionServer auth(String consignorSubsidiaryId, OnAuthListener listener) {
        CodeType codeKey = CodeType.getCodeType(consignorSubsidiaryId);
        if (codeKey == null) {
            listener.onAuthFailure("-1", "没有找到对应公司安全码");
            return this;
        } else {
            return this.auth(codeKey, listener);
        }
    }

    private BaseActionServer auth(CodeType codeType, OnAuthListener listener) {

        if (codeType == null) {
            listener.onAuthFailure("-1", "没有找到对应公司安全码");
        } else {
            out(codeType.toString());
            String debugModel = BuildConfig.HTTP_TYPE.equals("LINE") ? "release" : "debug";
            if (AUTH_TIME.contains(codeType.consignorSubsidiaryId)) {

                long lastTime = Math.abs(System.currentTimeMillis() - AUTH_TIME.get(codeType.consignorSubsidiaryId));
                if (lastTime > 1 * 60 * 60 * 1000) {
                    //超时
                    LocationOpenApi.auth(AppCacheManager.getApplication(), codeType.APPID, codeType.appSecurity, codeType.enterpriseSenderCode, debugModel, new AuthOnResultListener(codeType.consignorSubsidiaryId, listener));
                } else {
                    listener.onAuthSuccess(Collections.EMPTY_LIST);
                }
            } else {
                LocationOpenApi.auth(AppCacheManager.getApplication(), codeType.APPID, codeType.appSecurity, codeType.enterpriseSenderCode, debugModel, new AuthOnResultListener(codeType.consignorSubsidiaryId, listener));
            }
        }

        return this;
    }

    private class AuthOnResultListener implements OnResultListener {

        OnAuthListener listener;
        String key;

        AuthOnResultListener(String key, OnAuthListener listener) {
            this.listener = listener;
            this.key = key;
        }

        @Override
        public void onFailure(String s, String s1) {
            out(String.format("省货物平台系统2.0 授权 OnResultListener2.class onFailure() code:%s,msg:%s", s, s1));
            if (listener != null) {
                listener.onAuthFailure(s, s1);
            }
        }

        @Override
        public void onSuccess(List<ShippingNoteInfo> list) {
            out("省货物平台系统2.0 授权 OnResultListener2.class onSuccess() 成功" + (list != null ? list.size() : 0));
            if (listener != null) {
                listener.onAuthSuccess(list);
            }
            AUTH_TIME.put(this.key, System.currentTimeMillis());
        }
    }

    public static void out(String msg) {
        if (HttpURLConfig.isDebug()) {
            System.out.println("ShippingNoteInfoServer: " + msg);

//            TaskExecutorImpl.getInstance().executeOnMainThread(new Runnable() {
//                @Override
//                public void run() {
//                    Toast.makeText(AppCacheManager.getApplication(), msg, Toast.LENGTH_LONG).show();
//                }
//            });
        }
    }

}
