package com.zczy.plugin.order.stevedore.model.request;

import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.order.BaseOrderRequest;

import java.util.List;

/**
 * 功能描述:根据运单号新增装卸货费信息
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ReqCarrierAddLoadPay extends BaseOrderRequest<BaseRsp<ResultData>> {

    /***修改的时候填写发货明细id*/
    String detailId;

    /*** 运单号*/
    String orderId;
    /*** 收据照片*/
    List<String> pictureUrlJsonArray;
    /*** 备注*/
    String remark;
    /*** 装卸费*/
    String money;

    String userName;

    public ReqCarrierAddLoadPay() {

        super ("oms-app/order/loadpay/carrierAddLoadPay");
    }

    public void setMoney(String money) {

        this.money = money;
    }

    public void setDetailId(String detailId) {

        this.detailId = detailId;
    }

    public void setOrderId(String orderId) {

        this.orderId = orderId;
    }

    public void setPictureUrlJsonArray(List<String> pictureUrlJsonArray) {
        this.pictureUrlJsonArray = pictureUrlJsonArray;
    }

    public void setRemark(String remark) {

        this.remark = remark;
    }


}
