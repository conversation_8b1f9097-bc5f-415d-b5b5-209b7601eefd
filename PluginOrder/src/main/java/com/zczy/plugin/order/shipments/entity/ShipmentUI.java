//package com.zczy.plugin.order.shipments.entity;
//
//import android.text.TextUtils;
//import android.util.SparseArray;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
///**
// * 功能描述:发货UI 数据
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public class ShipmentUI {
//
//
//    /***预付开关-计算服务费[Fragment]*/
//    public static final String TAG_OFF_ON = "TAG_OFF_ON";
//
//    /**** 货物明细[Fragment]*/
//    public static final String TAG_GOODINFO = "TAG_GOODINFO";
//
//    /**** 单据照片[Fragment]*/
//    public static final String TAG_IMAGE_WAYBILL = "TAG_IMAGE_WAYBILL";
//
//    /**** 人车合影照片[Fragment]*/
//    public static final String TAG_IMAGE_PERSONCAR = "TAG_IMAGE_PERSONCAR";
//
//    /**** 诚信声明[Fragment]*/
//    public static final String TAG_SERVICE_CHARGE = "TAG_SERVICE_CHARGE";
//
//    //货源类型 CZY-7768	新增字段0正常货源 1集装箱货源
//    public String goodsSource;
//
//
//    public String orderId;
//
//    public String detailId;
//
//    /***个体司机信用分*/
//    public String creditPoint;
//
//    /*** 货物明细（EGoodInfo,包含输入发货吨位）*/
//    public List<EGoodInfo> goodInfos;
//
//    /*** 图片(多个类别图片集合)*/
//    public SparseArray<List<EWater>> images = new SparseArray<>(2);
//
//    /**** 司机是否选择了非指定预付款 1 选择 2 未选择*/
//    public String isAdvanceButtonOn;
//
//    /**** 预付方式*/
//    public String advanceWay;
//    /**
//     * 油气比例值
//     */
//    public String oilRatio;
//    /*** 优惠券ID*/
//    public String userCouponIds;
//    /***    预付业务立减金额 如果用户使用了预付业务则传，金额的传金额，折扣的传折扣数*/
//    public String couponMoney;
//
//    /***预付业务立减 1:固定额度;2:固定金额折扣;3:随机减*/
//    public String couponAmountType;
//    //立减后预付款优惠id
//    public String cutAdvanceCouponId;
//
//    /***申请预付标识 null普通发货，1预付申请*/
//    public String applyAdvance;
//
//    /***是否需要上传皮毛重  */
//    public boolean uploadGrossAndTare;
//    /**
//     * 省平台监管
//     */
//    public ESDKInfoObj sdkInfoObj;
//    /***SDK是否可打开 1 可打开 0 不可打开*/
//    public boolean haveOpenSdk;
//
//    /*判断是否是宁夏的运单*/
//    public String deliverTrackPersonImgFlag;
//
//    /***货物出厂时间 ZCZY-7729 冀东定制化需求*/
//    public String leaveFactoryTime;
//
//    //zczy-9782 天津
//    public String coordinateFlag;
//
//    //是否购买智运折扣卡;1:是;0否
//    public String discountCardBuyFlag;
//    //是否使用智运折扣卡;1:是;0否:
//    public String discountCardUseFlag;
//    //智运折卡号
//    public String discountCardNum;
//    //ZCZY-12277 【加急】2023惊蛰（3.6）活动趣味转盘
//    //活动id
//    public String activityId;
//
//    /***
//     * 货物明细
//     * @return
//     */
//    public String getCargoIdWeightData() {
//
//        StringBuilder builder = new StringBuilder(50);
//        final int size = goodInfos.size();
//        for (int i = 0; i < size; i++) {
//            builder.append(goodInfos.get(i).getCargoId()).append(":").append(goodInfos.get(i).getBeforeDeliverCargoWeight());
//            if (i < size - 1) {
//                builder.append(",");
//            }
//        }
//        return builder.toString();
//    }
//
//    /***
//     * 毛重
//     *  拼装字符串 货物ID:毛重,货物Id:毛重
//     * @return
//     */
//    public String getGrossWeightDetails() {
//        if (uploadGrossAndTare) {
//            StringBuilder builder = new StringBuilder(50);
//            final int size = goodInfos.size();
//            for (int i = 0; i < size; i++) {
//
//                if (!TextUtils.isEmpty(goodInfos.get(i).getBeforeDeliverGrossWeight())) {
//                    builder.append(goodInfos.get(i).getCargoId());
//                    builder.append(":").append(goodInfos.get(i).getBeforeDeliverGrossWeight());
//                }
//
//                if (i < size - 1) {
//                    builder.append(goodInfos.get(i).getCargoId());
//                    builder.append(",");
//                }
//            }
//            return builder.toString();
//        }
//        return null;
//    }
//
//    /***
//     * 皮重
//     * 拼装字符串 货物ID:皮重
//     * ,货物Id:皮重
//     * @return
//     */
//    public String getTareWeightDetails() {
//        if (uploadGrossAndTare) {
//            StringBuilder builder = new StringBuilder(50);
//            final int size = goodInfos.size();
//            for (int i = 0; i < size; i++) {
//
//
//                if (!TextUtils.isEmpty(goodInfos.get(i).getBeforeDeliverTareWeight())) {
//                    builder.append(goodInfos.get(i).getCargoId());
//                    builder.append(":").append(goodInfos.get(i).getBeforeDeliverTareWeight());
//                }
//
//                if (i < size - 1) {
//                    builder.append(goodInfos.get(i).getCargoId());
//                    builder.append(",");
//                }
//            }
//            return builder.toString();
//        }
//
//        return null;
//    }
//
//    /***
//     * 获取图片集合
//     * @param flag
//     * @return
//     */
//    public List<EWater> getImages(String flag) {
//
//        if (images == null) {
//            return Collections.emptyList();
//        }
//        return images.get(flag.hashCode());
//    }
//
//
//    public List<EContainer> getEContainer() {
//        List<EContainer> containers = new ArrayList<>();
//        if (goodInfos != null) {
//            for (EGoodInfo goodInfo : goodInfos) {
//                //防止为空
//                List<EContainer> list = goodInfo.getTempContainerSize();
//                if (list != null) {
//                    containers.addAll(list);
//                }
//            }
//        }
//        return containers;
//    }
//
//
//    /***
//     * 发生发货吨位修改数据
//     * @return
//     */
//    public List<EGoodInfo> getChangeSize() {
//        List<EGoodInfo> list = new ArrayList<>();
//        if (goodInfos != null) {
//            for (EGoodInfo goodInfo : goodInfos) {
//                if (!TextUtils.equals(goodInfo.getOldBeforeDeliverCargoWeight(), goodInfo.getBeforeDeliverCargoWeight())) {
//                    list.add(goodInfo);
//                }
//            }
//        }
//        return list;
//    }
//
//    public void clearAdvance() {
//        //清楚预付参数
//        this.userCouponIds = "";
//        this.couponAmountType = "";
//        this.cutAdvanceCouponId = "";
//        this.couponMoney = "";
//
//        this.discountCardBuyFlag = "";
//        this.discountCardUseFlag = "";
//        this.discountCardNum = "";
//
//        this.advanceWay = "";
//        this.isAdvanceButtonOn = "";
//        this.oilRatio = null;
//    }
//
//}
