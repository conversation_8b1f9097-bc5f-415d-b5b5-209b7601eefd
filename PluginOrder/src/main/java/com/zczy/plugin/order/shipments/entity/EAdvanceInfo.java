package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import com.zczy.comm.http.entity.ResultData;
import com.zczy.comm.utils.NumUtil;

import java.util.List;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/11/15
 */
public class EAdvanceInfo extends ResultData {

    /***预付场景：1：个体司机指定订单；2.个体司机非指定订单；3.车老板自己摘牌订单；4.个体司机关联车老板模式；5.承运商指派订单*/
    String advanceType;

    /***个体司机信用分*/
    String creditPoint;
    /***是否支持预付 isAdvance-->0：不支持，1：支持*/
    String isAdvance;
    /***不支持预付原因*/
    String noAdvanceReason;

    /*** 预计预付服务费 服务费新计算公式得出*/
    String newAdvanceServiceMoney;

    /***预付业务立减类型 1:固定额度;2:固定金额折扣;3:随机减*/
    String couponAmountType;

    /***立减内容 如：立减5元*/
    String couponContext;

    /***优惠金额*/
    String couponMoney;

    //立减后预付款优惠id
    String cutAdvanceCouponId;

    /**
     * 默认可以使用的抵扣券
     */
    String userCouponId;
    /**
     * 抵扣券类型
     * 0 抵扣比例 1 抵扣金额
     */
    String discountType;
    /**
     * 固定金额
     * 如果，抵用券是抵扣金额，则取这个值
     */
    String couponUnitMoney;
    /**
     * 抵扣比例
     * 如果，抵用券是抵扣比例，则取这个值
     */
    String discountRatio;

    /***预付方式*/
    List<EAdvanceType> advanceWay;

    //不能预付原因code 返回 21161为存在欠款
    String noAdvanceCode;
    /**
     * 折扣金额上限
     */
    String discountMoneyTop;

    //是否需要展示油卡选项
    String oilSelectBySelf;

    //油品 比例 （用于第一次带出默认或者重新预付带出）
    String oilDefaultRatio;


    /**
     * ZCZY_9452 司机端预付页面进行优化
     *
     * @return
     */
    String advanceOrderDriverCount;
    //趣味转盘活动展不展示，1展示
    String  activityShowFlag;
    EActivityInfo activityInfo;

    /*** 预付比例*/
    String advanceRatio;

    //预付情况下运单照片展示逻辑
    ShipmentsImgObj orderImgObj;
    //预付情况下人车货照片展示逻辑
    ShipmentsImgObj peopleVehicleImgObj;

    public ShipmentsImgObj getOrderImgObj() {
        return orderImgObj;
    }

    public ShipmentsImgObj getPeopleVehicleImgObj() {
        return peopleVehicleImgObj;
    }

    public void setAdvanceType(String advanceType) {
        this.advanceType = advanceType;
    }

    public void setCreditPoint(String creditPoint) {
        this.creditPoint = creditPoint;
    }

    public void setIsAdvance(String isAdvance) {
        this.isAdvance = isAdvance;
    }

    public void setNoAdvanceReason(String noAdvanceReason) {
        this.noAdvanceReason = noAdvanceReason;
    }

    public void setNewAdvanceServiceMoney(String newAdvanceServiceMoney) {
        this.newAdvanceServiceMoney = newAdvanceServiceMoney;
    }

    public void setCutAdvanceCouponId(String cutAdvanceCouponId) {
        this.cutAdvanceCouponId = cutAdvanceCouponId;
    }

    public void setAdvanceWay(List<EAdvanceType> advanceWay) {
        this.advanceWay = advanceWay;
    }

    public void setNoAdvanceCode(String noAdvanceCode) {
        this.noAdvanceCode = noAdvanceCode;
    }

    public void setDiscountMoneyTop(String discountMoneyTop) {
        this.discountMoneyTop = discountMoneyTop;
    }

    public void setOilSelectBySelf(String oilSelectBySelf) {
        this.oilSelectBySelf = oilSelectBySelf;
    }

    public void setOilDefaultRatio(String oilDefaultRatio) {
        this.oilDefaultRatio = oilDefaultRatio;
    }

    public void setActivityShowFlag(String activityShowFlag) {
        this.activityShowFlag = activityShowFlag;
    }

    public void setActivityInfo(EActivityInfo activityInfo) {
        this.activityInfo = activityInfo;
    }

    public String getAdvanceRatio() {
        return advanceRatio;
    }

    public void setAdvanceRatio(String advanceRatio) {
        this.advanceRatio = advanceRatio;
    }

//    public ESpecialOneAdvanceInfo getSpecialOneAdvanceInfo() {
//        return specialOneAdvanceInfo;
//    }
//
//    public void setSpecialOneAdvanceInfo(ESpecialOneAdvanceInfo specialOneAdvanceInfo) {
//        this.specialOneAdvanceInfo = specialOneAdvanceInfo;
//    }

    public String getActivityShowFlag() {
        return activityShowFlag;
    }

    public EActivityInfo getActivityInfo() {
        return activityInfo;
    }

    public String getAdvanceOrderDriverCount() {
        return advanceOrderDriverCount;
    }

    public void setAdvanceOrderDriverCount(String advanceOrderDriverCount) {
        this.advanceOrderDriverCount = advanceOrderDriverCount;
    }

    public String getOilDefaultRatio() {
        return oilDefaultRatio;
    }

    public String getOilSelectBySelf() {
        return oilSelectBySelf;
    }

    public String getCutAdvanceCouponId() {
        return cutAdvanceCouponId;
    }

    public String getDiscountMoneyTop() {
        return discountMoneyTop;
    }

    public String getNoAdvanceCode() {
        return noAdvanceCode;
    }

    public List<EAdvanceType> getAdvanceWay() {
        return advanceWay;
    }

    public String getIsAdvance() {
        return isAdvance;
    }

    public String getUserCouponId() {
        return userCouponId;
    }

    public String getDiscountType() {
        return discountType;
    }

    public String getCouponUnitMoney() {
        return couponUnitMoney;
    }

    public String getDiscountRatio() {
        return discountRatio;
    }

    public void setCouponAmountType(String couponAmountType) {
        this.couponAmountType = couponAmountType;
    }

    public void setCouponContext(String couponContext) {
        this.couponContext = couponContext;
    }

    public void setCouponMoney(String couponMoney) {
        this.couponMoney = couponMoney;
    }

    public void setUserCouponId(String userCouponId) {
        this.userCouponId = userCouponId;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public void setCouponUnitMoney(String couponUnitMoney) {
        this.couponUnitMoney = couponUnitMoney;
    }

    public void setDiscountRatio(String discountRatio) {
        this.discountRatio = discountRatio;
    }

    /****
     * 使用前提，可预付,是否显示立减
     * @return
     */
    public boolean isCouponShow() {

        return TextUtils.equals("1", this.couponAmountType)
                || TextUtils.equals("2", this.couponAmountType)
                || TextUtils.equals("3", this.couponAmountType);
    }

    /***立减内容*/
    public String getCouponContext() {
        return this.couponContext;
    }

    public String getCouponMoney() {
        return couponMoney;
    }

    public String getCouponAmountType() {
        return couponAmountType;
    }

    public String getNewAdvanceServiceMoney() {
        return newAdvanceServiceMoney;
    }

    public boolean isAdvance() {
        return TextUtils.equals("1", this.isAdvance);
    }


    public String getNoAdvanceReason() {
        return noAdvanceReason;
    }

    public String getAdvanceType() {
        return advanceType;
    }

    public String getCreditPoint() {
        return creditPoint;
    }


    /***
     * 默认优惠券的优惠金额
     * @return
     */
    public double getDiscountMoney(double serverMoney) {
        double discountMoney = 0.0;
        try {
            // 0 抵扣比例 1 抵扣金额
            if ("0".equals(this.getDiscountType())) {
                //预付服务费
                //抵扣比例 0-10
                double discountRatio = Double.parseDouble(this.getDiscountRatio());
                if (discountRatio == 10.00 || discountRatio == 100.00) {
                    //全额抵扣
                    discountRatio = 0.0;
                }
                discountMoney = NumUtil.halfup(NumUtil.mul(serverMoney, 1 - NumUtil.div(discountRatio, 10.0, 2)));
                if (!TextUtils.isEmpty(this.getDiscountMoneyTop())) {
                    // 折扣金额上限
                    double top = Double.parseDouble(this.getDiscountMoneyTop());
                    //取最小值
                    discountMoney = Math.min(discountMoney, top);
                }

            } else if ("1".equals(this.getDiscountType())) {
                discountMoney = Double.parseDouble(this.getCouponUnitMoney());
            }
        } catch (Exception e) {
        }
        return discountMoney;
    }

    /***
     * 获取立减之后预付金额
     * @param serverMoney
     * @return
     */
    public double getCouponToServerMoney(double serverMoney) {

        double money = serverMoney;
        try {
            if (money > 0){

                double couponMoney = TextUtils.isEmpty(this.getCouponMoney()) ? 0.00 : Double.parseDouble(this.getCouponMoney());

                if (couponMoney > 0) {

                    //1:固定额度;2:固定金额折扣;3:随机减
                    if (TextUtils.equals("2", this.getCouponAmountType())) {
                        if (couponMoney == 10.00 || couponMoney == 100.00) {
                            //全额抵扣
                            couponMoney = 0.0;
                        }
                        money = NumUtil.halfup(NumUtil.mul(money, NumUtil.div(couponMoney, 10.0, 2)));

                    } else {
                        //保留2位 四舍五入
                        money = NumUtil.halfup(money - couponMoney);
                    }
                    money = money < 0.0 ? 0.0 : money;
                }
            }

        } catch (Exception e) {
        }
        return money;
    }
}
