package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import com.zczy.comm.http.entity.ResultData;

public class EShipmentsSuccess extends ResultData {

    String discountPrice;

    /***真实服务费*/
    String realAdvanceServiceMoney;

    //失败ID
    String reqNo;

    public String getReqNo() {
        return reqNo;
    }

    public boolean isShowMsg() {
        return !TextUtils.isEmpty(discountPrice)
                && !TextUtils.equals("0", discountPrice)
                && !TextUtils.equals("0.0", discountPrice)
                && !TextUtils.equals("0.00", discountPrice);
    }

    public String getRealAdvanceServiceMoneyMsg() {

        return "此单预付服务费立减"+discountPrice+"元! \n 实际服务费："+realAdvanceServiceMoney+"元";
    }
}
