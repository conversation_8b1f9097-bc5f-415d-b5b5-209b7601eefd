package com.zczy.plugin.order.shipments;

import android.content.Context;


import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.JsonUtil;

import com.zczy.plugin.order.waybill.entity.EWaybill;


/**
 * 功能描述:重新申请预付(可预付才能打开)
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
public class ReapplyTerracePayActivity
        //extends AbstractLifecycleActivity<ShipmentsModel>
{

    /***
     * 重新预付（预付申请场景进入）
     * @param fragment
     * @param data
     */
    public static void start(Context fragment, EWaybill data) {
        AMainServer mainServer = AMainServer.getPluginServer();
        mainServer.openReactNativeActivity(fragment, "ReapplyTerracePayPage", JsonUtil.toJson(data));

        //Intent intent = new Intent(fragment, ReapplyTerracePayActivity.class);
        //intent.putExtra("orderId", data.getOrderId());
        //intent.putExtra("detailId", data.getDetailId());
        //intent.putExtra("advanceState", data.getAdvanceState());
        //intent.putExtra("senceAdvance", 1);
        //fragment.startActivity(intent);
    }

    /***
     * 预付申请（发货后预付申请）
     * @param fragment
     * @param data
     */
    public static void start2(Context fragment, EWaybill data) {
        AMainServer mainServer = AMainServer.getPluginServer();
        mainServer.openReactNativeActivity(fragment, "PrepaidApplyPage", JsonUtil.toJson(data));

        //Intent intent = new Intent(fragment, ReapplyTerracePayActivity.class);
        //intent.putExtra("orderId", data.getOrderId());
        //intent.putExtra("detailId", data.getDetailId());
        //intent.putExtra("advanceState", data.getAdvanceState());
        //intent.putExtra("senceAdvance", 2);
        //fragment.startActivity(intent);
    }
//
//    private TextView mTvOk;
//    private String orderId;
//    private String detailId;
//    /*** 预付款状态0:未确认，1:已确认，2:货主打回*/
//    private String advanceState;
//    private ShipmentsEGoodInfo shipmentsEGoodInfo;
//    private EAdvanceInfo mAdvanceInfo;
//    private SparseArray<ShipmentsBaseFragment> fragments = new SparseArray<>(5);
//    //自定义数据类型 1 : 预付申请场景进入 ,2：发货后预付申请
//    private int senceAdvance;
//    private ShipmenstIntegrityFragment integrity_fragment;
//
//    private TextView tv_money;
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate(savedInstanceState);
//        this.setContentView(R.layout.order_reapply_terracepay_activity);
//
//        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
//        this.orderId = getIntent().getStringExtra("orderId");
//        this.detailId = getIntent().getStringExtra("detailId");
//        this.advanceState = getIntent().getStringExtra("advanceState");
//        //自定义数据类型 1 : 预付申请场景进入 ,2：发货后预付申请
//        this.senceAdvance = getIntent().getIntExtra("senceAdvance", 1);
//
//        TextView tv_orderId = this.findViewById(R.id.tv_orderId);
//        tv_orderId.setText("运单编号   " + this.orderId);
//
//        this.tv_money = findViewById(R.id.tv_money);
//        this.mTvOk = findViewById(R.id.tv_ok);
//        this.putDisposable(UtilRxView.clicks(mTvOk, 1000).subscribe((Object o) -> {
//
//            if (shipmentsEGoodInfo.isAuthorize()) {
//                //ZCZY-6636 【互联互通】兰鑫钢铁系统对接
//                new ToastDialog().setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        ReapplyTerracePayActivity.this.postShipments();
//                    }
//                }).show(ReapplyTerracePayActivity.this);
//
//            } else {
//                ReapplyTerracePayActivity.this.postShipments();
//            }
//
//        }));
//        //协议
//        this.integrity_fragment = (ShipmenstIntegrityFragment) getSupportFragmentManager().findFragmentById(R.id.integrity_fragment);
//        //查询货物信息
//        this.getViewModel().openSence(orderId, detailId);
//    }
//
//
//    @LiveDataMatch(tag = "查询货物信息")
//    public void onDeliverCargoQuerySuccess(final ShipmentsEGoodInfo goodInfo, EAdvanceInfo advanceInfo) {
//
//        if (advanceInfo == null || goodInfo == null) {
//            showDialogToast("未查询到相关货物信息");
//            return;
//        }
//        if (TextUtils.equals("1", goodInfo.getOrderPolicyFlag())) {
//            //保障服务
//            TextView tv_instruction = findViewById(R.id.tv_instruction);
//            tv_instruction.setVisibility(View.VISIBLE);
//            tv_instruction.setText(String.format("温馨提示：此单已购买保障服务，预计扣除%s元货物保障 服务费", goodInfo.getOrderPolicyMoney()));
//        }
//        this.mAdvanceInfo = advanceInfo;
//        this.shipmentsEGoodInfo = goodInfo;
//        this.shipmentsEGoodInfo.setDetailId(detailId);
//        this.integrity_fragment.setData(detailId, goodInfo, advanceInfo);
//
//        //预付申请界面，如预付不可以使用退出当前界面
//        if (!advanceInfo.isAdvance()) {
//            DialogBuilder builder = new DialogBuilder();
//            builder.setCancelable(false);
//            builder.setHideCancel(true);
//            builder.setOKTextListener("知道了", new DialogBuilder.DialogInterface.OnClickListener() {
//                @Override
//                public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
//                    dialogInterface.dismiss();
//                    finish();
//                }
//            });
//            builder.setMessage(advanceInfo.getNoAdvanceReason());
//            showDialog(builder);
//            return;
//        }
//        switch (advanceInfo.getAdvanceType()) {
//            case "2":
//            case "4": {
//                // 2.个体司机非指定订单 => 走平台预付
//                // 4.个体司机关联车老板模式 => 走平台预付
//                FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//                // 预付费开关-优惠券【展示】
//                ShipmentsTerracePayFragment terrace = ShipmentsTerracePayFragment.newFragment();
//
//                terrace.setData(this.senceAdvance, this.advanceState, this.shipmentsEGoodInfo, advanceInfo, openColse -> ReapplyTerracePayActivity.this.openColseFragment());
//                this.fragments.put(ShipmentUI.TAG_OFF_ON.hashCode(), terrace);
//                transaction.add(R.id.ll_content, terrace, ShipmentUI.TAG_OFF_ON);
//
//                //货物明细【展示】
//                ShipmentsGoodsFragment goods = ShipmentsGoodsFragment.newFragment();
//                goods.setEGoodInfoList(shipmentsEGoodInfo, shipmentsEGoodInfo.uploadGrossAndTareWeightFlag());
//                goods.setSenceAdvance(senceAdvance);
//                //,2：发货后预付申请,不可编辑发货吨位信息
//                goods.setEditInput(this.senceAdvance == 2 ? false : true);
//                this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goods);
//                transaction.add(R.id.ll_content, goods, ShipmentUI.TAG_GOODINFO);
//
//                transaction.commitAllowingStateLoss();
//                break;
//            }
//            case "3": {
//                // 车老板自己摘牌订单 => 车老板预付
//                this.bossUI(this.shipmentsEGoodInfo, advanceInfo);
//                break;
//            }
//            case "1": {
//                //个体司机指定订单
//                if (advanceInfo.isAdvance()) {
//                    // => 走货主预付款
//                    this.hzTerracePayUI(this.shipmentsEGoodInfo, advanceInfo);
//                }
//                break;
//            }
//            default:
//                break;
//        }
//
//
//    }
//
//    /* ------------------------------ 平台预付-------------------------------------------------*/
//    private void openColseFragment() {
//
//        FragmentManager fragmentManager = this.getSupportFragmentManager();
//        FragmentTransaction transaction = fragmentManager.beginTransaction();
//
//        //1.单据照片【显示】  预付开启时，指定拍照,必传，加水印
//        ShipmentsImgObj orderImgObj = mAdvanceInfo.getOrderImgObj();
//        if (orderImgObj != null && orderImgObj.isShowUpload()) {
//            ShipMentFileFragment waybillFileFragment = ShipMentFileFragment.newFragment(orderId, this.shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//            waybillFileFragment.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.setCheckListener(waybillFileFragment, orderImgObj);
//
//            transaction.add(R.id.ll_content, waybillFileFragment, ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), waybillFileFragment);
//        }
//
//
//        //2.车货合影【显示】必传
//        ShipmentsImgObj peopleVehicleImgObj = mAdvanceInfo.getPeopleVehicleImgObj();
//        if (peopleVehicleImgObj != null && peopleVehicleImgObj.isShowUpload()) {
//            ShipMentFileFragment personFragment = ShipMentFileFragment.newFragment(orderId, this.shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//            personFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.setCheckListener(personFragment, peopleVehicleImgObj);
//
//            transaction.add(R.id.ll_content, personFragment, ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), personFragment);
//
//        }
//
//        //必需显示内容 协议 显示：（ 本地固定预付服务说明,预付类协议，确认发货协议）
//        integrity_fragment.setShowKey(EAgreement.Query.LOCAL, EAgreement.Query.INTEGRITY, EAgreement.Query.SHIP);
//        this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//
//        transaction.commitAllowingStateLoss();
//    }
//
//    /* ------------------------------ 【车老板预付】-------------------------------------------------*/
//    private void bossUI(ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo) {
//        //【车老板预付】
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//
//        //1.货物明细【展示】  预付开启时，指定拍照,必传，加水印
//        ShipmentsGoodsFragment goodsFragment = ShipmentsGoodsFragment.newFragment();
//        goodsFragment.setEGoodInfoList(shipmentsEGoodInfo, shipmentsEGoodInfo.uploadGrossAndTareWeightFlag());
//        goodsFragment.setSenceAdvance(senceAdvance);
//        //2：发货后预付申请,不可编辑发货吨位信息
//        goodsFragment.setEditInput(this.senceAdvance == 2 ? false : true);
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goodsFragment);
//        transaction.add(R.id.ll_content, goodsFragment, ShipmentUI.TAG_GOODINFO);
//
//        //2. 单据照片【展示】
//        ShipmentsImgObj orderImgObj = advanceInfo.getOrderImgObj();
//        if (orderImgObj != null && orderImgObj.isShowUpload()) {
//            ShipMentFileFragment shipmentBill = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//            shipmentBill.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.setCheckListener(shipmentBill, orderImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), shipmentBill);
//            transaction.add(R.id.ll_content, shipmentBill);
//        }
//
//        //3. 车货合影【展示】
//        ShipmentsImgObj peopleVehicleImgObj = advanceInfo.getPeopleVehicleImgObj();
//        if (peopleVehicleImgObj != null && peopleVehicleImgObj.isShowUpload()) {
//            ShipMentFileFragment shipmentScene = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//            shipmentScene.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.setCheckListener(shipmentScene, peopleVehicleImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), shipmentScene);
//            transaction.add(R.id.ll_content, shipmentScene);
//        }
//
//        //必需显示内容 协议 显示：（ 预付类协议，确认发货协议）
//        integrity_fragment.setShowKey(EAgreement.Query.INTEGRITY, EAgreement.Query.SHIP);
//        this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//
//        transaction.commitAllowingStateLoss();
//    }
//
//    /* ------------------------------ 【货主预付发货】-------------------------------------------------*/
//    private void hzTerracePayUI(final ShipmentsEGoodInfo shipmentsEGoodInfo, EAdvanceInfo advanceInfo) {
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//        //【货主预付发货】
//        tv_money.setVisibility(View.VISIBLE);
//        //货物明细【展示】
//        ShipmentsGoodsFragment goods = ShipmentsGoodsFragment.newFragment();
//        //监听货物明细计算预付金额
//        goods.setGoodInfoTextWatcher(new ShipmentsGoodsFragment.GoodInfoTextWatcher() {
//            @Override
//            public void advanceMoney(List<EGoodInfo> data) {
//                //计算货主预付费用
//                String money = "¥" + shipmentsEGoodInfo.calculationAdvanceMoney(mAdvanceInfo.getAdvanceRatio(), data);
//                SpannableStringBuilder builder = new SpannableStringBuilder("预付运费 ");
//                SpannableString text = new SpannableString(money);
//                text.setSpan(new ForegroundColorSpan(Color.parseColor("#FB6B40")), 0, money.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
//                builder.append(text);
//                tv_money.setText(builder);
//            }
//        });
//        goods.setEGoodInfoList(shipmentsEGoodInfo, shipmentsEGoodInfo.uploadGrossAndTareWeightFlag());
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goods);
//        transaction.add(R.id.ll_content, goods);
//
//        //2.单据照片【展示】
//        ShipmentsImgObj orderImgObj = advanceInfo.getOrderImgObj();
//        if (orderImgObj != null && orderImgObj.isShowUpload()) {
//            ShipMentFileFragment waybillFileFragment = ShipMentFileFragment.newFragment(orderId, shipmentsEGoodInfo.getImageJsonArr(), orderImgObj);
//            waybillFileFragment.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//            this.setCheckListener(waybillFileFragment, orderImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), waybillFileFragment);
//            transaction.add(R.id.ll_content, waybillFileFragment);
//        }
//
//        //3.车货合影【展示】
//        ShipmentsImgObj peopleVehicleImgObj = advanceInfo.getPeopleVehicleImgObj();
//        if (peopleVehicleImgObj != null && peopleVehicleImgObj.isShowUpload()) {
//            ShipMentFileFragment personCarFileFragment = ShipMentFileFragment.newFragment(orderId, this.shipmentsEGoodInfo.getImageJsonArr2(), peopleVehicleImgObj);
//            personCarFileFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            this.setCheckListener(personCarFileFragment, peopleVehicleImgObj);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), personCarFileFragment);
//            transaction.add(R.id.ll_content, personCarFileFragment);
//        }
//
//        //4 必需显示内容 协议 显示：（ 确认发货协议）
//        integrity_fragment.setShowKey(EAgreement.Query.SHIP);
//        this.fragments.put(ShipmentUI.TAG_SERVICE_CHARGE.hashCode(), integrity_fragment);
//
//        transaction.commitAllowingStateLoss();
//    }
//
//    private void setCheckListener(ShipMentFileFragment fileFragment, ShipmentsImgObj imgObj) {
//        //是否加水印
//        fileFragment.setWaterMarkFlag(imgObj.getWaterMarkFlag());
//        //指定拍照(图片可拍照，可选择相册)
//        fileFragment.setTakePhotoListener(imgObj.isTakePhoto());
//        //红色* 号提示必传/非必传
//        fileFragment.setShowReadToastIcon(imgObj.isUpload());
//        //设置校验检查
//        fileFragment.setNoCheckFileListener(list -> !imgObj.isUpload());
//    }
//
//    /* ------------------------------ 确认发货-------------------------------------------------*/
//    private void postShipments() {
//
//        //确认发货
//        UtilSoftKeyboard.hide(mTvOk);
//        //ZCZY-4491 司机确认收发货时获取APP定位优化
//        if (!LocationUtil.isOpenGPS(this)) {
//            //GPS 未打开
//            LocationUtil.openGPS(this);
//            return;
//        }
//
//        PermissionUtil.location(ReapplyTerracePayActivity.this, new PermissionCallBack() {
//            @Override
//            public void onHasPermission() {
//                shipments();
//            }
//        });
//    }
//
//    private void shipments() {
//        final int size = fragments.size();
//        if (size == 0) {
//            return;
//        }
//        //确认发货
//        final ShipmentUI shipmentUI = new ShipmentUI();
//        shipmentUI.goodsSource = shipmentsEGoodInfo.getGoodsSource();
//        shipmentUI.orderId = orderId;
//        shipmentUI.detailId = detailId;
//        shipmentUI.creditPoint = mAdvanceInfo.getCreditPoint();
//        shipmentUI.sdkInfoObj = shipmentsEGoodInfo.getSdkInfoObj();
//        //1预付申请
//        shipmentUI.applyAdvance = "1";
//        //发货后预付申请 or 重新预付 不需要再次上传省部平台2.0 订单数据
//        shipmentUI.haveOpenSdk = false;
//        shipmentUI.coordinateFlag = shipmentsEGoodInfo.getDeliverPicConfig();
//
//        ShipmentsTerracePayFragment terracePayFragment = null;
//        for (int i = 0; i < size; i++) {
//            ShipmentsBaseFragment fragment = fragments.valueAt(i);
//            if (fragment instanceof ShipmentsTerracePayFragment) {
//                terracePayFragment = (ShipmentsTerracePayFragment) fragment;
//            }
//            if (!fragment.checkParams(shipmentUI)) {
//                return;
//            }
//        }
//
//        if (TextUtils.equals("1", shipmentUI.isAdvanceButtonOn)) {
//            //预付款
//            ShipmentsTerracePayFragment finalTerracePayFragment = terracePayFragment;
//
//            if (TextUtils.equals("2", mAdvanceInfo.getAdvanceType())) {
//                //2.个体司机非指定订单
//                ReqQueryAdvanceInfoBeforeCommit infoBeforeCommit = new ReqQueryAdvanceInfoBeforeCommit();
//                infoBeforeCommit.setAdvanceType(shipmentUI.advanceWay);
//                infoBeforeCommit.setOrderId(shipmentUI.orderId);
//                infoBeforeCommit.setWeightDetails(shipmentUI.getCargoIdWeightData());
//                infoBeforeCommit.setAdvanceOilRatio(shipmentUI.oilRatio);
//                this.getViewModel(ShipmentsModel.class).execute(true, infoBeforeCommit, new IResult<BaseRsp<EAdvanceInfoBeforeCommit>>() {
//                    @Override
//                    public void onFail(HandleException e) {
//                        showDialogToast(e.getMsg());
//                    }
//
//                    @Override
//                    public void onSuccess(BaseRsp<EAdvanceInfoBeforeCommit> rsp) throws Exception {
//                        if (rsp.success()) {
//                            DialogBuilder dialogBuilder = new DialogBuilder();
//                            dialogBuilder.setTitle("提交预付款审核");
//                            if (TextUtils.equals("1", rsp.getData().getTipFlag())) {
//
//                                String attributeMsg = rsp.getData().getAttributeMsg();
//                                String totalMsg = rsp.getData().getTotalMsg();
//                                int startIndex = totalMsg.indexOf(attributeMsg);
//
//                                SpannableString stringBuilder = new SpannableString(totalMsg);
//                                stringBuilder.setSpan(new ForegroundColorSpan(Color.parseColor("#F42424")), startIndex, startIndex + attributeMsg.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//                                dialogBuilder.setMessage(stringBuilder);
//
//                            } else {
//                                dialogBuilder.setMessage(rsp.getData().getTotalMsg());
//                            }
//                            dialogBuilder.setCancelListener((dialog, which) -> {
//                                dialog.dismiss();
//
//                                if (TextUtils.equals("1", rsp.getData().getChangRatioFlag())) {
//                                    //预付比例是否变更 1:变更,变动界面比例
//                                    if (finalTerracePayFragment != null) {
//                                        finalTerracePayFragment.onChangeAdvanceRatio(rsp.getData().getAdvanceRatio());
//                                    }
//                                }
//                            });
//                            dialogBuilder.setOkListener((dialog, which) -> {
//                                dialog.dismiss();
//                                ZStatistics.onViewClick(ReapplyTerracePayActivity.this, "deliver_advance");
//                                if (senceAdvance == 2) {
//                                    //发货后预付申请
//                                    getViewModel(ShipmentsModel.class).doAdvanceApplyByDeliverAndNotReceive(shipmentUI);
//                                } else {
//                                    getViewModel(ShipmentsModel.class).confrimOrderDeliver(shipmentUI);
//                                }
//
//                            });
//                            showDialog(dialogBuilder);
//
//                        } else {
//                            showDialogToast(rsp.getMsg());
//                        }
//                    }
//                });
//
//            } else {
//                //预付款
//                DialogBuilder dialogBuilder = new DialogBuilder();
//                dialogBuilder.setTitle("提交预付款审核");
//                String msg = "";
//                if (TextUtils.equals("4", mAdvanceInfo.getAdvanceType())) {
//
//                    //"提交后平台会进行审核，审核通过后会将预付运费打款至您关联的车老板智运宝账户"
//                    msg = TextUtils.equals("2", shipmentUI.advanceWay) ?
//                            "提交后平台会进行审核，审核通过会将预付运费打款至您关联的车老板智运油卡账户" :
//                            TextUtils.equals("3", shipmentUI.advanceWay) ?
//                                    "提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本和智运油卡账户中进行查询" : "提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本中进行查询";
//
//                } else {
//                    //1:预付现金;2:预付油品;3:预付油品+现金
//                    //
//                    msg = TextUtils.equals("2", shipmentUI.advanceWay) ?
//                            "提交后平台会进行审核，审核通过会将预付运费打款至您的智运油卡账户" :
//                            TextUtils.equals("3", shipmentUI.advanceWay) ?
//                                    "提交后平台会进行审核，审核通过后您可在智运账本和智运油卡账户中进行查询" : "提交后平台会进行审核，审核通过后您可在智运账本中进行查询";
//                }
//                dialogBuilder.setMessage(msg);
//                dialogBuilder.setOkListener((dialog, which) -> {
//                    dialog.dismiss();
//                    ZStatistics.onViewClick(ReapplyTerracePayActivity.this, "deliver_advance");
//                    if (this.senceAdvance == 2) {
//                        //发货后预付申请
//                        getViewModel().doAdvanceApplyByDeliverAndNotReceive(shipmentUI);
//                    } else {
//                        getViewModel().confrimOrderDeliver(shipmentUI);
//                    }
//
//                });
//                this.showDialog(dialogBuilder);
//            }
//        } else {
//            if (this.senceAdvance == 2) {
//                //发货后预付申请
//                getViewModel(ShipmentsModel.class).doAdvanceApplyByDeliverAndNotReceive(shipmentUI);
//            } else {
//                getViewModel(ShipmentsModel.class).confrimOrderDeliver(shipmentUI);
//            }
//        }
//        ZStatistics.onViewClick(this, "deliver_deliver");
//    }
//
//    @LiveDataMatch(tag = "发货成功")
//    public void onShipmentSuccess(boolean payTerrace, EShipmentsSuccess data, ShipmentUI shipmentUI) {
//        //发货确认提示框
//        ShipmentsSuccessActivity.start(ReapplyTerracePayActivity.this, "1", shipmentUI.orderId, shipmentUI.advanceWay, shipmentsEGoodInfo, mAdvanceInfo);
//        finish();
//    }
//
//
//    @LiveDataMatch(tag = "客服电话")
//    public void onShowLineServerPhone() {
//        PhoneUtil.callPhone(this, Const.PHONE_SERVER_400);
//    }
//

}
