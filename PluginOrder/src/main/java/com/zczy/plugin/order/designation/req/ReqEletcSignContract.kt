package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：发起电子合同签署
 * 时间：2025/1/10 8:41
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=77464616
 * 对接：陈泰
 * */
class ReqEletcSignContract(
    var carrierMobile: String? = null, //签署手机号
) : BaseNewRequest<BaseRsp<RspEletcSignContract>>("oms-app//bigCarrier/contract/bigCarrierSignContract") {
}

data class RspEletcSignContract(
    var redirectUrl: String? = null,//跳转地址 http://trunk.scm.zczy.com/mobile/#/contractSign/confirmSign?type=1&documentId=ht123456697&token=
) : ResultData()