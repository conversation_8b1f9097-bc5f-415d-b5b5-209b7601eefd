package com.zczy.plugin.order.stevedore.model;

import android.text.TextUtils;

public class ESearchOrder {

   String deliverCity;//	目的地市
    String  deliverDis;//	目的地区
    String  despatchCity;//	启运地市
    String  despatchDis;//	启运地区
    String  orderId;//	运单号
    String  orderMoney;//	运价（成交价）
    String  startTime;//	发货时间
    String  allCargoName;//	总货物名称、数量
    String consignorCompany;//	公司名称
    String freightType;//	费用类型	0 包车价 1单价
    String carrierUserType;//运单归属:2 个体司机 3 承运商 10 车老板

    public String getOrderId() {

        return orderId;
    }


    /***
     * 启运地址【UI】
     * @return
     */
    public String getAddressSatrt() {
        return despatchCity + despatchDis;

    }

    /***
     * 目的地址【UI】
     * @return
     */
    public String getAddressEnd() {
        return deliverCity + deliverDis;
    }


    /***
     * 海尔电器|500吨|13米|高栏车【UI】
     * @return
     */
    public String getAllCargoNameCarType() {

        return allCargoName;
    }

    public String getConsignorCompany() {

        return consignorCompany;
    }

    /***
     * 包车价-单价（(不含税/含税)）【UI】
     * @return
     */
    public String getPriceTypeContent() {


       return TextUtils.equals("1", freightType) ? "单价" : "包车价";

    }
    public String getDespatchtrTimeUI() {

        return startTime;
    }

    /***
     *  当前运单承运人摘的单子
     * @return
     */
    public boolean pickCarrier() {

        //2 个体司机 3 承运商 10 车老板
         return TextUtils.equals("2", carrierUserType);
    }


    /***
     * 金额【UI】
     * @return
     */
    public String getMoney() {

        return orderMoney;
    }
}
