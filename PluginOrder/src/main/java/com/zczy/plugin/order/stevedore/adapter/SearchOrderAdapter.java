package com.zczy.plugin.order.stevedore.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.model.ESearchOrder;

public class SearchOrderAdapter extends BaseQuickAdapter<ESearchOrder, BaseViewHolder> {

    public SearchOrderAdapter() {
        super(R.layout.stevedore_search_order_adapter);
    }

    @Override
    protected void convert(BaseViewHolder helper, ESearchOrder data) {
        //显示运单ID
        helper.setText(R.id.tv_order, data.getOrderId());
        //发货地址
        helper.setText(R.id.tv_start, data.getAddressSatrt());
        //收货地址
        helper.setText(R.id.tv_end, data.getAddressEnd());
        //运单信息：海尔电器|500吨|13米|高栏车
        helper.setText(R.id.tv_vehicle_info, data.getAllCargoNameCarType());
        //发货公司
        helper.setText(R.id.tv_company, data.getConsignorCompany());

        //金额[判断承运人摘单显示价格]
        helper.setText(R.id.tv_price, data.pickCarrier() ? data.getMoney() : "");

        helper.setText(R.id.tv_price, data.getMoney());

        //包车价-单价
        helper.setText(R.id.tv_money_title, data.getPriceTypeContent());
        //装货时间
        helper.setText(R.id.tv_loading_time, data.getDespatchtrTimeUI());
    }
}
