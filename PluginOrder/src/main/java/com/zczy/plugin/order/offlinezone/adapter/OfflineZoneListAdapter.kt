package com.zczy.plugin.order.offlinezone.adapter

import androidx.core.content.ContextCompat
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.user.offlinezone.bean.OfflineZoneListRes
import com.zczy.plugin.order.R

/**
 * @description
 * @date 15:13 5/25/20
 * <AUTHOR>
 * @since 1.0
 **/
class OfflineZoneListAdapter : BaseQuickAdapter<OfflineZoneListRes, BaseViewHolder>(R.layout.item_offline_zone_list) {

    //1 司机列表 2 车老板列表 3 承运商列表
    var userType:String="";

    override fun convert(helper: BaseViewHolder, item: OfflineZoneListRes?) {
        item?.let { it ->

            helper.setText(R.id.tvId, it.orderId)
            helper.addOnClickListener(R.id.tvCopy)
            helper.addOnClickListener(R.id.tv_code)
            if ((it.orderStateStr?.startsWith("待发货") == true || it.orderStateStr?.startsWith("待收货") == true)
                && TextUtils.equals("1",userType)
            ){
                helper.setGone(R.id.tv_code,true)
            }else{
                helper.setGone(R.id.tv_code,false)
            }
            helper.setText(R.id.tvQuotationStatus, it.orderStateStr)

            helper.setText(R.id.tvStartAddr, it.getStartAddress())
            helper.setText(R.id.tvEndAddr,  it.getEndAddress())

            helper.setImageResource(R.id.imgBargain, if (it.orderModel == "1") R.drawable.order_waybill_jj else R.drawable.order_waybill_qd)

            helper.setGone(R.id.imgOffline, it.tenderFlag == "1")

            helper.setText(R.id.tvGoods, it.goodsDescription)

            if (TextUtils.equals("1",userType) && (!TextUtils.isEmpty(it.delistUserType)) &&(!TextUtils.equals("1",it.delistUserType)) ){
                //承运人列表 && 此单不属于承运人
                helper.setGone(R.id.tvCarPrice,false)
            }else{
                val displayMoney = "${it.displayMoney ?: "0.00"}元"
                val displayMoneySpannableString = SpannableString(displayMoney)
                displayMoneySpannableString.setSpan(AbsoluteSizeSpan(17, true), 0, displayMoneySpannableString.length - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                displayMoneySpannableString.setSpan(AbsoluteSizeSpan(13, true), displayMoneySpannableString.length - 1, displayMoneySpannableString.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                displayMoneySpannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.text_33)), 0, displayMoneySpannableString.length - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                displayMoneySpannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(mContext, R.color.text_99)), displayMoneySpannableString.length - 1, displayMoneySpannableString.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                helper.setText(R.id.tvCarPrice, displayMoneySpannableString).setGone(R.id.tvCarPrice,true)
            }

            helper.setText(R.id.tvCarPriceType, if (it.freightType == "1") "单价" else "包车价")

            helper.setText(R.id.tvCompany, it.consignorCompany)

            helper.setText(R.id.tvLoadGoodsTime, it.despatchStart + "~" + it.despatchEnd)

            val tenderButtonDto = it.tenderButtonDto
            tenderButtonDto?.let {
                val btnText = when {
                    it.quotePrice -> WANT_TO_QUOTE
                    it.cancelPrice -> CANCEL_QUOTE
                    it.reHangPrice -> RENEW_QUOTE
                    else -> ""
                }
                if (btnText.isBlank()) {
                    helper.setGone(R.id.btnQuote, false)
                } else {
                    helper.setGone(R.id.btnQuote, true)
                    helper.setText(R.id.btnQuote, btnText)
                    helper.addOnClickListener(R.id.btnQuote)
                }
            }

            helper.setGone(R.id.btnChange, tenderButtonDto?.modifyPrice == true)
                .addOnClickListener(R.id.btnChange)

            helper.setGone(R.id.bt_deliver_goods, tenderButtonDto?.deliverGoods == true)
                .addOnClickListener(R.id.bt_deliver_goods)

            helper.setGone(R.id.bt_receive_goods, tenderButtonDto?.receiveGoods == true)
                .addOnClickListener(R.id.bt_receive_goods)

            helper.setGone(R.id.bt_cancel_waybill, tenderButtonDto?.cancelRelease == true)
                .addOnClickListener(R.id.bt_cancel_waybill)

        }
    }

    companion object {
        const val WANT_TO_QUOTE = "我要报价"
        const val CANCEL_QUOTE = "取消报价"
        const val RENEW_QUOTE = "重新报价"
    }
}
