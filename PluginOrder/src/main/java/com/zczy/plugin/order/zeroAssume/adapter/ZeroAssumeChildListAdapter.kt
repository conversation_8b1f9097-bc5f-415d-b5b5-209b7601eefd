package com.zczy.plugin.order.zeroAssume.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isInvisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.SpaceItemDecoration
import com.zczy.plugin.order.R
import com.zczy.plugin.order.waybill.ExceptionListViewAdapter
import com.zczy.plugin.order.waybill.ExceptionListViewAdapter.LookMoreListener
import com.zczy.plugin.order.waybill.ExceptionListViewAdapterListener
import com.zczy.plugin.order.waybill.WaybillButtonLayout
import com.zczy.plugin.order.waybill.WaybillButtonLayout.OnButtonClickListener
import com.zczy.plugin.order.waybill.entity.EWaybill

class ZeroAssumeChildListAdapter :
    BaseQuickAdapter<EWaybill, BaseViewHolder>(R.layout.item_zero_assume_child_list) {
    private var buttonClickListener: OnButtonClickListener? = null
    private var exceptionListViewAdapterListener: ExceptionListViewAdapterListener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        val baseViewHolder = super.onCreateViewHolder(parent, viewType)
        val exceptionListView = baseViewHolder.getView<RecyclerView>(R.id.exception_list_view)
        if (exceptionListView != null) {
            exceptionListView.layoutManager = LinearLayoutManager(mContext)
            exceptionListView.addItemDecoration(SpaceItemDecoration(dp2px(7f)))
            exceptionListView.setHasFixedSize(true)
            exceptionListView.isNestedScrollingEnabled = false
            val exceptionListViewAdapter = ExceptionListViewAdapter()
            exceptionListViewAdapter.setLookMoreListener(object : LookMoreListener {
                override fun onLookMore(isLook: Boolean, fullText: String?) {
                    exceptionListViewAdapterListener?.onClickLookMore(fullText)
                }
            })
            exceptionListViewAdapter.setOnItemChildClickListener { adapter: BaseQuickAdapter<*, *>?, view: View, position: Int ->
                if (view.id == R.id.tv_handle_item) {
                    //去处理
                    val adapterPosition = baseViewHolder.adapterPosition
                    val waybill = getItem(adapterPosition)
                    val exceptionInfo = exceptionListViewAdapter.getItem(position)
                    exceptionListViewAdapterListener?.onClickToHandleBtn(waybill, exceptionInfo)
                }
            }
            exceptionListView.adapter = exceptionListViewAdapter
            exceptionListView.tag = exceptionListViewAdapter
        }
        return baseViewHolder
    }

    fun setExceptionListViewAdapterListener(listener: ExceptionListViewAdapterListener): BaseQuickAdapter<*, *>? {
        this.exceptionListViewAdapterListener = listener
        return this
    }

    private fun initExceptionListView(helper: BaseViewHolder, item: EWaybill) {
        val exceptionInfoNew = item.exceptionInfoNew
        if (exceptionInfoNew != null && exceptionInfoNew.size > 0) {
            //运单异常
            val exceptionListView = helper.setGone(R.id.exception_list_view, true).getView<RecyclerView>(R.id.exception_list_view)
            val exceptionListViewAdapter = exceptionListView.tag as ExceptionListViewAdapter
            exceptionListViewAdapter.setNewData(item.exceptionInfoNew)
        } else {
            helper.setGone(R.id.exception_list_view, false)
        }
    }

    override fun convert(helper: BaseViewHolder?, item: EWaybill?) {

        helper?.apply {
            item?.let { initExceptionListView(helper, it) }
            setText(R.id.tv_num, (helper.adapterPosition + 1).toString())
            setText(R.id.tv_order, item?.orderId)
            setText(R.id.tv_toast, item?.orderCurrentState)
            addOnClickListener(R.id.tv_copy)
            //隐藏支付方式：油卡图标-预付图标-回单付图标-收款方式
            setGone(R.id.iv_paly_oil, item?.oil() == true)
            setGone(R.id.tv_paly_prepay, item?.advance() == true)
            setGone(R.id.iv_paly_receipt, item?.receipt() == true)
            setGone(R.id.iv_risk, item?.risk() == true)
            setGone(R.id.iv_backup, item?.Backup() == true)
            setGone(R.id.iv_fine, item?.priorSelectFlag() == true)
            setGone(R.id.iv_takeCare, item?.ispolicyFlag() == true)
            //预挂单图标
            item?.orderPresetFlag()?.let { setGone(R.id.iv_paly_preset, it) }
            item?.getorderPresetFlagIcon()?.let { setImageResource(R.id.iv_paly_preset, it) }
            helper.setGone(R.id.iv_cintainer, TextUtils.equals("1", item?.goodsSource))
            //预付比例
            setText(R.id.tv_paly_prepay, item?.advanceText())
            //发货地址
            setText(R.id.tv_start, item?.addressSatrt)
            //收货地址
            setText(R.id.tv_end, item?.addressEnd)
            //运单信息：海尔电器|500吨|13米|高栏车
            setText(R.id.tv_vehicle_info, item?.allCargoNameCarType)
            //发货公司
            setText(R.id.tv_company, item?.consignorCompany)
            if (TextUtils.equals(
                    EWaybill.TYPE_MAKEOFFERS,
                    item?.orderQueryType
                ) || TextUtils.equals(EWaybill.TYPE_MIND, item?.orderQueryType)
            ) {
                //报价列表 金额[显示报价]
                setText(R.id.tv_price, item?.money)
            } else {
                //金额[判断承运人摘单显示价格]
                setText(
                    R.id.tv_price,
                    if (item?.pickCarrier() == true) item.money else ""
                )
            }
            //包车价-单价
            setText(R.id.tv_money_title, item?.getPriceTypeContentV1(item.goodsSource))
            //运单模式 议价 or 抢单
            setImageResource(
                R.id.iv_type,
                if (item?.biddingType() == true) R.drawable.base_order_type_bidding else R.drawable.base_order_type_rob
            )
            //装货-卸货图标 true 待装货图标 false 待卸货
            if (item?.showDelayShipmentIcon() == true) {
                setGone(R.id.iv_loading_time, true).setGone(R.id.tv_loading_time, true)
                //装货图标
                setImageResource(R.id.iv_loading_time, R.drawable.base_order_shipmen_time)
                //装货时间
                setText(R.id.tv_loading_time, item.despatchtrTimeUIV1)
            } else {
                //图标不显示
                helper.getView<ImageView>(R.id.iv_loading_time).isInvisible = true
                helper.getView<TextView>(R.id.tv_loading_time).isInvisible = true
            }
            setGone(R.id.vLine2, item?.buttons?.isAll == true)
            //复制
            addOnClickListener(R.id.tv_copy).addOnClickListener(R.id.tv_order)
            //导航
            setGone(R.id.iv_dh, false).addOnClickListener(R.id.iv_dh)
            //回单押金
            setText(R.id.orderMoney, "回单押金：" + item?.receiptMoney + "元")
                .setGone(R.id.orderMoney, !TextUtils.isEmpty(item?.receiptMoney))

            //操作按钮
            val buttonLayout = getView<WaybillButtonLayout>(R.id.waybillButtonLayout)
            buttonLayout.setGoneView()
            if (TextUtils.equals(item?.isReceipt, "1") || item?.buttons?.isAll == true) {
                buttonLayout.visibility = View.VISIBLE
            } else {
                buttonLayout.visibility = View.GONE
            }
            val map = buttonLayout.keyVue
            map["确认发货"] = item?.buttons?.isDeliverGoods
            if (TextUtils.equals(EWaybill.TYPE_UNLOAD, item?.orderQueryType) && item?.ownerBack() != true) {
                //0 普通收货,1 扫码收货,2 系统对接收货
                val content = if (TextUtils.equals("1", item?.coderType)) "扫码收货" else "确认卸货"
                map[content] = true
            }
            map["变更承运"] = item?.buttons?.isChangeTrs
            map["取消变更"] = item?.buttons?.isCancelCarrierChange
            map["取消运单"] = item?.buttons?.isBreachApply
            map["运单详情"] = item?.buttons?.isOrderStatus
            map["合同补签"] = item?.buttons?.isSignContract
            map["快递录入"] = TextUtils.equals("1", item?.isReceipt)
            map["查看评价"] = item?.buttons?.isLookEvaluation
            map["再来一单"] = item?.buttons?.isCarryAgain
            map["回单修改"] = item?.buttons?.isModifyBackOrder
            map["重新上传"] = item?.buttons?.isReloading
            map["卸货拍照"] = item?.buttons?.isCarrierReceivePicture


            //头部警告-不可见
            helper.setGone(R.id.cl_warning, false).addOnClickListener(R.id.tv_handle)

            if (item?.riskAuditFlag() == true) {
                helper.setGone(R.id.cl_warning, true).setText(R.id.tv_warning, "请尽快联系车老板完成认证")
                    .setGone(R.id.tv_handle, false)
            }
            if (TextUtils.equals(EWaybill.TYPE_DELAYSHIPMENT, item!!.orderQueryType)) {// 待装货
                // 警告提示，货主配置项中配置了只允许一单在途，承运方有在途的订单
                helper.setGone(R.id.cl_warning, item.promptOnTheWay())
                    .setText(R.id.tv_warning, "您有一单在途运单，请等结束后再发货")
                    .setGone(R.id.tv_handle, false)
                //导航
                helper.setGone(R.id.iv_dh, true)
                    .setBackgroundRes(R.id.iv_dh, R.drawable.order_to_cargo_up)
            } else if (TextUtils.equals(EWaybill.TYPE_UNLOAD, item.orderQueryType)) { // 待卸货
                //导航
                helper.setGone(R.id.iv_dh, true)
                    .setBackgroundRes(R.id.iv_dh, R.drawable.order_to_cargo_down)

            } else if (TextUtils.equals(EWaybill.TYPE_BARGAIN, item.orderQueryType)) {
                //议价头部显示
                if (item.isShowBargainingFlag) {
                    helper.setGone(R.id.cl_warning, true)
                        .setText(R.id.tv_warning, item.interveneUI)
                        .setGone(R.id.tv_handle, true)
                        .setText(R.id.tv_handle, item.showHander)
                }
            } else {
                //已完成
                helper.setGone(R.id.tv_handle, item.showHanderV1)
                    .setGone(R.id.cl_warning, item.showHanderV1)
                    .setText(R.id.tv_warning, item.interveneUI)
                    .setText(R.id.tv_handle, item.showHander)

            }
            buttonLayout.show(map)
            buttonLayout.setAdapterPosition(
                item,
                helper.adapterPosition,
                buttonClickListener
            )
        }
    }

    fun setButtonViewClickListener(buttonClickListener: OnButtonClickListener) {
        this.buttonClickListener = buttonClickListener
    }
}

