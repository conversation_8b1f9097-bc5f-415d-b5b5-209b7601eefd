//package com.zczy.plugin.order.shipments;
//
//import android.os.Bundle;
//import android.view.View;
//import android.widget.TextView;
//
//import androidx.annotation.Nullable;
//
//import com.zczy.comm.ui.BaseDialog;
//import com.zczy.plugin.order.R;
//
//
///**
// * 功能描述:ZCZY-6636 【互联互通】兰鑫钢铁系统对接
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/11/19
// */
//public class ToastDialog extends BaseDialog implements View.OnClickListener {
//
//    private String title;
//    private String contact;
//    private String ok;
//
//    @Override
//    protected String getDialogTag() {
//        return "ToastDialog";
//    }
//
//    @Override
//    protected int getDialogLayout() {
//        return R.layout.order_shipments_toast_dialog;
//    }
//
//    View.OnClickListener onClickListener;
//
//    public ToastDialog setOnClickListener(View.OnClickListener onClickListener) {
//        this.onClickListener = onClickListener;
//        return this;
//    }
//
//    @Override
//    public void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//    }
//
//    @Override
//    protected void bindView(View view, @Nullable Bundle bundle) {
//        TextView tv_title = view.findViewById(R.id.tv_title);
//        TextView tv_contact = view.findViewById(R.id.tv_contact);
//        TextView tv_ok = view.findViewById(R.id.tv_ok);
//        view.findViewById(R.id.iv_close).setOnClickListener(this);
//        tv_ok.setOnClickListener(this);
//        tv_title.setText(title);
//        tv_contact.setText(contact);
//        tv_ok.setText(ok);
//    }
//
//    @Override
//    public void onClick(View view) {
//        if (view.getId() == R.id.iv_close) {
//            dismissAllowingStateLoss();
//
//        } else if (view.getId() == R.id.tv_ok) {
//            dismissAllowingStateLoss();
//            if (onClickListener != null) {
//                onClickListener.onClick(view);
//            }
//        }
//    }
//
//    public ToastDialog setTitle(String title) {
//        this.title = title;
//        return this;
//    }
//
//    public ToastDialog setCon(String con) {
//        this.contact = con;
//        return this;
//    }
//
//    public ToastDialog setBtnText(String text) {
//        this.ok = text;
//        return this;
//    }
//}
