package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 8. 获取保证金
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=12355404
 * Created by sdx on 2019/2/22.
 */
data class Req8QueryCarrierBondMoneyConfig(
        var orderId: String = "", // 订单id
        var carrierUserId: String? = "" // 承运人id
) : BaseOrderRequest<BaseRsp<RspQueryCarrierBondMoneyConfig>>("oms-app/orderChange/queryCarrierBondMoneyConfig")

data class RspQueryCarrierBondMoneyConfig(
        var carrierBondMoney: String = "" // 保证金
) : ResultData()
