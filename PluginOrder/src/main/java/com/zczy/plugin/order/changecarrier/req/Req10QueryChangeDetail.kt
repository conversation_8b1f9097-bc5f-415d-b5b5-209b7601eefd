package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 10.运单变更详情
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355863
 * Created by sdx on 2019/2/22.
 */
data class Req10QueryChangeDetail(
        var changeId: String = "" // 变更id
) : BaseOrderRequest<BaseRsp<RspChangeDetail>>("oms-app/orderChange/queryChangeDetail")

data class RspChangeDetail(
        var orderId: String = "", // 订单ID
        var changeType: String = "", // 变更类型 变更类型：1 变更人，2 变更车，3 变更人和车
        var carrierUserId: String = "", // 变更人ID
        var name: String = "", // 变更人名称
        var mobile: String = "", // 变更人手机
        var plateNumber: String = "", // 变更人车牌号
        var oldCarrierUserId: String = "", // 原承运人ID
        var oldName: String = "", // 原承运人名称
        var oldMobile: String = "", // 原承运人手机
        var oldPlateNumber: String = "", // 原车牌号
        var examineContent: String = "", // 审核人意见
        /** 变更人是否同意：0否 1是   3 需要同意拒绝 */
        var carrierState: String = "", // 变更人是否同意 0 否，1 是
        var carrierContent: String = "", // 变更人意见
        /** 审核状态 [0-审核中 1-审核通过 2-审核不通过] */
        var examineState: String = "",
        var delistType: String = "",// 摘牌类型：1 团摘，2 直摘'
        /**是否加盟运力操作的变更  0 否 1 是*/
        var dispatchOperationFlag: String = ""
) : ResultData()


fun RspChangeDetail.formatChangeType(): String {
    return when (changeType) { // 1 变更人，2 变更车，3 变更人和车'
        "1" -> "变更人"
        "2" -> "变更车辆"
        "3" -> "变更人和车辆"
        else -> ""
    }
}

fun RspChangeDetail.formatDelistType(): String {
    return when (delistType) { // 摘牌类型：1 团摘，2 直摘'
        "1" -> "团摘"
        "2" -> "抢单"
        else -> ""
    }
}

fun RspChangeDetail.formatExamineState(): String {
    // 审核状态 [0-审核中 1-审核通过 2-审核不通过]
    return when (examineState) {
        "0" -> "未审核"
        "1" -> "后台通过"
        "2" -> "后台不通过"
        else -> ""
    }
}

fun RspChangeDetail.formatExamineContent(): String {
    return if (examineContent.isEmpty()) {
        "无"
    } else {
        examineContent
    }
}

fun RspChangeDetail.formatCarrierState(): String {
    return when (carrierState) { // 0 否，1 是
        "0" -> "否"
        "1" -> "是"
        else -> ""
    }
}

fun RspChangeDetail.formatCarrierContent(): String {
    return if (carrierContent.isEmpty()) {
        "无"
    } else {
        carrierContent
    }
}