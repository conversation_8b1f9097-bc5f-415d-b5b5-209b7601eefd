package layout

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.certificate.Utils
import com.zczy.plugin.order.R
import com.zczy.plugin.order.designation.req.ElectUserInfo

class ElectSignContractAdapter : BaseQuickAdapter<ElectUserInfo, BaseViewHolder>(R.layout.elect_sign_contract_item) {
    var selectItem: ElectUserInfo? = null
    override fun convert(helper: BaseViewHolder, item: ElectUserInfo) {
        helper.setText(R.id.tv_mobile, Utils.hideMobile(item.mobile))
            .setText(R.id.tv_name, item.userRealName)

        if (selectItem != null && item === selectItem) {
            helper.setImageResource(R.id.iv_select, R.drawable.check_circle_filled_v3)
                .setBackgroundRes(R.id.ll_container, R.drawable.bg_bill_select_v2)
        } else {
            helper.setImageResource(R.id.iv_select, R.drawable.base_check_unselect)
                .setBackgroundRes(R.id.ll_container, R.drawable.bg_bill_select)
        }
    }

    fun setSelect(item: ElectUserInfo?) {
        this.selectItem = item
        notifyDataSetChanged()
    }
}