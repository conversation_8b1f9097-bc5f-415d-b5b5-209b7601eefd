package com.zczy.plugin.order.shipments;

import android.content.Context;

import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.JsonUtil;

import java.util.HashMap;


/**
 * 功能描述:确认发货【线下专区专用】
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/12/20
 */
public class OffLineShipmentsActivity
//        extends AbstractLifecycleActivity<ShipmentsModel>
{

    public static void start(Context fragment, String orderId, String detailId) {
        AMainServer aMainServer = AMainServer.getPluginServer();
        HashMap<String, String> data = new HashMap<String, String>(3);
        data.put("orderId", orderId);
        data.put("detailId", detailId);
        aMainServer.openReactNativeActivity(fragment, "OffLineShipmentsPage", JsonUtil.toJson(data));

        //Intent intent = new Intent(fragment, OffLineShipmentsActivity.class);
        //intent.putExtra("orderId", orderId);
        //intent.putExtra("detailId", detailId);
        //fragment.startActivity(intent);
    }

//    private String orderId;
//    private String detailId;
//    private EditTextCloseView et_account;
//
//    private SparseArray<ShipmentsBaseFragment> fragments = new SparseArray<>(5);
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate(savedInstanceState);
//        this.setContentView(R.layout.order_offlineshipments_activity);
//
//        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
//        this.orderId = getIntent().getStringExtra("orderId");
//        this.detailId = getIntent().getStringExtra("detailId");
//        AppToolber appToolber = findViewById(R.id.appToolber);
//        appToolber.setLeftOnClickListener(v -> {
//            cancleAlertMsg();
//        });
//        TextView tv_orderId = this.findViewById(R.id.tv_orderId);
//        tv_orderId.setText("运单编号   " + this.orderId);
//        et_account = findViewById(R.id.et_account);
//        UtilTool.setEditTextInputSize(et_account, 4);
//
//        this.putDisposable(UtilRxView.clicks(findViewById(R.id.tv_ok), 1000).subscribe((Object o) -> {
//            postShipments();
//
//        }));
//        //查询货物信息
//        this.getViewModel(ShipmentsModel.class).openSenceOffline(new ReqOffLineBeforeDeliverCargoQuery(orderId));
//
//    }
//
//
//    @LiveDataMatch(tag = "查询货物信息")
//    public void onDeliverCargoQuerySuccess(OffLineShipmentsEGoodInfo info) {
//
//        if (info == null) {
//            showDialogToast("未查询到相关货物信息");
//            return;
//        }
//
//        if (TextUtils.equals("1", info.getLTCantUpdate())) {
//            //司机不能修改重量（龙腾特钢运单）不能修改返回 1
//            et_account.setEnabled(false);
//            et_account.setText(info.getLTTotalMoney());
//        }
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//
//        //  单据照片【展示】
//        ShipMentFileFragment shipmentBill = ShipMentFileFragment.newFragment(orderId, new ArrayList<>(), "纸质运单照片(选填)", "最多可上传3张照片", 3);
//        shipmentBill.setShowReadToastIcon(false);
//        shipmentBill.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//        shipmentBill.setNoCheckFileListener(new ShipMentFileFragment.NoCheckFileListener() {
//            @Override
//            public boolean noCheck(List<EProcessFile> list) {
//                //图片非必传
//                return true;
//            }
//        });
//        this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), shipmentBill);
//        transaction.add(R.id.ll_content, shipmentBill);
//        transaction.commitAllowingStateLoss();
//
//    }
//
//    /* ------------------------------ 确认发货-------------------------------------------------*/
//    private void postShipments() {
//        final int size = fragments.size();
//        if (size == 0) {
//            return;
//        }
//        //确认发货
//        UtilSoftKeyboard.hide(findViewById(R.id.tv_ok));
//        if (!LocationUtil.isOpenGPS(this)) {
//            //GPS 未打开
//            LocationUtil.openGPS(this);
//            return;
//        }
//
//        String weight = et_account.getText().toString();
//        if (TextUtils.isEmpty(weight)) {
//            showToast("请输入发货吨位！");
//            return;
//        }
//        if (Double.valueOf(weight) <= 0.0) {
//            showToast("请输入正确的发货吨位！");
//            return;
//        }
//
//        ShipmentUI shipmentUI = new ShipmentUI();
//        shipmentUI.orderId = orderId;
//        shipmentUI.detailId = detailId;
//
//        for (int i = 0; i < size; i++) {
//            ShipmentsBaseFragment fragment = fragments.valueAt(i);
//            if (!fragment.checkParams(shipmentUI)) {
//                return;
//            }
//        }
//        this.getViewModel(ShipmentsModel.class).confrimOrderDeliverOffLine(shipmentUI, weight);
//    }
//
//    @LiveDataMatch(tag = "发货成功")
//    public void onShipmentSuccess() {
//        this.showToast("发货成功！");
//        this.finish();
//    }
//
//    @Override
//    public boolean onKeyDown(int keyCode, KeyEvent event) {
//
//        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
//            cancleAlertMsg();
//            return true;
//        }
//        return super.onKeyDown(keyCode, event);
//    }
//
//    /**
//     * 点击返回键时候提示信息
//     */
//    private void cancleAlertMsg() {
//
//        DialogBuilder dialogBuilder = new DialogBuilder();
//        dialogBuilder.setTitle("提示");
//        dialogBuilder.setMessage("确认发货未完成，您确认要离开？");
//        dialogBuilder.setOkListener((dialog, which) -> {
//
//            dialog.dismiss();
//            finish();
//        });
//        this.showDialog(dialogBuilder);
//    }
}
