package com.zczy.plugin.order.changecarrier.model

import android.content.Context
import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.Const
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.pluginserver.ACertificateServer
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.pluginserver.AWisdomServer
import com.zczy.comm.utils.PhoneUtil.callPhone
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.source.bean.req.ReqQueryHigCargoMoneyOrderRiskControl
import com.zczy.plugin.order.source.bean.req.RspQueryHigCargoMoneyOrderRiskControl

class OrderChangeCarrierMainChangeHistoryModel : BaseViewModel() {
    fun getNetInfo(nowPage: Int, selectType: String, title: String = "") {
        execute(Req6QueryChangeOrderInfoPage(
            currentPage = nowPage,
            selectType = selectType,
            title = title
        ),
            object : IResult<BaseRsp<PageList<RspOrderChangeData>>> {
                override fun onSuccess(t: BaseRsp<PageList<RspOrderChangeData>>) {
                    if (t.success()) {
                        setValue("onGetNetInfoSuccess", t.data)
                    } else {
                        setValue("onGetNetInfoSuccess", null)
                        showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    setValue("onGetNetInfoSuccess", null)
                    showDialogToast(e.msg)
                }
            })
    }

    fun queryHigCargoMoneyOrderRiskControl(item: RspOrderChangeData) {
        val req = ReqQueryHigCargoMoneyOrderRiskControl()
        req.orderId = item.orderId
        this.execute(
            req
        ) { baseRsp: BaseRsp<RspQueryHigCargoMoneyOrderRiskControl> ->
            if (baseRsp.success()) {
                setValue("queryHigCargoMoneyOrderRiskControlSuccess", baseRsp.data, item)
            } else if (TextUtils.equals("1111", baseRsp.data?.resultCode)) {
                //ZCZY-17015 高货值摘单会员终审、实名认证管控
                setValue("queryHigCargoMoneyOrderRiskControlError", baseRsp.data, item)
            } else {
                showToast(baseRsp.msg)
            }
        }
    }

    // 变更决定   1同意
    fun decideChangeAgree(
        context: Context?,
        orderId: String,
        carrierHandleUserId: String?,
        changeId: String,
        carrierContent: String,
        esignFlag: String,
        orderSource: String,
    ) {

        var runNext = Runnable {
            execute(true,
                Req8QueryCarrierBondMoneyConfig(
                    orderId = orderId,
                    carrierUserId = carrierHandleUserId
                )
                    .task
                    .flatMap {
                        if (!it.success()) {
                            throw Exception(it.msg)
                        }
                        Req4decideChange(
                            carrierContent = carrierContent,
                            carrierState = "1",
                            changeId = changeId,
                            money = it.data?.carrierBondMoney ?: "",
                            esignFlag = esignFlag,
                            changeOrderSource = orderSource,
                        ).task
                    },
                IResultSuccess<BaseRsp<RspDecideChange>> { t ->
                    if (t.success()) {
                        setValue("onDecideChange", t.data)
                    } else if (TextUtils.equals("1113", t.code)) {
                        var dialog = DialogBuilder()
                        dialog.title = t.msg
                        dialog.message =
                            "承运方当前无可用诚意金订单，请提醒其支付诚意金订单后，再重新发起交易申请。"
                        dialog.setOKTextListener("支付诚意金") { dialog, _ ->
                            dialog.dismiss()
                            checkVerifyEarenstCanConfig()
                        }
                        showDialog(dialog)

                    } else if (TextUtils.equals("1114", t.code)) {
                        //ZCZY-17015  高货值摘单会员终审、实名认证管控
                        var dialog = DialogBuilder()
                        dialog.title = t.msg
                        dialog.message = t.msg
                        dialog.setOKTextListener("去完善") { dialog, _ ->
                            dialog.dismiss()
                            AMainServer.getPluginServer().userAuthent(context)
                        }
                        showDialog(dialog)
                    } else if (TextUtils.equals("2001", t.code)) {
                        var dialog = DialogBuilder()
                        dialog.message = t.msg
                        dialog.isHideCancel = true
                        dialog.setOKTextListener("去处理") { dialog, _ ->
                            dialog.dismiss()

                            setValue("openUserAuthent")

                        }
                        showDialog(dialog)
                    } else if (TextUtils.equals("2002", t.code)) {
                        //  2002 （去绑定银行卡按钮 此时有个字段bindingBankCard 为“1”）
                        var dialog = DialogBuilder()
                        dialog.message = t.msg
                        dialog.setOKTextListener("去绑定") { dialog, _ ->
                            dialog.dismiss()
                            AWisdomServer.getPluginServer().startWisdomBankListActivity(context)
                        }
                        showDialog(dialog)
                    } else if (TextUtils.equals("2003", t.code)) {
                        //  2003 （去处理道路运输证按钮 此时有个字段uploadRoadLicense 为“1”）
                        var dialog = DialogBuilder()
                        dialog.message = t.msg
                        dialog.setOKTextListener("去处理") { dialog, _ ->
                            dialog.dismiss()
                            ACertificateServer.getPluginServer().startOverdueExpiredUI(context, t.data?.vehicleId, t.data?.plateNumber, t.data?.licenseState)
                        }
                        showDialog(dialog)
                    } else {
                        showDialogToast(t.msg)
                    }
                })
        }

        this.execute(true,
            ReqCheckBackUpReceiveBeforeChange(changeId = changeId),
            IResultSuccess<BaseRsp<ResultData>> { t ->
                if (t.success()) {
                    //继续执行
                    runNext.run()
                } else if (TextUtils.equals("1111", t.code)) {
                    var dialog = DialogBuilder()
                    dialog.setMessage(t.msg)
                    dialog.setOkListener { dialog, _ ->
                        dialog.dismiss()
                        //继续执行
                        runNext.run()
                    }
                    showDialog(dialog)
                } else {
                    showDialogToast(t.msg)
                }
            })


    }

    fun decideChangeTms(
        context: Context?,
        changeId: String,
        carrierContent: String,
        esignFlag: String,
        orderSource: String,
    ) {
        this.execute(
            true, Req4decideChange(
                carrierContent = carrierContent,
                carrierState = "1",
                changeId = changeId,
                money = "",
                esignFlag = esignFlag,
                changeOrderSource = orderSource,
            )
        ) { t ->
            if (t.success()) {
                setValue("onDecideChange", t.data)
            } else if (TextUtils.equals("1113", t.code)) {
                var dialog = DialogBuilder()
                dialog.title = t.msg
                dialog.message =
                    "承运方当前无可用诚意金订单，请提醒其支付诚意金订单后，再重新发起交易申请。"
                dialog.setOKTextListener("支付诚意金") { dialog, _ ->
                    dialog.dismiss()
                    checkVerifyEarenstCanConfig()
                }
                showDialog(dialog)

            } else if (TextUtils.equals("1114", t.code)) {
                //ZCZY-17015  高货值摘单会员终审、实名认证管控
                var dialog = DialogBuilder()
                dialog.title = t.msg
                dialog.message = t.msg
                dialog.setOKTextListener("去完善") { dialog, _ ->
                    dialog.dismiss()
                    AMainServer.getPluginServer().userAuthent(context)
                }
                showDialog(dialog)
            } else if (TextUtils.equals("2001", t.code)) {
                var dialog = DialogBuilder()
                dialog.message = t.msg
                dialog.isHideCancel = true
                dialog.setOKTextListener("去处理") { dialog, _ ->
                    dialog.dismiss()
                    setValue("openUserAuthent")

                }
                showDialog(dialog)
            } else if (TextUtils.equals("2002", t.code)) {
                //  2002 （去绑定银行卡按钮 此时有个字段bindingBankCard 为“1”）
                var dialog = DialogBuilder()
                dialog.message = t.msg
                dialog.setOKTextListener("去绑定") { dialog, _ ->
                    dialog.dismiss()
                    AWisdomServer.getPluginServer().startWisdomBankListActivity(context)
                }
                showDialog(dialog)
            } else if (TextUtils.equals("2003", t.code)) {
                //  2003 （去处理道路运输证按钮 此时有个字段uploadRoadLicense 为“1”）
                var dialog = DialogBuilder()
                dialog.message = t.msg
                dialog.setOKTextListener("去处理") { dialog, _ ->
                    dialog.dismiss()
                    ACertificateServer.getPluginServer().startOverdueExpiredUI(context, t.data?.vehicleId, t.data?.plateNumber, t.data?.licenseState)
                }
                showDialog(dialog)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun checkVerifyEarenstCanConfig() {
        execute(true, ReqVerifyEarenstCanConfig(), IResultSuccess {
            if (it.success()) {
                if (TextUtils.equals("", it.data?.type)) {
                    //免保
                    showDialogToast("尊敬的用户，您当前为中储平台免保用户，交易过程中无需支付诚意金。")
                } else {
                    setValue("onPlayMoney", it.data?.earnestMoney)
                }

            } else {
                showDialogToast(it.msg)
            }
        })
    }

    // 变更决定 0不同意
    fun decideChangeReject(changeId: String, carrierContent: String, orderSource: String?) {
        execute(
            true,
            Req4decideChange(
                carrierContent = carrierContent,
                carrierState = "0",
                changeId = changeId,
                money = "0",
                changeOrderSource = orderSource,
            ),
            IResultSuccess<BaseRsp<RspDecideChange>> { t ->
                if (t.success()) {
                    setValue("onDecideChange", t.data)
                } else {
                    showDialogToast(t.msg)
                }
            })
    }

    /**
     * 检查是否开户
     * */
    fun checkHaveAccountState(context: Context?, orderId: String, consignorUserId: String?) {
        execute(
            true,
            ReqCheckHaveAccountState(orderId, consignorUserId)
        ) { t ->
            if (t.success()) {
                setValue("onCheckHaveAccountState")
            } else {
                if (TextUtils.equals("1", t.data?.operateType)) {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = t.msg
                    dialog.setOKText("放弃抢单")
                    dialog.setCancelTextListener("联系客服") { dialog: DialogBuilder.DialogInterface, _: Int ->
                        //联系客服
                        callPhone(context, Const.PHONE_SERVER_400)
                        dialog.dismiss()
                    }
                    dialog.setOkListener { dialog: DialogBuilder.DialogInterface, _: Int ->
                        dialog.dismiss()
                    }
                    showDialog(dialog)
                } else if (TextUtils.equals("2", t.data?.operateType)) {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = t.msg
                    dialog.setOKText("放弃抢单")
                    dialog.setCancelTextListener("确定开户") { dialog: DialogBuilder.DialogInterface, _: Int ->
                        //开户
                        openAccount()
                        dialog.dismiss()
                    }
                    dialog.setOkListener { dialog: DialogBuilder.DialogInterface, _: Int ->
                        dialog.dismiss()
                    }
                    showDialog(dialog)
                } else {
                    showDialogToast(t.msg)
                }
            }
        }
    }


    /**
     * 开户
     * */
    fun openAccount() {
        execute(
            true,
            ReqOpenAccount()
        ) { t ->
            if (t.success()) {
                setValue("onOpenAccount")
            } else {
                val dialog = DialogBuilder()
                dialog.title = "提示"
                dialog.message = t.msg
                dialog.setOKText("放弃抢单")
                dialog.setCancelTextListener("重新开户") { dialog: DialogBuilder.DialogInterface, _: Int ->
                    openAccount()
                    dialog.dismiss()
                }
                dialog.setOkListener { dialog: DialogBuilder.DialogInterface, _: Int ->
                    dialog.dismiss()
                }
                showDialog(dialog)
            }
        }
    }
}
