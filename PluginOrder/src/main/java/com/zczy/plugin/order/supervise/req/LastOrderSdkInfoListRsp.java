package com.zczy.plugin.order.supervise.req;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;

import java.util.ArrayList;
import java.util.List;

public class LastOrderSdkInfoListRsp extends ResultData {

    List<ESDKInfoObj> data = new ArrayList<>();

    public List<ESDKInfoObj> getData() {
        return data;
    }
}
