//package com.zczy.plugin.order.shipments.entity;
//
//import android.text.TextUtils;
//
///***
// * UI 统一文字
// */
//public enum ShipmentUIStr {
//
//    NX("宁夏","纸质运单照片","最多可上传8张照片",8,"当前运单结算需对发货单进行审核效验，请配合上传完整、清晰的单据照片。"
//    ,"人车货发货合影","最多可上传6张照片",6,"请上传已装载货物的人车货照片，体现承运人本人、车牌号、货物信息。"
//    ),
//    RT(" 融通","单据照片","最多可上传8张照片",8,"当前运单结算需对发货单进行审核效验，请配合上传完整、清晰的单据照片。"
//    ,"车货合影","最多可上传6张照片", 6, "当前运单结算需对发货单进行审核校验，请配合上传完整、清晰的装车照片、车货合影。"
//    ),
//    OTHER("其他","单据照片","最多可上传8张照片",8,"当前运单结算需对发货单进行审核效验，请配合上传完整、清晰的单据照片。"
//    ,"车货合影", "最多可上传6张照片", 6, "当前运单结算需对发货单进行审核校验，请配合上传完整、清晰的的装车照片、车货合影。"),
//
//    TERRACEPAY("非指定预付","单据照片","最多可上传8张照片",8,"当前运单结算需对发货单进行审核效验，请配合上传完整、清晰的单据照片。"
//                  ,"人车货合影", "最多可上传6张照片", 6, "当前运单结算需对发货单进行审核校验,请配合上传完整、清晰的人车照片、车货合影。");;
//
//
//    public  String waybillTitle;
//    public  String waybillToast;
//    public  int waybillMaxsize;
//    public  String waybillWarning;
//
//    public  String personcarTitle;
//    public  String personcarToast;
//    public  int personcarMaxsize;
//    public  String personcarWarning;
//
//    ShipmentUIStr(String des, String waybillTitle, String waybillToast, int waybillMaxsize, String waybillWarning
//            , String personcarTitle, String personcarToast, int personcarMaxsize, String personcarWarning
//    ){
//        this.waybillTitle = waybillTitle;
//        this.waybillToast = waybillToast;
//        this.waybillMaxsize = waybillMaxsize;
//        this.waybillWarning = waybillWarning;
//
//        this.personcarTitle = personcarTitle;
//        this.personcarToast = personcarToast;
//        this.personcarMaxsize = personcarMaxsize;
//        this.personcarWarning = personcarWarning;
//    }
//
//    public static ShipmentUIStr getInstance(ShipmentsEGoodInfo data,  String advanceType){
//        if (TextUtils.equals("1", data.getPlateType()) ){
//            // 1 融通平台
//            return  RT;
//        }
//        if (TextUtils.equals("1", data.getDeliverTrackPersonImgFlag())  || TextUtils.equals("1", data.getDeliverPicConfig()) || TextUtils.equals("2", data.getDeliverPicConfig())) {
//            //宁夏的运单
//            return  NX;
//        }
//        //2.个体司机非指定订单；3.车老板自己摘牌订单；4.个体司机关联车老板模式；
//        if (TextUtils.equals("2",advanceType) || TextUtils.equals("3",advanceType)|| TextUtils.equals("4",advanceType)){
//            return  TERRACEPAY;
//        }
//        return OTHER;
//    }
//
//    public static ShipmentUIStr getInstance(EShipmentsEditGoodInfo data){
//
//        if (TextUtils.equals("1", data.getDeliverTrackPersonImgFlag()) || TextUtils.equals("1", data.getDeliverPicConfig()) || TextUtils.equals("2", data.getDeliverPicConfig())){
//            //宁夏的运单
//            return  NX;
//        }
//        return OTHER;
//    }
//}
