package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 承运方处理变更时，点击同意变更后校验 备案卸货变更模块相关接口
 */
data class ReqCheckBackUpReceiveBeforeChange(
        var changeId: String = ""// 变更id
) : BaseOrderRequest<BaseRsp<ResultData>>("oms-app/orderChange/checkBackUpReceiveBeforeChange")

