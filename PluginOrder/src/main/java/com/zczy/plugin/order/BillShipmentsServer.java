package com.zczy.plugin.order;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.StringDef;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.utils.UtilLog;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.plugin.order.shipments.model.request.ReqDoCordinate;
import com.zczy.plugin.order.shipments.model.request.ReqSaveCoordinate;

/**
 * 功能描述:收发货定位
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/15
 */
public class BillShipmentsServer implements AMapLocationListener {

    public static final String TYPE_SHIPMENTS = "1";
    public static final String TYPE_BILL = "2";

    @StringDef({TYPE_SHIPMENTS, TYPE_BILL})
    public @interface Type {
    }

    private Application context;
    //发货为1 收货为2
    private String orderType;

    //货物ID
    private String detailId;
    //失败ID
    String reqNo;
    //zczy-9782 天津
    String coordinateFlag;

    private AMapLocationClient mLocationClient;


    public BillShipmentsServer(Application context, @Type String orderType, String detailId, String reqNo) {

        this(context,orderType,detailId);
        this.reqNo = reqNo;
    }

    public BillShipmentsServer(Application context, @Type String orderType, String detailId) {

        this(context);
        this.orderType = orderType;
        this.detailId = detailId;
    }


    public BillShipmentsServer(Application context) {

        this.context = context;
    }

    public BillShipmentsServer setContext(Application context) {
        this.context = context;
        return this;
    }

    public BillShipmentsServer setOrderType(String orderType) {
        this.orderType = orderType;
        return this;
    }

    public BillShipmentsServer setDetailId(String detailId) {
        this.detailId = detailId;
        return this;
    }

    public BillShipmentsServer setReqNo(String reqNo) {
        this.reqNo = reqNo;
        return this;
    }

    public BillShipmentsServer setCoordinateFlag(String coordinateFlag) {
        this.coordinateFlag = coordinateFlag;
        return this;
    }

    public void start() {
        UtilLog.e(BillShipmentsServer.class, "onStartCommand");
        try {
            if (this.mLocationClient == null) {
                AMapLocationClient.updatePrivacyShow(context, true, true);
                AMapLocationClient.updatePrivacyAgree(context, true);

                this.mLocationClient = new AMapLocationClient(context);

                //给定位客户端对象设置定位参数
                this.mLocationClient.setLocationOption(this.getEinmalConfig());
                //启动定位
                this.mLocationClient.setLocationListener(this);
            }

            this.mLocationClient.startLocation();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /***
     * 单次定位配置
     */
    private AMapLocationClientOption getEinmalConfig() {
        //初始化定位参数
        AMapLocationClientOption mLocationOption = new AMapLocationClientOption();
        //设置定位模式为Hight_Accuracy高精度模式，Battery_Saving为低功耗模式，Device_Sensors是仅设备模式
        mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
        //设置是否返回地址信息（默认返回地址信息）
        mLocationOption.setNeedAddress(true);
        //设置是否只定位一次,默认为false
        mLocationOption.setOnceLocation(true);
        return mLocationOption;
    }


    @Override
    public void onLocationChanged(AMapLocation location) {
        if (mLocationClient != null) {
            mLocationClient.onDestroy();
            mLocationClient = null;
        }

        if (location == null || location.getErrorCode() != 0) {
            // 定位失败
            String shipmen_bill_location = AppCacheManager.getCache("shipmen_bill_location", String.class);
            if (!TextUtils.isEmpty(shipmen_bill_location)) {
                //上报定位
                String[] saveLocation = shipmen_bill_location.split("_");
                this.postData(saveLocation[0], saveLocation[1]);
                AppCacheManager.removeCache("shipmen_bill_location");
                return;
            }
            final String reason = location == null ? "高德定位对象为NULL" : String.format("ErrorCode:%s, ErrorInfo:%s", location.getErrorCode(), location.getErrorInfo());
            // 告知服务器定位失败，进行LBS 定位处理
            ReqDoCordinate reqDoCordinate = new ReqDoCordinate();
            reqDoCordinate.setDetailId(detailId);
            reqDoCordinate.setOrderType(orderType);
            reqDoCordinate.setReason(reason);
            reqDoCordinate.sendRequest(new EmptyResult());

//            ZLog.e(BillShipmentsServer.class.getName(), String.format("[上报定位]高德异常：detailId:%s, orderType:%s,error:%s", detailId, orderType, reason));

        } else {
            //上报定位
            this.postData(String.valueOf(location.getLatitude()), String.valueOf(location.getLongitude()));
        }

    }

    private void postData(String latitude, String longitude) {
        //响应码	说明
        //1	GPS定位结果 2	返回上次定位结果 4	缓存定位结果 5	Wifi定位结果 6	基站定位结果 有效的一次定位 才入库
        // 收发货上传坐标点
        ReqSaveCoordinate reqSaveCoordinate = new ReqSaveCoordinate();
        reqSaveCoordinate.setDetailId(detailId);
        reqSaveCoordinate.setOrderType(orderType);
        reqSaveCoordinate.setReqNo(reqNo);
        reqSaveCoordinate.setCoordinateFlag(coordinateFlag);
        reqSaveCoordinate.setLatitude(latitude);
        reqSaveCoordinate.setLongitude(longitude);
        reqSaveCoordinate.sendRequest(new EmptyResult());
        // 上传数据库所有定位数据
        AMainServer mainServer = AMainServer.getPluginServer();
        if (mainServer != null) {
            mainServer.updateLocation();
        }

    }

}
