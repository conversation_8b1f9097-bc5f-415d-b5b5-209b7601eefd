package com.zczy.plugin.order.stevedore.model.request;

import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;
import com.zczy.plugin.order.BaseOrderRequest;

/**
 * 功能描述:装卸费货物明细
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ReqQueryCargoList extends BaseOrderRequest<BaseRsp<PageList<String>>> {

    String orderId;

    public ReqQueryCargoList(String orderId) {

        super ("oms-app/order/loadpay/queryCargoList");
        this.orderId = orderId;
    }
}
