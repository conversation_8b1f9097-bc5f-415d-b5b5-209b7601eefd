package com.zczy.plugin.order.changecarrier

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.rx.ui.UtilRxView
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.certificate.vehiclemanage.carowner.CarOwnerVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.carrier.CarrierCarRiskActivityV1.Companion.jumpPage
import com.zczy.certificate.vehiclemanage.carrier.CarrierVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.enterprise.EnterPriseVehicleManagementActivityV1
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.data.entity.EProcessFile
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.utils.getResColor
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.utils.imageselector.ImageSelectProgressView
import com.zczy.comm.utils.imageselector.ImageSelector
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.widget.pickerview.TimePickerUtil
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeCar
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeCarEsign
import com.zczy.plugin.order.changecarrier.dialog.OrderCarrierChooseCarDialog
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierChangeModel
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierShipmentsCarModel
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.source.pick.model.request.ReqCheckDelistOrderDocumentExpire
import com.zczy.plugin.order.source.pick.model.request.RspCheckDelistOrderDocumentExpire
import kotlinx.android.synthetic.main.order_change_carrier_change_activity.*
import java.io.File
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*

/**
 * 功能描述:待卸货-变更车辆
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/5/31
 */
class OrderChangeCarrierShipmentsCarActivity : BaseActivity<OrderChangeCarrierShipmentsCarModel>() {

    private var esignFlag: String = "0"
    private var consignorUserId: String? = null
    private val chooseCar by lazy { findViewById<InputViewClick>(R.id.choose_car) }
    private val chooseTime by lazy { findViewById<InputViewClick>(R.id.choose_time) }
    private val chooseImageSelectView by lazy { findViewById<ImageSelectProgressView>(R.id.choose_image_select_view) }
    private val etContent by lazy { findViewById<EditText>(R.id.et_content) }
    private var orderId: String = ""
    private val tvSize by lazy { findViewById<TextView>(R.id.tv_size) }
    private var mSelectCar: RspVehicleData = RspVehicleData()
    private var rspChangeOverLoad = RspChangeOverLoad()
    private val tvOverLoad by lazy { findViewById<TextView>(R.id.tv_over_load) }

    // 是否零担运单
    private val isCarPool by lazy { intent.getBooleanExtra("isCarPool", false) }

    var relation = CommServer.getUserServer().login.relation

    private var dialog: BaseDialog? = null

    override fun getLayout(): Int {
        return R.layout.order_change_carrier_shipments_activity
    }

    override fun initData() {
        if (!isCarPool) {
            viewModel?.onChangeOverLoad(orderId)
        }

    }

    override fun bindView(bundle: Bundle?) {

        orderId = intent.getStringExtra("orderId") ?: ""
        consignorUserId = intent.getStringExtra("consignorUserId") ?: ""
        if (TextUtils.isEmpty(orderId)) {
            return
        }
        chooseImageSelectView.setOnItemSelectListener(object :
            ImageSelectProgressView.OnItemSelectListener {
            override fun onSelectImageClick(surplus: Int) {
                ImageSelector.open(this@OrderChangeCarrierShipmentsCarActivity, surplus, true, 0x34)
            }

            override fun onUpImageClick(file: String) {
                viewModel?.upFile(file)
            }

            override fun onLookImageClick(file: List<EProcessFile>, position: Int) {
                //查看大图
                val list = ArrayList<EImage>(file.size)
                for (processFile in file) {
                    val image = EImage()
                    image.netUrl = HttpURLConfig.getUrlImage(processFile.imagUrl)
                    list.add(image)
                }
                ImagePreviewActivity.start(
                    this@OrderChangeCarrierShipmentsCarActivity,
                    list,
                    position
                )
            }

            override fun onDelateClick(position: Int) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.message = "确定删除当前图片吗？"
                dialogBuilder.setOkListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->

                    dialogInterface.dismiss()
                    chooseImageSelectView.deleteImage(position)
                }
                showDialog(dialogBuilder)
            }
        })

        chooseCar.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                //选择车辆
                viewModel?.queryChangePageInfo(orderId)
            }
        })
        chooseTime.setListener(object : InputViewClick.Listener() {
            override fun onClick(viewId: Int, view: InputViewClick, content: String) {
                //选择时间
                TimePickerUtil.showComm(
                    this@OrderChangeCarrierShipmentsCarActivity,
                    "",
                    System.currentTimeMillis()
                ) { date ->
                    val time = SimpleDateFormat("yyyy-MM-dd HH:mm").format(date)
                    chooseTime.content = time
                }
            }
        })
        etContent.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {

            }

            override fun onTextChanged(charSequence: CharSequence, i: Int, i1: Int, i2: Int) {

            }

            override fun afterTextChanged(s: Editable) {
                if (TextUtils.isEmpty(s.toString())) {
                    tvSize.text = "(0/200)"
                } else {
                    tvSize.text = String.format("%s/200", s.toString().length)
                }
            }
        })
        val disposable =
            UtilRxView.clicks(findViewById<Button>(R.id.bt_ok), 1000).subscribe { save() }

        this.putDisposable(disposable)
    }

    private fun save() {
        if (TextUtils.isEmpty(mSelectCar.vehicleId)) {
            showToast("请选择车辆")
            return
        }
        val time = chooseTime.content
        if (TextUtils.isEmpty(time)) {
            showToast("请选择实际变更时间")
            return
        }
        var picUrls = ""
        val files = chooseImageSelectView.dataList
        if (files != null && !files.isEmpty()) {
            val url = StringBuilder(100)
            for (processFile in files) {
                url.append(processFile.imagUrl).append(",")
            }
            //设置回单图片数据
            picUrls = url.substring(0, url.length - 1)
        }
        //承运方操作变更时校验（只变更车） 备案卸货变更模块相关接口
        this.viewModel?.checkPlateNumberBeforeChange(
            orderId,
            mSelectCar.vehicleId,
            "",
            mSelectCar.plateNumber
        )

    }

    @LiveDataMatch
    open fun vertifyCarTypeSuccess(resp: VertifyCarTypeChangeBeanResp) {
        if (resp.checkVehicleType == "2") {
            showCommDialog(resp.resultMsg)
        } else {
            val time = chooseTime.content

            var picUrls = ""
            val files = chooseImageSelectView.dataList
            if (files != null && !files.isEmpty()) {
                val url = StringBuilder(100)
                for (processFile in files) {
                    url.append(processFile.imagUrl).append(",")
                }
                //设置回单图片数据
                picUrls = url.substring(0, url.length - 1)
            }
            if (!isCarPool) {
                this.viewModel?.checkUnderTwelveVehicleConsignor(
                    orderId,
                    consignorUserId,
                    mSelectCar,
                    time,
                    picUrls,
                    etContent.text.toString()
                )
            } else {
                this.viewModel?.queryDelistUserElectronicSignState(orderId)
            }
        }
    }

    private fun showCommDialog(message: String) {
        val dialog = DialogBuilder()
        dialog.isCancelable = false
        dialog.isHideCancel = false
        dialog.title = "提示"
        dialog.message = message
        dialog.setMessageGravity(message, Gravity.CENTER)
        dialog.setOKText("确认车辆")
        dialog.setOkListener { dialog, _ ->
            val time = chooseTime.content

            var picUrls = ""
            val files = chooseImageSelectView.dataList
            if (files != null && !files.isEmpty()) {
                val url = StringBuilder(100)
                for (processFile in files) {
                    url.append(processFile.imagUrl).append(",")
                }
                //设置回单图片数据
                picUrls = url.substring(0, url.length - 1)
            }
            if (isCarPool) {
                this.viewModel?.queryDelistUserElectronicSignState(orderId)
            } else {
                this.viewModel?.checkUnderTwelveVehicleConsignor(
                    orderId,
                    consignorUserId,
                    mSelectCar,
                    time,
                    picUrls,
                    etContent.text.toString()
                )
            }
            dialog.dismiss()
        }
        dialog.cancelText = "重新选择"
        dialog.setCancelListener() { dialog, _ ->
            dialog.dismiss()
        }
        showDialog(dialog)
    }

    @LiveDataMatch(tag = "上传文件成功")
    open fun onFileSuccess(tag: File, url: String) {

        chooseImageSelectView.onUpLoadFileSuccess(tag.absolutePath, url)
    }

    @LiveDataMatch(tag = "上传文件失败")
    open fun onFileFailure(tag: File, error: String) {

        chooseImageSelectView.onUpLoadFileError(tag.absolutePath)
    }

    @LiveDataMatch
    open fun onCarSuccess(data: RspPageCar) {
        if (data.rootArray == null) {
            return
        }
        if (data.totalSize > 9) {
            // 待卸货为自己承运
            OrderChangeCarrierChooseAllCarActivity.start(
                this,
                orderId,
                CommServer.getUserServer().login.userId,
                mSelectCar,
                0x33,
                true
            )
        } else {
            OrderCarrierChooseCarDialog
                .instance(data.rootArray, true, true)
                .setSelectItem(this.mSelectCar)
                .setToastText(
                    if (TextUtils.equals("1", data.isNeedHandle)) {
                        "您本月交易车辆已超出平台当前角色管控要求，请选择可选车辆，如有疑问可咨询平台客服。"
                    } else {
                        ""
                    }
                )
                .setNoCheck { TextUtils.equals("0", it.isChoose) }
                .setNoCheckTxt { if (TextUtils.equals("0", it.isChoose)) "4" else "" }
                .setChooseListener { s, dialog ->
                    this.dialog = dialog
                    if (isCarPool) {
                        //零担货
                        getViewModel(OrderChangeCarrierChangeModel::class.java).checkVehicleCarpooling(
                            req = ReqCheckVehicleCarpooling(
                                orderCarpoolingId = orderId,
                                plateNumber = s.plateNumber
                            )
                        ) {
                            runOnUiThread {
                                if (it.batchPromptFlag.isTrue) {
                                    val dialogBuilder = DialogBuilder()
                                    dialogBuilder.title = "提示"
                                    dialogBuilder.message = it.resultMsg
                                    dialogBuilder.gravity = Gravity.CENTER
                                    dialogBuilder.isHideCancel = true
                                    dialogBuilder.cancelText = "好的"
                                    dialogBuilder.setOKText("确定")
                                    dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                                        dialog?.dismiss()
                                    }
                                    showDialog(dialogBuilder)
                                }
                            }
                        }
                    } else {
                        setTvOverLoad(s.vehicleLoad)
                        mSelectCar = s;
                        chooseCar.content = s.plateNumber
                        dialog.dismiss()
                    }
                }.show(this)
        }
    }


    @LiveDataMatch
    open fun onGetVehicleEmissionStandard(
        data: RspVehicleEmissionStandard?, vehicle: RspVehicleData?
    ) {
        dialog?.dismiss()
        mSelectCar = vehicle!!
        chooseCar.content = mSelectCar.plateNumber
        if (!isCarPool) {
            setTvOverLoad(vehicle.vehicleLoad)
        }
    }

    @LiveDataMatch
    open fun onSaveSuccess(resultDataBaseRsp: BaseRsp<Rsp3ChangeOne>) {
        if (resultDataBaseRsp.success()) {
            postEvent(RxEventChangeCar())
            showToast(resultDataBaseRsp.msg)
            finish()
        } else if (TextUtils.equals("1114", resultDataBaseRsp.code)) {
            if (TextUtils.equals("1", resultDataBaseRsp.data?.improveButton)) {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "温馨提示"
                dialogBuilder.message = "该运单对车辆“排放标准”有限制要求，请完善“排放标准”后继续摘单"
                dialogBuilder.gravity = Gravity.CENTER
                dialogBuilder.setOKTextListener("去完善") { dialog: DialogBuilder.DialogInterface, _: Int ->
                    dialog.dismiss()
                    if (relation.isCarrier) {
                        CarrierVehicleManagementActivity.start(this)
                    } else if (relation.isBoss) {
                        CarOwnerVehicleManagementActivity.start(this)
                    } else {
                        EnterPriseVehicleManagementActivityV1.start(this)
                    }
                }
                dialogBuilder.setCancelTextListener("放弃摘单") { dialog: DialogBuilder.DialogInterface, _: Int ->
                    dialog.dismiss()
                    mSelectCar = RspVehicleData()
                    chooseCar.content = ""
                }
                showDialog(dialogBuilder)
            } else {
                showDialogToast(resultDataBaseRsp.msg)
            }
        } else if (TextUtils.equals("1112", resultDataBaseRsp.code) && !TextUtils.isEmpty(
                resultDataBaseRsp.data?.vehicleErrorState
            )
        ) {
            //车辆
            val dialogBuilder = DialogBuilder()
            dialogBuilder.title = "提示"
            dialogBuilder.message = resultDataBaseRsp.msg
            when (resultDataBaseRsp.data?.vehicleErrorState) {
                "1" -> {
                    dialogBuilder.setOKTextListener(
                        "去完善"
                    ) { dialog, _ ->
                        dialog.dismiss()
                        jumpPage(
                            this@OrderChangeCarrierShipmentsCarActivity,
                            mSelectCar.vehicleId
                        )
                    }
                    showDialog(dialogBuilder)
                }

                "2" -> {
                    dialogBuilder.setOKTextListener(
                        "联系客服"
                    ) { dialog, _ ->
                        dialog.dismiss()
                        AMainServer.getPluginServer()
                            .openLineServer(this@OrderChangeCarrierShipmentsCarActivity)
                    }
                    showDialog(dialogBuilder)
                }
            }

        } else {
            showDialogToast(resultDataBaseRsp.msg)
        }
    }

    @LiveDataMatch
    open fun onChangeOverLoadSuccess(data: RspChangeOverLoad) {
        rspChangeOverLoad = data
    }

    /**
     * 进行重量校验
     *
     * @param actualWeight 货物吨位
     */
    @SuppressLint("SetTextI18n")
    fun setTvOverLoad(vehicleLoad: String) {
        val cargoCategory = rspChangeOverLoad.cargoCategory
        if (TextUtils.isEmpty(cargoCategory)) {
            return
        }
        if (!TextUtils.equals("1", cargoCategory)) {
            return
        }
        val actualWeight = rspChangeOverLoad.weight
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(vehicleLoad)) {
            return
        }
        val aDouble = actualWeight.toDouble()
        val bDouble = vehicleLoad.toDouble()
        val cDouble = aDouble - bDouble
        if (cDouble > 0) {
            this.tvOverLoad.visibility = View.VISIBLE
            val divide = divide(cDouble, bDouble, 2)
            if (divide > 30) {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb4040))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff4f8_rtg)
            } else {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb6b40))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff3f1_rtg)
            }
        } else {
            this.tvOverLoad.visibility = View.GONE
        }

    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    fun divide(a: Double, b: Double, scale: Int): Long {
        val bd1 = BigDecimal(a.toString())
        val bd2 = BigDecimal(b.toString())
        val bd3 = bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).toDouble()
        return Math.round(bd3 * 100.00)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == 0x33) {
            OrderChangeCarrierChooseAllCarActivity.obtainData(data)?.let { mSelectCar = it }
            chooseCar.content = mSelectCar.plateNumber
            if (isCarPool) {
                //零担货
                getViewModel(OrderChangeCarrierChangeModel::class.java).checkVehicleCarpooling(
                    req = ReqCheckVehicleCarpooling(
                        orderCarpoolingId = orderId,
                        plateNumber = mSelectCar.plateNumber
                    )
                ) {
                    runOnUiThread {
                        if (it.batchPromptFlag.isTrue) {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = it.resultMsg
                            dialogBuilder.gravity = Gravity.CENTER
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.cancelText = "好的"
                            dialogBuilder.setOKText("确定")
                            dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                                dialog?.dismiss()
                            }
                            showDialog(dialogBuilder)
                        } else {
                            viewModel?.queryCarrierOrderCarpooling(
                                req = ReqCheckDelistOrderDocumentExpire(
                                    vehicleId = mSelectCar.vehicleId
                                )
                            )
                        }
                    }
                }
            } else {
                viewModel?.queryCarrierOrderCarpooling(
                    req = ReqCheckDelistOrderDocumentExpire(
                        vehicleId = mSelectCar.vehicleId
                    )
                )
            }
        } else if (0x34 == requestCode && resultCode == Activity.RESULT_OK) {
            val file = ImageSelector.obtainPathResult(data)
            //#418520 java.lang.IllegalArgumentException
            //Parameter specified as non-null is null: method kotlin.jvm.internal.Intrinsics.checkParameterIsNotNull, parameter file
            //com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierShipmentsCarModel.upFile(Unknown Source:2)
            if (file.isNullOrEmpty()) {
                return;
            }
            for (path in file) {
                if (TextUtils.isEmpty(path)) {
                    showToast("图片损坏或图片不存在,请重新上传")
                    return
                }
                val select = File(path)
                if (!select.exists() || !select.canRead() || select.length() <= 1 * 1024) {
                    showToast("图片损坏或图片不存在,请重新上传")
                    return
                }
            }
            this.chooseImageSelectView.onUpLoadStart(file)
            file.let {
                this.viewModel?.upFile(it)
            }
        }
    }

    @LiveDataMatch
    open fun queryCarrierOrderCarpoolingSuccess(data: RspCheckDelistOrderDocumentExpire) {
        if (data.vehicleDocumentExpire.isTrue) {
            //用户证件过期
            val dialogBuilder = DialogBuilder()
            dialogBuilder.title = "提示"
            dialogBuilder.message = data.resultMsg
            dialogBuilder.gravity = Gravity.CENTER
            dialogBuilder.isCancelable = false
            dialogBuilder.isHideCancel = true
            dialogBuilder.setOKTextListener("我知道了") { dialog: DialogBuilder.DialogInterface, _: Int -> dialog.dismiss() }
            showDialog(dialogBuilder)
        }
    }

    @LiveDataMatch
    open fun onChangeOverLoadError(msg: String) {
        val dialogBuilder = DialogBuilder()
        dialogBuilder.message = msg
        dialogBuilder.isCancelable = false
        dialogBuilder.isHideCancel = true
        dialogBuilder.setOKTextListener("确定") { dialog, _ ->
            dialog.dismiss()
            finish()
        }
        showDialog(dialogBuilder)
    }

    @LiveDataMatch
    open fun onQueryDelistUserElectronicSignState(data: RspQueryDelistUserElectronicSignState) {
        //（电子签）
        var urlType = "#/driver?platform=XXX&isSign=1"
        var userType = "2"

        var plateNumber = mSelectCar

        if (TextUtils.equals("1", data!!.electronicSignState) && TextUtils.equals(
                "1",
                data!!.cycleState
            )
        ) {
            esignFlag = "2"
            X5WebNoToolBarActivity.startContentUI(
                this,
                HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/cycleCommitmentLetter?userType=" + userType + "&biddingMoney=&plateNumber=" + plateNumber + "&heavy=&orderId=" + orderId + "&changeFlag=1",
                "onChangeCarEsignSuccess"
            )
        } else {
            if (TextUtils.equals("0", data!!.electronicSignState) && TextUtils.equals(
                    "0",
                    data!!.cycleState
                )
            ) {
                esignFlag = "0"
            } else {
                esignFlag = "1"
            }
            X5WebNoToolBarActivity.startContentUI(
                this,
                HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + urlType + "&biddingMoney=&plateNumber=" + plateNumber + "&heavy=&orderId=" + orderId + "&changeFlag=1",
                "onChangeCarEsignSuccess"
            )
        }
    }

    @RxBusEvent(from = "电签")
    open fun onEsignSuccess(msg: RxEventChangeCarEsign) {
        val time = chooseTime.content

        var picUrls = ""
        val files = chooseImageSelectView.dataList
        if (files != null && !files.isEmpty()) {
            val url = StringBuilder(100)
            for (processFile in files) {
                url.append(processFile.imagUrl).append(",")
            }
            //设置回单图片数据
            picUrls = url.substring(0, url.length - 1)
        }
        getViewModel(OrderChangeCarrierShipmentsCarModel::class.java).save(
            orderId = orderId,
            car = mSelectCar,
            time = time,
            pic = picUrls,
            des = etContent.text.toString(),
            esignFlag = esignFlag
        )

    }

    companion object {
        @JvmStatic
        fun startUI(context: Context?, orderId: String, consignorUserId: String?, isCarPool: Boolean = false) {
            val intent = Intent(context, OrderChangeCarrierShipmentsCarActivity::class.java)
            intent.putExtra("orderId", orderId)
            intent.putExtra("consignorUserId", consignorUserId)
            intent.putExtra("isCarPool", isCarPool)
            context?.startActivity(intent)
        }

        @JvmStatic
        fun startUI(context: Context?, orderId: String, isCarPool: Boolean?) {
            val intent = Intent(context, OrderChangeCarrierShipmentsCarActivity::class.java)
            intent.putExtra("orderId", orderId)
            intent.putExtra("isCarPool", isCarPool)
            context?.startActivity(intent)
        }
    }
}
