package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 类描述：零担变更车前置校验接口
 * 作者：ssp
 * 创建时间：2024/3/15
 */
class ReqCheckVehicleCarpooling(
    var orderCarpoolingId: String? = null,//承运方app传 50开头的， 加盟运力传51开头的
    var plateNumber: String? = null,//车牌号
) : BaseNewRequest<BaseRsp<RspCheckVehicleCarpooling>>("oms-app/orderChange/checkVehicleCarpooling")

data class RspCheckVehicleCarpooling(
    var batchPromptFlag: String = "",    //“1” 弹窗
) : ResultData()