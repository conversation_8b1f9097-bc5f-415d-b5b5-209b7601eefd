package com.zczy.plugin.order.offlinezone;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/11/8
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.sfh.lib.event.RxBusEvent;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.offlinezone.fragment.OfflineBossFragment;
import com.zczy.plugin.order.offlinezone.fragment.OfflineCyrFragment;
import com.zczy.plugin.order.offlinezone.fragment.OfflineCysFragment;
import com.zczy.plugin.order.shipments.entity.RxEventShipmentsBillSuccess;
import com.zczy.plugin.order.source.pick.entity.RxEventPickOffer;
import com.zczy.plugin.order.waybill.ViewPageAdapter;
import com.zczy.user.offlinezone.bean.CarrierListCountNum;

import java.util.ArrayList;

public  class OfflineZoneListActivity2 extends BaseActivity<OfflineZoneListModel> {

    public static void start(Context context) {
//        Intent starter = new Intent(context, OfflineZoneListActivity2.class);
//        context.startActivity(starter);
        AMainServer mainServer = AMainServer.getPluginServer();
        mainServer.openReactNativeActivity(context, "OfflineZoneListPage");
    }
    ViewPager mViewPager;
    SlidingTabLayout mCommonTabLayout;

    @Override
    protected int getLayout() {
        return R.layout.offline_zone_list_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        mViewPager = findViewById(R.id.viewPager);
        mCommonTabLayout = findViewById(R.id.tab_layout);
    }

    @Override
    protected void initData() {
        //"" 全部  1 已成交 6 待发货 7 待收货
        final String[] title = {"已成交", "待装货", "待卸货", "全部"};
        ArrayList<Fragment> arrayList = new ArrayList<>(4);
        IRelation relation = CommServer.getUserServer().getLogin().getRelation();
        if (relation.isBoss()) {

            arrayList.add( OfflineBossFragment.newInstance("1"));
            arrayList.add( OfflineBossFragment.newInstance("6"));
            arrayList.add( OfflineBossFragment.newInstance("7"));
            arrayList.add( OfflineBossFragment.newInstance(""));

        } else if (relation.isCys()) {
            arrayList.add( OfflineCysFragment.newInstance("1"));
            arrayList.add( OfflineCysFragment.newInstance("6"));
            arrayList.add( OfflineCysFragment.newInstance("7"));
            arrayList.add( OfflineCysFragment.newInstance(""));
        } else {
            arrayList.add( OfflineCyrFragment.newInstance("1"));
            arrayList.add( OfflineCyrFragment.newInstance("6"));
            arrayList.add( OfflineCyrFragment.newInstance("7"));
            arrayList.add( OfflineCyrFragment.newInstance(""));
        }
        ViewPageAdapter pageAdapter = new ViewPageAdapter(getSupportFragmentManager(), arrayList);
        mViewPager.setAdapter(pageAdapter);
        this.mCommonTabLayout.setViewPager(mViewPager, title);

//        getViewModel(OfflineZoneListModel.class).queryCarrierListCountNum();
    }

    @RxBusEvent(from = "摘单议价成功")
    public void onEven(RxEventPickOffer pickOffer) {
        getViewModel(OfflineZoneListModel.class).queryCarrierListCountNum();
    }

    @RxBusEvent(from = " 发货成功 -》待卸货【菜单】")
    public void onEventClose(RxEventShipmentsBillSuccess success) {
        //0: 发货
        if (success.type == 2) {
            mCommonTabLayout.setCurrentTab(2);
        }
    }

    @RxBusEvent(from = " 刷新数字提示")
    public void on(CarrierListCountNum countNum){
        getViewModel(OfflineZoneListModel.class).queryCarrierListCountNum();
    }

    @LiveDataMatch
    public void countNum(CarrierListCountNum date){
        if (date == null){
            this. mCommonTabLayout.hideMsg(1);
            this.mCommonTabLayout.hideMsg(2);
            return;
        }
        int loadingNum = date.getLoadingNum();
        int unloadingNum = date.getUnloadingNum();
        //待装货
        if (loadingNum > 0){
            this.mCommonTabLayout.showMsg(1,loadingNum);
            if (loadingNum >= 10){
                this.mCommonTabLayout.setMsgMargin(1,9,2);
            }else{
                this.mCommonTabLayout.setMsgMargin(1,20,2);
            }
        }else{
            this. mCommonTabLayout.hideMsg(1);
        }
        //待卸货
        if (unloadingNum > 0){
            this. mCommonTabLayout.showMsg(2,unloadingNum);
            if (unloadingNum >= 10){
                this.mCommonTabLayout.setMsgMargin(2,9,2);
            }else{
                this.mCommonTabLayout.setMsgMargin(2,20,2);
            }
        }else{
            this.mCommonTabLayout.hideMsg(2);
        }
    }

}
