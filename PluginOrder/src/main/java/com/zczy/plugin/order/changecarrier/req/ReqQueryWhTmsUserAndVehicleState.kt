package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：汽运业务系统-APP端 查询证件过期 冻结 异常 缺失 风险 绑定等校验
 * 时间：2025/3/24 11:06
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=79922116
 * 对接：陈泰
 * */
class ReqQueryWhTmsUserAndVehicleState(
    var consignorUserId: String? = null, // 货主用户id
    var sourceId: String? = null, // 货源ID
    var vehicleId: String? = null, // 车辆ID
    var driverUserId: String? = null, // 司机ID //车老板模式必传
    var changeType: String? = null, // 变更方式 changeType:  1：变更人  2：变更车  3：变更人和车
) : BaseNewRequest<BaseRsp<RspWhTmsUserAndVehicleState>>("oms-app/carrier/common/queryWhTmsUserAndVehicleState") {
}

data class RspWhTmsUserAndVehicleState(
    var userAndVehicleState: String? = "", //  用户或者车辆状态 0 正常 1 异常 需要提示拦截
    var userAndVehicleType: String? = "", // 用户或者车辆类型 1 用户 2 车辆
    var operationFlag: String? = "", // 是否有操作按钮 0 否 1 是
) : ResultData()