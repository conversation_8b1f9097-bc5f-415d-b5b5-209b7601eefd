package com.zczy.plugin.order.changecarrier

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.TextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.data.ERNToNativeEvent
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.pluginserver.AWisdomServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.ex.setVisible
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeAgreeCarDetail
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierDetailModel
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity

/**
 * 变更承运 详情
 */
class OrderChangeCarrierDetailActivity : BaseActivity<OrderChangeCarrierDetailModel>() {

    private val eChangeId by lazy { intent.getStringExtra(EXTRA_CHANGE_ID) ?: "" }

    private var mData: RspChangeDetail? = null
    private var esignFlag: String = "0"

    // 运单号 tv_order_no
    private val tvOrderId by lazy { findViewById<TextView>(R.id.tv_order_no) }

    // - 姓名
    private val driver_name_value by lazy { findViewById<TextView>(R.id.driver_name_value) }

    // - 手机号
    private val driver_phone_value by lazy { findViewById<TextView>(R.id.driver_phone_value) }

    // - 车牌号码
    private val driver_car_num_value by lazy { findViewById<TextView>(R.id.driver_car_num_value) }

    // - 变更类型
    private val car_type_value by lazy { findViewById<TextView>(R.id.car_type_value) }

    // - 摘牌类型
    private val car_type_2_value by lazy { findViewById<TextView>(R.id.car_type_2_value) }

    // - 新姓名
    private val car_name_value by lazy { findViewById<TextView>(R.id.car_name_value) }

    // - 新号码
    private val car_phone_value by lazy { findViewById<TextView>(R.id.car_phone_value) }

    // - 新车辆
    private val car_num_value by lazy { findViewById<TextView>(R.id.car_num_value) }

    // - 审核结果
    private val car_result_value by lazy { findViewById<TextView>(R.id.car_result_value) }

    // - 意见
    private val car_message_value by lazy { findViewById<TextView>(R.id.car_message_value) }

    // - 是否接受
    private val car_accept_title by lazy { findViewById<TextView>(R.id.car_accept_title) }
    private val car_accept_value by lazy { findViewById<TextView>(R.id.car_accept_value) }

    // - 新司机意见
    private val car_new_message_title by lazy { findViewById<TextView>(R.id.car_new_message_title) }
    private val car_new_message_value by lazy { findViewById<TextView>(R.id.car_new_message_value) }

    // - 新司机意见输入 标题
    private val car_new_message_title_input by lazy { findViewById<TextView>(R.id.car_new_message_title_input) }

    // - 新司机意见输入view
    private val view_car_new_message_title_input by lazy { findViewById<LinearLayout>(R.id.view_car_new_message_title_input) }

    // - 新司机
    private val ed_message by lazy { findViewById<EditText>(R.id.noteTv) }

    // - sizeTv
    private val tv_message_size by lazy { findViewById<TextView>(R.id.sizeTv) }

    private val viewBtn by lazy { findViewById<View>(R.id.view_btn) }
    private val btnReject by lazy { findViewById<TextView>(R.id.btn_reject) }
    private val btnAgree by lazy { findViewById<TextView>(R.id.btn_agree) }

    companion object {
        private const val EXTRA_CHANGE_ID = "extra_change_id"

        @JvmStatic
        fun start(fragment: androidx.fragment.app.Fragment, changeId: String) {
            val intent = Intent(fragment.context, OrderChangeCarrierDetailActivity::class.java)
            intent.putExtra(EXTRA_CHANGE_ID, changeId)
            fragment.startActivity(intent)
        }

        @JvmStatic
        fun start(context: Context, changeId: String) {
            val intent = Intent(context, OrderChangeCarrierDetailActivity::class.java)
            intent.putExtra(EXTRA_CHANGE_ID, changeId)
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int = R.layout.order_change_carrier_detail_activity

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(btnReject)
        bindClickEvent(btnAgree)

        ed_message.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable) {
                if (s.length > 200) {
                    return
                }
                tv_message_size.setText("(" + s.length + "/200)")
            }
        })
    }

    override fun initData() {
        viewBtn.visibility = View.GONE
        viewModel?.queryChangeDetail(eChangeId)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        val data = mData
        when (v.id) {
            R.id.btn_reject -> {
                if (data != null) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认拒绝吗？"
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                            p0.dismiss()
                            viewModel?.decideChangeReject(
                                eChangeId,
                                ed_message.text.toString().trim()
                            )
                        }
                    showDialog(dialogBuilder)
                }
            }
            R.id.btn_agree -> {
                if (data != null) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.message = "确认同意吗？"
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { p0, _ ->
                            p0.dismiss()
                            if (CommServer.getUserServer().login.relation.isCys) {
                                //承运商 && 变更他人 不走电子签逻辑
                                viewModel?.decideChangeAgree(
                                    this@OrderChangeCarrierDetailActivity,
                                    data,
                                    eChangeId,
                                    ed_message.text.toString().trim(),
                                    "0"
                                )
                                return@OnClickListener
                            }

                            //查询是否需要静默签
                            getViewModel(BaseViewModel::class.java).execute(
                                true,
                                ReqQueryMemberSilentSignState(userId = CommServer.getUserServer().login.userId)
                            ) { t ->
                                if (t.success()) {
                                    if (TextUtils.equals("1", t.data!!.silentSignState)) {
                                        var dialog = DialogBuilder()
                                        dialog.title = "温馨提示"
                                        dialog.message = "您尚未完成电签认证不可进行业务，请优先完成电签认证"
                                        dialog.isHideCancel = true
                                        dialog.setOKTextListener("前往电签认证") { dialog: DialogBuilder.DialogInterface, _: Int ->
                                            dialog.dismiss()
                                            AMainServer.getPluginServer().changeMenuToEvent(
                                                this,
                                                AMainServer.MENU_USER,
                                                500,
                                                ERNToNativeEvent(type = "openElectorn")
                                            )
                                        }
                                        showDialog(dialog)
                                    } else {
                                        submitChangeAgree(data)
                                    }
                                } else {
                                    showToast(t.msg)
                                }
                            }
                        }
                    showDialog(dialogBuilder)
                }
            }
        }
    }

    fun submitChangeAgree(data: RspChangeDetail) {
        getViewModel(BaseViewModel::class.java).execute(
            true,
            ReqQueryDelistUserElectronicSignState(
                orderId = data.orderId,
            )
        ) { t ->
            if (t.success() && t.data != null
            ) {
                var urlType = ""
                var userType = ""
                //（电子签）
                if (CommServer.getUserServer().login.relation.isCarrier) {
                    urlType = "#/driver?platform=XXX&isSign=1"
                    userType = "2"
                } else {
                    urlType = "#/carBoss?isSign=1"
                    userType = "10"
                }

                if (TextUtils.equals("1", t.data!!.electronicSignState) && TextUtils.equals(
                        "1",
                        t.data!!.cycleState
                    )
                ) {
                    esignFlag = "2"
                    X5WebNoToolBarActivity.startContentUI(
                        this,
                        HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/cycleCommitmentLetter?userType=" + userType + "&biddingMoney=&plateNumber=" + data.plateNumber + "&heavy=&orderId=" + tvOrderId + "&changeFlag=2&changeId=" + eChangeId,
                        "onAgreeChangeDetail"
                    )
                } else {
                    if (TextUtils.equals(
                            "0",
                            t.data!!.electronicSignState
                        ) && TextUtils.equals(
                            "0",
                            t.data!!.cycleState
                        )
                    ) {
                        esignFlag = "0"
                    } else {
                        esignFlag = "1"
                    }
                    X5WebNoToolBarActivity.startContentUI(
                        this,
                        HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + urlType + "&biddingMoney=&plateNumber=" + data.plateNumber + "&heavy=&orderId=" + tvOrderId + "&changeFlag=2&changeId=" + eChangeId,
                        "onAgreeChangeDetail"
                    )
                }
            } else {
                showToast(t.msg)
            }
        }
    }

    @LiveDataMatch
    open fun openUserAuthent() {
        // ZCZY-18025 会员强控手机号三要素--业务配合
        AMainServer.getPluginServer().changeMenuToEvent(
            this,
            AMainServer.MENU_USER,
            500,
            ERNToNativeEvent(type = "openUserAuthent")
        )

    }

    @LiveDataMatch
    open fun onPlayMoney(earnestMoney: String) {
        //支付诚意金
        AWisdomServer.getPluginServer()
            .startWisdomEarnesrBuyActivity(this@OrderChangeCarrierDetailActivity, earnestMoney);
    }

    @LiveDataMatch
    open fun onQueryChangeDetail(data: RspChangeDetail?) {
        if (data == null) return

        mData = data

        // 运单号 tv_order_no
        tvOrderId.text = "运单号：${data.orderId}"
        // - 姓名
        driver_name_value.text = data.oldName
        // - 手机号
        driver_phone_value.text = data.oldMobile
        // - 车牌号码
        driver_car_num_value.text = data.oldPlateNumber

        // - 变更类型
        car_type_value.text = data.formatChangeType()
        // - 摘牌类型
        car_type_2_value.text = data.formatDelistType()
        // - 新姓名
        car_name_value.text = data.name
        // - 新号码
        car_phone_value.text = data.mobile
        // - 新车辆
        car_num_value.text = data.plateNumber
        // - 审核结果
        car_result_value.text = data.formatExamineState()
        // - 意见
        car_message_value.text = data.formatExamineContent()
        // - 是否接受
        car_accept_value.text = data.formatCarrierState()
        // - 新司机意见
        car_new_message_value.text = data.formatCarrierContent()

        // 新司机 意见输入
        // 按钮
        // examineState 0:未审核 1后台通过 2后台不通过 3-承运人不同意 4-承运人同意
        // changeType 变更类型 变更类型：1 变更人，2 变更车，3 变更人和车
        // dispatchOperationFlag 是否加盟运力操作的变更  0 否 1 是
        when {
            data.examineState == "1"
                    && data.carrierState == "3"
                    && data.dispatchOperationFlag == "1" -> {
                viewBtn.setVisible(true)
                car_new_message_title_input.setVisible(true)
                view_car_new_message_title_input.setVisible(true)
            }
            data.examineState == "1"
                    && data.carrierState == "3"
                    && data.changeType != "2"
                    && data.dispatchOperationFlag != "1" -> {
                viewBtn.setVisible(true)
                car_new_message_title_input.setVisible(true)
                view_car_new_message_title_input.setVisible(true)
            }
            else -> {
                viewBtn.setVisible(false)
                car_new_message_title_input.setVisible(false)
                view_car_new_message_title_input.setVisible(false)
            }
        }
        // 是否显示承运人接受结果和意见布局
        when {
            data.examineState == "1"
                    // 变更人是否同意：0否 1是   3 需要同意拒绝
                    && (data.carrierState == "0" || data.carrierState == "1") -> {
                car_accept_title.setVisible(true)
                car_accept_value.setVisible(true)
                car_new_message_title.setVisible(true)
                car_new_message_value.setVisible(true)
            }
            else -> {
                car_accept_title.setVisible(false)
                car_accept_value.setVisible(false)
                car_new_message_title.setVisible(false)
                car_new_message_value.setVisible(false)
            }
        }
    }

    @LiveDataMatch
    open fun onDecideChange() {
        setResult(Activity.RESULT_OK)
        viewModel?.queryChangeDetail(eChangeId)
    }

    @RxBusEvent(from = "电子签后发起变更")
    open fun onRxEventBusH5(type: RxEventChangeAgreeCarDetail) {
        mData?.let {
            viewModel?.decideChangeAgree(
                this@OrderChangeCarrierDetailActivity,
                it,
                eChangeId,
                ed_message.text.toString().trim(),
                esignFlag
            )
        }
    }
}
