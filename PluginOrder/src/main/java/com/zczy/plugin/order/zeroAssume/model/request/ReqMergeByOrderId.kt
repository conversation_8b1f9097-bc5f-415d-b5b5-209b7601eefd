package com.zczy.plugin.order.zeroAssume.model.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


/*=============================================================================================
 * 功能描述:查询当前零担订单是否有同货主同线路的合成单的子单
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqMergeByOrderId(
    var orderId: String?, //	订单id
    var processFlag:String?,//   发货传0    收货为1
    var orderCarpoolingId:String?,//   零担货id
) : BaseNewRequest<BaseRsp<RspMergeByOrderId>>("oms-app/order/deliver/queryOtOrderLTLOfSmConsignorAndPL")

data class RspMergeByOrderId(
    var contMoreFlag: String? = null, // 是否可合并 1:是，0为否
    var firstFlag: String? = null, // 1是，0 否   首次卸货
) : ResultData()
