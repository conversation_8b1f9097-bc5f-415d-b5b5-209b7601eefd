package com.zczy.plugin.order.zeroAssume.model.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


/**
 * 注释：零担货列表角标数
 * 时间：2024/2/29 8:37
 * 作者：王家辉
 * */
class ReqQueryOrderCarpoolingListCorner(
    var queryType: String? = null, //1 全部 2 进行中 3 已卸货
) : BaseNewRequest<BaseRsp<RspOrderCarpoolingListCorner>>("oms-app/display/queryOrderCarpoolingListCorner")

data class RspOrderCarpoolingListCorner(
    var orderCarpoolingTransportCount: String? = null, // 进行中角标数，有值就展示
) : ResultData()
