package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * WLHY-9279【汽运】证件风险通过规则调整
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=74974175
 * 黄进
 */
data class ReqCheckCarrierAndVehicleCertificate(
    var friendId: String = "", // 新承运人id
    var changeType: String = "", // 变更方式 changeType:  1：变更人  2：变更车  3：变更人和车
    var plateNumber: String = "", // 新车牌号  选车的时候必传
    var vehicleId: String = "",// 车辆id
    var orderId: String = "",//	订单号
    var sourceId: String = "",// 货源id
    var consignorUserId: String = "",//	货主id
) : BaseOrderRequest<BaseRsp<RspCheckCarrierAndVehicleCertificate>>("oms-app/orderChange/checkCarrierAndVehicleCertificate")

class RspCheckCarrierAndVehicleCertificate(
    val riskLimitationDelistFlag: String = "",//是否限制成交
    val isOperation: String = "",//是否完善资料
    val userAndVehicleRiskStates: String = "",//是否有风险	string	0 没有风险 1 有风险
) : ResultData()