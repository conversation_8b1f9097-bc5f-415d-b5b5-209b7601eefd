package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 3.承运人发起变更运单
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355392
 * 黄进
 * Created by sdx on 2019/2/22.
 */
data class Req3ChangeOne(
    var orderId: String? = "", // 订单id
    var sourceId: String? = "", // 货源id
    var friendId: String = "", // 新承运人id 变更人或者人和车时必传
    var changeType: String = "", // 变更方式 changeType:  1：变更人  2：变更车  3：变更人和车
    var plateNumber: String = "", // 新车牌号  变更车或者人和车时必传
    var vehicleId: String = "",// 车辆id 变更车或者人和车时必传
    var actualTime: String = "",//实际变更时间
    var evidenceUrlStr: String = "",//证明照片
    var remarks: String = "",//备注
    var consignorUserId: String = "",// 货主ID
    var esignFlag: String = "",// 0 不需要电子签  1 只签合同  2 签约合同和承诺函

    var sdOilCardFlag: String = "",//	油卡类型 0没有油卡 1可选油卡 2强制油卡 3承运方强制油卡
    var oilCalculateType: String = "",//油卡种类 1.比例 2.固额
    var oilFixedCredit: String = "",//	油卡金额
    var sdOilCardRatio: String = "",//	油卡比例
    var sdGasCardRatio: String = "",//	气卡比例
    var gasFixedCredit: String = "",//	汽品固额
    var oilCardOrGas: String = "",// 油1  气2
) : BaseOrderRequest<BaseRsp<Rsp3ChangeOne>>("oms-app/orderChange/changeOne")

fun Req3ChangeOne.setPeople(data: RspFriendData) {
    friendId = data.friendId
}

class Rsp3ChangeOne(
    val orderBreachType: String = "",//"1"-待货主处理，"2"-待承运方处理，"3"-平台介入中，"4"-咨询单介入中
    val isShowUploadFlag: String = "",//是否展示上传从业资格证按钮 1 展示 0 不展示
    val userErrorState: String = "", //司机 1 点击完善我的证件材料  跳转会员资料完善 2 联系客服
    val vehicleErrorState: String = "",//车辆 1 完善证件材料 跳转从业资格证风险 2 联系客服
    var userVehicleType: String? = "",//
    var carrierLicenseState: String = "",//0 不提示 1 提示 不拦截（此时resultCode是2001）  2 提示 拦截（此时resultCode 是1113）
    var updateLicenseFlag: String = "",//0 不能更新证件  1 可以更新证件
    var bindingBankCard: String = "",//去绑银行卡 按钮 “1”
    var uploadRoadLicense: String = "",//WLHY-1643道路运输经营许可证管控 新增 1
    var improveButton: String = "",//此时resultCode 是1114
    var licenseState: String = "",//此时resultCode 是1116
) : ResultData()