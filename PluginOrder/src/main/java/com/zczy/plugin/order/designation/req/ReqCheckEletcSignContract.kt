package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：电子合同签署前置检验接口
 * 时间：2025/1/10 8:39
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=77464611
 * 对接：陈泰
 * */
class ReqCheckEletcSignContract : BaseNewRequest<BaseRsp<RspDataCheckEletcSignContract>>("oms-app//bigCarrier/contract/queryCheckBigCarrierSignContract") {
}

data class RspDataCheckEletcSignContract(
    var customerSignState: String? = null, //电签认证状态 0 未认证 1已认证
    var mobile: String? = null, //用户手机号
    var underWayContractSignState: String? = null, //进行中合同签署状态 0-未签署 1-已签署 2-临期
    var unSignContractExistFlag: String? = null, //是否存在待签署合同 0-否 1-是
    var signUserMobile: String? = null, //已指定签署人手机号 有值就是已指定，没值就是当前登录人
    var signUserName: String? = null, //已指定签署人
    var userList: MutableList<ElectUserInfo>? = null, //已指定签署人
) : ResultData()

data class ElectUserInfo(
    var mobile: String? = null, //	手机号
    var userName: String? = null, //用户名
    var userRealName: String? = null, //真实名称
    var userId: String? = null, //用户id
)

fun RspDataCheckEletcSignContract.getCustomerSignStateStr(): String {
    return when (customerSignState) {
        "0" -> "未认证"
        "1" -> "已认证"
        else -> ""
    }
}

fun RspDataCheckEletcSignContract.getCustomerSignStateStrColor(): String {
    return when (customerSignState) {
        "0" -> "#727F9E"
        "1" -> "#11AF41"
        else -> "#999999"
    }
}

fun RspDataCheckEletcSignContract.getContractSignStateStr(): String {
    return when (underWayContractSignState) {
        "0" -> "待签署"
        "1" -> "已签署"
        "2" -> "临期"
        else -> ""
    }
}