package com.zczy.plugin.order.shipments.entity

import android.text.TextUtils

data class ShipmentsImgObj(
    var showUploadFlag:String,//是否展示 1:展示 ;0:不展示
    var uploadFlag:String,//是否比传 1:比传;0:不比传
    var limitCount:Int,//图片上传上限
    var waterMarkFlag:String,//是否加水印 1:是;0:不是
    var takePhotoFlag:String,//是否只拍照 1:是;0:不是
    var warningMsg:String,//未传图片时给的提示信息
    var title:String,//照片标题
){

    /**
     * 是否显示
     */
    fun isShowUpload():Boolean{
        return TextUtils.equals("1",showUploadFlag)
    }
    /**
     * 是否必传
     */
    fun isUpload():Boolean{
        return TextUtils.equals("1",uploadFlag)
    }
    /**
     * 是否加水印
     */
    fun isWaterMark():Boolean{
        return TextUtils.equals("1",waterMarkFlag)
    }
    /**
     * 是否只拍照
     */
    fun isTakePhoto():Boolean{
        return TextUtils.equals("1",takePhotoFlag)
    }
}
