package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 变更查询油品信息
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=72877885
 * 对接人：陈泰
 * 孙飞虎
 */
data class ReqQueryChangeOrderOilCard(
    var orderId: String = "",
) : BaseOrderRequest<BaseRsp<RspChangeOrderOil>>("oms-app/carrier/common/queryChangeOrderOilCard")


class RspChangeOrderOil(
    var dlOilCardFlag: String = "",//	是否成交油品订单	0 否 1 是
    var pbOilCardType: String = "",//	发布类型	0 没有油品,1 可选油品,2 强制油品
    var oilCardOrGas: String = "",//	发布油气品类型	1 油品 2 气品 3 油气都有
    var oilCardType: String = "",//油气品种类	1:敬业油品 2:山东油品 3:德达油品
    var dlOilCardRealTypeFlag: String = "",//	成交实际选择油品类型	0 默认，1 油品，2 气品
    var dlOilCalculateType: String = "",//	成交油品计算方式	1 按比例计算油品,2 按固定额度计算油品
    var dlOilCardRatio: String = "",//	成交油品比例
    var dlOilCardMoney: String = "",//	成交油品金额
    var dlGasCardRatio: String = "",//	成交气品比例
    var dlGasCardMoney: String = "",//	成交气品金额
    var oilCalculateType: String = "",//	发布油品计算方式	1 按比例计算油品,2 按固定额度计算油品 3 比例和固额
    var pbOilCardRatio: String = "",//	发布油品比例
    var pbGasCardRatio: String = "",//	发布气品比例
    var pbOilCardMoney: String = "",//	发布油品金额
    var pbGasCardMoney: String = "",//发布气品金额
) : ResultData()


/**
 * 查询车辆燃料类型是否支持油气品
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=72877781
 * 对接人：陈泰
 * 孙飞虎
 */
data class ReqQueryFuelTypeOilGasControl(
    var fuelType: String? = "",//车辆燃料类型
) : BaseOrderRequest<BaseRsp<RspFuelTypeOilGas>>("oms-app/carrier/common/queryFuelTypeOilGasControl")

class RspFuelTypeOilGas(
    var fuelTypeOilGasState : String = "",//	燃料类型是否限制使用	 0 否 1 是(不支持使用油气品)
) : ResultData()
