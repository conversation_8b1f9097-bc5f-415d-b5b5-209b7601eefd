package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/** 功能描述:
 *
 * <AUTHOR> 贾发展
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/7/15
 */
data class ReqCheckUnderTwelveVehicleConsignor(
        var vehicleId: String? = "",
        var consignorUserId: String? = ""
) : BaseNewRequest<BaseRsp<ResultData>>("oms-app/carrier/common/checkUnderTwelveVehicleConsignor")