package com.zczy.plugin.order.zeroAssume.model.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqFirstTipFlagByOrderId(
    var orderId: String? = null, //	订单id
) : BaseNewRequest<BaseRsp<RspFirstTipFlagByOrderId>>("oms-app/order/deliver/queryFirstTipFlagByOrderId")

data class RspFirstTipFlagByOrderId(
    var firstFlag: String? = null, // 是否首次发货 1:是，0为否
) : ResultData()
