package com.zczy.plugin.order.zeroAssume.model.request

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.waybill.entity.EWaybill


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqZeroAssumeChildList(
    var orderCarpoolingId: String? = null,
    var queryType: String? = null //1 全部 2 进行中 3 已卸货
) : BaseNewRequest<BaseRsp<PageList<EWaybill>>>("oms-app/display/queryOrderCarpoolingChildOrderList")


