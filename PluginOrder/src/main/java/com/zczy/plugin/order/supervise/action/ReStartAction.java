package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:省货物平台系统2.0 【回复运单定位运行】
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2020/10/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/



import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnResultListener;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;

public class ReStartAction extends BaseActionServer implements OnResultListener, Function<ReStartAction, Boolean> {

    private ESDKInfoObj sdkInfoObj;
    private BaseActionServer nextAction;
    private String remark = "";


    public ReStartAction(ESDKInfoObj sdkInfoObj, String remark) {
        this.sdkInfoObj = sdkInfoObj;
        this.remark = remark;
    }

    public ReStartAction setNextAction(BaseActionServer nextAction) {
        this.nextAction = nextAction;
        return this;
    }

    @Override
    public void start() {
        //授权
        RetrofitManager.executeSigin(Observable.just(ReStartAction.this).map(ReStartAction.this), new EmptyResult<>());
    }

    @Override
    public Boolean apply(@NonNull ReStartAction shippingNoteInfoServer) throws Exception {
        this.auth(sdkInfoObj.getConsignorSubsidiaryId(), new OnAuthListener() {
            @Override
            public void onAuthFailure(String code, String msg) {
                ReStartAction.this.onFailure(code, msg);
            }

            @Override
            public void onAuthSuccess(List<ShippingNoteInfo> list) {
                ReStartAction.this.post();
            }
        });
        return true;
    }

    private void post() {

        out(String.format("省货物平台系统2.0=ReStart=>回复运单定位运行[构建数据]  sdkInfoObj:%s", sdkInfoObj));

        ShippingNoteInfo[] shippingNoteNumbers = new ShippingNoteInfo[]{sdkInfoObj.getShippingNoteInfo()};

        // 重新定位
        LocationOpenApi.restart(AppCacheManager.getApplication(), sdkInfoObj.getVehicleNumber(), sdkInfoObj.getDriverName(), remark, shippingNoteNumbers, ReStartAction.this);

    }

    @Override
    public void onSuccess(List<ShippingNoteInfo> list) {

        out(String.format("省货物平台系统2.0=ReStart=>回复运单定位运行[成功]  sdkInfoObj:%s,size:%s", sdkInfoObj, (list != null ? list.size() : 0)));

        if (list != null && list.size() > 0) {
            //定位间隔时间(单位 ms)毫秒
            sdkInfoObj.setInterval(list.get(0).getInterval());
            new TimeWorkerAction.Build().setInfo(sdkInfoObj).build();

            if (nextAction != null) {
                nextAction.start();
            }
        }
    }

    @Override
    public void onFailure(String code, String msg) {

        out(String.format("省货物平台系统2.0=ReStart=>回复运单定位运行[失败]  sdkInfoObj:%s,code:%s,msg:%s", sdkInfoObj, code, msg));

    }


}
