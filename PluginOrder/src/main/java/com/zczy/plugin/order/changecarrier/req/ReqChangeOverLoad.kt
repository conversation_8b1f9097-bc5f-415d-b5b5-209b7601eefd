package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 *    author : Ssp
 *    date   : 2020/1/17 17:42
 *    desc   : 获取运单实际承运吨位
 */
class ReqChangeOverLoad(
        var orderId: String? = "",
        var sourceId: String? = "",
        var consignorUserId: String? = "",
) : BaseOrderRequest<BaseRsp<RspChangeOverLoad>>("oms-app/orderChange/orderChangeQueryOrderBean")

data class RspChangeOverLoad(
        var weight: String = "",
        var cargoCategory: String = ""
) : ResultData()