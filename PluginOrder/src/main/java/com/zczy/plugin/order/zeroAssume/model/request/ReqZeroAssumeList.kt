package com.zczy.plugin.order.zeroAssume.model.request

import android.graphics.Color
import android.graphics.Typeface
import android.text.*
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import androidx.core.content.ContextCompat
import com.zczy.comm.data.entity.EGoods
import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.utils.ex.toDoubleRoundDownString
import com.zczy.plugin.driver.R
import com.zczy.plugin.order.waybill.entity.EWaybill


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ReqZeroAssumeList(
    var currentPage: Int,
    var pageSize: Int = 10,
    var queryType: String? = null //1 全部 2 进行中 3 已卸货
) : BaseNewRequest<BaseRsp<PageList<RspZeroAssumeList>>>("oms-app/display/queryOrderCarpoolingList")

data class RspZeroAssumeList(
    var orderCarpoolingId: String? = null, // 拼车单ID
    var orderCarpoolingState: String? = null, // 拼车单状态:0 默认,1 待装货,2 运输中,3 已卸货,4 已完成,5 已终止
    var plateNumber: String? = null, // 车牌号
    var childOrderNum: String? = null, // 拼车单子单总数
    var totalTonnageStr: String? = null, // 拼车单子单总吨位，单位吨
    var totalVolumeStr: String? = null, // 拼车单子单总体积，单位方
    var consignorUserId: String? = null, // 货主id
    var isOpen: Boolean = false, // 是否展开
    var wayBill: MutableList<EWaybill> = mutableListOf(),
    var subOrderList: MutableList<EWaybill> = mutableListOf(),
)

fun RspZeroAssumeList.getStateStr(): String {
    when (orderCarpoolingState) {
        "0" -> {
            return "默认"
        }
        "1" -> {
            return "待装货"
        }
        "2" -> {
            return "运输中"
        }
        "3" -> {
            return "已卸货"
        }
        "4" -> {
            return "已完成"
        }
        "5" -> {
            return "已终止"
        }
        else -> {
            return ""
        }
    }
}

fun RspZeroAssumeList.getBatchApplyShow(): Boolean {
    when (orderCarpoolingState) {
        "0" -> {
            return true
        }
        "1" -> {
            return true
        }
        "2" -> {
            return true
        }
        "3" -> {
            return true
        }
        "4" -> {
            return false
        }
        "5" -> {
            return false
        }
        else -> {
            return false
        }
    }
}

fun RspZeroAssumeList.getBatchChangeShow(): Boolean {
    when (orderCarpoolingState) {
        "0" -> {
            return true
        }
        "1" -> {
            return true
        }
        "2" -> {
            return true
        }
        "3" -> {
            return false
        }
        "4" -> {
            return false
        }
        "5" -> {
            return false
        }
        else -> {
            return false
        }
    }

}

fun RspZeroAssumeList.getDesStr(): CharSequence {
    var des = ""
    var num = ""
    if(!TextUtils.isEmpty(totalTonnageStr)){
        des += totalTonnageStr + "吨"
    }
    if(!TextUtils.isEmpty(totalVolumeStr)){
        des += totalVolumeStr + "方"
    }
    if(!TextUtils.isEmpty(childOrderNum)){
        num = childOrderNum.toString()
    }
    val sb = SpannableStringBuilder()
    val spannedString1 = SpannableString(num)
    val spannedString2 = SpannableString("笔零担拼单｜共$des")
    spannedString1.setSpan(
        ForegroundColorSpan(Color.parseColor("#FF602E")),
        0,
        spannedString1.length,
        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    spannedString1.setSpan(StyleSpan(Typeface.BOLD), 0, spannedString1.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    spannedString2.setSpan(
        ForegroundColorSpan(Color.parseColor("#555555")),
        0,
        spannedString2.length,
        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
    )
    spannedString2.setSpan(StyleSpan(Typeface.BOLD), 0, spannedString2.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
    sb.append(spannedString1).append(spannedString2)
    return sb
}

