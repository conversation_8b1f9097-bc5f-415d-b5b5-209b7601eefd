package com.zczy.plugin.order.supervise.req;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/14
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;


public class ReqQueryLastOrderSdkInfo extends BaseNewRequest<BaseRsp<LastOrderSdkInfoListRsp>> {

    public ReqQueryLastOrderSdkInfo() {
        super("/oms-app/carrier/common/queryLastOrderSdkInfo");
    }
}
