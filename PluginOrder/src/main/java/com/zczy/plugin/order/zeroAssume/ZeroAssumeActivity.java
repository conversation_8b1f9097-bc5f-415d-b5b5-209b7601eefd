package com.zczy.plugin.order.zeroAssume;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.waybill.ViewPageAdapter;
import com.zczy.plugin.order.zeroAssume.fragment.ZeroAssumeFragment;
import com.zczy.plugin.order.zeroAssume.model.ZeroAssumeModel;

import java.util.ArrayList;

/*=============================================================================================
 * 功能描述:零担拼车单
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
public class ZeroAssumeActivity extends BaseActivity<ZeroAssumeModel> {
    public static void start(Context context) {
        Intent starter = new Intent(context, ZeroAssumeActivity.class);
        context.startActivity(starter);
    }

    ViewPager mViewPager;
    SlidingTabLayout mCommonTabLayout;

    @Override
    protected int getLayout() {
        return R.layout.zero_assume_list_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        mViewPager = findViewById(R.id.viewPager);
        mCommonTabLayout = findViewById(R.id.tab_layout);

    }

    @Override
    protected void initData() {
        final String[] title = {"进行中", "已卸货", "全部"};
        ArrayList<Fragment> arrayList = new ArrayList<>(3);
        arrayList.add(ZeroAssumeFragment.newInstance("2"));
        arrayList.add(ZeroAssumeFragment.newInstance("3"));
        arrayList.add(ZeroAssumeFragment.newInstance("1"));
        ViewPageAdapter pageAdapter = new ViewPageAdapter(getSupportFragmentManager(), arrayList);
        mViewPager.setAdapter(pageAdapter);
        this.mCommonTabLayout.setViewPager(mViewPager, title);
        //零担列表角标
        queryOrderCarpoolingListCorner();
    }

    /**
     * 注释：零担列表角标
     * 时间：2024/2/23 15:39
     * 作者：王家辉
     * */
    private void queryOrderCarpoolingListCorner() {
        getViewModel(ZeroAssumeModel.class).queryOrderCarpoolingListCorner("2");
    }

    @LiveDataMatch
    public void countNum(String date){
        if (date == null){
            this. mCommonTabLayout.hideMsg(0);
            return;
        }
        int loadingNum = Integer.parseInt(date);
        //进行中
        if (loadingNum > 0){
            this.mCommonTabLayout.showMsg(0,loadingNum);
            if (loadingNum >= 10){
                this.mCommonTabLayout.setMsgMargin(0,40,2);
            }else{
                this.mCommonTabLayout.setMsgMargin(0,40,2);
            }
        }else{
            this. mCommonTabLayout.hideMsg(1);
        }
    }
}
