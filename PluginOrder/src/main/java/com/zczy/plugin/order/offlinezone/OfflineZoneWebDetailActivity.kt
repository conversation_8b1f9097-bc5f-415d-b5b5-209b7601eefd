package com.zczy.plugin.order.offlinezone

import android.Manifest
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.webkit.JavascriptInterface
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.Gson
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.tencent.smtt.sdk.WebSettings
import com.zczy.comm.X5BaseJavascriptInterface
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.config.RouterConfig
import com.zczy.comm.data.entity.EImage
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil.checkPermissions
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.imageselector.ImagePreviewActivity
import com.zczy.comm.x5.X5WebView
import com.zczy.plugin.order.R
import com.zczy.plugin.order.source.pick.PickSourceTools
import com.zczy.plugin.order.source.pick.entity.RxEventPickOffer
import com.zczy.plugin.order.waybill.entity.ContractToPdf
import com.zczy.plugin.order.waybill.entity.EWaybill
import io.reactivex.disposables.Disposable

/**
 * 【线下专区运单详情】
 * @description
 * @date 10:41 6/2/20
 * <AUTHOR>
 * @since 1.0
 **/
@Route(path = RouterConfig.OfflineZoneWebDetailRouter.PATH)
class OfflineZoneWebDetailActivity : BaseActivity<OfflineZoneListModel>() {

    private lateinit var webLayout: X5WebView
    private lateinit var jsUserInfoInterface: X5BaseJavascriptInterface
    var orderId: String = ""

    override fun getLayout(): Int {
        return R.layout.offline_zone_web_activity
    }

    override fun initData() {
        orderId = intent.getStringExtra("orderId")?:""
    }

    override fun bindView(bundle: Bundle?) {
        webLayout = findViewById(R.id.webLayout)
        val webViewSettings: WebSettings = webLayout.getSettings()
        val baseAgent = webViewSettings.userAgentString
        webViewSettings.setUserAgent("$baseAgent;app/ANDROID")
        jsUserInfoInterface = getX5BaseJavascriptInterface()
        webLayout.addJavascriptInterface(jsUserInfoInterface, "android")
        webLayout.loadUrl(HttpURLConfig.getWebUrl("form_h5/order/index.html?_t="
                + System.currentTimeMillis() + "#/tenderOrderDetail?orderId="
                + intent.getStringExtra("orderId")))
    }

    override fun onDestroy() {
        //释放资源
        webLayout.destroy()
        jsUserInfoInterface.destroy()
        super.onDestroy()
    }

    private fun getX5BaseJavascriptInterface(): X5BaseJavascriptInterface {
        return object : X5BaseJavascriptInterface(this) {

            @JavascriptInterface
            fun previewImgs(imgs: Array<String?>?) {
                if (imgs != null) {
                    val list: MutableList<EImage> = ArrayList()
                    for (img in imgs) {
                        val eImage = EImage()
                        eImage.netUrl = img!!
                        list.add(eImage)
                    }
                    ImagePreviewActivity.start(this@OfflineZoneWebDetailActivity, list)
                }
            }

            @JavascriptInterface
            fun cancelPriceFn(orderId: String) {
                //取消报价 公用【功能】
                <EMAIL> { viewModel?.cancelBid(orderId) }
            }

            // 重新报价
            @JavascriptInterface
            fun reHangPriceFn(data: String) {
                try {
                    val waybill = Gson().fromJson(data, EWaybill::class.java)
                    // 重新报价 公用【功能】
                    val action = if (waybill.buttons.isCancelPrice) PickSourceTools.ACTION_OFFER_AGAIN else PickSourceTools.ACTION_OFFER_CANCLE_AGAIN
                    runOnUiThread(Runnable {
                        if (TextUtils.equals(action, PickSourceTools.ACTION_OFFER_AGAIN)) {
                            pickOrOffer(data, action)
                        } else {
                            pickOrOffer(data, action)
                        }
                    })
                } catch (e: Exception) {
                    showToast(e.message)
                }
            }

            @JavascriptInterface
            fun editPriceFn(orderDetail: String) {
                //修改报价交互
                reHangPriceFn(orderDetail)
            }

            @JavascriptInterface
            fun download() {
                runOnUiThread(Runnable { viewModel.addContract(orderId) })
            }
        }
    }

    var offerOrPickPrice: Disposable? = null

    fun pickOrOffer(data: String?, action: String?) {
        if (offerOrPickPrice != null) {
            offerOrPickPrice?.dispose()
        }
        offerOrPickPrice = PickSourceTools().inScene(PickSourceTools.SCENE_WAYBILL).setData(data).setAction(action).start(this)
        if (offerOrPickPrice!=null){
            putDisposable(offerOrPickPrice)
        }
    }

    @LiveDataMatch
    open fun cancelBid() {
        webLayout.reload()
    }

    @RxBusEvent(from = "摘单议价成功")
    open fun onEven(pickOffer: RxEventPickOffer?) {
        finish()
    }

    @LiveDataMatch(tag = "下载文件成功")
    open fun downLoadSuccess(path: String) {
        showDialogToast("文件保存在$path")
    }

    @LiveDataMatch(tag = "查询合同pdf")
    open fun addContractSuccess(data: ContractToPdf) {
        val listP = data.fileOssSignPath.split("/".toRegex()).toTypedArray()
        val url = listP[listP.size - 1]
        val url2 = data.fileOssSignPath
        val list = arrayOf(
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
        checkPermissions(context = baseContext, "中储智运需申请您的文件存储权限,以便您可以正常使用文件保存、浏览功能。拒绝或取消授权不影响使用其他服务", list, object : PermissionCallBack() {
            override fun onHasPermission() {
                viewModel.loadFile(url2, url)
            }
        })
    }

    companion object {
        @JvmStatic
        fun start(context: Context, orderId: String) {
            val intent = Intent(context, OfflineZoneWebDetailActivity::class.java)
            intent.putExtra("orderId", orderId)
            context.startActivity(intent)
        }
    }
}