//package com.zczy.plugin.order.shipments;
//
//import android.graphics.Color;
//import android.os.Bundle;
//import androidx.annotation.Nullable;
//import android.text.SpannableString;
//import android.text.SpannableStringBuilder;
//import android.text.Spanned;
//import android.text.method.LinkMovementMethod;
//import android.text.style.ForegroundColorSpan;
//import android.view.View;
//import android.widget.TextView;
//
//import com.zczy.comm.ui.BaseDialog;
//import com.zczy.plugin.order.R;
//import com.zczy.plugin.order.shipments.entity.EGoodInfo;
//
//import java.util.List;
//
//
///**
// * 功能描述:ZCZY-7660 发货数量修正功能
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/11/19
// */
//public class ShipmentEditDialog extends BaseDialog implements View.OnClickListener {
//
//
//    @Override
//    protected String getDialogTag() {
//        return "ToastDialog";
//    }
//
//    @Override
//    protected int getDialogLayout() {
//        return R.layout.order_shipments_edit_dialog;
//    }
//
//    View.OnClickListener onClickListener;
//    List<EGoodInfo> goodInfos;
//
//    public ShipmentEditDialog setGoodInfos(List<EGoodInfo> goodInfos) {
//        this.goodInfos = goodInfos;
//        return this;
//    }
//
//    public ShipmentEditDialog setOnClickListener(View.OnClickListener onClickListener) {
//        this.onClickListener = onClickListener;
//        return this;
//    }
//
//    @Override
//    protected void bindView(View view, @Nullable Bundle bundle) {
//        view.findViewById(R.id.iv_close).setOnClickListener(this);
//        view.findViewById(R.id.tv_ok).setOnClickListener(this);
//        view.findViewById(R.id.tv_cancle).setOnClickListener(this);
//
//        TextView tv_content = view.findViewById(R.id.tv_content);
//        tv_content.setMovementMethod(LinkMovementMethod.getInstance());
//        if (this.goodInfos != null) {
//            SpannableStringBuilder builder = new SpannableStringBuilder();
//            int color = Color.parseColor("#FB6B40");
//            for (EGoodInfo info : this.goodInfos) {
//                builder.append(String.format("【%s】发货吨位原值%s%s，修改值：", info.getCargoName(), info.getOldBeforeDeliverCargoWeight(), info.getCargoCategory()));
//                SpannableString str = new SpannableString(info.getBeforeDeliverCargoWeight());
//                str.setSpan(new ForegroundColorSpan(color), 0, info.getBeforeDeliverCargoWeight().length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
//                builder.append(str).append(info.getCargoCategory());
//                builder.append("\n");
//            }
//
//            tv_content.setText(builder);
//        }
//    }
//
//    @Override
//    public void onClick(View view) {
//        if (view.getId() == R.id.iv_close || view.getId() == R.id.tv_cancle) {
//            dismissAllowingStateLoss();
//
//        } else if (view.getId() == R.id.tv_ok) {
//            dismissAllowingStateLoss();
//            if (onClickListener != null) {
//                onClickListener.onClick(view);
//            }
//        }
//    }
//}
