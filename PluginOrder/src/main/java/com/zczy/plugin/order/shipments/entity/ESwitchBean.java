//package com.zczy.plugin.order.shipments.entity;
//
//import android.text.TextUtils;
//
//import com.zczy.comm.http.entity.ResultData;
//
///**
// * 功能描述:确认发货前 非指定预付款 熔断校验
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2019/2/23
// */
//public class ESwitchBean extends ResultData {
//    /***1:可以预付;0:不可预付*/
//    String advanceFlag;
//
//    /***不可预付原因
//     * 0:默认条件不满足;
//     * 1:存在欠款;
//     * 2:承运人配置了不可使用预付
//     * 3:承运人三笔预付未结算不可使用
//     * 4：应急账期存在欠款
//     * 5：信用分不满足，不支持预付，核桃信用560分起可享
//     * 6: 预付驳回，超时发货不可再次申请
//     * 7：存在未处理异常的运单，不可预付（车老板 承运人）*/
//    String noAdvanceType;
//
//    /***【可以预付-前提】上传人车合影 0 需要 1 不需要*/
//    String peopleCarImg;
//
//
//    /***
//     * 判断是否不能使用预付款
//     * @return true
//     */
//    public boolean userEable(){
//        return TextUtils.equals("1",this.advanceFlag) ;
//    }
//
//    /***
//     * 需要上传人车合影
//     * @return true 非必填 false 必填
//     */
//    public boolean checkNoPeopleCarImg(){
//
//        return this.userEable() && TextUtils.equals("1",this.peopleCarImg) ;
//    }
//    public String getNoAdvanceType() {
//        return noAdvanceType;
//    }
//
//    public String getAdvanceFlag() {
//        return advanceFlag;
//    }
//}
