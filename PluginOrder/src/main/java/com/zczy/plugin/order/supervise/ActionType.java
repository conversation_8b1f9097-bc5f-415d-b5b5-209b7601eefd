package com.zczy.plugin.order.supervise;
/*=============================================================================================
 * 功能描述:动作类型
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/14
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

public enum ActionType {

    Init("0","初始化"),
    Start("1","发货"),
    Stop("2","收货"),
    Pause("3","运单列表中所有运单标记为暂停状态"),
    ReStart("4","执行重启定位操作");


    public String type;
    public String msg;

    ActionType(String type,String msg){
        this.type = type;
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "ActionType{" +
                "type='" + type + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
