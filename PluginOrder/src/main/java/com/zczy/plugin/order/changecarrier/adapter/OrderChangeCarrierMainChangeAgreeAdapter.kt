package com.zczy.plugin.order.changecarrier.adapter

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.utils.getResColor
import com.zczy.comm.widget.itemdecoration.CommItemDecoration
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.req.OrderChangesDetailDto
import com.zczy.plugin.order.changecarrier.req.RspOrderChangeData

class OrderChangeCarrierMainChangeAgreeAdapter
    :
    BaseQuickAdapter<RspOrderChangeData, BaseViewHolder>(R.layout.order_change_carrier_main_change_agree_item) {

    override fun convert(helper: BaseViewHolder, item: RspOrderChangeData) {
        helper.addOnClickListener(R.id.tv_ok)
        helper.addOnClickListener(R.id.tv_no)
        helper.addOnClickListener(R.id.tv_copy)
        helper.addOnClickListener(R.id.iv_contact_shippers)
        helper.addOnClickListener(R.id.tv_check)
        helper.setGone(R.id.iv_lingdan, TextUtils.equals("1", item.isLessThanOrder))
            .setGone(R.id.tv_check, (TextUtils.equals("1", item.modeType) || TextUtils.equals("2", item.modeType)) && TextUtils.equals("1", item.changeSourceType))
        helper
            .setText(R.id.tv_order, item.orderId)
        val detailList: List<OrderChangesDetailDto> = item.detailList
        val orderChangeAdapter = OrderChangeCarrierAgreeChildAdapter()
        val recyclerView =
            helper.getView<androidx.recyclerview.widget.RecyclerView>(R.id.recyclerView)
        recyclerView.let {
            it.layoutManager =
                androidx.recyclerview.widget.LinearLayoutManager(mContext)
            it.isFocusable = false
            it.isNestedScrollingEnabled = false
            it.addItemDecoration(
                CommItemDecoration.createVertical(mContext, getResColor(R.color.comm_divider_e3), 1)
            )
            it.adapter = orderChangeAdapter
        }
        orderChangeAdapter.setNewData(detailList)
    }
}
