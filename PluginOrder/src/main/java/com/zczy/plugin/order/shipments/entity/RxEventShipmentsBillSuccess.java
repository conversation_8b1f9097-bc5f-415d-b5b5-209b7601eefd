package com.zczy.plugin.order.shipments.entity;

/**
 * 功能描述:发货卸货成功[消息]
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/15
 */
public class RxEventShipmentsBillSuccess {

    /*** 0: 发货 1 卸货 2 线下专区发货，3 线下专区卸货*/
    public int type;

    public RxEventShipmentsBillSuccess(int type) {
        this.type = type;
    }

    /***
     * 收货成功通知
     * @return
     */
    public static RxEventShipmentsBillSuccess billSuccess() {
        return new RxEventShipmentsBillSuccess(1);
    }

    /***
     * 发货成功通知
     * @return
     */
    public static RxEventShipmentsBillSuccess ShipmentsSuccess() {
        return new RxEventShipmentsBillSuccess(0);
    }
}
