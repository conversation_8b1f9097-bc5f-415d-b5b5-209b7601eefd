package com.zczy.plugin.order.changecarrier.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.changecarrier.req.Req9QueryFriendsInfo
import com.zczy.plugin.order.changecarrier.req.RspFriendData
import com.zczy.plugin.order.designation.req.ReqAssignDriverTransportCheck
import com.zczy.plugin.order.designation.req.RspAssignDriverTransportCheck

/**
 * PS:
 * Created by sdx on 2019/2/22.
 */
class OrderChangeCarrierPeopleListModel : BaseViewModel() {
    fun queryFriendsInfo(nowPage: Int, key: String, orderId: String) {
        execute(true,
                Req9QueryFriendsInfo(orderId = orderId, nowPage = nowPage, contacter = key, contacterPhone = key),
                object : IResult<BaseRsp<PageList<RspFriendData>>> {
                    override fun onSuccess(t: BaseRsp<PageList<RspFriendData>>) {
                        if (t.success()) {
                            setValue("onGetNetInfoSuccess", t.data)
                        } else {
                            setValue("onGetNetInfoSuccess", null)
                            showDialogToast(t.msg)
                        }
                    }

                    override fun onFail(e: HandleException) {
                        setValue("onGetNetInfoSuccess", null)
                        showDialogToast(e.msg)
                    }
                })
    }

    fun queryFriendsInfoTMS(nowPage: Int, key: String, sourceId: String?, consignorUserId: String?) {
        execute(
            true,
            Req9QueryFriendsInfo(sourceId = sourceId, orderId = sourceId, nowPage = nowPage, contacter = key, contacterPhone = key, consignorUserId = consignorUserId),
            object : IResult<BaseRsp<PageList<RspFriendData>>> {
                override fun onSuccess(t: BaseRsp<PageList<RspFriendData>>) {
                    if (t.success()) {
                        setValue("onGetNetInfoSuccess", t.data)
                    } else {
                        setValue("onGetNetInfoSuccess", null)
                        showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    setValue("onGetNetInfoSuccess", null)
                    showDialogToast(e.msg)
                }
            })
    }

    /**
     * 司机在途
     * */
    fun assignDriverTransportCheck(req: ReqAssignDriverTransportCheck, next: (data: BaseRsp<RspAssignDriverTransportCheck>) -> Unit = {}, nextFail: () -> Unit = {}) {
        execute(false, req) {
            if (it.success()) {
                it?.let { it1 -> next(it1) }
            } else {
                nextFail()
            }
        }
    }
}