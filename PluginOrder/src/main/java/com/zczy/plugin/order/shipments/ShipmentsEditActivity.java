package com.zczy.plugin.order.shipments;

import android.content.Context;

import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.utils.JsonUtil;

import com.zczy.plugin.order.waybill.entity.EWaybill;

import java.util.List;

/***
 * 发货单修改
 */
public class ShipmentsEditActivity
//        extends AbstractLifecycleActivity<ShipmentsModel>
{

    public static void start(Context fragment, EWaybill data) {
        AMainServer mainServer = AMainServer.getPluginServer();
        mainServer.openReactNativeActivity(fragment, "ShipmentsEditPage", JsonUtil.toJson(data));

        //Intent intent = new Intent(fragment, ShipmentsEditActivity.class);
        //intent.putExtra("orderId", data.getOrderId());
        //intent.putExtra("detailId", data.getDetailId());
        //fragment.startActivity(intent);
    }
//
//
//    private TextView mTvOk;
//    private SparseArray<ShipmentsBaseFragment> fragments = new SparseArray<>(5);
//    private String orderId;
//    private String detailId;
//    private EShipmentsEditGoodInfo shipmentsEditGoodInfo;
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//        setContentView(R.layout.order_shipments_edit_activity);
//
//        orderId = getIntent().getStringExtra("orderId");
//        detailId = getIntent().getStringExtra("detailId");
//
//        TextView tv_orderId = findViewById(R.id.tv_orderId);
//        tv_orderId.setText("运单编号 " + orderId);
//
//        getViewModel(ShipmentsModel.class).queryOrderDataBeforeUpdateDeliverInfo(orderId, detailId);
//
//        mTvOk = findViewById(R.id.tv_ok);
//        mTvOk.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                postShipments();
//            }
//        });
//    }
//
//    @LiveDataMatch
//    public void onInfo(EShipmentsEditGoodInfo data) {
//
//        shipmentsEditGoodInfo = data;
//
//        FragmentTransaction transaction = this.getSupportFragmentManager().beginTransaction();
//        //货物明细【展示】
//        ShipmentsEditGoodsFragment goodsFragment = ShipmentsEditGoodsFragment.newFragment();
//        goodsFragment.setEGoodInfoList(data);
//
//        this.fragments.put(ShipmentUI.TAG_GOODINFO.hashCode(), goodsFragment);
//        transaction.add(R.id.ll_top, goodsFragment, ShipmentUI.TAG_GOODINFO);
//
//        //发货信息
//        if (data.isDeliverNoEmpty() || data.isPageImageJsonObjArrNoEmpty()) {
//            TitleFragment titleFragment = new TitleFragment();
//            titleFragment.setTitle("发货信息", false);
//            transaction.add(R.id.ll_file, titleFragment);
//        }
//
//        //  单据照片【默认展示，只判断必传，非必传】
//        ShipmentsImgObj orderImgObj = data.getOrderImgObj();
//        ShipMentFileFragment shipmentBill = ShipMentFileFragment.newFragment(orderId, data.getPageImageJsonObjArr(), orderImgObj);
//        shipmentBill.setFlag(ShipmentUI.TAG_IMAGE_WAYBILL);
//        shipmentBill.setTakePhotoListener(orderImgObj.isTakePhoto());
//        shipmentBill.setWaterMarkFlag(orderImgObj.getWaterMarkFlag());
//        shipmentBill.setShowReadToastIcon(orderImgObj.isUpload());
//        shipmentBill.setNoCheckFileListener(list -> {
//            //true 不必传 ， false 必传
//            return !orderImgObj.isUpload();
//        });
//
//        this.fragments.put(ShipmentUI.TAG_IMAGE_WAYBILL.hashCode(), shipmentBill);
//        transaction.add(R.id.ll_file, shipmentBill);
//
//        ShipmentsImgObj peopleVehicleImgObj = data.getPeopleVehicleImgObj();
//
//        if (peopleVehicleImgObj.isShowUpload()){
//            //必传
//            ShipMentFileFragment shipMentFileFragment = ShipMentFileFragment.newFragment(orderId, data.getDeliverImageJsonObjArr(), peopleVehicleImgObj);
//            shipMentFileFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            shipMentFileFragment.setShowReadToastIcon(peopleVehicleImgObj.isUpload());
//            shipMentFileFragment.setWaterMarkFlag(peopleVehicleImgObj.getWaterMarkFlag());
//            shipMentFileFragment.setTakePhotoListener(peopleVehicleImgObj.isTakePhoto());
//            shipMentFileFragment.setNoCheckFileListener(list -> {
//                //true 不必传 ， false 必传
//                return !peopleVehicleImgObj.isUpload();
//            });
//            shipMentFileFragment.setEdit(true);
//
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), shipMentFileFragment);
//            transaction.add(R.id.ll_file, shipMentFileFragment);
//
//        }else if (data.isDeliverNoEmpty()) {
//            // 车货合影【展示】【只看】
//            ShipMentFileFragment shipMentFileFragment = ShipMentFileFragment.newFragment(orderId, data.getDeliverImageJsonObjArr(),  peopleVehicleImgObj);
//            shipMentFileFragment.setFlag(ShipmentUI.TAG_IMAGE_PERSONCAR);
//            shipMentFileFragment.setShowReadToastIcon(false);
//            shipMentFileFragment.setEdit(false);
//            this.fragments.put(ShipmentUI.TAG_IMAGE_PERSONCAR.hashCode(), shipMentFileFragment);
//            transaction.add(R.id.ll_file, shipMentFileFragment);
//        }
//
//        transaction.commitAllowingStateLoss();
//    }
//
//    /* ------------------------------ 确认发货-------------------------------------------------*/
//    private void postShipments() {
//
//        //确认发货
//        UtilSoftKeyboard.hide(mTvOk);
//
//        final int size = fragments.size();
//        if (size == 0) {
//            return;
//        }
//        ShipmentUI shipmentUI = new ShipmentUI();
//
//        for (int i = 0; i < size; i++) {
//            ShipmentsBaseFragment fragment = fragments.valueAt(i);
//            if (!fragment.checkParams(shipmentUI)) {
//                return;
//            }
//        }
//        shipmentUI.orderId = orderId;
//        shipmentUI.detailId = detailId;
//
//        List<EGoodInfo> list = shipmentUI.getChangeSize();
//        if (!list.isEmpty()) {
//            //有发货吨位数据变动
//            new ShipmentEditDialog().setGoodInfos(list).setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    getViewModel(ShipmentsModel.class).updateDeliverInfoBeforeReceive(shipmentUI);
//                }
//            }).show(ShipmentsEditActivity.this);
//        } else {
//            getViewModel(ShipmentsModel.class).updateDeliverInfoBeforeReceive(shipmentUI);
//        }
//
//    }
//
//    @LiveDataMatch
//    public void onShipmentSuccess(ResultData resultData) {
//        showDialog(new DialogBuilder().setCancelable(false).setHideCancel(true).setMessage(resultData.getResultMsg()).setOkListener(new DialogBuilder.DialogInterface.OnClickListener() {
//            @Override
//            public void onClick(DialogBuilder.DialogInterface dialogInterface, int i) {
//                dialogInterface.dismiss();
//                finish();
//            }
//        }));
//    }
//
//    @LiveDataMatch
//    public void onExit() {
//        finish();
//    }
}
