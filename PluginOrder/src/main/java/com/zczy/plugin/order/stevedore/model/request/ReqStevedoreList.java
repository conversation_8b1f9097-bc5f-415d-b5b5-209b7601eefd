package com.zczy.plugin.order.stevedore.model.request;

import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;
import com.zczy.plugin.order.BaseOrderRequest;
import com.zczy.plugin.order.stevedore.model.ESteveDore;

/**
 * 功能描述:装卸费分页
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ReqStevedoreList extends BaseOrderRequest<BaseRsp<PageList<ESteveDore>>> {


    int nowPage;
    int pageSize = 10;

    public ReqStevedoreList(int nowPage) {

        super ("oms-app/order/loadpay/selectList");
        this.nowPage = nowPage;
    }
}
