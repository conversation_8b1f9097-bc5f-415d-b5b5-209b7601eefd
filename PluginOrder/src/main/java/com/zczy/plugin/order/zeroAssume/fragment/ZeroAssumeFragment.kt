package com.zczy.plugin.order.zeroAssume.fragment

import android.Manifest
import android.app.Activity
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.AbstractLifecycleFragment
import com.sfh.lib.ui.dialog.DialogBuilder
import com.sfh.lib.utils.UtilTool
import com.zczy.comm.config.RouterConfig
import com.zczy.comm.data.entity.EBoss
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.permission.PermissionCallBack
import com.zczy.comm.permission.PermissionUtil.checkPermissions
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.utils.RouterUtils
import com.zczy.comm.utils.toJson
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.lib_zstatistics.sdk.ZStatistics
import com.zczy.plugin.order.R
import com.zczy.plugin.order.bill.*
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierChangeActivity
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierShipmentsCarActivity
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeCar
import com.zczy.plugin.order.express.OrderExpressMainActivity.Companion.start
import com.zczy.plugin.order.navigation.ActionNavigation
import com.zczy.plugin.order.onemore.OrderOneMoreMainActivity.Companion.start
import com.zczy.plugin.order.shipments.ShipmentsActivity
import com.zczy.plugin.order.shipments.entity.RxEventShipmentsBillSuccess
import com.zczy.plugin.order.source.pick.entity.RxCancleOffer
import com.zczy.plugin.order.source.pick.entity.RxEventPickOffer
import com.zczy.plugin.order.violate.OrderViolateAddActivity
import com.zczy.plugin.order.violate.OrderViolateDetailApplyingActivity.Companion.start
import com.zczy.plugin.order.violate.OrderViolateListActivity
import com.zczy.plugin.order.waybill.WaybillButtonLayout.OnButtonClickListener
import com.zczy.plugin.order.waybill.WaybillCommDetailActivity
import com.zczy.plugin.order.waybill.WaybillTransportContractDetailActivity
import com.zczy.plugin.order.waybill.cyr.BargainMoneyDialog
import com.zczy.plugin.order.waybill.cyr.DisagreeReconsiderActivity
import com.zczy.plugin.order.waybill.cyr.ShipmentsSafeActivity
import com.zczy.plugin.order.waybill.dialog.WaybillReceiveCheckErrorDialog
import com.zczy.plugin.order.waybill.dialog.WaybillReceiveCheckErrorDialogV1
import com.zczy.plugin.order.waybill.entity.EOrderBreachStatus
import com.zczy.plugin.order.waybill.entity.EReconsiderInfo
import com.zczy.plugin.order.waybill.entity.EWaybill
import com.zczy.plugin.order.waybill.model.WaybillCyrModel
import com.zczy.plugin.order.zeroAssume.ZeroShipmentToastDialog
import com.zczy.plugin.order.zeroAssume.adapter.ChildListClickListener
import com.zczy.plugin.order.zeroAssume.adapter.ZeroAssumeListAdapter
import com.zczy.plugin.order.zeroAssume.model.ZeroAssumeModel
import com.zczy.plugin.order.zeroAssume.model.request.RspZeroAssumeList
import kotlinx.android.synthetic.main.zero_assume_list_fragment.*
import kotlinx.android.synthetic.main.zero_assume_list_fragment.view.*

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
open class ZeroAssumeFragment : AbstractLifecycleFragment<ZeroAssumeModel>(),
    ChildListClickListener,
    OnButtonClickListener {
    private var queryType = ""
    private var adapter: ZeroAssumeListAdapter? = ZeroAssumeListAdapter()

    companion object {
        @JvmStatic
        fun newInstance(queryType: String?): ZeroAssumeFragment {
            val args = Bundle()
            args.putString("queryType", queryType)
            val fragment = ZeroAssumeFragment()
            fragment.arguments = args
            return fragment
        }
    }

    override fun getLayout(): Int {
        return R.layout.zero_assume_list_fragment
    }

    private val onItemClickListener: OnItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {

        }

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position) as RspZeroAssumeList
            when (view.id) {
                R.id.tv_copy -> {
                    //复制order
                    UtilTool.setCopyText(context, "运单号", item.orderCarpoolingId)
                    showToast("复制成功")
                }

                R.id.tv_apply -> {
                    //批零违约申请
                    OrderViolateAddActivity.startContentUI(
                        this@ZeroAssumeFragment,
                        item.orderCarpoolingId,
                        true,
                        false,
                        1001
                    )
                }

                R.id.tv_change -> {
                    //批零变更承运
                    OrderChangeCarrierShipmentsCarActivity.startUI(
                        context = activity,
                        orderId = item.orderCarpoolingId.toString(),
                        isCarPool = true
                    )
                }

                R.id.tv_open_close, R.id.iv_open_close -> {
                    if (item.isOpen) {
                        item.isOpen = !item.isOpen
                        adapter.notifyItemChanged(position)
                    } else {
                        if (TextUtils.equals("2", queryType)) {
                            // 进行中的列表queryOrderCarpoolingList接口直接返回了子单信息subOrderList，已卸货和全部需要在点击“展开明细”的时候自行请求queryOrderCarpoolingChildOrderList获取子单信息
                            item.isOpen = !item.isOpen
                            adapter.notifyItemChanged(position)
                        } else {
                            viewModel?.getOrderCarpoolingChildList(
                                item.orderCarpoolingId,
                                queryType,
                            ) {
                                item.wayBill = it?.rootArray ?: mutableListOf()
                                item.isOpen = !item.isOpen
                                adapter.notifyItemChanged(position)
                            }
                        }
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun getOrderCarpoolingList(data: PageList<RspZeroAssumeList>) {
        for (tData in data.rootArray) {
            for (mData in tData.wayBill) {
                mData.orderCarpoolingId = tData.orderCarpoolingId
            }
            for (mData in tData.subOrderList) {
                mData.orderCarpoolingId = tData.orderCarpoolingId
            }
        }
        swipe_refresh_more_layout.onRefreshCompale(data)
    }

    override fun onClickToHandleBtn(view: View, waybill: EWaybill?) {
        when (view.id) {
            R.id.tv_copy -> {
                //复制order
                UtilTool.setCopyText(context, "运单号", waybill?.orderId)
                showToast("复制成功")
            }

            R.id.iv_dh -> {
                if (TextUtils.equals(EWaybill.TYPE_DELAYSHIPMENT, waybill?.orderQueryType)) {
                    // 待装货导航
                    activity?.let {
                        it.runOnUiThread {
                            if (waybill != null) {
                                viewModel?.let { it1 ->
                                    ActionNavigation().setEWaybillData(waybill)
                                        .setOrderId(waybill.orderId).setFlag("1").start(
                                        it, it1
                                    )
                                }
                            }
                        }
                    }
                } else if (TextUtils.equals(EWaybill.TYPE_UNLOAD, waybill?.orderQueryType)) {
                    //待卸货导航
                    waybill?.let {
                        context?.let { it1 ->
                            ActionNavigation()
                                .setEWaybillData(it)
                                .setOrderId(waybill.orderId)
                                .setFlag("2")
                                .start(it1, getViewModel(WaybillCyrModel::class.java))
                        }
                    }
                }

            }

            R.id.tv_handle -> {
                getViewModel(WaybillCyrModel::class.java).queryReconsiderInfoByDetailId(waybill)
            }
        }
    }

    override fun onClickChildItem(waybill: EWaybill?) {
        WaybillCommDetailActivity.start(context, waybill?.orderId, waybill?.orderQueryType)
        postEvetId("on_item", waybill?.orderId)
    }

    override fun onButtonClick(view: View?, title: String?, waybill: EWaybill?, position: Int) {
        when (title) {
            "确认发货" -> {
                viewModel?.queryMergeByOrderId(waybill, "0")
                postEvetId("tv_shipment", waybill?.orderId)
            }

            "变更承运" -> {
                if (waybill?.advanceStateOwnerBack() == true) {
                    //预付款订单货主打回状态提示
                    showDialogToast("当前运单被打回，无法申请变更！")
                    return
                }
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "关于变更审核"
                dialogBuilder.message =
                    "当承运方发起变更时，如果涉及到变更承运人，首先需要平台审核核实，核实后，需要变更方同意后才能完成变更操作。"
                dialogBuilder.setOKText("我知道了")
                dialogBuilder.isHideCancel = true
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                        dialogInterface.dismiss()
                        ZStatistics.onViewClick(activity, "tv_changeRenge_ok")
                        waybill?.let {
                            OrderChangeCarrierChangeActivity.start(
                                fragment = this@ZeroAssumeFragment,
                                orderId = it.orderId,
                                consignorUserId = it.consignorUserId.toString(),
                                isZeroAssume = true
                            )
                        }
                    }
                showDialog(dialogBuilder)

                postEvetId("tv_change", waybill?.orderId)
            }

            "取消变更" -> {
                val dialogBuilder = DialogBuilder()
                dialogBuilder.title = "温馨提示"
                dialogBuilder.message = "是否取消变更"
                dialogBuilder.okListener =
                    DialogBuilder.DialogInterface.OnClickListener { dialogInterface, i ->
                        dialogInterface.dismiss()
                        getViewModel(WaybillCyrModel::class.java).cancelCarrierChange(waybill?.orderId, waybill?.sourceId, waybill?.consignorUserId)
                    }
                showDialog(dialogBuilder)
            }

            "运单详情" -> {
                waybill?.let {
                    this.gotoWaybillStatusUI(it)
                    postEvetId("tv_state", it.orderId)
                }

            }

            "取消运单" -> {
                waybill?.let {
                    this.nospecifyDialog(it)
                    postEvetId("tv_violate", it.orderId)
                }
            }

            "合同补签" -> {
                WaybillTransportContractDetailActivity.start(activity, waybill?.orderId, true)
            }

            "确认卸货", "扫码收货" -> {
                viewModel?.queryMergeByOrderId(waybill, "1")
            }

            "重新上传", "重新卸货" -> {
                waybill?.zeroType = "1"
                getViewModel(WaybillCyrModel::class.java).confirmUnload(activity, waybill)
            }

            "快递录入" -> {
                start(this)
            }

            "查看评价" -> {
                val mainServer = AMainServer.getPluginServer()
                mainServer?.jumpEvaluateDetails(activity, waybill?.orderId)
            }

            "再来一单" -> {
                waybill?.let { start(activity, it) }
            }

            "回单修改" -> {
                BillNormalEditActivity.startContentUI(
                    activity,
                    waybill?.orderId,
                    waybill?.detailId
                )
            }

            "卸货拍照" -> {
                BillClockInActivity.start(activity, waybill)
            }

        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        //议价拒绝之后
        if (requestCode == 10001 && resultCode == Activity.RESULT_OK) {
            swipe_refresh_more_layout.onAutoRefresh()
        }
    }


    @LiveDataMatch(tag = "回单议价信息")
    open fun onReconsiderInfo(data: EWaybill?, info: EReconsiderInfo) {
        BargainMoneyDialog(
            context, data, info
        ) { dialog, yesNo ->
            dialog.dismiss()
            if (yesNo) {
                //同意
                getViewModel(WaybillCyrModel::class.java).reconsiderConfirmByDetailId(data, info)
            } else {
                //拒绝
                DisagreeReconsiderActivity.start(this@ZeroAssumeFragment, info.detailId, 10001)
            }
        }.show()
    }

    @RxBusEvent(from = "议价同意，取消报价,刷新我的报价列表")
    open fun onEventRefreshSuccess(s: RxCancleOffer?) {
        //重新加载
        if (TextUtils.equals(queryType, EWaybill.TYPE_MAKEOFFERS) || TextUtils.equals(
                queryType,
                EWaybill.TYPE_SETTLEMENT
            ) || TextUtils.equals(queryType, EWaybill.TYPE_MIND)
        ) {
            swipe_refresh_more_layout.onAutoRefresh()
        }
    }

    /***
     * 打开运单状态界面
     */
    private fun gotoWaybillStatusUI(data: EWaybill) {
        // 运单状态界面
        WaybillCommDetailActivity.start(activity, data.orderId)
    }

    /**
     * 违约申请
     *
     * @param data
     */
    private fun nospecifyDialog(data: EWaybill) {
        getViewModel(WaybillCyrModel::class.java).checkNospecify(activity, data)
    }

    @LiveDataMatch(tag = "确认发货")
    open fun gotoFahuoAction(data: EWaybill?, read: Boolean) {
        if (read) {
            //安全告知书
            ShipmentsSafeActivity.start(context, data)
        } else {
            //直接发货
            ShipmentsActivity.start(context, data)
        }
    }

    @LiveDataMatch(tag = "确认卸货->风险卸货")
    open fun onUnloadRiskAction(data: EWaybill?) {
        onUnloadAction(data, "1")
    }

    @LiveDataMatch(tag = "确认卸货->备案卸货")
    open fun onUnloadCopyAction(data: EWaybill?) {
        onUnloadAction(data, "2")
    }

    @LiveDataMatch(tag = "确认卸货")
    open fun onUnloadAction(data: EWaybill?) {
        onUnloadAction(data, "")
    }

    @LiveDataMatch(tag = "货主账号余额不足,提示余额不足，不能卸货")
    open fun onUnloadActionError(orderId: String?) {
        WaybillReceiveCheckErrorDialog().setOrderIdData(orderId).show(this)
    }

    @LiveDataMatch(tag = "当前在途时间不足，不可卸货，是否提前上传")
    open fun onUnloadActionErrorV1(orderId: String?) {
        WaybillReceiveCheckErrorDialogV1().setOrderIdData(orderId).show(this)
    }


    @LiveDataMatch(tag = "取消变更成功")
    open fun onCancelCarrierChange(msg: String?) {
        //刷新待装货运单列表
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "温馨提示"
        dialogBuilder.message = msg
        dialogBuilder.isHideCancel = true
        dialogBuilder.setOKText("我知道了")
        dialogBuilder.isHideCancel = false
        dialogBuilder.okListener =
            DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
                dialogInterface.dismiss()
                swipe_refresh_more_layout.onAutoRefresh()
            }
        showDialog(dialogBuilder)
    }

    @LiveDataMatch(tag = "违约申请")
    open fun onViolateSuccess(status: EOrderBreachStatus, waybill: EWaybill) {
        if (TextUtils.equals("1", status.jumpType)) {
            //违约列表
            OrderViolateListActivity.startContentUI(activity, waybill.orderId)
        } else if (TextUtils.equals("2", status.jumpType)) {
            //违约详情
            start(this@ZeroAssumeFragment, status.breachApplyId, 1001)
        } else {
            //新增违约
            OrderViolateAddActivity.startContentUI(
                this@ZeroAssumeFragment,
                waybill.orderId,
                true,
                1001
            )
        }
    }

    @LiveDataMatch(tag = "合并发货提示，卸货")
    open fun onQueyMergeBill(merge: Boolean, waybill: EWaybill?) {
        if (merge) {
            ZeroShipmentToastDialog()
                .setTitle("合并卸货提示")
                .setMsg("当前卸货地存在多个同货主运单,您是否需要一并确认卸货？")
                .setOnClickListener(object : DialogInterface.OnClickListener {
                    override fun onClick(dialog: DialogInterface?, which: Int) {
                        if (which == 0) {
                            //合并
                            waybill?.zeroType = "2"
                        } else {
                            //单一
                            waybill?.zeroType = "1"
                        }
                        getViewModel(WaybillCyrModel::class.java).confirmUnload(activity, waybill)
                    }

                }).show(activity)
        } else {
            //单一
            waybill?.zeroType = "1"
            getViewModel(WaybillCyrModel::class.java).confirmUnload(activity, waybill)
        }
    }

    @LiveDataMatch(tag = "合并发货提示，发货")
    open fun onQueyMergeShipment(merge: Boolean, waybill: EWaybill?) {
        if (merge) {
            ZeroShipmentToastDialog()
                .setTitle("合并发货提示")
                .setMsg("当前发货地存在多个同货主运单，您是否需要一并确认发货？")
                .setOnClickListener(object : DialogInterface.OnClickListener {
                    override fun onClick(dialog: DialogInterface?, which: Int) {
                        if (which == 0) {
                            //合并
                            waybill?.zeroType = "2"
                        } else {
                            //单一
                            waybill?.zeroType = "1"
                        }
                        getViewModel(WaybillCyrModel::class.java).shipment(activity, waybill)
                    }

                }).show(activity)
        } else {
            //单一
            waybill?.zeroType = "1"
            getViewModel(WaybillCyrModel::class.java).shipment(activity, waybill)
        }
    }

    /***
     * 上报埋点
     */
    fun postEvetId(name: String, orderId: String?) {

        ZStatistics.onEvent(
            this@ZeroAssumeFragment::class.java.name,
            this@ZeroAssumeFragment::class.java.simpleName + "#" + name
        ) {
            val requestParam: MutableMap<String, String> = HashMap(1)
            requestParam["orderId"] = orderId ?: ""
            it.put("requestParam", requestParam)
        }
    }

    open fun onUnloadAction(data: EWaybill?, risk: String) {
        if (data == null) {
            return
        }
        //区分 零担单个。批量零担 正常

        if (TextUtils.equals("2", data.zeroType)) {
            //批量零担(卸货)
            val params = mutableMapOf<String, Any>()
            params["page"] = "BillZeroAssumePage"
            params["data"] = mapOf(
                Pair("orderId", data.orderId),
                Pair("risk", risk),
                Pair("detailId", data.detailId)
            ).toJson()
            RouterUtils.skipRouter(RouterConfig.ReactRouter.URI, params)
        } else {
            //正常货源卸货 or 单个零担
            //收货类型
            if (TextUtils.equals("1", data.coderType)) {
                context?.let {
                    checkPermissions(
                        it,
                        "中储智运需申请您的摄像头权限，以便为您提供二维码识别、实现扫码收货服务。拒绝或取消授权不影响使用其他服务",
                        arrayOf(Manifest.permission.CAMERA),
                        object : PermissionCallBack() {
                            override fun onHasPermission() {
                                // 扫码收货
                                ScanBillActivity.startContentUI(
                                    context,
                                    data.orderId,
                                    data.detailId,
                                    data.advanceState,
                                    true
                                )
                            }
                        })
                }
            } else if (TextUtils.equals("2", data.coderType)) {
                // 系统对接收货
                BillSystemActivity.startUI(
                    context,
                    data.orderId,
                    data.detailId,
                    data.advanceState,
                    data.tag
                )
            } else {
                // 普通收货
                BillNormalActivity.startContentUI(
                    context,
                    data.orderId,
                    data.detailId,
                    data.advanceState,
                    risk,
                    data.tag,
                    true,
                )
            }
        }

    }

    override fun initData(view: View?) {
        queryType = arguments?.getString("queryType") ?: ""
        val emptyView = CommEmptyView.creatorDef(context)
        adapter?.setButtonViewClickListener(this)
        adapter?.setChildListClickListener(this)
        adapter?.setQueryType(queryType)
        view?.swipe_refresh_more_layout?.apply {
            addItemDecorationSize(15)
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addOnItemListener(onItemClickListener)
            setOnLoadListener2 {
                adapter?.setIsFirst(true)
                viewModel?.getOrderCarpoolingList(it, queryType)
            }
            onAutoRefresh()
        }
    }

    @RxBusEvent(from = "摘单议价成功")
    open fun onEventPickOfferSuccess(success: RxEventPickOffer) {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @RxBusEvent(from = "发货/发货成功")
    open fun onEventShipmentsBillSSuccess(success: RxEventShipmentsBillSuccess?) {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @RxBusEvent(from = "待卸货-变更车辆 ->待卸货 列表")
    open fun onChangeCarSuccess(s: RxEventChangeCar?) {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @RxBusEvent(from = "切换关联车老板刷新")
    open fun changeBoss(eBoss: EBoss?) {
        swipe_refresh_more_layout.onAutoRefresh()
    }

    @RxBusEvent(from = "待卸货-变更车辆")
    open fun changeBoss(car: RxEventChangeCar?) {
        swipe_refresh_more_layout.onAutoRefresh()
    }

}