package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 准驾车型校验
 */
data class VertifyCarTypeBeanReq(
    var orderId: String = "",
    var vehicleId: String = "",
    var driverUserId: String = ""
) : BaseOrderRequest<BaseRsp<VertifyCarTypeBeanResp>>("/oms-app/carrier/common/checkCarrierVehicleType")

data class VertifyCarTypeBeanResp(
    var checkVehicleType: String = ""
) : ResultData()