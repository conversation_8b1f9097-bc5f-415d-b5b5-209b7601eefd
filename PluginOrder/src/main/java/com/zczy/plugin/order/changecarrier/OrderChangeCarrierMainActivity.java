package com.zczy.plugin.order.changecarrier;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.zczy.comm.CommServer;
import com.zczy.comm.config.HttpURLConfig;
import com.zczy.comm.data.IUserServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.tablayout.CommonTabEntity;
import com.zczy.comm.x5.X5WebActivity;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.changecarrier.fragment.OrderChangeCarrierMainChangeAgreeFragment;
import com.zczy.plugin.order.changecarrier.fragment.OrderChangeCarrierMainChangeHistoryFragment;
import com.zczy.plugin.order.changecarrier.fragment.OrderChangeCarrierMainOrderChangeFragment;

import java.util.ArrayList;

/**
 * 变更承运
 *
 * <AUTHOR>
 */
public class OrderChangeCarrierMainActivity extends BaseActivity<BaseViewModel> {
    private AppToolber toolbar;
    private IUserServer userServer = CommServer.getUserServer();
    public static void start(Context context, int tab) {
        Intent intent = new Intent(context, OrderChangeCarrierMainActivity.class);
        intent.putExtra("tab", tab);
        context.startActivity(intent);
    }

    @Override
    protected int getLayout() {
        return R.layout.order_change_carrier_main_activity;
    }

    @Override
    protected void bindView(@Nullable Bundle bundle) {
        initCommonTab();
        toolbar = findViewById(R.id.appToolber);
        toolbar.setRightOnClickListener(v -> {
            // 服务端地址/form_h5/order/index.html?_t=时间戳#/changeCarHelp?type=1  （参数type：1个体司机，2车老板，3物流企业）
            // （参数type：1 个体司机，2 车老板，3 物流企业）
            String type = "1";
            String title = "个体司机在途车辆变更";
            if (userServer != null) {
                ELogin login = userServer.getLogin();
                if (login != null) {
                    IRelation relation = login.getRelation();
                    if (relation.isBoss()) {
                        type = "2";
                        title = "车老板在途车辆变更";
                    } else if (relation.isCys()) {
                        type = "3";
                        title = "物流企业在途车辆变更";
                    }
                }
            }
            String url = HttpURLConfig.getWebUrl() +
                    "/form_h5/order/index.html" +
                    "?_t=" + System.currentTimeMillis() +
                    "#/changeCarHelp" +
                    "?type=" + type;
            X5WebActivity.start(this, url, title);
        });
    }

    @Override
    protected void initData() {

    }

    private ArrayList<Fragment> fragments = new ArrayList<>();

    private ArrayList<CustomTabEntity> tabEntitys = new ArrayList<>();

    private void initCommonTab() {
        int tab = getIntent().getIntExtra("tab", userServer.getLogin().getRelation().isCys() ? 0 : 1);
        CommonTabLayout commonTabLayout = findViewById(R.id.common_tab_layout);
        if (userServer.getLogin().getRelation().isCys()) {
            CommonTabEntity tabEntity1 = new CommonTabEntity();
            tabEntity1.title = getString(R.string.order_change_carrier_main_title_0_order_change);
            tabEntitys.add(tabEntity1);
            OrderChangeCarrierMainOrderChangeFragment fragment1 = (OrderChangeCarrierMainOrderChangeFragment) Fragment.instantiate(this, OrderChangeCarrierMainOrderChangeFragment.class.getName());
            fragments.add(fragment1);
        }

        CommonTabEntity tabEntity2 = new CommonTabEntity();
        tabEntity2.title = "变更待处理";
        CommonTabEntity tabEntity3 = new CommonTabEntity();
        tabEntity3.title = getString(R.string.order_change_carrier_main_title_1_changer_history);

        tabEntitys.add(tabEntity2);
        tabEntitys.add(tabEntity3);

        OrderChangeCarrierMainChangeAgreeFragment fragment2 = (OrderChangeCarrierMainChangeAgreeFragment) Fragment.instantiate(this, OrderChangeCarrierMainChangeAgreeFragment.class.getName());
        fragments.add(fragment2);
        OrderChangeCarrierMainChangeHistoryFragment fragment3 = (OrderChangeCarrierMainChangeHistoryFragment) Fragment.instantiate(this, OrderChangeCarrierMainChangeHistoryFragment.class.getName());
        fragments.add(fragment3);

        commonTabLayout.setTabData(tabEntitys, this, R.id.frame_layout, fragments);
        if (tab > 0) {
            tab = userServer.getLogin().getRelation().isCys() ? tab : tab - 1;
        }
        commonTabLayout.setCurrentTab(tab);
    }
}
