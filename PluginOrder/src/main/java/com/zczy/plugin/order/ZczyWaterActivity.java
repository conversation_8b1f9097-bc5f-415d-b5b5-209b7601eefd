package com.zczy.plugin.order;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.LayoutInflater;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.huawei.hms.framework.common.IoUtils;
import com.sfh.lib.AppCacheManager;
import com.zczy.comm.utils.imgloader.ImgUtil;
import com.zczy.libwater.util.DateTimeUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

/***
 * 加水印
 */
public class ZczyWaterActivity extends FragmentActivity {


    public static void start(Fragment context,
                             String orderId,
                             String position,
                             String latLon,
                             String imagePath, int requestCode) {

        Intent starter = new Intent(context.getActivity(), ZczyWaterActivity.class);
        starter.putExtra("imagePath", imagePath);
        starter.putExtra("orderId", orderId);
        starter.putExtra("position", position);
        starter.putExtra("latLon", latLon);
        context.startActivityForResult(starter, requestCode);
    }

    public static void start(Activity context,
                             String orderId,
                             String position,
                             String latLon,
                             String imagePath, int requestCode) {

        Intent starter = new Intent(context, ZczyWaterActivity.class);
        starter.putExtra("imagePath", imagePath);
        starter.putExtra("orderId", orderId);
        starter.putExtra("position", position);
        starter.putExtra("latLon", latLon);
        context.startActivityForResult(starter, requestCode);
    }
    public static final String KEY_IMAGE_PATH = "imagePath";
    public static final String KEY_IMAGE_PATH_WATERMASK = "imagePathWaterMask";

    String imagePath;
    String waterImagePath;
    ImageView iv_img;
    TextView tv_time;
    TextView tv_order_id;
    TextView tv_date_location_lat_lon;
    TextView tv_location;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.zczy_water_activity);
        this.init();
    }

    private void init() {

        iv_img = findViewById(R.id.iv_img);
        tv_order_id = findViewById(R.id.tv_order_id);
        tv_date_location_lat_lon = findViewById(R.id.tv_date_location_lat_lon);
        tv_location = findViewById(R.id.tv_location);
        tv_time = findViewById(R.id.tv_time);

        String orderId = this.getIntent().getStringExtra("orderId");
        String position = this.getIntent().getStringExtra("position");
        String latLon = this.getIntent().getStringExtra("latLon");
        imagePath = this.getIntent().getStringExtra("imagePath");

        tv_order_id.setText(String.format("运单号：%s", orderId));
        tv_location.setText(TextUtils.isEmpty(position) ? "" : position);
        tv_date_location_lat_lon.setText(TextUtils.isEmpty(latLon) ? "" : latLon);

        tv_time.postDelayed(time, 1000);

        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        findViewById(R.id.iv_ok).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //保存图片
                save();
            }
        });

        ImgUtil.loadFile(iv_img, imagePath);
    }

    private Runnable time = new Runnable() {
        @Override
        public void run() {
            tv_time.setText(DateTimeUtils.currentDateTimeFomart("yyyy-MM-dd HH:mm:ss"));
            tv_time.postDelayed(time, 1000);
        }
    };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        tv_time.removeCallbacks(time);
    }

    private void save() {

        Toast.makeText(this, "正在保存图片...", Toast.LENGTH_SHORT).show();
        new AsyncTask<Void, Void, Intent>() {

            @Override
            protected Intent doInBackground(Void... voids) {

                Bitmap combinedBitmap = createCombinedBitmap();
                if (null != combinedBitmap) {
                     saveBitmap(combinedBitmap);
                }
                Intent intent = new Intent();
                intent.putExtra(KEY_IMAGE_PATH, imagePath);
                intent.putExtra(KEY_IMAGE_PATH_WATERMASK, waterImagePath);
                return intent;
            }

            @Override
            protected void onPostExecute(Intent intent) {
                if (intent != null) {
                    setResult(RESULT_OK, intent);
                }
                finish();
            }
        }.execute();

    }


//    public Bitmap createBitmapFromView(View view) {
//
//        try {
//            if (null == view) {
//                return null;
//            } else {
//                DisplayMetrics outMetrics = new DisplayMetrics();
//                this.getWindowManager().getDefaultDisplay().getRealMetrics(outMetrics);
//                int width = outMetrics.widthPixels;
//                int height = outMetrics.heightPixels;
//                view.setDrawingCacheEnabled(true);
//                view.buildDrawingCache();
//                view.measure(View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY), View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY));
//                view.layout(0, 0, width, height);
//                Bitmap bitmap1 = view.getDrawingCache(true);
//                Bitmap bitmap2 = Bitmap.createBitmap(bitmap1, 0, 0, width, height);
//                view.setDrawingCacheEnabled(false);
//                view.destroyDrawingCache();
//                return bitmap2;
//            }
//        }
//        catch (Exception e){
//            return null;
//        }
//    }
    /**
     * 创建垂直拼接的图片（原图 + 水印信息）
     */
    private Bitmap createCombinedBitmap() {
        try {
            // 1. 加载原图（禁用自动旋转，保持原始状态）
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = false;
            options.inPreferredConfig = Bitmap.Config.ARGB_8888;
            // 禁用自动旋转
            options.inScaled = false;

            Bitmap originalBitmap = BitmapFactory.decodeFile(imagePath, options);
            if (originalBitmap == null) {
                return null;
            }

            // 2. 创建水印View并转换为Bitmap
            Bitmap watermarkBitmap = createWatermarkBitmap(originalBitmap.getWidth());
            if (watermarkBitmap == null) {
                return originalBitmap;
            }

            // 3. 创建拼接后的Bitmap
            int totalHeight = originalBitmap.getHeight() + watermarkBitmap.getHeight();
            Bitmap combinedBitmap = Bitmap.createBitmap(originalBitmap.getWidth(), totalHeight, Bitmap.Config.ARGB_8888);

            Canvas canvas = new Canvas(combinedBitmap);
            Paint paint = new Paint();
            paint.setAntiAlias(true);

            // 4. 绘制原图
            canvas.drawBitmap(originalBitmap, 0, 0, paint);

            // 5. 绘制水印（在原图下方）
            canvas.drawBitmap(watermarkBitmap, 0, originalBitmap.getHeight(), paint);

            // 6. 回收临时Bitmap
            if (!originalBitmap.isRecycled()) {
                originalBitmap.recycle();
            }
            if (!watermarkBitmap.isRecycled()) {
                watermarkBitmap.recycle();
            }

            return combinedBitmap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 创建水印信息的Bitmap
     */
    private Bitmap createWatermarkBitmap(int width) {
        try {
            // 创建水印布局
            LayoutInflater inflater = LayoutInflater.from(this);
            View watermarkView = inflater.inflate(R.layout.watermark_info_layout, null);

            // 设置水印信息
            TextView tvOrderId = watermarkView.findViewById(R.id.tv_order_id);
            TextView tvTime = watermarkView.findViewById(R.id.tv_time);
            TextView tvLatLon = watermarkView.findViewById(R.id.tv_date_location_lat_lon);
            TextView tvLocation = watermarkView.findViewById(R.id.tv_location);

            tvOrderId.setText(tv_order_id.getText());
            tvTime.setText(tv_time.getText());
            tvLatLon.setText(tv_date_location_lat_lon.getText());
            tvLocation.setText(tv_location.getText());

            // 测量和布局
            int widthSpec = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY);
            int heightSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
            watermarkView.measure(widthSpec, heightSpec);
            watermarkView.layout(0, 0, watermarkView.getMeasuredWidth(), watermarkView.getMeasuredHeight());

            // 转换为Bitmap
            Bitmap bitmap = Bitmap.createBitmap(watermarkView.getMeasuredWidth(),
                    watermarkView.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            watermarkView.draw(canvas);

            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Bitmap createBitmapFromView(View view) {

        try {
            Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            view.draw(canvas);
            return bitmap;
        } catch (Exception e) {
            return null;
        }
    }


    public boolean saveBitmap(Bitmap bitmap) {
        FileOutputStream fos = null;
        try {
            File cameraFolder =  AppCacheManager.getFileCache();
            if (cameraFolder == null || !cameraFolder.exists()){
                String cameraPath = Environment.getExternalStorageDirectory().getPath() + File.separator + "DCIM" + File.separator + "Camera";
                cameraFolder = new File(cameraPath);
                if (!cameraFolder.exists()) {
                    cameraFolder.mkdirs();
                }
            }

            File imageFile = new File(cameraFolder,String.format("JPEG_WATER_MASK_%s.jpg",System.currentTimeMillis()));
            waterImagePath = imageFile.getAbsolutePath();
            File parent = imageFile.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }

            fos = new FileOutputStream(imageFile);
            boolean b = bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
            return b;
        } catch (FileNotFoundException var6) {
        } catch (IOException var7) {
        } finally {
            if (fos != null) {
                IoUtils.closeSecure(fos);
            }
        }

        return false;
    }



}
