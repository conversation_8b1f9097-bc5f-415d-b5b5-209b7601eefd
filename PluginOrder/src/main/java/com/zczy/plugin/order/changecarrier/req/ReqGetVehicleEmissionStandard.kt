package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

data class ReqGetVehicleEmissionStandard(
    var allowType: String?, //货主配置项允许类型
    var vehicleId: String?, //车辆ID
) : BaseOrderRequest<BaseRsp<RspVehicleEmissionStandard>>("mms-app/vehicle/getVehicleEmissionStandard")

data class RspVehicleEmissionStandard(
    var emissionStandard: String = "", //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
): ResultData()