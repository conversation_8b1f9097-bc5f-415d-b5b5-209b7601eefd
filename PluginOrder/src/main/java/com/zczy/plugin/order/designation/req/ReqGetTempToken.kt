package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：获取临时token(app端
 * 时间：2025/1/13 9:42
 * @author：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=65178644
 * 对接：沈振
 * */
class ReqGetTempToken(
    var tenantCode: String? = "ZCZY", //需要指定登录的租户code，没有默认中储租户
    var systemFlag: String? = "34", //需要指定跳转的系统来源端，    12.金融客户端  34.合同客户端 2.TMS-货主端
) : BaseNewRequest<BaseRsp<RspGetTempToken>>("/mms-app/mms/login/getTempToken") {

}

data class RspGetTempToken(
    var token: String? = null,//临时token
) : ResultData()