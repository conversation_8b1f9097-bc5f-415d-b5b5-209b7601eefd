package com.zczy.plugin.order.changecarrier.dialog

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.itemdecoration.CommItemGirdDecoration
import com.zczy.plugin.order.R

/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/11/11
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

class ChooseCarGirdView2<T>(context: Context, attrs: AttributeSet?, defStyle: Int) : RecyclerView(context, attrs, defStyle) {

    constructor(context: Context) : this(context, null, 0)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    private val showLines = 3
    private val spanCount = 3

    private val chooseCarAdapter = ChooseCarAdapter()

    private var selectData: T? = null

    // 原始数据
    private val originalData = mutableListOf<T>()

    // 是否显示有限的行数
    var limitLines = false

    var listener: RegisterChooseCarViewListener<T>? = null

    var flatMap: T.() -> String = { toString() }

    var flatShowLogo: T.() -> Boolean = { false }

    var noCheck: T.() -> Boolean = { false }
    var noCheckTxt: T.() -> String = { "" }
    private val onItemTouchListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            if (limitLines && position == showLines * spanCount - 1) { // 最后一个是 查看更多 占位的
                listener?.onClickMore()
            } else {
                val item = adapter.getItem(position) as? T
                item?.let {
                    if (it.noCheck()) {
                        return
                    }
                    selectData = it
                    adapter.notifyDataSetChanged()
                    listener?.onSelectCar(it)
                }
            }
        }
    }

    init {
        initAttrs(attrs)
        layoutManager = GridLayoutManager(getContext(), 3)
        setHasFixedSize(true)
        addItemDecoration(CommItemGirdDecoration(dp2px(10f), dp2px(15f)))
        isNestedScrollingEnabled = limitLines
        isFocusable = false
        chooseCarAdapter.setNewData(emptyList())
        adapter = chooseCarAdapter

        addOnItemTouchListener(onItemTouchListener)
    }

    private fun initAttrs(attrs: AttributeSet?) {
        if (attrs != null) {
            val a = context.obtainStyledAttributes(attrs, R.styleable.ChooseCarGirdView)
            limitLines = a.getBoolean(R.styleable.ChooseCarGirdView_limitLines, false)
            a.recycle()
        }
    }

    fun setNewData(data: List<T>) {
        this.originalData.clear()
        this.originalData.addAll(data)
        if (limitLines && originalData.size > spanCount * showLines) {
            val take = originalData.take(spanCount * showLines - 1).toMutableList()
            // 最后添加一个 数据充当 查看更多
            take.add(originalData[0])
            this.chooseCarAdapter.setNewData(take)
        } else {
            this.chooseCarAdapter.setNewData(originalData)
        }
    }


    fun addSelectCar(selectCar: T) {
        selectData = selectCar
        if (limitLines) {
            val indexOf = originalData.indexOf(selectCar)
            if (indexOf != -1) {
                if (indexOf > spanCount * showLines - 1) {
                    originalData.remove(selectCar)
                    originalData.add(0, selectCar)
                    setNewData(originalData.toList())
                } else {
                    chooseCarAdapter.notifyDataSetChanged()
                }
            }
        } else {
            chooseCarAdapter.notifyDataSetChanged()
        }
    }

    fun setSelectData(selectCar: T) {
        selectData = selectCar
    }

    private inner class ChooseCarAdapter : BaseQuickAdapter<T, BaseViewHolder>(R.layout.order_pick_choose_car_gird_view) {
        override fun convert(helper: BaseViewHolder, item: T) {

            val tvCar = helper.getView<TextView>(R.id.tv_car)

            if (limitLines && helper.layoutPosition == showLines * spanCount - 1) {
                // 最后一个是 查看更多 占位的
                helper
                    .setGone(R.id.tv_more, true)
                    .setGone(R.id.img_inreview, false)
                    .setGone(R.id.ivStatus, false)
                    .setGone(R.id.img_select, false)
                tvCar.isSelected = true
                tvCar.text = ""
            } else {
                // 非空 是车牌
                val s = item.flatMap()
                tvCar.text = if (!TextUtils.isEmpty(s) && s.length > 2) {
                    "${s.substring(0, 2)} ${s.substring(2)}"
                } else {
                    s
                }
                if (item.noCheck()) {
                    tvCar.isEnabled = false
                    helper
                        .setGone(R.id.img_inreview, false)
                        .setGone(R.id.tv_more, false)
                        .setGone(R.id.ivStatus, true)
                        .setImageResource(
                            R.id.ivStatus, when (item.noCheckTxt()) {
                                "1" -> {
                                    R.drawable.select_vehicle_state_1
                                }
                                "2" -> {
                                    R.drawable.select_vehicle_state_2
                                }
                                "3" -> {
                                    R.drawable.select_vehicle_state_3
                                }
                                else -> {
                                    R.color.ps_color_transparent
                                }
                            }
                        )
                        .setGone(R.id.img_select, false)
                } else {
                    val c = item == selectData
                    val imgSelect = helper.getView<ImageView>(R.id.img_select)

                    imgSelect.isSelected = c
                    helper
                        .setGone(R.id.img_inreview, item.flatShowLogo())
                        .setGone(R.id.tv_more, false)
                        .setGone(R.id.ivStatus, false)
                        .setGone(R.id.img_select, true)
                    tvCar.isSelected = c
                    tvCar.isEnabled = true
                }
            }

        }
    }

    interface RegisterChooseCarViewListener<T> {
        fun onSelectCar(num: T)
        fun onClickMore()
    }
}