package com.zczy.plugin.order.changecarrier.model

import android.text.TextUtils
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.CommServer
import com.zczy.comm.file.IFileServer
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck
import com.zczy.plugin.order.designation.req.RspAssignVehicleTransportCheck
import com.zczy.plugin.order.source.pick.model.request.ReqCheckDelistOrderDocumentExpire
import com.zczy.plugin.order.source.pick.model.request.ReqCheckUnderTwelveVehicleConsignor
import com.zczy.plugin.order.source.pick.model.request.RspCheckDelistOrderDocumentExpire
import java.io.File

/**
 * 功能描述:待卸货-变更车辆
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/5/31
 */
class OrderChangeCarrierShipmentsCarModel : BaseViewModel(), IFileServer.OnFileUploaderListener {

    fun save(orderId: String, car: RspVehicleData, time: String, pic: String, des: String, esignFlag: String) {
        val req3ChangeOne = Req3ChangeOne()
        req3ChangeOne.changeType = "2"
        req3ChangeOne.orderId = orderId
        req3ChangeOne.vehicleId = car.vehicleId
        req3ChangeOne.plateNumber = car.plateNumber
        req3ChangeOne.evidenceUrlStr = pic
        req3ChangeOne.actualTime = time
        req3ChangeOne.remarks = des
        req3ChangeOne.esignFlag = esignFlag
        execute(false, req3ChangeOne) { resultDataBaseRsp -> setValue("onSaveSuccess", resultDataBaseRsp) }
    }

    fun saveTms(sourceId: String, car: RspVehicleData, time: String, pic: String, des: String, esignFlag: String, consignorUserId: String) {
        val req3ChangeOne = Req3ChangeOne()
        req3ChangeOne.changeType = "2"
        req3ChangeOne.sourceId = sourceId
        req3ChangeOne.vehicleId = car.vehicleId
        req3ChangeOne.plateNumber = car.plateNumber
        req3ChangeOne.evidenceUrlStr = pic
        req3ChangeOne.actualTime = time
        req3ChangeOne.remarks = des
        req3ChangeOne.esignFlag = esignFlag
        req3ChangeOne.consignorUserId = consignorUserId
        execute(false, req3ChangeOne) { resultDataBaseRsp -> setValue("onSaveSuccess", resultDataBaseRsp) }
    }

    /**
     * 12吨以下货主摘单限制
     */
    fun checkUnderTwelveVehicleConsignor(orderId: String, consignorUserId: String?, car: RspVehicleData, time: String, pic: String, des: String) {
        val req = ReqCheckUnderTwelveVehicleConsignor()
        req.vehicleId = car.vehicleId
        req.consignorUserId = consignorUserId
        this.execute(true, req, object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(resultDataBaseRsp: BaseRsp<ResultData>) {
                if (resultDataBaseRsp.success()) {
                    queryDelistUserElectronicSignState(orderId)
                } else {
                    showDialogToast(resultDataBaseRsp.data?.resultMsg)
                }
            }
        })
    }

    /***
     * 查询车辆信息
     */
    fun queryChangePageInfo(orderId: String) {

        val login = CommServer.getUserServer().login
        this.execute(true, Req5QueryChangePageInfo(orderId = orderId, friendId = login.userId, nowPage = 1, pageSize = 10)) { pageListBaseRsp ->
            if (pageListBaseRsp.success()) {
                setValue("onCarSuccess", pageListBaseRsp.data)
            } else {
                showDialogToast(pageListBaseRsp.msg)
            }
        }
    }

    fun queryChangePageInfoTMS(sourceId: String, consignorUserId: String) {

        val login = CommServer.getUserServer().login
        this.execute(
            true,
            Req5QueryChangePageInfo(sourceId = sourceId, orderId = sourceId, consignorUserId = consignorUserId, friendId = login.userId, nowPage = 1, pageSize = 10)
        ) { pageListBaseRsp ->
            if (pageListBaseRsp.success()) {
                setValue("onCarSuccess", pageListBaseRsp.data)
            } else {
                showDialogToast(pageListBaseRsp.msg)
            }
        }
    }

    /***
     * 上传文件
     * @param file
     */
    fun upFile(file: List<String>) {

        for (p in file) {
            this.upFile(p)
        }
    }

    fun upFile(file: String) {

        val disposable = CommServer.getFileServer().update(File(file), this)
        this.putDisposable(disposable)
    }

    override fun onSuccess(tag: File, url: String) {

        setValue("onFileSuccess", tag, url)
    }

    override fun onFailure(tag: File, error: String) {
        showToast(error)
        setValue("onFileFailure", tag, error)
    }

    fun onChangeOverLoad(orderId: String) {
        val load = ReqChangeOverLoad()
        load.orderId = orderId
        execute(true, load) { t ->
            if (t.success()) {
                setValue("onChangeOverLoadSuccess", t.data)
            } else if (TextUtils.equals("2222", t.code)) {
                setValue("onChangeOverLoadError", t.data?.resultMsg)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun onChangeOverLoadTMS(sourceId: String, consignorUserId: String) {
        val load = ReqChangeOverLoad()
        load.sourceId = sourceId
        load.consignorUserId = consignorUserId
        load.orderId = sourceId
        execute(true, load) { t ->
            if (t.success()) {
                setValue("onChangeOverLoadSuccess", t.data)
            } else if (TextUtils.equals("2222", t.code)) {
                setValue("onChangeOverLoadError", t.data?.resultMsg)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /**
     * 准驾车型校验
     *
     */
    fun vertifyCarType(orderId: String?, vehicleId: String?, driverUserId: String?) {


        val req = VertifyCarTypeChangeBeanReq()
        req.orderId = orderId!!
        req.vehicleId = vehicleId!!
        req.driverUserId = driverUserId!!

        this.execute(true, req, object : IResult<BaseRsp<VertifyCarTypeChangeBeanResp>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(resultDataBaseRsp: BaseRsp<VertifyCarTypeChangeBeanResp>) {
                if (resultDataBaseRsp.success()) {
                    setValue("vertifyCarTypeSuccess", resultDataBaseRsp.data)
                } else {
                    showDialogToast(resultDataBaseRsp.msg)
                }
            }
        })
    }

    fun checkPlateNumberBeforeChange(orderId: String, vehicleId: String?, driverUserId: String?, plateNumber: String) {
        execute(
            true,
            ReqCheckPlateNumberBeforeChange(changeType = "2", plateNumber = plateNumber, orderId = orderId)
        ) { t ->
            if (t.success()) {
                vertifyCarType(orderId, vehicleId, driverUserId)
            } else if (TextUtils.equals("1111", t.code)) {
                var dialog = DialogBuilder()
                dialog.setMessage(t.msg)
                dialog.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    vertifyCarType(orderId, vehicleId, driverUserId)
                }
                showDialog(dialog)
            } else {
                showDialogToast(t.msg)
            }
        }

    }

    fun queryCarrierOrderCarpooling(req: ReqCheckDelistOrderDocumentExpire) {
        execute(
            req
        ) { rspCheckDelistOrderDocumentExpireBaseRsp: BaseRsp<RspCheckDelistOrderDocumentExpire> ->
            if (rspCheckDelistOrderDocumentExpireBaseRsp.success()) {
                setValue("queryCarrierOrderCarpoolingSuccess", rspCheckDelistOrderDocumentExpireBaseRsp.data)
            } else {
                showToast(rspCheckDelistOrderDocumentExpireBaseRsp.msg)
            }
        }
    }

    /**
     * 货主配置车辆排放类型
     * */
    fun ReqQueryDelistOrderConsignorrConfig(req: ReqQueryDelistOrderConsignorrConfig, next: (standard: String?) -> Unit = {}) {
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                next(t.data?.allowDieselCarWithSpecifiedStandardsForTransport)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun getVehicleEmissionStandard(allowType: String?, vehicle: RspVehicleData?) {
        execute(
            true,
            ReqGetVehicleEmissionStandard(allowType = allowType, vehicleId = vehicle?.vehicleId)
        ) { t ->
            setValue("onGetVehicleEmissionStandard", t.data, vehicle)
        }
    }

    /**
     * 注释：查询签署的电签类型
     * 时间：2024/5/6 11:36
     * 作者：王家辉
     * */
    fun queryDelistUserElectronicSignState(orderId: String) {
        execute(
            true,
            ReqQueryDelistUserElectronicSignState(orderId)
        ) { t ->
            if (t.success()) {
                setValue("onQueryDelistUserElectronicSignState", t.data)
            } else {
                showToast(t.msg)
            }
        }
    }

    /**
     * 车辆在途校验
     * */
    fun assignVehicleTransportCheck(req: ReqAssignVehicleTransportCheck, next: (data: BaseRsp<RspAssignVehicleTransportCheck>) -> Unit = {}, nextFail: () -> Unit = {}) {
        execute(false, req) {
            if (it.success()) {
                it?.let { it1 -> next(it1) }
            } else {
                nextFail()
            }
        }
    }

    /**
     * 查询证件过期 冻结 异常 缺失 风险 绑定等校验
     * */
    fun queryWhTmsUserAndVehicleState(req: ReqQueryWhTmsUserAndVehicleState) {
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                setValue("onQueryWhTmsUserAndVehicleState", t.data)
            } else {
                showToast(t.msg)
            }
        }
    }
}
