package com.zczy.plugin.order.shipments;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;

import androidx.annotation.Nullable;

import com.amap.api.maps.model.LatLng;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.route.RouteSearch;
import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.tencent.TencetMavigatorMapActivity;
import com.zczy.comm.widget.AppToolber;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.navigation.req.OrderCoordinate;
import com.zczy.plugin.order.navigation.req.ReqOrderCoordinate;
import com.zczy.plugin.order.shipments.model.ShipmentOkModel;

public class ShipmentSuccessActivity extends AbstractLifecycleActivity<ShipmentOkModel> implements View.OnClickListener {
    OrderCoordinate rGoodMaps;
    private String orderId;
    private String desp;

    public static void start(Context context) {
        Intent intent = new Intent(context, ShipmentSuccessActivity.class);
        context.startActivity(intent);
    }

    @LiveDataMatch
    public void queryOrderCoordinateSuccessV1(OrderCoordinate data) {
        lookRouteSearch(data);
    }

    public void lookRouteSearch(OrderCoordinate data) {
        String despatchProCityDisPlace = data.getDespatchProCityDisPlace();
        String deliverProCityDisPlace = data.getDeliverProCityDisPlace();
        String despatchCoordinateY = data.getDespatchCoordinateY();
        String despatchCoordinateX = data.getDespatchCoordinateX();
        String deliverCoordinateY = data.getDeliverCoordinateY();
        String deliverCoordinateX = data.getDeliverCoordinateX();

        if (data == null) {
            return;
        }
        if (TextUtils.isEmpty(despatchCoordinateY) ||
                TextUtils.isEmpty(despatchCoordinateX) ||
                TextUtils.isEmpty(deliverCoordinateY) ||
                TextUtils.isEmpty(deliverCoordinateX)) {
            return;
        }
        rGoodMaps = data;

    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipment_success);
        // 正确位置获取 Intent 数据
        orderId = getIntent().getStringExtra("orderId");
        desp = getIntent().getStringExtra("desp");
        if (!TextUtils.equals("1", desp)) {
            rGoodMaps = getIntent().getParcelableExtra("route");
        }
        initView();
    }

    private void initView() {
        AppToolber appToolber = findViewById(R.id.appToolber);
        appToolber.setLeftOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });


        Button btnNavigateUnload = findViewById(R.id.btn_navigate_unload);
        btnNavigateUnload.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                try {
                    // 起始地
                    LatLng p1 = new LatLng(Double.valueOf(rGoodMaps.getDeliverCoordinateY()), Double.valueOf(rGoodMaps.getDeliverCoordinateX()));
                    // 目的地
                    LatLng p2 = new LatLng(Double.valueOf(rGoodMaps.getDespatchCoordinateY()), Double.valueOf(rGoodMaps.getDespatchCoordinateX()));

                    TencetMavigatorMapActivity.start(ShipmentSuccessActivity.this, p1.latitude, p1.longitude, rGoodMaps.getDeliverProCityDisPlace(), p2.latitude, p2.latitude, rGoodMaps.getDespatchProCityDisPlace());

                } catch (Exception e) {
                    e.printStackTrace();
                }
                finish();
            }
        });
        if (TextUtils.equals("1", desp)) {
            btnNavigateUnload.setVisibility(View.GONE);
            appToolber.setTitle("卸货成功");
        }
    }

    @Override
    public void onClick(View view) {

    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }
}