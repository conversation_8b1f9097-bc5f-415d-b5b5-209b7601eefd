package com.zczy.plugin.order.changecarrier.adapter

import android.text.TextUtils
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.OrderChangeData
import com.zczy.plugin.order.changecarrier.bean.formatEndAddress
import com.zczy.plugin.order.changecarrier.bean.formatExamineState
import com.zczy.plugin.order.changecarrier.bean.formatStartAddress

class OrderChangeCarrierMainOrderChangeAdapter :
    BaseQuickAdapter<OrderChangeData, BaseViewHolder>(R.layout.order_change_carrier_main_order_change_item) {

    override fun convert(helper: BaseViewHolder, item: OrderChangeData) {
        helper
            // 运单号 tv_order_num
            .setText(R.id.tv_order_num, item.orderId ?: "")
            // 待审核状态 tv_order_state
            .setText(R.id.tv_order_state, item.formatExamineState())
            // 起点地址 tv_order_start
            .setText(R.id.tv_order_start, item.formatStartAddress())
            // 终点地址 tv_order_end
            .setText(R.id.tv_order_end, item.formatEndAddress())
            // 货物名称 yv_order_good_name_value
            .setText(R.id.yv_order_good_name_value, item.cargoName)
            // 承运人 yv_order_owner_name_value
            .setText(R.id.yv_order_owner_name_value, item.carrierName)
            // 出货时间 yv_order_time_value
            .setText(R.id.yv_order_time_value, item.despatchStart)
            .setVisible(R.id.btn_cancel_change, TextUtils.equals("1", item.isCancelChange))
            .setGone(R.id.iv_lingdan, TextUtils.equals("1", item.isLessThanOrder))
            .addOnClickListener(R.id.btn_cancel_change)
        // 我要变更按钮 btn_change
        val btnChange = helper.getView<TextView>(R.id.btn_change)

        //1，可变更。2，待审核。3，待接受
        when (item.changeState) {
            "1" -> {
                btnChange.isEnabled = true
                helper.addOnClickListener(R.id.btn_change)
            }
            "2", "3" -> {
                btnChange.isEnabled = false
            }
            else -> {
                btnChange.isEnabled = false
            }
        }
    }
}
