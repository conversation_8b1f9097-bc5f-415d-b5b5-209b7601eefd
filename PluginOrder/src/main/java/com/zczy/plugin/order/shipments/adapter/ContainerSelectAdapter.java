package com.zczy.plugin.order.shipments.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.zczy.comm.http.entity.PageList;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.shipments.entity.EContainer;
import com.zczy.plugin.order.shipments.view.SelectContainerDialog;

import java.util.ArrayList;
import java.util.List;

/*=============================================================================================
 * 功能描述:根据订单号查询集装箱号-适配器
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2022/2/28
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
public class ContainerSelectAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private PageList<EContainer> mList;
    private Context mContext;
    private LayoutInflater mInflater;
    private SparseBooleanArray mSelectedPositions = new SparseBooleanArray();
    private String weight; //摘单箱数
    private String cargoCategory;
    private SelectContainerDialog.SelectContainerDialogListener listener;


    public ContainerSelectAdapter(Context context, SelectContainerDialog.SelectContainerDialogListener listener) {
        this.mContext = context;
        this.mInflater = LayoutInflater.from(mContext);
        mList = new PageList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        ContactHolder holder = new ContactHolder(mInflater.inflate(R.layout.container_select_recyclerview_item, viewGroup, false));
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int i) {

    }

    //设置给定位置条目的选择状态
    private void setItemChecked(int position, boolean isChecked) {
        mSelectedPositions.put(position, isChecked);
    }

    //根据位置判断条目是否选中
    private boolean isItemChecked(int position) {
        return mSelectedPositions.get(position);
    }

    //获得选中条目的结果
    public ArrayList<EContainer> getSelectedItem() {
        ArrayList<EContainer> selectList = new ArrayList<>();
        for (int i = 0; i < mList.getRootArray().size(); i++) {
            if (isItemChecked(i)) {
                selectList.add(mList.getRootArray().get(i));
            }
        }
        return selectList;
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position, @NonNull List<Object> payloads) {
        final ContactHolder contact = (ContactHolder) holder;
        if (payloads.isEmpty()) {
            String unit = TextUtils.equals("1", cargoCategory) ? "吨" : TextUtils.equals("2", cargoCategory) ? "方" : "";
            contact.tv_container_name.setText(mList.getRootArray().get(position).containerNo);
            contact.tv_container_weight.setText(mList.getRootArray().get(position).containerUnitWeight + unit);
        }
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isItemChecked(position)) {
                    setItemChecked(position, false);
                    contact.rl_item.setBackgroundResource(R.drawable.container_selector_normal);
                } else {
                    if (getSelectedItem().size() >= Integer.parseInt(weight)) {
                        listener.selectError();
                        return;
                    }
                    setItemChecked(position, true);
                    contact.rl_item.setBackgroundResource(R.drawable.container_selector_select);
                }

            }
        });
    }

    @Override
    public int getItemCount() {
        return mList.getRootArray().size();
    }

    public void setDataSource(PageList<EContainer> list, String weight, String cargoCategory) {
        this.mList = list;
        this.weight = weight;
        this.cargoCategory = cargoCategory;
    }

    public class ContactHolder extends RecyclerView.ViewHolder {
        public RelativeLayout rl_item;
        public TextView tv_container_name;
        public TextView tv_container_weight;

        public ContactHolder(View itemView) {
            super(itemView);
            rl_item = itemView.findViewById(R.id.rl_item);
            tv_container_name = (TextView) itemView.findViewById(R.id.tv_container_name);
            tv_container_weight = (TextView) itemView.findViewById(R.id.tv_container_weight);
        }
    }
}
