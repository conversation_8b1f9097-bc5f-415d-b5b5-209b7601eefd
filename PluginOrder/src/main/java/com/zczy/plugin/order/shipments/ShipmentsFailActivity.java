//package com.zczy.plugin.order.shipments;
//
//import android.content.Context;
//import android.content.Intent;
//import android.graphics.Color;
//import android.os.Bundle;
//import androidx.annotation.Nullable;
//import androidx.core.content.ContextCompat;
//import android.text.Spannable;
//import android.text.SpannableString;
//import android.text.SpannableStringBuilder;
//import android.text.TextUtils;
//import android.text.style.ForegroundColorSpan;
//import android.view.View;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import com.sfh.lib.ui.AbstractLifecycleActivity;
//import com.zczy.comm.Const;
//import com.zczy.comm.ui.UtilStatus;
//import com.zczy.comm.utils.PhoneUtil;
//import com.zczy.plugin.order.R;
//
///**
// * 功能描述:确认发货失败、预付款申请提交失败
// *
// * <AUTHOR> 孙飞虎
// * @company 中储智运科技股份有限公司
// * @copyright （版权）中储智运科技股份有限公司所有
// * @date 2018/12/20
// */
//public class ShipmentsFailActivity extends AbstractLifecycleActivity implements View.OnClickListener {
//
//
//    /***
//     *
//     * @param context
//     * @param type 0  普通/预付，1平台预付
//     */
//    public static void start(Context context, String type) {
//
//        Intent intent = new Intent (context, ShipmentsFailActivity.class);
//        intent.putExtra ("type", type);
//        context.startActivity (intent);
//    }
//
//    private ImageView ivSuccessImage;
//
//    /**
//     * 恭喜，发货成功！
//     */
//    private TextView tvSuccessTitle;
//
//    /**
//     * 联系在线客服
//     */
//    private TextView tvServiceLine;
//
//    /**
//     * 查看运单
//     */
//    private TextView tvFailOk;
//
//    private TextView tvServicePhone;
//
//    @Override
//    protected void onCreate(@Nullable Bundle savedInstanceState) {
//
//        super.onCreate (savedInstanceState);
//        setContentView (R.layout.order_shipments_fail_activity);
//        UtilStatus.initStatus (this, ContextCompat.getColor (this,R.color.comm_title_bg));
//        initView ();
//    }
//
//    private void initView() {
//
//        ivSuccessImage = findViewById (R.id.iv_success_image);
//        tvSuccessTitle = findViewById (R.id.tv_success_title);
//        tvServiceLine = findViewById (R.id.tv_service_line);
//        tvServiceLine.setOnClickListener (this);
//        tvFailOk = findViewById (R.id.tv_fail_ok);
//        tvFailOk.setOnClickListener (this);
//        tvServicePhone = findViewById (R.id.tv_service_phone);
//        tvServicePhone.setOnClickListener (this);
//
//        //0  普通/预付，1平台预付
//        String type = getIntent ().getStringExtra ("type");
//        if (TextUtils.equals ("0", type)) {
//            ivSuccessImage.setImageResource (R.drawable.comm_recycler_empty_2_nothing_info);
//            tvSuccessTitle.setText ("发货失败");
//        } else {
//
//            ivSuccessImage.setImageResource (R.drawable.base_list_empty_ic_def);
//            tvSuccessTitle.setText ("预付款申请提交失败");
//        }
//        if (tvServicePhone != null) {
//            SpannableStringBuilder spannable = new SpannableStringBuilder ("客服热线");
//            //设置文字的前景色，2、4分别表示可以点击文字的起始和结束位置。
//            SpannableString phoneSpan = new SpannableString (Const.PHONE_SERVER_400);
//            phoneSpan.setSpan (new ForegroundColorSpan (Color.parseColor ("#3c75ed")), 0, Const.PHONE_SERVER_400.length (), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
//            spannable.append (phoneSpan);
//            tvServicePhone.setText (spannable);
//        }
//    }
//
//    @Override
//    public void onClick(View v) {
//
//        if (v.getId () == R.id.tv_service_line){
//            //TODO 在线客服
//            finish ();
//        }else if (v.getId () == R.id.tv_fail_ok){
//            finish ();
//        }else if (v.getId () == R.id.tv_service_phone){
//            // 客服电话
//            PhoneUtil.callPhone(getApplicationContext(),Const.PHONE_SERVER_400);
//        }
//    }
//}
