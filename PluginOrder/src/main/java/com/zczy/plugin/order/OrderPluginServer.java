package com.zczy.plugin.order;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.alibaba.fastjson.JSONObject;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.event.RxBusEventManager;
import com.zczy.certificate.vehiclemanage.carowner.CarOwnerVehicleManagementActivity;
import com.zczy.certificate.vehiclemanage.carrier.CarrierVehicleManagementActivity;
import com.zczy.certificate.vehiclemanage.enterprise.EnterPriseVehicleManagementActivityV1;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.pluginserver.AOrderServer;
import com.zczy.comm.utils.JsonUtil;
import com.zczy.lib_zstatistics.sdk.ZStatistics;
import com.zczy.plugin.order.bill.BillNormalActivity;
import com.zczy.plugin.order.bill.BillScanActivity;
import com.zczy.plugin.order.bill.BillSuccessActivity;
import com.zczy.plugin.order.bill.BillSystemActivity;
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierDetailActivity;
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierMainActivity;
import com.zczy.plugin.order.coupon.OrderCouponMainActivity;
import com.zczy.plugin.order.emergency.EmergencyLogisticsListActivity;
import com.zczy.plugin.order.emergency.EmergencyLogisticsMessageActivity;
import com.zczy.plugin.order.express.OrderExpressMainActivity;
import com.zczy.plugin.order.navigation.ActionNavigation;
import com.zczy.plugin.order.navigation.req.OrderCoordinate;
import com.zczy.plugin.order.shipments.ActionDialog;
import com.zczy.plugin.order.shipments.ShipmentsActivity;
import com.zczy.plugin.order.shipments.ShipmentsCouponActivity;
import com.zczy.plugin.order.shipments.ShipmentsSuccessActivity;
import com.zczy.plugin.order.shipments.entity.EActivityInfo;
import com.zczy.plugin.order.shipments.entity.EAdvanceInfo;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.shipments.entity.RxEventShipmentsBillSuccess;
import com.zczy.plugin.order.shipments.entity.ShipmentsEGoodInfo;
import com.zczy.plugin.order.source.detail.OrderSourceBatchDetailActivity;
import com.zczy.plugin.order.source.detail.OrderSourceDetailActivity;
import com.zczy.plugin.order.source.list.dialog.OrderSourceDialog;
import com.zczy.plugin.order.source.onehz.OrderSourceOneHZActivity;
import com.zczy.plugin.order.source.pick.dialog.FactoryInformDialog;
import com.zczy.plugin.order.source.pick.entity.RxEventPickOffer;
import com.zczy.plugin.order.source.pick.model.request.EntryIndustryNotice;
import com.zczy.plugin.order.source.pick.model.request.PolicyActivity;
import com.zczy.plugin.order.source.returnsource.OrderReturnSourceActivity;
import com.zczy.plugin.order.source.search.ui.OrderSourceSearchActivity;
import com.zczy.plugin.order.stevedore.StevedoreAddEditActivity;
import com.zczy.plugin.order.stevedore.StevedoreListActivity;
import com.zczy.plugin.order.supervise.ActionType;
import com.zczy.plugin.order.supervise.SuperviseManager;
import com.zczy.plugin.order.violate.OrderViolateMainActivity;
import com.zczy.plugin.order.waybill.WaybillCommDetailActivity;
import com.zczy.plugin.order.waybill.WaybillSearchActivity;
import com.zczy.plugin.order.waybill.WaybillSettlementActivity;
import com.zczy.plugin.order.waybill.cyr.ScanCodeConfirmClaimActivity;
import com.zczy.plugin.order.waybill.entity.EWaybill;
import com.zczy.version.sdk.ZVersionManager;

/**
 * 功能描述:
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/10/23
 */
public class OrderPluginServer extends AOrderServer {

    @Override
    public void openCarPoolDetails(Context context, String orderId, boolean inScan,String delistMethod) {
        OrderSourceDetailActivity.start(context, orderId, true, inScan,delistMethod);
    }

    @Override
    public void openWaybillDetails(Context context, boolean batch, String orderId, boolean inScan, String presetPlanType,String dispatchOrder,String delistMethod) {
        if (batch) {
            OrderSourceBatchDetailActivity.start(context, orderId, inScan, "", presetPlanType,dispatchOrder,delistMethod);
        } else {
            OrderSourceDetailActivity.start(context, orderId, inScan,dispatchOrder,delistMethod);
        }
    }

    @Override
    public void openWaybillDetails(Context context, boolean batch, String orderId, boolean inScan, String presetPlanType, String dispatchOrder, boolean isTms,String delistMethod) {
        if (batch) {
            OrderSourceBatchDetailActivity.start(context, orderId, inScan, "", presetPlanType,dispatchOrder,delistMethod);
        } else {
            OrderSourceDetailActivity.start(context, orderId, inScan,dispatchOrder,isTms,delistMethod);
        }
    }

    @Override
    public void openOrderDetails(Context context, String orderId) {

        WaybillCommDetailActivity.start(context, orderId);
    }

    @Override
    public void openOrderMainSearchRouteActivity(Fragment fragment) {
        OrderSourceSearchActivity.start(fragment);
    }

    @Override
    public void openOrderMainSearchRouteActivity(Context fragment) {
        OrderSourceSearchActivity.start(fragment);
    }

    @Override
    public void openCouponActivity(Fragment fragment) {
        OrderCouponMainActivity.start(fragment);
    }

    @Override
    public void openCouponActivity(Context context, String from) {
        OrderCouponMainActivity.start(context, from);
    }

    @Override
    public void openExpressMainActivity(Fragment fragment) {
        OrderExpressMainActivity.start(fragment);
    }

    @Override
    public void openExpressMainActivity(Activity activity) {
        OrderExpressMainActivity.start(activity);
    }

    @Override
    public void openOrderViolateMainActivity(Activity context) {

        OrderViolateMainActivity.start(context);
    }

    @Override
    public void openOrderChangeCarrier(Activity context) {

        OrderChangeCarrierMainActivity.start(context, 0);
    }

    @Override
    public void openOrderChangeCarrier(Activity context, int tab) {

        OrderChangeCarrierMainActivity.start(context, tab);
    }

    @Override
    public void openOrderChangeCarrierDetail(Activity context, String changeId) {
        OrderChangeCarrierDetailActivity.start(context, changeId);
    }

    @Override
    public void openSteveDoreActivity(Activity activity) {

        StevedoreListActivity.startContentUI(activity);
    }

    @Override
    public void openAddSteveDoreActivity(Activity activity, String detailId) {
        StevedoreAddEditActivity.startContentUI(activity, 2, detailId);
    }

    @Override
    public void openOrderReturnSourceActivity(Context context, String node) {
        OrderReturnSourceActivity.start(context, node);
    }

    @Override
    public void openEmergencyLogisticsListActivity(Context context) {
        EmergencyLogisticsListActivity.start(context);
    }

    @Override
    public void openEmergencyLogisticsMessageActivity(Context context, String tbcId) {
        EmergencyLogisticsMessageActivity.start(context, tbcId);
    }

    @Override
    public void openOrderSourceDialog(Fragment fragment, Runnable run) {
        new OrderSourceDialog()
                .setListener(() -> {
                    run.run();
                    return null;
                })
                .show(fragment);
    }

    @Override
    public void openOrderSourceDialog(FragmentActivity fragment, Runnable run) {
        new OrderSourceDialog()
                .setListener(() -> {
                    run.run();
                    return null;
                })
                .show(fragment);
    }

    @Override
    public void openOrderSourceOneHzActivity(@Nullable Context context,
                                             @Nullable String hzName,
                                             @Nullable String hzId, boolean inScan) {
        OrderSourceOneHZActivity.start(context, hzName, hzId, null, inScan);
    }

    @Override
    public void openOrderSourceFranchiseesActivity(Context context, String hzName, String hzId, String franchiserUserId, String franchiserChildId, boolean inScan) {
        OrderSourceOneHZActivity.start(context, hzName, hzId, franchiserUserId, franchiserChildId, null, inScan);
    }

    @Override
    public void openWaybillSettlementActivity(Context context) {
        WaybillSettlementActivity.start(context);
    }

    @Override
    public void openWaybillSearch(Context context, String search) {
        WaybillSearchActivity.start(context, search);
    }

    @Override
    public void onShipNactiveUI(Context context, String json) {
//        EWaybill waybill = JsonUtil.toJsonObject(json, EWaybill.class);
//        ShipmentsActivity.startNactive(context, waybill);
    }

    public static class ShipmentData {
        public ShipmentsEGoodInfo shipmentsEGoodInfo;
        public EAdvanceInfo advanceInfo;
        public String payTerrace;
        public String advanceWay;
        public String haveOpenSdk;
        //是否需要异步上传定位 1:是，其余否
        public String synUploadLocationFlag;
        public String scene;
    }

    @Override
    public void onShipmentSuccess(Context context, String json) {

        try {
            ShipmentData shipmentData = JsonUtil.toJsonObject(json, ShipmentData.class);
            ShipmentsEGoodInfo shipmentsEGoodInfo = shipmentData.shipmentsEGoodInfo;
            EAdvanceInfo mAdvanceInfo = shipmentData.advanceInfo;
            String payTerrace = shipmentData.payTerrace;
            String advanceWay = shipmentData.advanceWay;
            String haveOpenSdk = shipmentData.haveOpenSdk;
            String scene = shipmentData.scene;

            if (TextUtils.equals("1", shipmentData.synUploadLocationFlag)) {
                new BillShipmentsServer(AppCacheManager.getApplication())
                        .setOrderType(BillShipmentsServer.TYPE_SHIPMENTS)
                        .setDetailId(shipmentsEGoodInfo.getDetailId())
                        .setCoordinateFlag(shipmentsEGoodInfo.getDeliverPicConfig())
                        .start();
                //ZCZY-4491 司机确认收发货时获取APP定位优化(在【确认收货】【确认发货】时，分别执行两次定位信息获取。)
                new BillShipmentsServer(AppCacheManager.getApplication())
                        .setOrderType(BillShipmentsServer.TYPE_SHIPMENTS)
                        .setDetailId(shipmentsEGoodInfo.getDetailId())
                        .setCoordinateFlag(shipmentsEGoodInfo.getDeliverPicConfig())
                        .start();
            }

            //发货成功通知
            RxBusEventManager.postEvent(RxEventShipmentsBillSuccess.ShipmentsSuccess());

            ShipmentsSuccessActivity.start(context, payTerrace, shipmentsEGoodInfo.getOrderId(), advanceWay, shipmentsEGoodInfo, mAdvanceInfo, scene);

            if (TextUtils.equals("1", haveOpenSdk) && shipmentsEGoodInfo.isHaveOpenSdk()) {
                //省货物平台系统
                ESDKInfoObj sdkInfoObj = shipmentsEGoodInfo.getSdkInfoObj();
                SuperviseManager.getInstance().postOrderInfo(ActionType.Start, shipmentsEGoodInfo.getOrderId(), sdkInfoObj);
            }

        } catch (Exception e) {

        }

    }

    public static class BillData {
        public String coordinateFlag;
        public String detailId;
        public String orderId;
        public String haveOpenSdk; //是否需要异步上传定位 1:是，其余否
        public String synUploadLocationFlag;
        public ESDKInfoObj sdkInfoObj;
    }

    @Override
    public void onReceiveSuccsss(Context context, String json) {

        try {
            BillData billData = JsonUtil.toJsonObject(json, BillData.class);

            if (TextUtils.equals("1", billData.synUploadLocationFlag)) {
                // 收货定位
                new BillShipmentsServer(AppCacheManager.getApplication())
                        .setOrderType(BillShipmentsServer.TYPE_BILL)
                        .setDetailId(billData.detailId)
                        .setCoordinateFlag(billData.coordinateFlag)
                        .start();
                //ZCZY-4491 司机确认收发货时获取APP定位优化(在【确认收货】【确认发货】时，分别执行两次定位信息获取。)
                new BillShipmentsServer(AppCacheManager.getApplication())
                        .setOrderType(BillShipmentsServer.TYPE_BILL)
                        .setDetailId(billData.detailId)
                        .setCoordinateFlag(billData.coordinateFlag)
                        .start();
            }

            // 发送 消息通知 收货确认成功
            RxBusEventManager.postEvent(RxEventShipmentsBillSuccess.billSuccess());

            //省货物平台系统
            if (TextUtils.equals("1", billData.haveOpenSdk)) {
                SuperviseManager.getInstance().postOrderInfo(ActionType.Stop, billData.orderId, billData.sdkInfoObj);
            }
        } catch (Exception e) {

        }

    }


    @Override
    public void onBillShipmentsServer(Context context, String json) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String detailId = jsonObject.getString("detailId");
            String reqNo = jsonObject.getString("reqNo");
            String type = jsonObject.getString("type");

            if (TextUtils.isEmpty(reqNo)) {
                new BillShipmentsServer(AppCacheManager.getApplication(), type, detailId).start();
            } else {
                new BillShipmentsServer(AppCacheManager.getApplication(), type, detailId, reqNo).start();
            }
        } catch (Exception e) {

        }
    }

    @Override
    public void openShipmentsCouponActivity(Activity activity, String orderId, String pbCarrierMoney, String serverMoney, String userCouponIds,      String forcePayFlag , int requestCode) {
        ShipmentsCouponActivity.startContentUI(activity, orderId, pbCarrierMoney, serverMoney, userCouponIds,forcePayFlag, requestCode);
    }

    @Override
    public void openScanCodeConfirmClaimActivity(Context context, boolean batch, String id) {
        ScanCodeConfirmClaimActivity.start(context, id);
    }

    @Override
    public void startBillShipmentsServer(String orderType, String detailId, Boolean isOffLine, Boolean fixBug) {
        if (TextUtils.equals(orderType, "1")) {
            //发送发货成功通知
            RxBusEventManager.postEvent(new RxEventShipmentsBillSuccess(isOffLine ? 2 : 0));
        } else if (TextUtils.equals(orderType, "2")) {
            // 发送 消息通知 收货确认成功
            RxBusEventManager.postEvent(new RxEventShipmentsBillSuccess(isOffLine ? 1 : 3));
        }
        //开启定位服务
        new BillShipmentsServer(AppCacheManager.getApplication(), orderType, detailId).start();
        if (fixBug) {
            new BillShipmentsServer(AppCacheManager.getApplication(), orderType, detailId).start();
        }
    }

    /**
     * 注释：发送摘单全局事件
     * 时间：2023/10/31 0031 11:29
     * 作者：郭翰林
     *
     * @param type
     */
    @Override
    public void postOrderPickEvent(int type) {
        RxBusEventManager.postEvent(new RxEventPickOffer(type));
    }

    /**
     * 注释：显示活动弹窗
     * 时间：2023/10/31 0031 14:29
     * 作者：郭翰林
     *
     * @param activityShowFlag
     * @param activityInfo
     */
    @Override
    public void showPolicyActivity(Activity activity, String activityShowFlag, String activityInfo) {
        if (TextUtils.equals("1", activityShowFlag)) {
            PolicyActivity info = JsonUtil.toJsonObject(activityInfo, PolicyActivity.class);
            if (info != null) {
                EActivityInfo eActivityInfo = new EActivityInfo();
                eActivityInfo.setActivityId(info.getActivityId());
                eActivityInfo.setActivityName(info.getAdvertName());
                eActivityInfo.setAdvertUrl(info.getAdvertUrl());
                if (activity instanceof FragmentActivity) {
                    ActionDialog.showDialogUI((FragmentActivity) activity, eActivityInfo);
                }
            }
        }
    }

    @Override
    public void showEActivityInfo(Activity activity, String activityShowFlag, String activityInfo) {
        if (TextUtils.equals("1", activityShowFlag)) {
            EActivityInfo info = JsonUtil.toJsonObject(activityInfo, EActivityInfo.class);
            if (info != null && activity instanceof FragmentActivity) {
                ActionDialog.showDialogUI((FragmentActivity) activity, info);
            }
        }
    }

    /**
     * 注释：开始导航
     * 时间：2023/10/31 0031 15:29
     * 作者：郭翰林
     *
     * @param orderId
     * @param eWaybill
     */
    @Override
    public void startNavigation(Activity activity, String orderId, String eWaybill, String orderCoordinate) {
        OrderCoordinate coordinate = JsonUtil.toJsonObject(orderCoordinate, OrderCoordinate.class);
        if (!TextUtils.isEmpty(eWaybill)) {
            EWaybill waybill = JsonUtil.toJsonObject(eWaybill, EWaybill.class);
            if (waybill != null) {
                new ActionNavigation()
                        .setOrderId(orderId)
                        .setEWaybillData(waybill)
                        .setFlag("1")
                        .lookRouteSearch(coordinate, () -> {
                            activity.finish();
                        });
            }
        } else {
            new ActionNavigation()
                    .setOrderId(orderId)
                    .setFlag("1")
                    .lookRouteSearch(coordinate, () -> {
                        activity.finish();
                    });
        }
        ZStatistics.onViewClick(activity, "pick_result&navigation");
    }

    /**
     * 注释：跳转车辆管理页面
     * 时间：2023/11/3 0003 10:55
     * 作者：郭翰林
     */
    @Override
    public void gotoVehicleManagement(Activity activity) {
        ELogin eLogin = CommServer.getUserServer().getLogin();
        if (eLogin != null) {
            if (eLogin.getRelation().isCarrier()) {
                CarrierVehicleManagementActivity.start(activity);
            } else if (eLogin.getRelation().isBoss()) {
                CarOwnerVehicleManagementActivity.start(activity);
            } else if (eLogin.getRelation().isCys()) {
                EnterPriseVehicleManagementActivityV1.start(activity);
            }
        }
    }

    /**
     * 注释：显示入场须知弹窗
     * 时间：2023/11/15 0015 11:37
     * 作者：郭翰林
     *
     * @param json
     */
    @Override
    public void showFactoryInformDialog(Activity activity, String json, View.OnClickListener listener) {
        EntryIndustryNotice entryIndustryNotice = JsonUtil.toJsonObject(json, EntryIndustryNotice.class);
        new FactoryInformDialog(activity, listener, entryIndustryNotice).show();
    }

    /**
     * 打开原生卸货界面
     *
     * @param context
     * @param orderId
     * @param detailId
     * @param advanceState
     * @param risk
     */
    @Override
    public void openBillNormalActivity(Context context, String orderId, String detailId, String advanceState, String risk) {

        BillNormalActivity.startContentUI(context,
                orderId,
                detailId,
                advanceState,
                risk, ""
        );
    }

    /***
     * 打开原生扫描卸货界面
     * @param context
     * @param orderId
     * @param detailId
     * @param advanceState
     * @param qRData
     */
    @Override
    public void openBillScanActivity(Context context, String orderId, String detailId, String advanceState, String qRData) {

        BillScanActivity.startUI(context,
                orderId,
                detailId,
                advanceState,
                qRData
        );
    }

    /***
     * 打开原生系统对接卸货界面
     * @param context
     * @param orderId
     * @param detailId
     * @param advanceState
     */
    @Override
    public void openBillSystemActivity(Context context, String orderId, String detailId, String advanceState) {

        BillSystemActivity.startUI(context,
                orderId,
                detailId,
                advanceState, ""
        );
    }

    /***
     * 打开原生卸货成功页
     * @param context
     * @param json
     */
    @Override
    public void openBillSuccessActivity(Context context, String json) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        String orderId = jsonObject.getString("orderId");
        String detailId = jsonObject.getString("detailId");
        boolean isLoadPay = jsonObject.getBooleanValue("isLoadPay");
        String orderReceiptFlag = jsonObject.getString("orderReceiptFlag");
        String orderReceiptMoney = jsonObject.getString("orderReceiptMoney");
        String mailingAddress = jsonObject.getString("mailingAddress");
        String offlineSendExpress = jsonObject.getString("offlineSendExpress");
        String expressEntry = jsonObject.getString("expressEntry");
        String vehicleLoad = jsonObject.getString("vehicleLoad");
        String orderReceiptChangeFlag = jsonObject.getString("orderReceiptChangeFlag");

        BillSuccessActivity.startContentUI(context,
                orderId,
                detailId,
                isLoadPay,
                orderReceiptFlag,
                orderReceiptMoney,
                mailingAddress,
                offlineSendExpress,
                expressEntry,
                vehicleLoad,
                orderReceiptChangeFlag
        );
    }

    /***
     * 收发货定位上报与收发货异常上报
     * @param context
     * @param json
     */
    @Override
    public void onBillShipmentsServerNew(Context context, String json) {

        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String detailId = jsonObject.getString("detailId");
            String reqNo = jsonObject.getString("reqNo");
            String type = jsonObject.getString("type");
            String coordinateFlag = jsonObject.getString("coordinateFlag");
            //允许次数
            int count = jsonObject.getIntValue("count");

            for (int i = 0; i < count; i++) {
                new BillShipmentsServer(AppCacheManager.getApplication())
                        .setDetailId(detailId)
                        .setReqNo(reqNo)
                        .setOrderType(type)
                        .setCoordinateFlag(coordinateFlag)
                        .start();
            }

        } catch (Exception e) {

        }
    }

    /***
     * 收发货成功通知
     * @param type
     */
    @Override
    public void onEventShipmentsBillSuccess(int type) {
        //发货成功通知
        RxBusEventManager.postEvent(new RxEventShipmentsBillSuccess(type));
    }


    /***
     * 打开原生发货成功页
     * @param context
     * @param json
     */
    @Override
    public void openShipmentsSuccessActivity(Context context, String json) {

        ShipmentData shipmentData = JsonUtil.toJsonObject(json, ShipmentData.class);
        ShipmentsSuccessActivity.start(context,
                shipmentData.payTerrace,
                shipmentData.shipmentsEGoodInfo.getOrderId(),
                shipmentData.advanceWay,
                shipmentData.shipmentsEGoodInfo,
                shipmentData.advanceInfo,
                shipmentData.scene);
    }


    /***
     * 省物平台
     */
    public static class SuperviseJson {
        public String orderId;
        public String haveOpenSdkType;//0 初始化 1 发货 2 收货 3运单列表中所有运单标记为暂停状态 4执行重启定位操作
        public String haveOpenSdk;
        public ESDKInfoObj sdkInfoObj;
    }

    /***
     * 省物平台上报
     * @param context
     * @param json
     */
    @Override
    public void onPostESDKInfoObj(Context context, String json) {
        try {
            SuperviseJson data = JsonUtil.toJsonObject(json, SuperviseJson.class);

            if (TextUtils.equals("1", data.haveOpenSdk) && data.sdkInfoObj != null) {
                //省货物平台系统
                ActionType type = TextUtils.equals("0", data.haveOpenSdkType) ? ActionType.Init :
                        TextUtils.equals("1", data.haveOpenSdkType) ? ActionType.Start :
                                TextUtils.equals("2", data.haveOpenSdkType) ? ActionType.Stop :
                                        TextUtils.equals("3", data.haveOpenSdkType) ? ActionType.Pause :
                                                TextUtils.equals("4", data.haveOpenSdkType) ? ActionType.ReStart : ActionType.Init;

                SuperviseManager.getInstance().postOrderInfo(type, data.orderId, data.sdkInfoObj);
            }
        } catch (Exception e) {

        }
    }

    @Override
    public void onOrderExpressMainActivity(Context context, int index) {
        OrderExpressMainActivity.start(context, index);
    }
}
