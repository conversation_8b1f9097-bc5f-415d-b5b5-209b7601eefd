package com.zczy.plugin.order.changecarrier.adapter

import android.graphics.Paint
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.req.OrderChangesDetailDto

class OrderChangeCarrierHistoryChildAdapter
    : BaseQuickAdapter<OrderChangesDetailDto, BaseViewHolder>(R.layout.order_change_carrier_history_child_item) {

    override fun convert(helper: BaseViewHolder, item: OrderChangesDetailDto) {

        helper
                .setText(R.id.tv_order_change_before_value, item.oldValue)
                .setText(R.id.tv_order_change_after_value, item.newValue)

                .setText(R.id.tv_order_change_before_name, item.changeTypeStr)
                .setText(R.id.tv_order_change_after_name, item.changeTypeStr)
        // 时间 tv_order_time
        helper.setText(R.id.tv_order_people_time, item.effectTimeStr)

        helper.getView<TextView>(R.id.tv_order_change_before_value)
                .paint.flags = Paint.STRIKE_THRU_TEXT_FLAG
    }
}
