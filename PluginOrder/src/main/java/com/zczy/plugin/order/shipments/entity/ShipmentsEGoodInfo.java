package com.zczy.plugin.order.shipments.entity;

import android.text.TextUtils;

import com.zczy.comm.http.entity.PageList;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:确认发货时查询货物信息
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/23
 */
public class ShipmentsEGoodInfo extends PageList<EGoodInfo> {

    String detailId;

    /*** 订单ID*/
    String orderId;

    /*** 承运方总价*/
    String pbCarrierMoney;

    /*** 承运方单价*/
    String pbCarrierUnitMoney;

    /*** 货物总量*/
    String weight;

    /*** 费用类型 0：包车价 1：单价*/
    String freightType;

    /*** 货物类别 1：重货,2 泡货*/
    String cargoCategory;

    /*** 是否高级承运人 0：不是 1：是*/
    String isSeniorCarrier;


    /*** 是否可以开启非指定预付款 0：不是 1：是*/
    String isNoSpecialAdvance;

    /*** 服务费*/
    String expectMoney;

    /****  是否含税: 0：不是 1：是*/
    String haveInvoice;

    /*** 是否弹框  1:不弹框  2：弹去绑卡  3：弹我要预付  4:预付打回，之前选择了预付的，且当前不符合条件*/
    String advanceFlagState;

    /*** 上传发货单配置项 1必填 0 非必填（不展示上传模块） 2非必填（展示上传回单模块 司机可以不上传）*/
    String uploadInvoiceConfig;

    /*** 打回原因*/
    String advanceReason;

    /***货主userId*/
    String consignorUserId;

    /***结算依据 1:确认发货吨位结算  2：确认收货吨位结算  3: 按收发货榜单较小值结算*/
    String settleBasisType;

    /***车老板预付字段：0-关 1-开*/
    String bossAdvance;

    /***1:预付现金;2:预付油品;3:预付油品+现金*/
    String advanceWay;

    /*** 是否需要上传皮毛重 “0”:"否"，“1”：“是”*/
    String uploadGrossAndTareWeightFlag;

    /**
     * 发货起点区域代码
     */
    String despatchDisCode;
    /**
     * 发货终点区域代码
     */
    String deliverDisCode;

    /***SDK是否可打开 1 可打开 0 不可打开*/
    String haveOpenSdk;

    //保险提示  	1:提示;其余不提示
    String orderPolicyFlag;
    //保险金额
    String orderPolicyMoney;

    ArrayList<EWater> imageJsonObjArr;

    ArrayList<EWater> imageJsonObjArr2;

    //业务平台0 中储智运平台 1 融通平台
    String plateType;
    // 是否提示兰鑫货主运单	0 否 1 是
    String haveAuthorize;
    ESDKInfoObj sdkInfoObj;
    /*判断是否是宁夏的运单*/
    String deliverTrackPersonImgFlag;

    //龙腾特钢一期需求 总重量
    String LTTotalMoney;
    //总重量是否为过磅后重量  已经是过磅后的重量就传 1，如果传了，app端吨位不能修改
    String LTCantUpdate;
    // 是否必传出场时间 1:是0否	ZCZY-7729 如果是冀东的单子需要必传出厂时间
    String deliverOutStageFlag;
    //	出场时间 ZCZY-7729 如果是冀东的单子需要必传出厂时间
    String outStageTime;
    //货源类型 CZY-7768	新增字段0正常货源 1集装箱货源
    String goodsSource;
    //加水印 0 不必填 1必填（不限制） 2 必填（只拍照）
    String deliverPicConfig;

    //平台货物的装卸货要求
    String platFormCargoPrompt;
    //   货主发单时的装卸货要求
    String consignorPrompt;
    //   装卸货要求是否展示	 1:展示0：不展示
    String promptFlag;

    //1 是 0 否 是否大件运输
    String  bulkCargoFlag;
    //自维护发货场景 0：确认发货，1：申请预付，2：重新申请预付
    String  scene;
    //司机通行证图片
    ArrayList<EWater>  carrierBulkCargoPicUrlList;

    //非预付情况下运单照片展示逻辑
    ShipmentsImgObj orderImgObj;
    //非预付情况下人车货照片展示逻辑
    ShipmentsImgObj peopleVehicleImgObj;
    //ZCZY-18042
    EAddressInfoObj addressInfoObj;

    public String getScene() {
        return scene;
    }

    public EAddressInfoObj getAddressInfoObj() {
        return addressInfoObj;
    }

    public ShipmentsImgObj getOrderImgObj() {
        return orderImgObj;
    }

    public ShipmentsImgObj getPeopleVehicleImgObj() {
        return peopleVehicleImgObj;
    }

    public String getBulkCargoFlag() {
        return bulkCargoFlag;
    }

    public void setBulkCargoFlag(String bulkCargoFlag) {
        this.bulkCargoFlag = bulkCargoFlag;
    }

    public ArrayList<EWater> getCarrierBulkCargoPicUrlList() {
        return carrierBulkCargoPicUrlList;
    }

    public void setCarrierBulkCargoPicUrlList(ArrayList<EWater> carrierBulkCargoPicUrlList) {
        this.carrierBulkCargoPicUrlList = carrierBulkCargoPicUrlList;
    }

    public String getPlatFormCargoPrompt() {
        return platFormCargoPrompt;
    }

    public String getConsignorPrompt() {
        return consignorPrompt;
    }

    public String getPromptFlag() {
        return promptFlag;
    }

    public String getDeliverPicConfig() {
        return deliverPicConfig;
    }

    public void setDeliverPicConfig(String deliverPicConfig) {
        this.deliverPicConfig = deliverPicConfig;
    }

    public String getGoodsSource() {
        return goodsSource;
    }

    public void setGoodsSource(String goodsSource) {
        this.goodsSource = goodsSource;
    }

    public String getHaveAuthorize() {
        return haveAuthorize;
    }

    public void setHaveAuthorize(String haveAuthorize) {
        this.haveAuthorize = haveAuthorize;
    }

    public void setSdkInfoObj(ESDKInfoObj sdkInfoObj) {
        this.sdkInfoObj = sdkInfoObj;
    }

    public String getDeliverOutStageFlag() {
        return deliverOutStageFlag;
    }

    public void setDeliverOutStageFlag(String deliverOutStageFlag) {
        this.deliverOutStageFlag = deliverOutStageFlag;
    }

    public String getOutStageTime() {
        return outStageTime;
    }

    public void setOutStageTime(String outStageTime) {
        this.outStageTime = outStageTime;
    }

    public String getLTTotalMoney() {
        return LTTotalMoney;
    }

    public void setLTTotalMoney(String LTTotalMoney) {
        this.LTTotalMoney = LTTotalMoney;
    }

    public String getLTCantUpdate() {
        return LTCantUpdate;
    }

    public void setLTCantUpdate(String LTCantUpdate) {
        this.LTCantUpdate = LTCantUpdate;
    }

    public String getDeliverTrackPersonImgFlag() {
        return deliverTrackPersonImgFlag;
    }

    public void setDeliverTrackPersonImgFlag(String deliverTrackPersonImgFlag) {
        this.deliverTrackPersonImgFlag = deliverTrackPersonImgFlag;
    }

    public ESDKInfoObj getSdkInfoObj() {
        return sdkInfoObj;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setPbCarrierMoney(String pbCarrierMoney) {
        this.pbCarrierMoney = pbCarrierMoney;
    }

    public void setPbCarrierUnitMoney(String pbCarrierUnitMoney) {
        this.pbCarrierUnitMoney = pbCarrierUnitMoney;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public void setFreightType(String freightType) {
        this.freightType = freightType;
    }

    public void setCargoCategory(String cargoCategory) {
        this.cargoCategory = cargoCategory;
    }

    public void setIsSeniorCarrier(String isSeniorCarrier) {
        this.isSeniorCarrier = isSeniorCarrier;
    }

    public void setIsNoSpecialAdvance(String isNoSpecialAdvance) {
        this.isNoSpecialAdvance = isNoSpecialAdvance;
    }

    public void setExpectMoney(String expectMoney) {
        this.expectMoney = expectMoney;
    }

    public void setHaveInvoice(String haveInvoice) {
        this.haveInvoice = haveInvoice;
    }

    public void setAdvanceFlagState(String advanceFlagState) {
        this.advanceFlagState = advanceFlagState;
    }

    public void setUploadInvoiceConfig(String uploadInvoiceConfig) {
        this.uploadInvoiceConfig = uploadInvoiceConfig;
    }

    public void setAdvanceReason(String advanceReason) {
        this.advanceReason = advanceReason;
    }

    public void setConsignorUserId(String consignorUserId) {
        this.consignorUserId = consignorUserId;
    }

    public void setSettleBasisType(String settleBasisType) {
        this.settleBasisType = settleBasisType;
    }

    public void setBossAdvance(String bossAdvance) {
        this.bossAdvance = bossAdvance;
    }

    public void setAdvanceWay(String advanceWay) {
        this.advanceWay = advanceWay;
    }

    public String getUploadGrossAndTareWeightFlag() {
        return uploadGrossAndTareWeightFlag;
    }

    public void setUploadGrossAndTareWeightFlag(String uploadGrossAndTareWeightFlag) {
        this.uploadGrossAndTareWeightFlag = uploadGrossAndTareWeightFlag;
    }

    public void setDespatchDisCode(String despatchDisCode) {
        this.despatchDisCode = despatchDisCode;
    }

    public void setDeliverDisCode(String deliverDisCode) {
        this.deliverDisCode = deliverDisCode;
    }

    public String getHaveOpenSdk() {
        return haveOpenSdk;
    }

    public void setHaveOpenSdk(String haveOpenSdk) {
        this.haveOpenSdk = haveOpenSdk;
    }

    public void setOrderPolicyFlag(String orderPolicyFlag) {
        this.orderPolicyFlag = orderPolicyFlag;
    }

    public void setOrderPolicyMoney(String orderPolicyMoney) {
        this.orderPolicyMoney = orderPolicyMoney;
    }

    public ArrayList<EWater> getImageJsonObjArr() {
        return imageJsonObjArr;
    }

    public void setImageJsonObjArr(ArrayList<EWater> imageJsonObjArr) {
        this.imageJsonObjArr = imageJsonObjArr;
    }

    public ArrayList<EWater> getImageJsonObjArr2() {
        return imageJsonObjArr2;
    }

    public void setImageJsonObjArr2(ArrayList<EWater> imageJsonObjArr2) {
        this.imageJsonObjArr2 = imageJsonObjArr2;
    }

    public String getPlateType() {
        return plateType;
    }

    public void setPlateType(String plateType) {
        this.plateType = plateType;
    }

    public String getOrderPolicyFlag() {
        return orderPolicyFlag;
    }

    public String getOrderPolicyMoney() {
        return orderPolicyMoney;
    }

    public boolean isNoEmptyImage() {
        return imageJsonObjArr != null && !imageJsonObjArr.isEmpty();
    }

    public ArrayList<EWater> getImageJsonArr() {
        return imageJsonObjArr;
    }

    public boolean isNoEmptyImage2() {
        return imageJsonObjArr2 != null && !imageJsonObjArr2.isEmpty();
    }

    public ArrayList<EWater> getImageJsonArr2() {
        return imageJsonObjArr2;
    }

    public boolean isHaveOpenSdk() {
        return TextUtils.equals("1", haveOpenSdk);
    }

    public String getDeliverDisCode() {
        return deliverDisCode;
    }

    public String getDespatchDisCode() {
        return despatchDisCode;
    }

    public boolean uploadGrossAndTareWeightFlag() {
        return TextUtils.equals("1", uploadGrossAndTareWeightFlag);
    }

    public String getAdvanceWay() {
        return advanceWay;
    }

    public String getBossAdvance() {
        return bossAdvance;
    }

    public String getSettleBasisType() {
        return settleBasisType;
    }

    /***
     * 是否需要上传发货单[预付款模式下]
     *
     * @return
     */
    public boolean tarraceShowWaybill() {
        if (TextUtils.equals("1", plateType) || TextUtils.equals("1", deliverTrackPersonImgFlag)) {
            //  融通平台 （必传） || 宁夏的运单 （必传）
            return true;
        }
        if (TextUtils.equals("1", settleBasisType) || TextUtils.equals("3", settleBasisType)) {
            // 结算依据 1:确认发货吨位结算  3: 按收发货榜单较小值结算
            return true;
        }
        // 上传发货单配置项（1必填  2非必填（展示上传回单模块 司机可以不上传））
        if (TextUtils.equals("1", uploadInvoiceConfig) || TextUtils.equals("2", uploadInvoiceConfig)) {
            return true;
        }
        return false;
    }

    /***
     * 纸质运单照片(显示状态)
     * true ： 显示 false 不显示
     * */
    public boolean waybillImageShow(String specifyFlag) {

        if (TextUtils.equals("1", plateType) || TextUtils.equals("1", deliverTrackPersonImgFlag)) {
            //  融通平台 （必传） || 宁夏的运单 （必传）
            return true;
        }

        if (TextUtils.equals("1", settleBasisType) || TextUtils.equals("3", settleBasisType)) {
            // 结算依据 1:确认发货吨位结算  3: 按收发货榜单较小值结算
            return true;
        }
        // 非指定 && 上传发货单配置项（1必填  2非必填（展示上传回单模块 司机可以不上传））
        if ((!TextUtils.equals("1", specifyFlag)) && (TextUtils.equals("1", uploadInvoiceConfig) || TextUtils.equals("2", uploadInvoiceConfig))) {
            return true;
        }

        return false;
    }


    /***
     * 人车照片(显示状态)
     * true ： 显示 false 不显示
     */
    public boolean perCarImageShow() {
        if (TextUtils.equals("1", plateType) || TextUtils.equals("1", deliverTrackPersonImgFlag) || TextUtils.equals("1", deliverPicConfig) || TextUtils.equals("2", deliverPicConfig)) {
            //  融通平台 （必传） || 宁夏的运单 （必传）||  必传配置
            return true;
        }
        return false;
    }

    public String getConsignorUserId() {

        return consignorUserId;
    }

    public String getUploadInvoiceConfig() {

        return uploadInvoiceConfig;
    }

    public String getHaveInvoice() {

        return haveInvoice;
    }

    public String getAdvanceFlagState() {

        return advanceFlagState;
    }

    public String getAdvanceReason() {

        return advanceReason;
    }


    public String getOrderId() {

        return orderId;
    }

    public String getPbCarrierMoney() {

        return pbCarrierMoney;
    }

    public String getPbCarrierUnitMoney() {

        return pbCarrierUnitMoney;
    }

    public String getWeight() {

        return weight;
    }

    public String getFreightType() {

        return freightType;
    }

    public String getCargoCategory() {

        return cargoCategory;
    }

    public String getIsSeniorCarrier() {

        return isSeniorCarrier;
    }

    public String getIsNoSpecialAdvance() {

        return isNoSpecialAdvance;
    }

    public String getExpectMoney() {

        return expectMoney;
    }

    /**
     * 计算发货预付款金额[货主预付]
     *
     * @return
     */
    public String calculationAdvanceMoney(String advanceRatioStr,List<EGoodInfo> goodsList) {

        // 预付比例
        double advanceRatio = 0.0;
        if (!TextUtils.isEmpty(advanceRatioStr)) {
            advanceRatio = Double.parseDouble(advanceRatioStr);
        }

        // 服务费
        double expectMoney = 0.0;
        if (TextUtils.isEmpty(this.getExpectMoney())) {
            expectMoney = 0.0;
        } else {
            expectMoney = Double.parseDouble(this.getExpectMoney());
        }

        if (TextUtils.equals("1", this.getFreightType())) {
            //单价   预付运费 = (运费(单价 * 数量)- 服务费) * 预付比例
            double tmpWeight = 0.0;
            for (EGoodInfo goodsBean : goodsList) {
                String before_amount = goodsBean.getBeforeDeliverCargoWeight();
                tmpWeight += Double.valueOf(this.isNumber(before_amount));
            }
            double tempMoney = tmpWeight * Double.valueOf(this.isNumber(this.getPbCarrierUnitMoney())) - expectMoney;
            // 防止未输入吨位减去服务费出现负值
            tempMoney = tempMoney <= 0.00 ? 0.00 : tempMoney;
            String totalMoneyNew = totalMoneyNew(tempMoney);
            double unitAdvanceMoney = Double.valueOf(totalMoneyNew) * advanceRatio / 100.0;

            return totalMoneyNew(unitAdvanceMoney);
        } else {
            //包车价 预付运费 = （包车价 - 服务费） * 预付比例
            double tempMoney = Double.parseDouble(this.isNumber(this.getPbCarrierMoney())) - expectMoney;
            String totalMoneyNew = totalMoneyNew(tempMoney);
            double advanceMoney = Double.valueOf(totalMoneyNew) * advanceRatio / 100.0;
            return totalMoneyNew(advanceMoney);
        }
    }

    /**
     * 对double数据进行取精度.
     *
     * @param value        double数据.
     * @param scale        精度位数(保留的小数位数).
     * @param roundingMode 精度取值方式.
     * @return 精度计算后的数据.
     */
    private double round(double value, int scale, int roundingMode) {

        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(scale, roundingMode);
        return bd.doubleValue();
    }

    /**
     * 四舍五入保留2位小数
     *
     * @param money
     * @return
     */
    private String totalMoneyNew(double money) {

        return String.valueOf(this.round(Math.round(money * 100) * 0.01D, 2, BigDecimal.ROUND_HALF_UP));
    }


    /**
     * 判断传入的字符串是不是数字
     *
     * @param number
     * @return
     */
    private String isNumber(String number) {

        if (TextUtils.isEmpty(number)) {
            return "0";
        } else {
            return number;
        }
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public boolean isAuthorize() {
        return TextUtils.equals("1", haveAuthorize);
    }

    public boolean isJDcustom() {
        //冀东定制化需求
        return TextUtils.equals("1", deliverOutStageFlag);
    }


    //智运卡
    private EShipCard discountCardInfo;

    public EShipCard getDiscountCardInfo() {
//            List<ShipmentsDiscountCard> list = new ArrayList<>(2);
//            list.add(new ShipmentsDiscountCard("30", "10", "000000", "黄金9折卡", "有效期30天", 0.9, "月卡"));
//            list.add(new ShipmentsDiscountCard("40", "20", "111111", "铂金5折卡", "有效期50天", 0.5, "年卡"));
//            discountCardInfo = new EShipCard("1", "0", list,"xxxxxxx");
        return discountCardInfo;
    }

    public void setDiscountCardInfo(EShipCard discountCardInfo) {
        this.discountCardInfo = discountCardInfo;
    }
}
