package com.zczy.plugin.order.changecarrier.bean

/**
 * PS:运单变更列表
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=12355402
 * Created by sdx on 2019/2/22.
 */
data class OrderChangeData(
    var orderId: String? = null, //
    var sourceId: String? = null, //
    var title: String = "", // 标题
    var contactName: String = "", // 货主姓名
    var contactPhone: String = "", // 货主紧急联系电话
    /**
     * 5 摘单,6 确认发货,7 确认收货,8 已终止
     * 5是袋装货 6 带卸货
     */
    var consignorState: String = "", // 货主状态
    var cargoName: String = "", // 货物名称
    var consignorUserId: String = "",// 货主ID
    var despatchStart: String = "", // 装货时间(起) 格式:yyyy-mm-dd hh:mm:ss
    var despatchEnd: String = "", // 装货时间(止) 格式:yyyy-mm-dd hh:mm:ss
    var despatchPro: String = "", // 启运地省
    var despatchCity: String = "", // 启运地市
    var despatchDis: String = "", // 启运地区
    var deliverPro: String = "", // 目的地省
    var deliverCity: String = "", // 目的地市
    var deliverDis: String = "", // 目的地区
    var carrierMoney: String = "", // 运费总价
    var carrierName: String = "", // 成交承运方名称
    var haveDeal: String = "", // 是否成交 0未，1是
    var changeState: String = "", //1，可变更。2，待审核。3，待接受 。4，不在途。
    var examineState: String = "", // 审核状态 0:未审核 1后台通过 2后台不通过 3-承运人不同意 4-承运人同意
    var changeType: String = "", // 变更类型 1：人，2：车，3：人和车'
    var createdTimeStr: String = "", // 创建时间 格式:yyyy-mm-dd hh:mm:ss
    var changeId: String = "", // 变更id
    var oldPlateNumber: String = "", // 原承运人车牌号
    var dispatchOperationFlag: String = "", // 是否加盟运力变更 0 否 1 是
    var isCancelChange: String = "", //是否可以取消变更 0：不可以  1：可以
    var personalSafety: String = "", //司机人身保障
    var isLessThanOrder: String = "", //1 表示零担
)

fun OrderChangeData.formatExamineState(): String {
    // 审核状态 0:未审核 1-后台通过 2-后台不通过 3-承运人不同意 4-承运人同意
    return when (changeState) {
        "2" -> {
            "待审核"
        }

        "3" -> {
            "待接受"
        }

        "4" -> {
            "不在途"
        }

        else -> ""
    }
}

fun OrderChangeData.formatStartAddress(): String {
    val sb = StringBuilder()
    if (despatchCity != "市辖区") {
        sb.append(despatchCity)
    } else {
        sb.append(despatchPro)
    }
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(despatchDis)
    return sb.toString()
}

fun OrderChangeData.formatEndAddress(): String {
    val sb = StringBuilder()
    if (deliverCity != "市辖区") {
        sb.append(deliverCity)
    } else {
        sb.append(deliverPro)
    }
    if (sb.isNotEmpty()) {
        sb.append(" ")
    }
    sb.append(deliverDis)
    return sb.toString()
}