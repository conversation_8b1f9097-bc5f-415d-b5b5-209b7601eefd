package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：司机有一单及以上在途
 * 时间：2025/2/18 17:01
 * @author：王家辉
 * wiki：
 * 对接：黄琎
 * */
class ReqAssignDriverTransportCheck(
    var driverUserId: String? = null
) : BaseNewRequest<BaseRsp<RspAssignDriverTransportCheck>>("oms-app/carrier/common/assignDriverTransportCheck") {
}

data class RspAssignDriverTransportCheck(
    var isTransportFlag: String? = null// 0 否  1 是
) : ResultData()