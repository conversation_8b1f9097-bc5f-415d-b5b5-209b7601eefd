package com.zczy.plugin.order.changecarrier;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.certificate.vehiclemanage.carowner.CarOwnerVehicleManagementActivity;
import com.zczy.certificate.vehiclemanage.carrier.CarrierVehicleManagementActivity;
import com.zczy.certificate.vehiclemanage.enterprise.EnterPriseVehicleManagementActivityV1;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.ui.BaseActivity;
import com.zczy.comm.utils.json.JsonUtil;
import com.zczy.comm.widget.gird.ChooseCarGirdView;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierChangeModel;
import com.zczy.plugin.order.changecarrier.req.RspPageCar;
import com.zczy.plugin.order.changecarrier.req.RspVehicleData;
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck;

import org.jetbrains.annotations.NotNull;

/**
 * 功能描述:
 *
 * <AUTHOR>
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/11/28
 */
public class OrderChangeCarrierChooseAllCarTMSActivity extends BaseActivity<OrderChangeCarrierChangeModel> {

    private static final String EXTRA_SELECT_ITEM = "extra_select_item";
    private static final String EXTRA_FRIENDID = "extra_friendId";
    private static final String EXTRA_ORDERID = "extra_orderId";
    private static final String EXTRA_ISMINE = "extra_isMine";

    private ChooseCarGirdView<RspVehicleData> registerChooseCarView;
    private RspVehicleData selectItem;
    private Boolean isMine = false; //是否自己承运
    private String consignorUserId = "";

    public static void start(Activity activity, String orderId, String friendId, @Nullable RspVehicleData selectData, int requestCode, Boolean isMine, String consignorUserId) {
        Intent intent = new Intent(activity, OrderChangeCarrierChooseAllCarTMSActivity.class);
        intent.putExtra(EXTRA_SELECT_ITEM, JsonUtil.toJson(selectData));
        intent.putExtra(EXTRA_FRIENDID, friendId);
        intent.putExtra(EXTRA_ORDERID, orderId);
        intent.putExtra(EXTRA_ISMINE, isMine);
        intent.putExtra("consignorUserId", consignorUserId);
        activity.startActivityForResult(intent, requestCode);
    }

    @Nullable
    public static RspVehicleData obtainData(@Nullable Intent intent) {
        if (intent == null) {
            return null;
        }
        String extraSelect = intent.getStringExtra(EXTRA_SELECT_ITEM);
        return JsonUtil.toJsonObject(extraSelect, RspVehicleData.class);
    }

    @Override
    protected int getLayout() {
        return R.layout.order_change_carrier_choose_all_car_activity;
    }


    @Override
    protected void bindView(@Nullable Bundle bundle) {
        String extraSelect = getIntent().getStringExtra(EXTRA_SELECT_ITEM);
        selectItem = JsonUtil.toJsonObject(extraSelect, RspVehicleData.class);
        registerChooseCarView = findViewById(R.id.register_choose_car_view);
        registerChooseCarView.setListener(new ChooseCarGirdView.RegisterChooseCarViewListener<RspVehicleData>() {
            @Override
            public void onSelectCar(@NotNull RspVehicleData vehicleData) {

                if (TextUtils.equals("0", vehicleData.isChoose())) {
                    showToast("当前车辆不可用！");
                    return;
                }
                ReqAssignVehicleTransportCheck req = new ReqAssignVehicleTransportCheck();
                req.setPlateNumber(vehicleData.getPlateNumber());
                getViewModel(OrderChangeCarrierChangeModel.class).assignVehicleTransportCheck(req, (data) -> {
                    if (TextUtils.equals("1", data.getData().isTransportFlag())) {
                        DialogBuilder dialog = new DialogBuilder();
                        dialog.setTitle("提示");
                        dialog.setMessage(data.getMsg());
                        dialog.setHideCancel(true);
                        dialog.setOKTextListener("我知道了", (dialog1, which) -> {
                            dialog1.dismiss();
                            selectCheck(vehicleData);
                        });
                        showDialog(dialog);
                    } else {
                        selectCheck(vehicleData);
                    }
                    return null;
                }, () -> {
                    selectCheck(vehicleData);
                    return null;
                });
            }

            @Override
            public void onClickMore() {
            }
        });
        TextView btnCommit = findViewById(R.id.btn_commit);

        bindClickEvent(btnCommit);

        ConstraintLayout clVehicleInfo = findViewById(R.id.clVehicleInfo);
        clVehicleInfo.setVisibility(View.VISIBLE);
        TextView tvVehicleInfo = findViewById(R.id.tvVehicleInfo);
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        SpannableString spannableString1 = new SpannableString("不显示报废期车辆与冻结车辆，可至");
        SpannableString spannableString2 = new SpannableString("车辆管理");
        spannableString2.setSpan(new ForegroundColorSpan(Color.parseColor("#F75B1F")), 0, spannableString2.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        SpannableString spannableString3 = new SpannableString("查看");
        spannableStringBuilder.append(spannableString1).append(spannableString2).append(spannableString3);
        tvVehicleInfo.setText(spannableStringBuilder);
        TextView tvLookVehicle = findViewById(R.id.tvLookVehicle);
        tvLookVehicle.setOnClickListener(v -> {
            ELogin login = CommServer.getUserServer().getLogin();
            if (login != null) {
                IRelation relation = login.getRelation();
                if (relation.isCarrier()) {
                    CarrierVehicleManagementActivity.start(OrderChangeCarrierChooseAllCarTMSActivity.this);
                } else if (relation.isCys()) {
                    EnterPriseVehicleManagementActivityV1.start(OrderChangeCarrierChooseAllCarTMSActivity.this);
                } else if (relation.isBoss()) {
                    CarOwnerVehicleManagementActivity.start(OrderChangeCarrierChooseAllCarTMSActivity.this);
                }
            }
        });
    }

    @Override
    protected void initData() {
        String friendId = getIntent().getStringExtra(EXTRA_FRIENDID);
        String orderId = getIntent().getStringExtra(EXTRA_ORDERID);
        isMine = getIntent().getBooleanExtra(EXTRA_ISMINE, false);
        consignorUserId = getIntent().getStringExtra("consignorUserId");

        getViewModel(OrderChangeCarrierChangeModel.class).queryChangePageInfoTMS(orderId, friendId, consignorUserId, 1);
    }

    @Override
    protected void onSingleClick(@NonNull View v) {
        super.onSingleClick(v);
        if (v.getId() == R.id.btn_commit) {
            if (selectItem == null) {
                showDialogToast("请选择车辆");
            } else {
                Intent intent = getIntent();
                intent.putExtra(EXTRA_SELECT_ITEM, JsonUtil.toJson(selectItem));
                setResult(RESULT_OK, intent);
                finish();
            }
        }
    }

    public void selectCheck(RspVehicleData vehicleData) {
        selectItem = vehicleData;
    }

    @LiveDataMatch
    public void onQueryChangePageInfo(RspPageCar data) {
        if (data != null) {
            registerChooseCarView.setNewData(data.getRootArray());
            registerChooseCarView.setFlatMap(RspVehicleData::getPlateNumber);
            if (selectItem != null) {
                for (RspVehicleData item : data.getRootArray()) {
                    if (item.getVehicleId().equals(selectItem.getVehicleId())) {
                        registerChooseCarView.addSelectCar(item);
                        break;
                    }
                }
            }

            //ZCZY-15868 安庆税务车老板摘单管控优化需求加急
            if (TextUtils.equals("1", data.isNeedHandle())) {
                TextView tv_toast_text = findViewById(R.id.tv_toast_text);
                tv_toast_text.setVisibility(View.VISIBLE);
                tv_toast_text.setText("您本月交易车辆已超出平台当前角色管控要求，请选择可选车辆，如有疑问可咨询平台客服。");
            }

        }

    }
}
