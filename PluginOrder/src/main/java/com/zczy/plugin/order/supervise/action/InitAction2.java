package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:货物平台系统2.0 【APP 启动】(临时使用,非正常流程)
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/16
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import android.app.Application;
import android.text.TextUtils;

import androidx.work.WorkManager;

import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.supervise.HookLocationOpenApi;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.functions.Function;

public class InitAction2 extends BaseActionServer implements Function<InitAction2, Boolean> {

    List<ESDKInfoObj> esdkInfoObjList;

    public InitAction2(Application context, List<ESDKInfoObj> esdkInfoObjList) {

        this.esdkInfoObjList = esdkInfoObjList;
    }

    @Override
    public Boolean apply(@NonNull InitAction2 initAction) throws Exception {

        HookLocationOpenApi hook = new HookLocationOpenApi(AppCacheManager.getApplication());
        //当前已缓存数据
        List<ShippingNoteInfo> oldList = hook.getShippingNoteList();
        for (ShippingNoteInfo info : oldList) {
            WorkManager.getInstance().cancelUniqueWork(info.getShippingNoteNumber());
        }
        Thread.sleep(700);

        long interval = 30000L;
        if (oldList != null && oldList.size() > 0) {
            interval = oldList.get(0).getInterval();
        }
        //先删除原缓存
        hook.deleteShippingNotes(oldList);
        oldList.clear();

        for (ESDKInfoObj obj : esdkInfoObjList) {
            ShippingNoteInfo info = obj.getShippingNoteInfo();
            obj.setInterval(interval);
            oldList.add(info);
        }
        //再重新缓存
        hook.saveShippingNotes(oldList);

        for (ESDKInfoObj obj : esdkInfoObjList) {

            if (TextUtils.equals("7", obj.getOrderState())) {
                //7已收货(非APP操作收货)
                new StopAction(obj, "").start();
            } else if (TextUtils.equals("8", obj.getOrderState())) {
                //8已终止
                new StopAction(obj, "[00]货主取消此运单终止运输").start();
            } else if (TextUtils.equals("1", obj.getChangeMacFlag())) {
                //变更设备
                new ReStartAction(obj, "[02]换手机").start();
            } else {
                //正常
                this.auth(obj.getConsignorSubsidiaryId(), new OnAuthListener2(obj));
            }
        }

        return Boolean.TRUE;
    }

    @Override
    public void start() {
        RetrofitManager.executeSigin(Observable.just(InitAction2.this).map(InitAction2.this), new EmptyResult<>());
    }

    class OnAuthListener2 implements OnAuthListener {

        ESDKInfoObj esdkInfoObj;

        public OnAuthListener2(ESDKInfoObj esdkInfoObj) {
            this.esdkInfoObj = esdkInfoObj;
        }

        @Override
        public void onAuthFailure(String code, String msg) {
            out(String.format("省货物平台系统2.0=Init=>APP启动[失败] code:%s,msg:%s", code, msg));
        }

        @Override
        public void onAuthSuccess(List<ShippingNoteInfo> list) {
            out(String.format("省货物平台系统Init=>APP启动[成功]，缓存运单数量size:%s", (list != null ? list.size() : 0)));
            //开启定时上报定位
            if (list != null && list.size() > 0) {
                //开启定时上报定位
                esdkInfoObj.setInterval(list.get(0).getInterval());
                new TimeWorkerAction.Build().setInfo(esdkInfoObj).build();
            }

        }
    }

}
