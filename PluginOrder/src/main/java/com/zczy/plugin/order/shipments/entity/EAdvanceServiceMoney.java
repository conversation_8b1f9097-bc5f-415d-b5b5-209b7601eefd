//package com.zczy.plugin.order.shipments.entity;
//
//import com.zczy.comm.http.entity.ResultData;
//
//public class EAdvanceServiceMoney extends ResultData {
//
//    /***    预计预付服务费	服务费新计算公式得出*/
//    String newAdvanceServiceMoney;
//
//
//    /***       是否展示提示
//     0:不展示；1:展示*/
//    String showFlag;
//
//
//    /***  提示语	如：信用分达到590分以上时，预付款服务费：约××元*/
//    String showMsg;
//
//    /***
//     * 现金+油品预付时预估服务费
//     */
//    String realAdvanceServiceMoney;
//    // 50902版本 ZCZY-10592
//    String oilRewardRatioFlag;    //是否展示油品比例	0或者空：无 ;1:有
//    String rewardOilCashMoneyFlag; //是否展示油品金额
//    String rewardOilCashMoney;    //油品奖励现金金额
//    String rewardOilRatio;    //油品奖励比例
//
//    public String getOilRewardRatioFlag() {
//        return oilRewardRatioFlag;
//    }
//
//    public String getRewardOilCashMoneyFlag() {
//        return rewardOilCashMoneyFlag;
//    }
//    public String getNewAdvanceServiceMoney() {
//        return newAdvanceServiceMoney;
//    }
//
//    public String getShowFlag() {
//        return showFlag;
//    }
//
//    public String getShowMsg() {
//        return showMsg;
//    }
//
//    public String getRealAdvanceServiceMoney() {
//        return realAdvanceServiceMoney;
//    }
//
//
//    public String getRewardOilCashMoney() {
//        return rewardOilCashMoney;
//    }
//
//    public String getRewardOilRatio() {
//        return rewardOilRatio;
//    }
//}
