package com.zczy.plugin.order.changecarrier.fragment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.ex.isTrue
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierChangeActivity
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierShipmentsCarActivity
import com.zczy.plugin.order.changecarrier.adapter.OrderChangeCarrierMainOrderChangeAdapter
import com.zczy.plugin.order.changecarrier.bean.OrderChangeData
import com.zczy.plugin.order.changecarrier.bean.RxEventChangeCar
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierMainOrderChangeModel

class OrderChangeCarrierMainOrderChangeFragment : BaseFragment<OrderChangeCarrierMainOrderChangeModel>() {

    private var swipeRefreshMoreLayout: SwipeRefreshMoreLayout? = null

    companion object {
        private const val REQUEST_CHANGE = 0x34
    }

    override fun getLayout(): Int = R.layout.order_common_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout = view.findViewById(R.id.swipe_refresh_more_layout)

        val adapter = OrderChangeCarrierMainOrderChangeAdapter()
        val emptyView = CommEmptyView.creatorDef(context)
        swipeRefreshMoreLayout?.apply {
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener2 { nowPage ->
                viewModel?.getNetInfo(nowPage)
            }
        }
    }

    override fun initData() {
        swipeRefreshMoreLayout?.onAutoRefresh()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_CHANGE -> {
                    swipeRefreshMoreLayout?.onAutoRefresh()
                }
            }
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {}

        override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, v: View, position: Int) {
            super.onItemChildClick(adapter, view, position)
            val item = adapter.getItem(position)
            if (item is OrderChangeData) {
                when (v.id) {
                    R.id.btn_change -> {
                        /**
                         * 5 摘单,6 确认发货,7 确认收货,8 已终止
                         * 5是袋装货 6 带卸货
                         */
                        if ("5" == item.consignorState) {
                            // 变更司机和车辆
                            OrderChangeCarrierChangeActivity.start(
                                fragment = this@OrderChangeCarrierMainOrderChangeFragment,
                                orderId = item.orderId ?: "",
                                consignorUserId = item.consignorUserId,
                                isZeroAssume = item.isLessThanOrder.isTrue,
                                requestCode = REQUEST_CHANGE
                            )
                        } else if ("6" == item.consignorState) {
                            // 点击变更跳转到变更车辆可以变更车辆
                            OrderChangeCarrierShipmentsCarActivity.startUI(
                                context = context,
                                orderId = item.orderId ?: "",
                                consignorUserId = item.consignorUserId,
                                isCarPool = item.isLessThanOrder.isTrue
                            )
                        }
                    }

                    R.id.btn_cancel_change -> {
                        //取消变更
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "温馨提示"
                        dialogBuilder.message = "是否取消变更?"
                        dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface?, i: Int ->
                            dialogInterface?.dismiss()
                            viewModel?.cancelCarrierChange(item.orderId, item.sourceId, item.consignorUserId)
                        }
                        showDialog(dialogBuilder)
                    }
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: PageList<OrderChangeData>?) {
        swipeRefreshMoreLayout?.onRefreshCompale(data)
    }

    @RxBusEvent(from = "变更车辆")
    open fun onChangeCarSuccess(s: RxEventChangeCar) {
        //重新加载
        swipeRefreshMoreLayout?.onAutoRefresh()
    }

    @LiveDataMatch
    open fun onCancelCarrierChange(msg: String) {
        //刷新待装货运单列表

        //刷新待装货运单列表
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "温馨提示"
        dialogBuilder.message = msg
        dialogBuilder.setOKText("我知道了")
        dialogBuilder.isHideCancel = true
        dialogBuilder.okListener = DialogBuilder.DialogInterface.OnClickListener { dialogInterface: DialogBuilder.DialogInterface, i: Int ->
            dialogInterface.dismiss()
            //重新加载
            swipeRefreshMoreLayout?.onAutoRefresh()
        }
        showDialog(dialogBuilder)
    }
}
