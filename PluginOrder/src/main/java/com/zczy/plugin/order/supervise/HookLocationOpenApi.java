package com.zczy.plugin.order.supervise;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import android.content.Context;

import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

public class HookLocationOpenApi {
    Context context;

    public HookLocationOpenApi(Context context) {
        this.context = context;
    }

    //删除全部换成信息
    public void deleteShippingNotesOld() {
        try {
            List<ShippingNoteInfo> list = this.getShippingNoteList();
            if (list == null || list.isEmpty()) {
                return;
            }
            this.deleteShippingNotes(list);

        } catch (Exception e) {
        }

    }

    public void deleteShippingNotes(List<ShippingNoteInfo> list) {
        try {
            if (list == null || list.isEmpty()) {
                return;
            }
            Method deleteShippingNoteInfo = LocationOpenApi.class.getDeclaredMethod("deleteShippingNoteInfo", Context.class, List.class);
            deleteShippingNoteInfo.setAccessible(true);

            Method deleteShippingNotes = LocationOpenApi.class.getDeclaredMethod("deleteShippingNotes", Context.class, List.class);
            deleteShippingNotes.setAccessible(true);

            deleteShippingNotes.invoke(null, context, list);
            deleteShippingNoteInfo.invoke(null, context, list);

        } catch (Exception e) {
        }

    }

    public List<ShippingNoteInfo> getShippingNoteList() throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {

        Method getShippingNoteList = LocationOpenApi.class.getDeclaredMethod("getShippingNoteList", Context.class);
        getShippingNoteList.setAccessible(true);
        Object data = getShippingNoteList.invoke(null, context);
        return data != null ? (List<ShippingNoteInfo>) data : new ArrayList<>(0);
    }

    public void saveShippingNotes(List<ShippingNoteInfo> list) {

        try {
            if (list == null || list.isEmpty()) {
                return;
            }

            Method saveShippingNoteInfo = LocationOpenApi.class.getDeclaredMethod("saveShippingNoteInfo", Context.class, ArrayList.class);
            saveShippingNoteInfo.setAccessible(true);

            Method saveShippingNotes = LocationOpenApi.class.getDeclaredMethod("saveShippingNotes", Context.class, List.class);
            saveShippingNotes.setAccessible(true);

            saveShippingNotes.invoke(null, context, list);
            saveShippingNoteInfo.invoke(null, context, list);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
