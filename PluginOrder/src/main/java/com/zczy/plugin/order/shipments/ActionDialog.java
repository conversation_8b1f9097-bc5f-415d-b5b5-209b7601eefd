package com.zczy.plugin.order.shipments;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.webkit.JavascriptInterface;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;

import com.tencent.smtt.sdk.WebSettings;
import com.zczy.comm.X5BaseJavascriptInterface;
import com.zczy.comm.x5.X5WebView;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.shipments.entity.EActivityInfo;

/**
 * 功能描述:全屏广告
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2017/9/5
 */
public class ActionDialog extends DialogFragment {

    public static void showDialogUI(FragmentActivity activity, EActivityInfo advert) {
        ActionDialog dialog = new ActionDialog();
        dialog.showDialog(activity, advert);
    }

    private EActivityInfo mAdvert;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.shipment_action_advert_dialog, container, false);
        X5WebView web_view = view.findViewById(R.id.web_view);
        web_view.setBackgroundColor(Color.TRANSPARENT);
        if (mAdvert != null) {
            WebSettings webViewSettings = web_view.getSettings();
            String baseAgent = webViewSettings.getUserAgentString();
            webViewSettings.setUserAgent(baseAgent + ";app/ANDROID");

            web_view.addJavascriptInterface(new X5BaseJavascriptInterface(getActivity()) {
                @Override
                @JavascriptInterface
                public void finishActivity() {
                    dismiss();
                }
            }, "android");
            web_view.loadUrl(mAdvert.getAdvertUrl());
        }
        return view;
    }


    @Override
    public void onStart() {
        super.onStart();
        getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(0x00000000));
        getDialog().getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        return dialog;
    }

    private void showDialog(FragmentActivity activity, EActivityInfo advert) {

        try {
            this.mAdvert = advert;
            if (activity == null) {
                return;
            }
            if (isAdded()) {
                return;
            }
            super.show(activity.getSupportFragmentManager(), ActionDialog.class.getName());

        } catch (Exception e) {
        }
    }

    @Override
    public void dismiss() {
        FragmentActivity activity = this.getActivity();
        if (activity != null && !activity.isFinishing()) {
            this.dismissAllowingStateLoss();
        }
    }


}
