package com.zczy.plugin.order.stevedore.model;

import com.sfh.lib.exception.HandleException;
import com.sfh.lib.mvvm.service.BaseViewModel;
import com.sfh.lib.rx.IResult;
import com.sfh.lib.rx.IResultSuccess;
import com.zczy.comm.CommServer;
import com.zczy.comm.file.IFileServer;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.http.entity.ResultData;
import com.zczy.plugin.order.stevedore.model.request.ReqCarrierAddLoadPay;
import com.zczy.plugin.order.stevedore.model.request.ReqQueryCargoList;
import com.zczy.plugin.order.stevedore.model.request.ReqQueryHandlingChargesDetail;
import com.zczy.plugin.order.stevedore.model.request.ReqStevedoreList;
import com.zczy.plugin.order.stevedore.model.request.ReqStevedoreOrderList;

import java.io.File;
import java.util.List;

import io.reactivex.disposables.Disposable;

/**
 * 功能描述:装卸费
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class StevedoreModel extends BaseViewModel implements IFileServer.OnFileUploaderListener{

    /***
     *装卸费货物明细
     */
    public void queryCargoList(String orderId){

        this.execute (true, new ReqQueryCargoList (orderId), new IResultSuccess<BaseRsp<PageList<String>>> () {

            @Override
            public void onSuccess(BaseRsp<PageList<String>> pageList) throws Exception {
                if (pageList.success ()){
                    setValue ("onCargoListSuccess",pageList.getData ().getRootArray ());
                }else{
                    showDialogToast (pageList.getMsg ());
                }
            }
        });
    }

    /***
     * 车老板装卸费明细详情
     * @param detailId
     */
    public void queryHandlingChargesDetail(String detailId){

        this.execute (true, new ReqQueryHandlingChargesDetail (detailId), new IResultSuccess<BaseRsp<ESteveDoreDeatil>> () {

            @Override
            public void onSuccess(BaseRsp<ESteveDoreDeatil> pageList) throws Exception {
                if (pageList.success () ){
                    setValue ("onDetailSuccess",pageList.getData ());
                }else{
                    showDialogToast (pageList.getMsg ());
                }
            }
        });
    }

    /***
     * 根据运单号新增装卸货费信息
     */
    public void carrierAddLoadPay(ReqCarrierAddLoadPay req){

        this.execute (false, req, new IResultSuccess<BaseRsp<ResultData>> () {

            @Override
            public void onSuccess(BaseRsp<ResultData> resultDataBaseRsp) throws Exception {
                if (resultDataBaseRsp.success ()){

                    setValue ("onAddLoadPaySuccess");
                }else{
                    showDialogToast (resultDataBaseRsp.getMsg ());
                }
            }
        });
    }


    public void onPageList(int nowPage) {

        this.execute (new ReqStevedoreList (nowPage), new IResult<BaseRsp<PageList<ESteveDore>>> () {

            @Override
            public void onFail(HandleException e) {

                showToast (e.getMsg ());
                setValue ("onPageSuccess");
            }

            @Override
            public void onSuccess(BaseRsp<PageList<ESteveDore>> pageListBaseRsp) throws Exception {

                if (pageListBaseRsp.success ()) {
                    setValue ("onPageSuccess", pageListBaseRsp.getData ());
                } else {
                    showToast (pageListBaseRsp.getMsg ());
                    setValue ("onPageSuccess");
                }
            }
        });
    }

    /***
     * 上传文件
     * @param file
     */
    public void upFile(List<String> file) {

        for (String p : file) {
            this.upFile (p);
        }
    }

    public void upFile(String file) {

        Disposable disposable = CommServer.getFileServer ().update (new File (file), this);
        this.putDisposable (disposable);
    }

    @Override
    public void onSuccess(File tag, String url) {

        setValue ("onFileSuccess", tag, url);
    }

    @Override
    public void onFailure(File tag, String error) {
        showToast(error);
        setValue ("onFileFailure", tag, error);
    }


    /***
     * 订单列表
     * @param nowPage
     * @param txt
     */
    public void searchListOrder(int nowPage,String txt){
        this.execute(new ReqStevedoreOrderList(nowPage, txt), new IResult<BaseRsp<PageList<ESearchOrder>>>() {
            @Override
            public void onFail(HandleException e) {
                setValue("onPageSuccess");
                showToast(e.getMsg());
            }

            @Override
            public void onSuccess(BaseRsp<PageList<ESearchOrder>> pageListBaseRsp) throws Exception {
                if (pageListBaseRsp.success()){
                    setValue("onPageSuccess",pageListBaseRsp.getData());
                }else {
                    setValue("onPageSuccess");
                    showToast(pageListBaseRsp.getMsg());
                }
            }
        });
    }
}
