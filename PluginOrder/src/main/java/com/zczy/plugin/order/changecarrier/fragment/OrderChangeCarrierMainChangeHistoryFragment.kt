package com.zczy.plugin.order.changecarrier.fragment

import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.utils.UtilTool
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseFragment
import com.zczy.comm.utils.dp2px
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.OrderChangeCarrierHistorySearchActivity
import com.zczy.plugin.order.changecarrier.adapter.OrderChangeCarrierMainChangeHistoryAdapter
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierMainChangeHistoryModel
import com.zczy.plugin.order.changecarrier.req.RspOrderChangeData

class OrderChangeCarrierMainChangeHistoryFragment :
        BaseFragment<OrderChangeCarrierMainChangeHistoryModel>() {

    private var swipeRefreshMoreLayout: SwipeRefreshMoreLayout? = null

    private var tvFilter1: TextView? = null
    private var tvFilter2: TextView? = null

    private var tvSearch: TextView? = null

    private var filterType = "3"

    override fun getLayout(): Int = R.layout.order_change_carrier_main_change_history_fragment

    override fun bindView(view: View, bundle: Bundle?) {
        swipeRefreshMoreLayout = view.findViewById(R.id.swipe_refresh_more_layout)
        tvFilter1 = view.findViewById(R.id.tv_filter_1)
        tvFilter2 = view.findViewById(R.id.tv_filter_2)

        tvSearch = view.findViewById(R.id.tv_search)
        tvSearch?.setOnClickListener {
            val c = context ?: return@setOnClickListener
            OrderChangeCarrierHistorySearchActivity.start(c)
        }

        tvFilter1?.setOnClickListener {
            when (filterType) {
                "4" -> {
                    filterType = "3"
                    tvFilter1?.isSelected = true
                    tvFilter2?.isSelected = false
                    swipeRefreshMoreLayout?.apply {
                        if (isRefreshing) {
                            isRefreshing = false
                        }
                        if (isLoadingMore) {
                            isLoadingMore = false
                        }
                        showLoading(true)
                        postDelayed({
                            hideLoading()
                            onAutoRefresh()
                        }, 1000)
                    }
                }
                else -> {
                    swipeRefreshMoreLayout?.onAutoRefresh()
                }
            }
        }
        tvFilter2?.setOnClickListener {
            when (filterType) {
                "3" -> {
                    filterType = "4"
                    tvFilter1?.isSelected = false
                    tvFilter2?.isSelected = true
                    swipeRefreshMoreLayout?.apply {
                        if (isRefreshing) {
                            isRefreshing = false
                        }
                        if (isLoadingMore) {
                            isLoadingMore = false
                        }
                        showLoading(true)
                        postDelayed({
                            hideLoading()
                            onAutoRefresh()
                        }, 1000)
                    }
                }
                else -> {
                    swipeRefreshMoreLayout?.onAutoRefresh()
                }
            }
        }

        val adapter = OrderChangeCarrierMainChangeHistoryAdapter()
        val emptyView = CommEmptyView.creatorDef(context)
        swipeRefreshMoreLayout?.apply {
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addItemDecorationSize(dp2px(7f))
            addOnItemListener(onItemClickListener)
            addOnItemChildClickListener(onItemChildClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    viewModel?.getNetInfo(nowPage, filterType)
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    viewModel?.getNetInfo(nowPage, filterType)
                }
            })
        }
    }

    override fun initData() {
        tvFilter1?.isSelected = true
        swipeRefreshMoreLayout?.onAutoRefresh()
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
        }
    }

    private val onItemChildClickListener = BaseQuickAdapter.OnItemChildClickListener { adapter, view, position ->
        val item = adapter.getItem(position)
        if (item is RspOrderChangeData) {
            when (view.id) {
                R.id.tv_copy -> {
                    //复制order
                    UtilTool.setCopyText(context!!, "运单号", item.orderId)
                    showToast("复制成功")
                }
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: PageList<RspOrderChangeData>?) {
        swipeRefreshMoreLayout?.onRefreshCompale(data)
    }
}
