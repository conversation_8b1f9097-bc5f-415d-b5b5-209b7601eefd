package com.zczy.plugin.order.stevedore.model;

import com.zczy.comm.http.entity.ResultData;

import java.util.List;

/**
 * 功能描述:装修费详情
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class ESteveDoreDeatil  extends ResultData {

    String money;

    String orderId;

    String plateNo;

    String createdTime;

    String remark;

    String state;

    List<String> urlArr;

    String examineOpinion;

    public String getMoney() {

        return money;
    }

    public String getOrderId() {

        return orderId;
    }

    public String getPlateNo() {

        return plateNo;
    }

    public String getCreatedTime() {

        return createdTime;
    }

    public String getRemark() {

        return remark;
    }

    public String getState() {

        return state;
    }

    public List<String> getUrlArr() {

        return urlArr;
    }

    public String getExamineOpinion() {

        return examineOpinion;
    }
}
