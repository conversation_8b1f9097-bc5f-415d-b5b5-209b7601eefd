package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.plugin.order.BaseOrderRequest

/**
 * PS: 5. 获取变更车辆
 *  http://wiki.zczy56.com/pages/viewpage.action?pageId=12355396
 * Created by sdx on 2019/2/22.
 */
data class Req5QueryChangePageInfo(
    var orderId: String? = null,
    var sourceId: String? = null,
    var consignorUserId: String? = null,
    var friendId: String = "",
    var nowPage: Int = 1,
    var pageSize: Int = 10
) : BaseOrderRequest<BaseRsp<RspPageCar>>("oms-app/orderChange/queryChangePageInfo")

data class RspPageCar(
    var isNeedHandle: String? = ""//安庆车老板需要特殊处理	 “0”无需特殊处理 1需要特殊处理（zczy-15868 新增）
) : PageList<RspVehicleData>()

data class RspVehicleData(
    var vehicleId: String = "",
    var plateNumber: String = "",
    var vehicleLoad: String = "",
    var isChoose: String = "",//1可选 0 不可选（zczy-15868 新增）
    var emissionStandard: String = "", //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    var fuelType: String = "",//燃油类型 “柴油”
    var newEnergyType: String = "", //燃油类型
    var fuelTypeStr: String = "",//   1 气 2 油
) {
    override fun toString(): String {
        return plateNumber
    }
}