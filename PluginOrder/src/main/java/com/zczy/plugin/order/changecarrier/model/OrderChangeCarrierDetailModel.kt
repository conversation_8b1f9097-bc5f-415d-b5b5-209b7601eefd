package com.zczy.plugin.order.changecarrier.model

import android.content.Context
import android.text.TextUtils
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResultSuccess
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.pluginserver.ACertificateServer
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.pluginserver.AWisdomServer
import com.zczy.plugin.order.changecarrier.req.*

class OrderChangeCarrierDetailModel : BaseViewModel() {
    //  10.运单变更详情
    fun queryChangeDetail(changeId: String) {
        execute(true,
            Req10QueryChangeDetail(changeId = changeId),
            IResultSuccess<BaseRsp<RspChangeDetail>> { t ->
                if (t.success()) {
                    setValue("onQueryChangeDetail", t.data)
                } else {
                    showDialogToast(t.msg)
                }
            })
    }

    // 变更决定 0不同意
    fun decideChangeReject(changeId: String, carrierContent: String) {
        execute(true,
            Req4decideChange(
                carrierContent = carrierContent,
                carrierState = "0",
                changeId = changeId,
                money = "0"
            ),
            IResultSuccess<BaseRsp<RspDecideChange>> { t ->
                if (t.success()) {
                    setValue("onDecideChange", t.data)
                } else {
                    showDialogToast(t.msg)
                }
            })
    }

    // 变更决定   1同意
    fun decideChangeAgree(context: Context, data: RspChangeDetail, changeId: String, carrierContent: String, esignFlag: String) {
        execute(true,
            Req8QueryCarrierBondMoneyConfig(orderId = data.orderId, carrierUserId = data.carrierUserId)
                .task
                .flatMap {
                    if (!it.success()) {
                        throw Exception(it.msg)
                    }
                    Req4decideChange(
                        carrierContent = carrierContent,
                        carrierState = "1",
                        changeId = changeId,
                        money = it.data?.carrierBondMoney ?: "",
                        esignFlag = esignFlag
                    ).task
                },
            IResultSuccess<BaseRsp<RspDecideChange>> { t ->
                if (t.success()) {
                    setValue("onDecideChange", t.data)
                } else if (TextUtils.equals("1113", t.code)) {
                    var dialog = DialogBuilder()
                    dialog.message = "承运方当前无可用诚意金订单，请提醒其支付诚意金订单后，再重新发起交易申请。"
                    dialog.setOKTextListener("支付诚意金") { dialog, _ ->
                        dialog.dismiss()
                        checkVerifyEarenstCanConfig()
                    }
                    showDialog(dialog)
                } else if (TextUtils.equals("1114", t.code)) {
                    //ZCZY-17015  高货值摘单会员终审、实名认证管控
                    var dialog = DialogBuilder()
                    dialog.message = t.msg
                    dialog.setOKTextListener("去完善") { dialog, _ ->
                        dialog.dismiss()
                        AMainServer.getPluginServer().userAuthent(context)
                    }
                    showDialog(dialog)
                } else if (TextUtils.equals("2001", t.code)) {
                    var dialog = DialogBuilder()
                    dialog.message = t.msg
                    dialog.isHideCancel = true
                    dialog.setOKTextListener("去处理") { dialog, _ ->
                        dialog.dismiss()
                        setValue("openUserAuthent")
                    }
                    showDialog(dialog)
                } else if (TextUtils.equals("2002", t.code)) {
                    //  2002 （去绑定银行卡按钮 此时有个字段bindingBankCard 为“1”）
                    var dialog = DialogBuilder()
                    dialog.message = t.msg
                    dialog.setOKTextListener("去绑定") { dialog, _ ->
                        dialog.dismiss()
                        AWisdomServer.getPluginServer().startWisdomBankListActivity(context)
                    }
                    showDialog(dialog)
                } else if (TextUtils.equals("2003", t.code)) {
                    //  2003 （去处理道路运输证按钮 此时有个字段uploadRoadLicense 为“1”）
                    var dialog = DialogBuilder()
                    dialog.message = t.msg
                    dialog.setOKTextListener("去处理") { dialog, _ ->
                        dialog.dismiss()
                        ACertificateServer.getPluginServer().startOverdueExpiredUI(context, t.data?.vehicleId, t.data?.plateNumber, t.data?.licenseState)
                    }
                    showDialog(dialog)
                } else {
                    showDialogToast(t.msg)
                }
            })


    }


    fun checkVerifyEarenstCanConfig() {
        execute(true, ReqVerifyEarenstCanConfig(), IResultSuccess {
            if (it.success()) {
                if (TextUtils.equals("", it.data?.type)) {
                    //免保
                    showDialogToast("尊敬的用户，您当前为中储平台免保用户，交易过程中无需支付诚意金。")
                } else {
                    setValue("onPlayMoney", it.data?.earnestMoney)
                }

            } else {
                showDialogToast(it.msg)
            }
        })
    }

}
