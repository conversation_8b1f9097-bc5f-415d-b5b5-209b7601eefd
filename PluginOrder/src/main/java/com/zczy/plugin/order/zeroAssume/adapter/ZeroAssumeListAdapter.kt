package com.zczy.plugin.order.zeroAssume.adapter

import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.zczy.comm.utils.ex.isNotNull
import com.zczy.comm.utils.getResColor
import com.zczy.comm.widget.itemdecoration.CommItemDecoration
import com.zczy.plugin.order.R
import com.zczy.plugin.order.waybill.WaybillButtonLayout
import com.zczy.plugin.order.waybill.entity.EWaybill
import com.zczy.plugin.order.zeroAssume.model.request.RspZeroAssumeList
import com.zczy.plugin.order.zeroAssume.model.request.getBatchApplyShow
import com.zczy.plugin.order.zeroAssume.model.request.getDesStr
import com.zczy.plugin.order.zeroAssume.model.request.getStateStr


/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR>  on  2023/3/23
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/
class ZeroAssumeListAdapter :
    BaseQuickAdapter<RspZeroAssumeList, BaseViewHolder>(R.layout.item_zero_assume_list) {
    private var childListClickListener: ChildListClickListener? = null
    private var buttonClickListener: WaybillButtonLayout.OnButtonClickListener? = null
    private var queryType = ""
    private var isFirst = true
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        val baseViewHolder = super.onCreateViewHolder(parent, viewType)
        val recyclerView = baseViewHolder.getView<RecyclerView>(R.id.recyclerView)
        val adapterV1 = ZeroAssumeChildListAdapter()
        adapterV1.apply {
            setOnItemChildClickListener { adapter, view, position ->
                val eWaybill = adapter.getItem(position) as EWaybill
                childListClickListener?.onClickToHandleBtn(view, eWaybill)
            }
            setOnItemClickListener { adapter, _, position ->
                val eWaybill = adapter.getItem(position) as EWaybill
                childListClickListener?.onClickChildItem(eWaybill)
            }
        }
        recyclerView?.let {
            it.layoutManager = LinearLayoutManager(mContext)
            it.addItemDecoration(
                CommItemDecoration.createVertical(
                    mContext,
                    getResColor(R.color.comm_divider_e3),
                    1
                )
            )
            it.setHasFixedSize(true)
            it.isNestedScrollingEnabled = false
            it.tag = adapterV1
            it.adapter = adapterV1
        }
        this.buttonClickListener?.let {
            adapterV1.setButtonViewClickListener(it)
        }
        return baseViewHolder
    }

    override fun convert(helper: BaseViewHolder, item: RspZeroAssumeList) {
        if (TextUtils.equals("2", queryType) && isFirst) {
            // 进行中
            item.isOpen = true
            isFirst = false
        }
        helper.apply {
            setText(R.id.tv_id, item.orderCarpoolingId)
            setText(R.id.tv_car_num, item.plateNumber)
            setText(R.id.tv_state, item.getStateStr())
            setText(R.id.tv_des, item.getDesStr())
            setGone(R.id.tv_apply, item.getBatchApplyShow())
            setGone(R.id.tv_change, TextUtils.equals("1", item.orderCarpoolingState) || TextUtils.equals("2", item.orderCarpoolingState))
            val iv = helper.getView<ImageView>(R.id.iv_open_close)
            iv.isSelected = item.isOpen == true
            setText(R.id.tv_open_close, if (item.isOpen) "收起明细" else "展开明细")
            val recyclerView = getView<RecyclerView>(R.id.recyclerView)
            if (TextUtils.equals("2", queryType)) {
                recyclerView.visibility = if (item.isOpen) View.VISIBLE else View.GONE
            }
            if (recyclerView.isNotNull) {
                val view = recyclerView as RecyclerView
                val tag = view.tag
                if (tag is ZeroAssumeChildListAdapter) {
                    if (TextUtils.equals("2", queryType)) {
                        tag.setNewData(item.subOrderList)
                    } else {
                        if (item.isOpen) {
                            tag.setNewData(item.wayBill)
                        } else {
                            tag.setNewData(emptyList())
                        }
                    }
                }
            }
            addOnClickListener(R.id.tv_copy)
            addOnClickListener(R.id.tv_apply)
            addOnClickListener(R.id.tv_change)
            addOnClickListener(R.id.tv_open_close)
            addOnClickListener(R.id.iv_open_close)
        }
    }


    fun setChildListClickListener(listener: ChildListClickListener): BaseQuickAdapter<*, *> {
        childListClickListener = listener
        return this
    }

    fun setButtonViewClickListener(buttonClickListener: WaybillButtonLayout.OnButtonClickListener) {
        this.buttonClickListener = buttonClickListener
    }

    fun setQueryType(queryType: String) {
        this.queryType = queryType
    }
    fun setIsFirst(b: Boolean) {
        this.isFirst = b
    }
}


interface ChildListClickListener {
    fun onClickToHandleBtn(view: View, waybill: EWaybill?)
    fun onClickChildItem(waybill: EWaybill?)
}

