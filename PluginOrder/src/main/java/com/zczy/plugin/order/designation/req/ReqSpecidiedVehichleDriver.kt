package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 *    author : Ssp
 *    date   : 2019/7/1 15:14
 *    desc   : 已指定司机车辆
 */
data class ReqSpecidiedVehichleDriver(
        /**
         * 运单号
         */
        var orderId: String = "",
        /**
         *司机id
         */
        var driverUserId: String = "",
        /**
         *车辆id
         */
        var vehicleId: String = "",
        /**
         *是否选择预付
         */
        var isAdvance: String = "",
        /**
         * 是否需要电子签 0 不要  1 只签合同  2 签约合同和承诺函
         * */
        var esignFlag: String = ""
) : BaseOrderRequest<BaseRsp<ResultData>>("oms-app/carrier/common/bossAssignDriverVehicle")
