package com.zczy.plugin.order.supervise.action;
/*=============================================================================================
 * 功能描述:省货物平台系统2.0 【回单上传】
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2020/10/30
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/


import android.text.TextUtils;

import androidx.work.WorkManager;

import com.hdgq.locationlib.LocationOpenApi;
import com.hdgq.locationlib.entity.ShippingNoteInfo;
import com.hdgq.locationlib.listener.OnResultListener;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.zczy.plugin.order.shipments.entity.ESDKInfoObj;
import com.zczy.plugin.order.supervise.req.ReqAddSdkRecord;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.annotations.NonNull;
import io.reactivex.functions.Function;

public class StopAction extends BaseActionServer implements OnResultListener, Function<StopAction, Boolean> {

    private int stopCount = 0;

    private ESDKInfoObj sdkInfoObj;
    private String remark ="";
    public StopAction(ESDKInfoObj sdkInfoObj) {
        this.sdkInfoObj = sdkInfoObj;
        this.remark = TextUtils.equals("1",sdkInfoObj.getChangeFlag())?"[01]货主更换收货地址":"";
    }
    public StopAction(ESDKInfoObj sdkInfoObj,String remark) {
        this.sdkInfoObj = sdkInfoObj;
        this.remark = remark;
    }
    @Override
    public void start() {
        //授权
        stopCount ++;
        RetrofitManager.executeSigin(Observable.just(StopAction.this).map(StopAction.this), new EmptyResult<>());
    }

    @Override
    public Boolean apply(@NonNull StopAction shippingNoteInfoServer) throws Exception {
        this.auth(sdkInfoObj.getConsignorSubsidiaryId(), new OnAuthListener() {
            @Override
            public void onAuthFailure(String code, String msg) {
                StopAction.this.onFailure(code, msg);
            }

            @Override
            public void onAuthSuccess(List<ShippingNoteInfo> list) {
                StopAction.this.post();
            }
        });
        return true;
    }

    private void post() {

        out(String.format("省货物平台系统2.0=Stop=>回单上传[构建数据] sdkInfoObj:%s", sdkInfoObj));

        ShippingNoteInfo[] shippingNoteNumbers = new ShippingNoteInfo[]{sdkInfoObj.getShippingNoteInfo()};;
        //收货
        LocationOpenApi.stop(AppCacheManager.getApplication(), sdkInfoObj.getVehicleNumber(), sdkInfoObj.getDriverName(), remark, shippingNoteNumbers, StopAction.this);

        //移除定时任务
        WorkManager.getInstance().cancelUniqueWork(sdkInfoObj.getOrderId());
    }

    @Override
    public void onSuccess(List<ShippingNoteInfo> list) {

        out(String.format("省货物平台系统2.0=Stop=>回单上传[成功] sdkInfoObj:%s,size:%s", sdkInfoObj, (list != null ? list.size() : 0)));
        //收货-发货，上报公司平台信息
        new ReqAddSdkRecord(sdkInfoObj.getOrderId(), "code:0, msg:省平台stop成功, type:Android").buildStop();
    }

    @Override
    public void onFailure(String code, String msg) {
        out(String.format("省货物平台系统2.0=Stop=>回单上传[失败]  sdkInfoObj:%s,code:%s,msg:%s", sdkInfoObj, code, msg));
        //收货,上报公司平台信息
        new ReqAddSdkRecord(sdkInfoObj.getOrderId(), String.format("code:%s, msg:%s, type:Android", code, msg)).buildStop();

        if (TextUtils.equals("888884", code) && stopCount < 3) {
            //有未开始的运单
            new ReStartAction(sdkInfoObj,remark).setNextAction(this).start();
        }
    }


}
