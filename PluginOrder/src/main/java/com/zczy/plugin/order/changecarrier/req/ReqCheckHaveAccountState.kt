package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 校验开户状态
 * */
class ReqCheckHaveAccountState(
    var sourceId: String? = null, //
    var consignorUserId: String? = null, //
) : BaseOrderRequest<BaseRsp<RspProcessOrderPay>>("oms-app/carrier/common/checkHaveAccountState") {
}

data class RspProcessOrderPay(
    var operateType: String? = null, //1联系客服 2开户（resultCode 1111）
) : ResultData()