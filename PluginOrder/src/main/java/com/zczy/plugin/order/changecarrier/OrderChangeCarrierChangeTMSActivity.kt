package com.zczy.plugin.order.changecarrier

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.certificate.UserAuthentActivity
import com.zczy.certificate.driver.DriverEmploymentRiskActivity
import com.zczy.certificate.vehiclemanage.carrier.CarrierCarRiskActivityV1
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.data.ERNToNativeEvent
import com.zczy.comm.data.help.getLoginInfo
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.pluginserver.AMainServer
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.ui.BaseDialog
import com.zczy.comm.utils.ex.setVisible
import com.zczy.comm.utils.getResColor
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.inputv2.InputViewClick
import com.zczy.comm.x5.X5WebActivity
import com.zczy.lib_zstatistics.sdk.ZStatistics
import com.zczy.overdue.boss.BossExpiredCertificateManagementActivity
import com.zczy.overdue.cyr.CyrExpiredCertificateManagementActivity
import com.zczy.overdue.cys.CysExpiredCertificateManagementActivity
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.bean.RxEventChange
import com.zczy.plugin.order.changecarrier.dialog.OrderCarrierChooseCarDialog
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierChangeModel
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.violate.OrderViolateMainActivity
import io.reactivex.Observable
import kotlinx.android.synthetic.main.order_change_carrier_change_tms_activity.*
import java.math.BigDecimal

/**
 * 变更承运 变更 (TMS货源
 */
class OrderChangeCarrierChangeTMSActivity : BaseActivity<OrderChangeCarrierChangeModel>() {

    private val sourceId by lazy { intent.getStringExtra(EXTRA_ORDER_ID) ?: "" }
    private val consignorUserId by lazy { intent.getStringExtra("consignorUserId") ?: "" }
    private val req: Req3ChangeOne = Req3ChangeOne()
    private val reqVertifyCarType: VertifyCarTypeBeanReq = VertifyCarTypeBeanReq()

    private val mAppToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }
    private val viewHelp by lazy { findViewById<View>(R.id.view_help) }

    //    private val viewDivider by lazy { findViewById<View>(R.id.view_divider) }
    private val chooseCarrier by lazy { findViewById<InputViewClick>(R.id.choose_carrier) }
    private val chooseCar by lazy { findViewById<InputViewClick>(R.id.choose_car) }

    private val btnCommit by lazy { findViewById<TextView>(R.id.btn_commit) }
    private val tvOverLoad by lazy { findViewById<TextView>(R.id.tv_over_load) }

    private val viewHInt by lazy { findViewById<View>(R.id.view_hint) }

    private var rspChangeOverLoad: RspChangeOverLoad = RspChangeOverLoad()

    // 是否开票
    private val isBill by lazy { intent.getBooleanExtra("isBill", false) }

    private val ly_noChoose_carrier by lazy { findViewById<View>(R.id.ly_noChoose_carrier) }

    private var esignFlag: String = "0"

    var relation = CommServer.getUserServer().login.relation
    var login = CommServer.getUserServer().login

    companion object {
        private const val EXTRA_ORDER_ID = "extra_order_id"
        private const val REQUEST_CARRIER = 0x32
        private const val REQUEST_CAR = 0x33

        @JvmStatic
        @JvmOverloads
        fun start(
            fragment: Fragment,
            sourceId: String,
            consignorUserId: String,
            isBill: Boolean,
            requestCode: Int = -1
        ) {
            val intent = Intent(fragment.context, OrderChangeCarrierChangeTMSActivity::class.java)
            intent.putExtra(EXTRA_ORDER_ID, sourceId)
            intent.putExtra("consignorUserId", consignorUserId)
            intent.putExtra("isBill", isBill)
            fragment.startActivityForResult(intent, requestCode)
        }
    }


    override fun getLayout(): Int = R.layout.order_change_carrier_change_tms_activity

    override fun bindView(bundle: Bundle?) {
        /*
        * 变更承运，初始状态配置
        * */
        changemine.isSelected = true
        changother.isSelected = false
        choose_carrier.visibility = View.GONE
        ly_noChoose_carrier.visibility = View.GONE
        tv_boss_select_driver.visibility = View.GONE
        val relation = CommServer.getUserServer()?.login?.relation
        if (relation != null) {
            viewHInt.setVisible(relation.isCarrier)
            changemine.setVisible(true)
        } else {
            viewHInt.setVisible(false)
        }
        chooseCarrier.tvTitle.typeface = Typeface.DEFAULT_BOLD
        chooseCarrier.setListener(listener)
        chooseCar.tvTitle.typeface = Typeface.DEFAULT_BOLD
        chooseCar.setListener(listener)
        changemine.setOnClickListener {
            if (changemine.isSelected) {
                return@setOnClickListener
            } else {
                changemine.isSelected = true
                changother.isSelected = false
                choose_carrier.visibility = View.GONE
                ly_noChoose_carrier.visibility = View.GONE
                chooseCar.content = ""
                chooseCarrier.content = ""
                req.plateNumber = ""
                req.vehicleId = ""
                req.friendId = ""
                reqVertifyCarType.vehicleId = ""
            }
        }
        changother.setOnClickListener {
            if (changother.isSelected) {
                return@setOnClickListener
            } else {
                changemine.isSelected = false
                changother.isSelected = true
                choose_carrier.visibility = View.VISIBLE
                chooseCar.content = ""
                chooseCarrier.content = ""
                req.plateNumber = ""
                req.vehicleId = ""
                req.friendId = ""
                reqVertifyCarType.vehicleId = ""
            }
        }
        mAppToolber.setRightOnClickListener {
            //点击客服
            val mainServer = AMainServer.getPluginServer()
            mainServer?.openLineServerHaveParams(
                this@OrderChangeCarrierChangeTMSActivity,
                "{\"customField12\":\"变更承运\"}"
            )
            ZStatistics.onViewClick(this@OrderChangeCarrierChangeTMSActivity, "ChangeTransfer_service")

        }
        mAppToolber.setLeftOnClickListener {
            ZStatistics.onViewClick(this@OrderChangeCarrierChangeTMSActivity, "ChangeTransfer_back")
            finish()
        }

        bindClickEvent(btnCommit)
        bindClickEvent(viewHelp)

        val changes1 = RxTextView.textChanges(chooseCarrier.contentView)
        val changes2 = RxTextView.textChanges(chooseCar.contentView)
        Observable
            .combineLatest(
                changes1, changes2
            ) { t1, t2 ->
                if (changother.isSelected) {
                    t1.isNotEmpty() && t2.isNotEmpty()
                } else {
                    t1.isNotEmpty() || t2.isNotEmpty()
                }
            }
            .subscribe { b ->

                btnCommit.isEnabled = b
            }
            .let {
                putDisposable(it)
            }
    }

    override fun initData() {
        req.sourceId = sourceId
        req.orderId = sourceId
        viewModel?.onChangeOverLoadTMS(sourceId, consignorUserId)
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.view_help -> {
                gotoHelp()
            }

            R.id.btn_commit -> {
                //承运商 && 司机变更他人 不走电子签逻辑
                continueLogic()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            when (requestCode) {
                REQUEST_CARRIER -> {
                    val obtainData = OrderChangeCarrierPeopleListActivity.obtainData(data)
                    req.friendId = obtainData.friendId
                    if (!TextUtils.equals(chooseCarrier.content, obtainData.customerName)) {
                        chooseCar.content = ""
                        req.plateNumber = ""
                        req.vehicleId = ""
                        reqVertifyCarType.vehicleId = ""
                    }
                    chooseCarrier.content = obtainData.customerName
                    reqVertifyCarType.driverUserId = obtainData.friendId

                    //ZCZY-7178  车老板提现流程优化
                    val relation = CommServer.getUserServer().login.relation
                    val isBoss = relation.isBoss
                    if (isBoss && TextUtils.equals("0", obtainData.carrierFinalPass)) {
                        tv_boss_select_driver.setVisible(true)
                    } else {
                        tv_boss_select_driver.setVisible(false)
                    }
                }

                REQUEST_CAR -> {
                    val obtainData = OrderChangeCarrierChooseAllCarActivity.obtainData(data)
                    chooseCar.content = obtainData?.plateNumber ?: ""
                    req.plateNumber = obtainData?.plateNumber ?: ""
                    req.vehicleId = obtainData?.vehicleId ?: ""
                    reqVertifyCarType.vehicleId = obtainData?.vehicleId ?: ""
                }
            }
        }
    }

    private fun gotoHelp() {
        val url =
            HttpURLConfig.getWebUrl() + "form_h5/order/index.html?_t=" + System.currentTimeMillis() + "#/changeCarrierHelp"
        X5WebActivity.start(this, url, "使用帮助")
    }

    private val listener = object : InputViewClick.Listener() {
        override fun onClick(viewId: Int, view: InputViewClick, content: String) {
            when (viewId) {
                R.id.choose_carrier -> {
                    OrderChangeCarrierPeopleListActivity.start(
                        this@OrderChangeCarrierChangeTMSActivity,
                        sourceId = sourceId,
                        requestCode = REQUEST_CARRIER,
                        consignorUserId = consignorUserId
                    )
                    ZStatistics.onViewClick(
                        this@OrderChangeCarrierChangeTMSActivity,
                        "ChangeTransfer_change1"
                    )
                }

                R.id.choose_car -> {

                    viewModel?.queryChangePageInfoTMS(sourceId, getFriendId(), consignorUserId, 1)

                    ZStatistics.onViewClick(
                        this@OrderChangeCarrierChangeTMSActivity,
                        "ChangeTransfer_change2"
                    )
                }
            }
        }
    }

    @LiveDataMatch
    open fun onQueryChangePageInfo(data: RspPageCar?) {
        if (data == null || data.rootArray == null) return
        val find = data.rootArray?.find { it.vehicleId == req.vehicleId }
        if (data.totalSize > 9) {
            OrderChangeCarrierChooseAllCarTMSActivity.start(
                this@OrderChangeCarrierChangeTMSActivity,
                sourceId,
                getFriendId(),
                find,
                REQUEST_CAR,
                changemine.isSelected,
                consignorUserId
            )
        } else {
            OrderCarrierChooseCarDialog
                .instance(data.rootArray, true, true)
                .setSelectItem(find)
                .setFlatMap { it.plateNumber }
                .setToastText(
                    if (TextUtils.equals("1", data.isNeedHandle)) {
                        "您本月交易车辆已超出平台当前角色管控要求，请选择可选车辆，如有疑问可咨询平台客服。"
                    } else {
                        ""
                    }
                )
                .setNoCheck { TextUtils.equals("0", it.isChoose) }
                .setNoCheckTxt {
                    if (TextUtils.equals("0", it.isChoose)) {
                        "4"
                    } else {
                        ""
                    }
                }
                .setChooseListener { s, dialog ->
                    val req = ReqAssignVehicleTransportCheck()
                    req.plateNumber = s.plateNumber
                    viewModel.assignVehicleTransportCheck(req, {
                        if (TextUtils.equals("1", it.data?.isTransportFlag)) {
                            val dialog2 = DialogBuilder()
                            dialog2.title = "提示"
                            dialog2.message = it.msg
                            dialog2.isHideCancel = true
                            dialog2.setOKTextListener("我知道了") { dialog1: DialogBuilder.DialogInterface, which: Int ->
                                dialog1.dismiss()
                                selectCarStep1(s, dialog)
                            }
                            showDialog(dialog2)
                        } else {
                            selectCarStep1(s, dialog)
                        }
                    }, {
                        selectCarStep1(s, dialog)
                    })

                }
                .show(this@OrderChangeCarrierChangeTMSActivity)
        }
    }

    private fun selectCarStep1(s: RspVehicleData, dialog: BaseDialog) {
        dialog.dismiss()
        chooseCar.content = s.plateNumber
        req.plateNumber = s.plateNumber
        req.vehicleId = s.vehicleId
        reqVertifyCarType.vehicleId = s.vehicleId
        setTvOverLoad(s.vehicleLoad)
    }

    @LiveDataMatch
    open fun onChangeOne() {
        setResult(Activity.RESULT_OK)
        finish()
    }

    @LiveDataMatch
    open fun onChangeOneError(data: Rsp3ChangeOne?, msg: String) {
        data?.let {
            // "1"-待货主处理，"2"-待承运方处理，"3"-平台介入中，"4"-咨询单介入中
            when (it.orderBreachType) {
                "2" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("去处理")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeTMSActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "1" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("去取消")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeTMSActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "3" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = false
                    dialogBuilder.cancelText = "好的"
                    dialogBuilder.setOKText("查看详情")
                    dialogBuilder.cancelListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ ->
                            dialog?.dismiss()
                            OrderViolateMainActivity.start(this@OrderChangeCarrierChangeTMSActivity)
                        }
                    showDialog(dialogBuilder)
                }

                "4" -> {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = msg
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.isCancelable = false
                    dialogBuilder.isHideCancel = true
                    dialogBuilder.setOKText("我知道了")
                    dialogBuilder.okListener =
                        DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                    showDialog(dialogBuilder)
                }

                else -> {
                    //是否展示上传从业资格证按钮 1 展示 0 不展示
                    when (it.isShowUploadFlag) {
                        "1" -> {
                            //提示 有按钮
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "温馨提示"
                            dialogBuilder.message = msg
                            dialogBuilder.gravity = Gravity.CENTER
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("上传从业资格证")
                            dialogBuilder.okListener =
                                DialogBuilder.DialogInterface.OnClickListener { dialog: DialogBuilder.DialogInterface, which: Int ->
                                    dialog.dismiss()
                                    // 上传从业资格证
                                    DriverEmploymentRiskActivity.start(this)
                                }
                            showDialog(dialogBuilder)
                        }

                        else -> {
                            val dialogBuilder = DialogBuilder()
                            dialogBuilder.title = "提示"
                            dialogBuilder.message = msg
                            dialogBuilder.gravity = Gravity.CENTER
                            dialogBuilder.isCancelable = false
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOKText("我知道了")
                            dialogBuilder.okListener =
                                DialogBuilder.DialogInterface.OnClickListener { dialog, _ -> dialog?.dismiss() }
                            showDialog(dialogBuilder)
                        }
                    }
                }
            }

        }
    }

    @LiveDataMatch
    open fun onCheckErrorState(data: Rsp3ChangeOne, msg: String) {
        val dialogBuilder = DialogBuilder()
        dialogBuilder.title = "提示"
        dialogBuilder.message = msg
        val userErrorState = data.userErrorState
        val vehicleErrorState = data.vehicleErrorState

        //司机
        when (userErrorState) {
            "1" -> {
                //提交材料按钮
                dialogBuilder.setOKTextListener(
                    "去完善"
                ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()

                    if (TextUtils.equals("2", data.userVehicleType)) {
                        DriverEmploymentRiskActivity.startUi(this@OrderChangeCarrierChangeTMSActivity)
                    } else {
                        UserAuthentActivity.start(this@OrderChangeCarrierChangeTMSActivity)
                    }
                }
                showDialog(dialogBuilder)
            }

            "2" -> {
                //联系客服
                dialogBuilder.setOKTextListener(
                    "联系客服"
                ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                    dialog.dismiss()
                    AMainServer.getPluginServer()
                        .openLineServer(this@OrderChangeCarrierChangeTMSActivity)
                }
                showDialog(dialogBuilder)
            }

            else -> {
                // 车辆状态
                when (vehicleErrorState) {
                    "1" -> {
                        //提交材料按钮
                        dialogBuilder.setOKTextListener(
                            "去完善"
                        ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                            dialog.dismiss()
                            CarrierCarRiskActivityV1.jumpPage(
                                this@OrderChangeCarrierChangeTMSActivity,
                                req.vehicleId
                            )
                        }
                        showDialog(dialogBuilder)
                    }

                    "2" -> {
                        //联系客服
                        dialogBuilder.setOKTextListener(
                            "联系客服"
                        ) { dialog: DialogBuilder.DialogInterface, which: Int ->
                            dialog.dismiss()
                            AMainServer.getPluginServer()
                                .openLineServer(this@OrderChangeCarrierChangeTMSActivity)
                        }
                        showDialog(dialogBuilder)
                    }

                    else -> {
                        dialogBuilder.isHideCancel = true
                        showDialog(dialogBuilder)
                    }
                }
            }
        }
    }

    private fun getFriendId(): String {
        if (chooseCarrier.content.isEmpty()) {
            val loginInfo = getLoginInfo()
            return loginInfo?.userId ?: ""
        } else {
            return req.friendId;
        }
    }

    private fun continueLogic() {
        // 1：变更人  2：变更车  3：变更人和车
        req.consignorUserId = consignorUserId
        when {
            chooseCarrier.content.isEmpty() && chooseCar.content.isEmpty() -> {
                showToast("必须输入一项")
                return
            }

            chooseCarrier.content.isEmpty() -> {
                req.changeType = "2"
            }

            chooseCar.content.isEmpty() -> {
                req.changeType = "1"
            }

            else -> {
                req.changeType = "3"
            }
        }
        val req1 = ReqQueryWhTmsUserAndVehicleState()
        req1.vehicleId = req.vehicleId
        req1.driverUserId = req.friendId
        req1.consignorUserId = req.consignorUserId
        req1.sourceId = sourceId
        req1.changeType = req.changeType
        viewModel?.queryWhTmsUserAndVehicleState(req1)
        ZStatistics.onViewClick(this@OrderChangeCarrierChangeTMSActivity, "ChangeTransfer_confirm")
    }


    @LiveDataMatch
    open fun onChangeOverLoad(data: RspChangeOverLoad) {
        rspChangeOverLoad = data
    }

    @LiveDataMatch
    open fun onClearCar() {
        chooseCar.content = ""
        req.plateNumber = ""
        req.vehicleId = ""
        reqVertifyCarType.vehicleId = ""
    }

    /**
     * 进行重量校验
     *
     * @param actualWeight 货物吨位
     */
    @SuppressLint("SetTextI18n")
    fun setTvOverLoad(vehicleLoad: String) {
        val cargoCategory = rspChangeOverLoad.cargoCategory
        if (TextUtils.isEmpty(cargoCategory)) {
            return
        }
        if (!TextUtils.equals("1", cargoCategory)) {
            return
        }
        val actualWeight = rspChangeOverLoad.weight
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(actualWeight)) {
            return
        }
        if (TextUtils.isEmpty(vehicleLoad)) {
            return
        }
        val aDouble = actualWeight.toDouble()
        val bDouble = vehicleLoad.toDouble()
        val cDouble = aDouble - bDouble
        if (cDouble > 0) {
            this.tvOverLoad.visibility = View.VISIBLE
            val divide = divide(cDouble, bDouble, 2)
            if (divide > 30) {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb4040))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff4f8_rtg)
            } else {
                this.tvOverLoad.text = "超载$divide%"
                this.tvOverLoad.setTextColor(getResColor(R.color.color_fb6b40))
                this.tvOverLoad.setBackgroundResource(R.drawable.file_over_load_fff3f1_rtg)
            }
        } else {
            this.tvOverLoad.visibility = View.GONE
        }
    }

    /**
     * 两个double类型的数相除，保留两位小数
     *
     * @param a
     * @param b
     * @param scale
     * @return
     */
    private fun divide(a: Double, b: Double, scale: Int): Long {
        val bd1 = BigDecimal(a.toString())
        val bd2 = BigDecimal(b.toString())
        val bd3 = bd1.divide(bd2, scale, BigDecimal.ROUND_HALF_UP).toDouble()
        return Math.round(bd3 * 100.00)
    }

    @RxBusEvent(from = "电子签后发起变更")
    open fun onRxEventBusH5(data: RxEventChange) {
        req.esignFlag = this.esignFlag
        viewModel?.changeOneTms(this@OrderChangeCarrierChangeTMSActivity, req)
    }

    @LiveDataMatch
    open fun onQueryWhTmsUserAndVehicleState(data: RspWhTmsUserAndVehicleState) {
        if (!TextUtils.equals("1", data.userAndVehicleState)) {
            if (!TextUtils.isEmpty(chooseCarrier.content) && !TextUtils.isEmpty(chooseCar.content)) {
                //变更人和车
                if (isBill || login.relation.isCarrier) {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = "是否确认发起变更"
                    dialog.setCancelTextListener("取消") { dialog, which ->
                        dialog.dismiss()
                    }
                    dialog.setOKTextListener("确认") { dialog, which ->
                        dialog.dismiss()
                        if (isBill && login.relation.isBoss) {
                            X5WebNoToolBarActivity.startContentUI(
                                this,
                                HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/tmsContract?sourceId=" + sourceId + "&consignorUserId=" + consignorUserId + "&carrierUserId=" + login.userId + "&plateNumber=" + req.plateNumber + "&vehicleId=" + req.vehicleId + "&friendId=" + req.friendId + "&isChange=1",
                                "1"
                            )
                        } else {
                            viewModel?.changeOneTms(this@OrderChangeCarrierChangeTMSActivity, req)
                        }
                    }
                    showDialog(dialog)
                } else {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = "是否确认变更?"
                    dialog.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, which: Int ->
                        dialog.dismiss()
                        viewModel?.changeOneTms(this@OrderChangeCarrierChangeTMSActivity, req)
                    }
                    dialog.setCancelTextListener("取消") { dialog: DialogBuilder.DialogInterface, which: Int ->
                        dialog.dismiss()
                    }
                    showDialog(dialog)
                }
            } else {
                if (isBill) {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = "变更需要货主同意确认，是否确认变更"
                    dialog.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, which: Int ->
                        dialog.dismiss()
                        X5WebNoToolBarActivity.startContentUI(
                            this,
                            HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/tmsContract?sourceId=" + sourceId + "&consignorUserId=" + consignorUserId + "&carrierUserId=" + login.userId + "&plateNumber=" + req.plateNumber + "&vehicleId=" + req.vehicleId + "&friendId=" + req.friendId + "&isChange=1",
                            "1"
                        )
                    }
                    showDialog(dialog)
                } else {
                    val dialog = DialogBuilder()
                    dialog.title = "提示"
                    dialog.message = "是否确认变更?"
                    dialog.setOKTextListener("确定") { dialog: DialogBuilder.DialogInterface, which: Int ->
                        dialog.dismiss()
                        viewModel?.changeOneTms(this@OrderChangeCarrierChangeTMSActivity, req)
                    }
                    dialog.setCancelTextListener("取消") { dialog: DialogBuilder.DialogInterface, which: Int ->
                        dialog.dismiss()
                    }
                    showDialog(dialog)
                }
            }
        } else {
            //证件管理
            if (TextUtils.equals("1", data.operationFlag)) {
                val dialog = DialogBuilder()
                dialog.title = "提示"
                dialog.message = data.resultMsg ?: ""
                dialog.isHideCancel = true
                dialog.setOKTextListener("去处理") { dialog, which ->
                    val tab = if (TextUtils.equals("2", data.userAndVehicleType)) {
                        1
                    } else {
                        0
                    }
                    if (relation.isCarrier) {
                        CyrExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeTMSActivity, tab)
                    } else if (relation.isBoss) {
                        BossExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeTMSActivity, tab)
                    } else if (relation.isCys) {
                        CysExpiredCertificateManagementActivity.jumpPage(this@OrderChangeCarrierChangeTMSActivity, tab)
                    }
                    if (TextUtils.equals("2", data.userAndVehicleType)) {
                        val params2 = HashMap<String, Any>()
                        params2["vehicleId"] = req.vehicleId
                        AMainServer.getPluginServer().sendEventBusToRN("event_H5_page", params2)
                    }
                }
                showDialog(dialog)
            } else {
                showDialogToast(data.resultMsg)
            }
        }
    }

    @LiveDataMatch
    open fun onRspQueryProcessOrderPay(data: BaseRsp<RspQueryProcessOrderPay>) {
        val dialog = DialogBuilder()
        dialog.title = "提示"
        if (TextUtils.equals("1", data.data?.payStatus)) {
            dialog.message = "您已支付本单技术服务费，请确认是否变更"
        } else {
            dialog.message = "是否确认发起变更"
        }
        dialog.setCancelTextListener("取消") { dialog, which ->
            dialog.dismiss()
        }
        dialog.setOKTextListener("确认") { dialog, which ->
            dialog.dismiss()
            if (isBill && login.relation.isBoss) {
                X5WebNoToolBarActivity.startContentUI(
                    this,
                    HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/tmsContract?sourceId=" + sourceId + "&consignorUserId=" + consignorUserId + "&carrierUserId=" + login.userId + "&plateNumber=" + req.plateNumber + "&vehicleId=" + req.vehicleId + "&friendId=" + req.friendId + "&isChange=1",
                    "1"
                )
            } else {
                viewModel?.changeOneTms(this@OrderChangeCarrierChangeTMSActivity, req)
            }
        }
        showDialog(dialog)
    }
}
