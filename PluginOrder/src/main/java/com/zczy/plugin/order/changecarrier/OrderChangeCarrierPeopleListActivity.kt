package com.zczy.plugin.order.changecarrier

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.dp2px
import com.zczy.comm.utils.json.toJson
import com.zczy.comm.utils.json.toJsonObject
import com.zczy.comm.widget.AppToolber
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.adapter.OrderChangeCarrierPeopleAdapter
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierPeopleListModel
import com.zczy.plugin.order.changecarrier.req.RspFriendData
import com.zczy.plugin.order.designation.req.ReqAssignDriverTransportCheck

/**
 * 变更承运 选择承运人列表
 */
class OrderChangeCarrierPeopleListActivity :
    BaseActivity<OrderChangeCarrierPeopleListModel>() {

    private val orderId by lazy { intent?.getStringExtra(ORDER_ID) ?: "" }
    private val sourceId by lazy { intent?.getStringExtra("sourceId") ?: "" }
    private val consignorUserId by lazy { intent?.getStringExtra("consignorUserId") ?: "" }

    private val btnSearch by lazy { findViewById<TextView>(R.id.btn_search) }

    private val editSearch by lazy { findViewById<EditText>(R.id.edit_search) }
    private val btnClear by lazy { findViewById<ImageView>(R.id.btn_clear) }
    private val appToolber by lazy { findViewById<AppToolber>(R.id.appToolber) }

    private val swipeRefreshMoreLayout by lazy { findViewById<SwipeRefreshMoreLayout>(R.id.swipe_refresh_more_layout) }

    private var key = ""

    companion object {
        private const val EXTRA_DATA = "extra_data"
        private const val ORDER_ID = "order_id"


        fun start(fragment: Activity, orderId: String, requestCode: Int) {
            val intent = Intent(fragment, OrderChangeCarrierPeopleListActivity::class.java)
            intent.putExtra(ORDER_ID, orderId)
            fragment.startActivityForResult(intent, requestCode)
        }

        fun start(fragment: Activity, sourceId: String, requestCode: Int, consignorUserId: String) {
            val intent = Intent(fragment, OrderChangeCarrierPeopleListActivity::class.java)
            intent.putExtra("sourceId", sourceId)
            intent.putExtra("consignorUserId", consignorUserId)
            fragment.startActivityForResult(intent, requestCode)
        }

        fun obtainData(intent: Intent?): RspFriendData {
            return intent?.getStringExtra(EXTRA_DATA)?.toJsonObject(RspFriendData::class.java)
                ?: RspFriendData()
        }
    }

    override fun getLayout(): Int = R.layout.order_change_carrier_people_list_activity

    override fun bindView(bundle: Bundle?) {
        val adapter = OrderChangeCarrierPeopleAdapter()
        appToolber.tvRight.text = adapter.getShowMobile()
        appToolber.setRightOnClickListener {
            adapter.hideMobilePart()
            appToolber.tvRight.text = adapter.getShowMobile()
        }

        swipeRefreshMoreLayout.apply {
            setAdapter(adapter, true)
            setEmptyView(CommEmptyView.creatorDef(this@OrderChangeCarrierPeopleListActivity))
            addItemDecorationSize(dp2px(0.5f))
            addOnItemListener(onItemClickListener)
            setOnLoadListener(object : OnLoadingListener {
                override fun onRefreshUI(nowPage: Int) {
                    if (TextUtils.isEmpty(sourceId)) {
                        viewModel?.queryFriendsInfo(nowPage, key, orderId = orderId)
                    } else {
                        viewModel?.queryFriendsInfoTMS(nowPage, key, sourceId = sourceId, consignorUserId = consignorUserId)
                    }
                }

                override fun onLoadMoreUI(nowPage: Int) {
                    if (TextUtils.isEmpty(sourceId)) {
                        viewModel?.queryFriendsInfo(nowPage, key, orderId = orderId)
                    } else {
                        viewModel?.queryFriendsInfoTMS(nowPage, key, sourceId = sourceId, consignorUserId = consignorUserId)
                    }
                }
            })
        }

        RxTextView.textChanges(editSearch)
            .map(CharSequence::toString)
            .subscribe {
                btnClear.visibility = if (it.isEmpty()) View.GONE else View.VISIBLE
            }
            .apply {
                putDisposable(this)
            }

        bindClickEvent(btnSearch)
        bindClickEvent(btnClear)
    }

    override fun initData() {
        swipeRefreshMoreLayout.onAutoRefresh()
    }

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.btn_clear -> {
                editSearch.setText("")
                key = ""
                swipeRefreshMoreLayout.onAutoRefresh()
            }

            R.id.btn_search -> {
                val inputManager = editSearch.context.applicationContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                inputManager.hideSoftInputFromWindow(editSearch.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
                key = editSearch.text.toString().trim()
                swipeRefreshMoreLayout.onAutoRefresh()
            }
        }
    }

    private val onItemClickListener = object : OnItemClickListener() {
        override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
            val item = adapter.getItem(position)
            if (item is RspFriendData) {
                val req = ReqAssignDriverTransportCheck()
                req.driverUserId = item.friendId
                viewModel.assignDriverTransportCheck(req, {
                    if (TextUtils.equals("1", it.data?.isTransportFlag)) {
                        val dialog1 = DialogBuilder()
                        dialog1.title = "提示"
                        dialog1.message = it.msg
                        dialog1.isHideCancel = true
                        dialog1.setOKTextListener("我知道了") { dialogInterface, _ ->
                            dialogInterface.dismiss()
                            intent.putExtra(EXTRA_DATA, item.toJson())
                            setResult(Activity.RESULT_OK, intent)
                            finish()
                        }
                        showDialog(dialog1)
                    } else {
                        intent.putExtra(EXTRA_DATA, item.toJson())
                        setResult(Activity.RESULT_OK, intent)
                        finish()
                    }
                }, {
                    intent.putExtra(EXTRA_DATA, item.toJson())
                    setResult(Activity.RESULT_OK, intent)
                    finish()
                })
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: PageList<RspFriendData>?) {
        swipeRefreshMoreLayout.onRefreshCompale(data)
    }

}
