package com.zczy.plugin.order.stevedore;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.rx.IResultSuccess;
import com.sfh.lib.rx.ui.UtilRxView;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.widget.EditTextCloseView;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.adapter.SearchOrderAdapter;
import com.zczy.plugin.order.stevedore.model.ESearchOrder;
import com.zczy.plugin.order.stevedore.model.StevedoreModel;

/***
 * 查询录入装卸货运单列表
 */
public class SearchOrderListActivity extends AbstractLifecycleActivity<StevedoreModel> {

    public static void start(Activity context,int requestCode) {
        Intent starter = new Intent(context, SearchOrderListActivity.class);
        context.startActivityForResult(starter,requestCode);
    }

    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.order_stevedore_order_list_activity);

        final EditTextCloseView et_search = findViewById(R.id.et_search);

        swipeRefreshMoreLayout = findViewById(R.id.swipe_refresh_more_layout);
        swipeRefreshMoreLayout.setAdapter(new SearchOrderAdapter(), true);
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creator(this, R.drawable.comm_recycler_empty_2_nothing_info, "没有找到相关信息"));
        swipeRefreshMoreLayout.setOnLoadListener2(new OnLoadingListener2() {
            @Override
            public void onLoadUI(int i) {
                getViewModel(StevedoreModel.class).searchListOrder(i, et_search.getText().toString());
            }
        });
        swipeRefreshMoreLayout.addOnItemListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter baseQuickAdapter, View view, int i) {
                ESearchOrder dataItem = (ESearchOrder) baseQuickAdapter.getItem(i);
                Intent intent = new Intent();
                intent.putExtra("orderId", dataItem.getOrderId());
                setResult(RESULT_OK, intent);
                finish();
            }
        });

        UtilRxView.afterTextChangeEvents(et_search, 500, new IResultSuccess<CharSequence>() {
            @Override
            public void onSuccess(CharSequence charSequence) throws Exception {
                swipeRefreshMoreLayout.onAutoRefresh();
            }
        });
        swipeRefreshMoreLayout.onAutoRefresh();
    }

    @LiveDataMatch(tag = "分页")
    public void onPageSuccess(PageList<ESearchOrder> pageList) {

        this.swipeRefreshMoreLayout.onRefreshCompale(pageList);
    }
}
