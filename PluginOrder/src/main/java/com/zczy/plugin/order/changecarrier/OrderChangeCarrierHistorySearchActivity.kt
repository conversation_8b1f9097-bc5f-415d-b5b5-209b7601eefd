package com.zczy.plugin.order.changecarrier

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.jakewharton.rxbinding2.widget.RxTextView
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.utils.UtilTool
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.widget.pulltorefresh.CommEmptyView
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout
import com.zczy.plugin.order.R
import com.zczy.plugin.order.changecarrier.adapter.OrderChangeCarrierMainChangeHistoryAdapter
import com.zczy.plugin.order.changecarrier.model.OrderChangeCarrierMainChangeHistoryModel
import com.zczy.plugin.order.changecarrier.req.RspOrderChangeData
import io.reactivex.android.schedulers.AndroidSchedulers

/**
 * 变更承运 搜索历史
 *
 * <AUTHOR>
 */
class OrderChangeCarrierHistorySearchActivity : BaseActivity<OrderChangeCarrierMainChangeHistoryModel>() {

    private val imgback by lazy { findViewById<ImageView>(R.id.img_back) }
    private val edSearch by lazy { findViewById<EditText>(R.id.ed_search) }
    private val imgSearchClear by lazy { findViewById<ImageView>(R.id.img_search_clear) }
    private val btnSearch by lazy { findViewById<TextView>(R.id.btn_search) }
    private val refreshMoreLayout by lazy { findViewById<SwipeRefreshMoreLayout>(R.id.swipe_refresh_more_layout) }

    private var searchKey = ""
    override fun getLayout(): Int {
        return R.layout.order_change_carrier_history_search_activity
    }

    override fun bindView(bundle: Bundle?) {
        bindClickEvent(imgback)
        bindClickEvent(btnSearch)
        bindClickEvent(imgSearchClear)

        edSearch.setOnKeyListener(View.OnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_UP) {
                if (searchKey.isEmpty()) {
                    showToast("请输入搜索关键词")
                    return@OnKeyListener false
                }
                val imm = v.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                if (imm.isActive) {
                    imm.hideSoftInputFromWindow(v.applicationWindowToken, 0)
                }
                // 软键盘点击回车的事件处理
                viewModel?.getNetInfo(0, searchKey)
                return@OnKeyListener true
            }
            false
        })

        RxTextView.textChanges(edSearch)
                .observeOn(AndroidSchedulers.mainThread())
                .map(CharSequence::toString)
                .subscribe {
                    when {
                        it.isEmpty() -> {
                            imgSearchClear.visibility = View.GONE
                        }
                        else -> {
                            imgSearchClear.visibility = View.VISIBLE
                        }
                    }
                    searchKey = it
                }
                .apply {
                    putDisposable(this)
                }

        refreshMoreLayout.apply {

            val adapter = OrderChangeCarrierMainChangeHistoryAdapter()
            val emptyView = CommEmptyView.creatorDef(context)
            setAdapter(adapter, true)
            setEmptyView(emptyView)
            addOnItemListener(object : OnItemClickListener() {
                override fun onSimpleItemClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                }

                override fun onItemChildClick(adapter: BaseQuickAdapter<*, *>, view: View, position: Int) {
                    super.onItemChildClick(adapter, view, position)
                    val item = adapter.getItem(position)
                    if (item is RspOrderChangeData) {
                        when (view.id) {
                            R.id.tv_copy -> {
                                //复制order
                                UtilTool.setCopyText(this@OrderChangeCarrierHistorySearchActivity, "运单号", item.orderId)
                                showToast("复制成功")
                            }
                        }
                    }
                }
            })
            setOnLoadListener2 {
                viewModel?.getNetInfo(nowPage = it, selectType = "2", title = searchKey)
            }
        }
    }

    override fun initData() {}

    override fun onSingleClick(v: View) {
        super.onSingleClick(v)
        when (v.id) {
            R.id.img_back -> {
                finish()
            }
            R.id.btn_search -> {
                if (searchKey.isEmpty()) {
                    showToast("请输入运单号搜索")
                    return
                }
                refreshMoreLayout.onAutoRefresh()
            }
            R.id.img_search_clear -> {
                edSearch.setText("")
            }
        }
    }

    @LiveDataMatch
    open fun onGetNetInfoSuccess(data: PageList<RspOrderChangeData>?) {
        refreshMoreLayout?.onRefreshCompale(data)
    }

    companion object {
        @JvmStatic
        fun start(context: Context) {
            val intent = Intent(context, OrderChangeCarrierHistorySearchActivity::class.java)
            context.startActivity(intent)
        }
    }
}