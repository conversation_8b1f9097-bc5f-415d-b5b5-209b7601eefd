package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseNewRequest
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData

/**
 * 注释：车辆有一单及以上在途
 * 时间：2025/2/18 17:01
 * @author：王家辉
 * wiki：
 * 对接：黄琎
 * */
class ReqAssignVehicleTransportCheck(
    var plateNumber: String? = null
) : BaseNewRequest<BaseRsp<RspAssignVehicleTransportCheck>>("oms-app/carrier/common/assignVehicleTransportCheck") {
}

data class RspAssignVehicleTransportCheck(
    var isTransportFlag: String? = null// 0 否  1 是
) : ResultData()