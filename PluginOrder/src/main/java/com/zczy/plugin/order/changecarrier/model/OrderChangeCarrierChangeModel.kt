package com.zczy.plugin.order.changecarrier.model

import android.content.Context
import android.text.TextUtils
import android.view.Gravity
import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.certificate.vehiclemanage.carowner.CarOwnerVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.carrier.CarrierVehicleManagementActivity
import com.zczy.certificate.vehiclemanage.enterprise.EnterPriseVehicleManagementActivityV1
import com.zczy.comm.CommServer
import com.zczy.comm.config.HttpURLConfig
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.comm.pluginserver.ACertificateServer
import com.zczy.overdue.boss.BossExpiredCertificateManagementActivity
import com.zczy.overdue.cyr.CyrExpiredCertificateManagementActivity
import com.zczy.overdue.cys.CysExpiredCertificateManagementActivity
import com.zczy.plugin.order.changecarrier.req.*
import com.zczy.plugin.order.designation.req.ReqAssignVehicleTransportCheck
import com.zczy.plugin.order.designation.req.RspAssignVehicleTransportCheck
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.source.pick.model.request.ReqCheckDelistOrderDocumentExpire
import com.zczy.plugin.order.source.pick.model.request.RspCheckDelistOrderDocumentExpire

class OrderChangeCarrierChangeModel : BaseViewModel() {
    fun queryChangePageInfo(orderId: String, friendId: String, nowPage: Int) {
        execute(
            true,
            Req5QueryChangePageInfo(orderId = orderId, friendId = friendId, nowPage = nowPage)
        ) { t ->
            if (t.success()) {
                setValue("onQueryChangePageInfo", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun queryChangePageInfoV2(orderId: String, friendId: String, nowPage: Int) {
        execute(
            true,
            Req5QueryChangePageInfo(orderId = orderId, friendId = friendId, nowPage = nowPage)
        ) { t ->
            if (t.success()) {
                setValue("onQueryChangePageInfoV2", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun queryChangePageInfoTMS(sourceId: String, friendId: String, consignorUserId: String, nowPage: Int) {
        execute(
            true,
            Req5QueryChangePageInfo(sourceId = sourceId, friendId = friendId, nowPage = nowPage, orderId = sourceId, consignorUserId = consignorUserId)
        ) { t ->
            if (t.success()) {
                setValue("onQueryChangePageInfo", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /**
     * 准驾车型校验
     */
    fun vertifyCarType(orderId: String, vehicleId: String, driverUserId: String) {
        execute(
            true,
            VertifyCarTypeChangeBeanReq(
                orderId = orderId,
                vehicleId = vehicleId,
                driverUserId = driverUserId
            )
        ) { t ->
            if (t.success()) {
                setValue("vertifyCarTypeSuccess", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun vertifyCarTypeTMS(sourceId: String, vehicleId: String, driverUserId: String, consignorUserId: String) {
        execute(
            true,
            VertifyCarTypeChangeBeanReq(
                sourceId = sourceId,
                vehicleId = vehicleId,
                driverUserId = driverUserId,
                consignorUserId = consignorUserId
            )
        ) { t ->
            if (t.success()) {
                setValue("vertifyCarTypeSuccess", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun changeOne(context: Context, data: Req3ChangeOne) {
        val builder = DialogBuilder()
        builder.message = "是否确认要发起变更"
        builder.setOkListener { dialogInterface, _ ->
            dialogInterface.dismiss()
            execute(false, data) { t ->
                if (t.success()) {
                    showToast(t.msg)
                    setValue("onChangeOne")
                } else if (TextUtils.equals("1114", t.data?.resultCode)) {
                    if (TextUtils.equals("1", t.data?.improveButton)) {
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "温馨提示"
                        dialogBuilder.message =
                            "该运单对车辆“排放标准”有限制要求，请完善“排放标准”后继续摘单"
                        dialogBuilder.gravity = Gravity.CENTER
                        dialogBuilder.cancelText = "放弃摘单"
                        dialogBuilder.setOKTextListener("去完善") { dialog: DialogBuilder.DialogInterface, _: Int ->
                            dialog.dismiss()
                            val relation = CommServer.getUserServer().login.relation
                            if (relation.isCarrier) {
                                CarrierVehicleManagementActivity.start(context)
                            } else if (relation.isBoss) {
                                CarOwnerVehicleManagementActivity.start(context)
                            } else {
                                EnterPriseVehicleManagementActivityV1.start(context)
                            }
                        }
                        dialogBuilder.setCancelTextListener("放弃摘单") { dialog: DialogBuilder.DialogInterface, _: Int ->
                            dialog.dismiss()
                            setValue("onClearCar")
                        }
                        showDialog(dialogBuilder)
                    } else {
                        showDialogToast(t.msg)
                    }
                } else if (t.data?.resultCode == "2001" || t.data?.resultCode == "1113") {
                    if (t.data?.carrierLicenseState == "1") {
                        //提示不拦截业务
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = t.data?.resultMsg
                        if (t.data?.updateLicenseFlag == "1") {
                            dialogBuilder.cancelText = "我知道了"
                            dialogBuilder.setOKText("立即更新")
                            dialogBuilder.setCancelListener { dialog, _ ->
                                dialog.dismiss()
                                setValue("onChangeOne")
                            }
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                                //过期管理
                                val relation = CommServer.getUserServer().login.relation

                                if (relation.isCarrier) {
                                    CyrExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isBoss) {
                                    BossExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isCys) {
                                    CysExpiredCertificateManagementActivity.jumpPage(context)
                                }
                            }
                        } else {
                            dialogBuilder.setOKText("我知道了")
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                                setValue("onChangeOne")
                            }
                        }
                        showDialog(dialogBuilder)
                    } else if (t.data?.carrierLicenseState == "2") {
                        //提示拦截业务
                        val dialogBuilder = DialogBuilder()
                        dialogBuilder.title = "提示"
                        dialogBuilder.message = t.data?.resultMsg
                        if (t.data?.updateLicenseFlag == "1") {
                            dialogBuilder.setOKText("立即更新")
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                                //过期管理
                                val relation = CommServer.getUserServer().login.relation

                                if (relation.isCarrier) {
                                    CyrExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isBoss) {
                                    BossExpiredCertificateManagementActivity.jumpPage(context)
                                } else if (relation.isCys) {
                                    CysExpiredCertificateManagementActivity.jumpPage(context)
                                }
                            }
                        } else {
                            dialogBuilder.setOKText("我知道了")
                            dialogBuilder.isHideCancel = true
                            dialogBuilder.setOkListener { dialog, _ ->
                                dialog.dismiss()
                            }
                        }
                        showDialog(dialogBuilder)
                    }
                } else if (TextUtils.equals("1116", t.code)) {
                    //  1116 （去处理道路运输证按钮 此时有个字段uploadRoadLicense 为“1”）
                    if (TextUtils.equals("1", t.data?.uploadRoadLicense)) {
                        var dialog = DialogBuilder()
                        dialog.message = t.msg
                        dialog.setOKTextListener("去处理") { dialog, _ ->
                            dialog.dismiss()
                            ACertificateServer.getPluginServer().startOverdueExpiredUI(context, data.vehicleId, data.plateNumber, t.data?.licenseState)
                        }
                        showDialog(dialog)
                    } else {
                        showDialogToast(t.msg)
                    }
                } else if (TextUtils.equals("1112", t.code)) {
                    setValue("onCheckErrorState", t.data, t.msg)
                } else {
                    setValue("onChangeOneError", t.data, t.msg)
                }
            }
        }
        showDialog(builder)
    }

    fun changeOneTms(context: Context, data: Req3ChangeOne) {
        execute(false, data) { t ->
            if (t.success()) {
                showToast(t.msg)
                setValue("onChangeOne")
            } else if (TextUtils.equals("1114", t.data?.resultCode)) {
                if (TextUtils.equals("1", t.data?.improveButton)) {
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "温馨提示"
                    dialogBuilder.message =
                        "该运单对车辆“排放标准”有限制要求，请完善“排放标准”后继续摘单"
                    dialogBuilder.gravity = Gravity.CENTER
                    dialogBuilder.cancelText = "放弃摘单"
                    dialogBuilder.setOKTextListener("去完善") { dialog: DialogBuilder.DialogInterface, _: Int ->
                        dialog.dismiss()
                        val relation = CommServer.getUserServer().login.relation
                        if (relation.isCarrier) {
                            CarrierVehicleManagementActivity.start(context)
                        } else if (relation.isBoss) {
                            CarOwnerVehicleManagementActivity.start(context)
                        } else {
                            EnterPriseVehicleManagementActivityV1.start(context)
                        }
                    }
                    dialogBuilder.setCancelTextListener("放弃摘单") { dialog: DialogBuilder.DialogInterface, _: Int ->
                        dialog.dismiss()
                        setValue("onClearCar")
                    }
                    showDialog(dialogBuilder)
                } else {
                    showDialogToast(t.msg)
                }
            } else if (t.data?.resultCode == "2001" || t.data?.resultCode == "1113") {
                if (t.data?.carrierLicenseState == "1") {
                    //提示不拦截业务
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = t.data?.resultMsg
                    if (t.data?.updateLicenseFlag == "1") {
                        dialogBuilder.cancelText = "我知道了"
                        dialogBuilder.setOKText("立即更新")
                        dialogBuilder.setCancelListener { dialog, _ ->
                            dialog.dismiss()
                            setValue("onChangeOne")
                        }
                        dialogBuilder.setOkListener { dialog, _ ->
                            dialog.dismiss()
                            //过期管理
                            val relation = CommServer.getUserServer().login.relation

                            if (relation.isCarrier) {
                                CyrExpiredCertificateManagementActivity.jumpPage(context)
                            } else if (relation.isBoss) {
                                BossExpiredCertificateManagementActivity.jumpPage(context)
                            } else if (relation.isCys) {
                                CysExpiredCertificateManagementActivity.jumpPage(context)
                            }
                        }
                    } else {
                        dialogBuilder.setOKText("我知道了")
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOkListener { dialog, _ ->
                            dialog.dismiss()
                            setValue("onChangeOne")
                        }
                    }
                    showDialog(dialogBuilder)
                } else if (t.data?.carrierLicenseState == "2") {
                    //提示拦截业务
                    val dialogBuilder = DialogBuilder()
                    dialogBuilder.title = "提示"
                    dialogBuilder.message = t.data?.resultMsg
                    if (t.data?.updateLicenseFlag == "1") {
                        dialogBuilder.setOKText("立即更新")
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOkListener { dialog, _ ->
                            dialog.dismiss()
                            //过期管理
                            val relation = CommServer.getUserServer().login.relation

                            if (relation.isCarrier) {
                                CyrExpiredCertificateManagementActivity.jumpPage(context)
                            } else if (relation.isBoss) {
                                BossExpiredCertificateManagementActivity.jumpPage(context)
                            } else if (relation.isCys) {
                                CysExpiredCertificateManagementActivity.jumpPage(context)
                            }
                        }
                    } else {
                        dialogBuilder.setOKText("我知道了")
                        dialogBuilder.isHideCancel = true
                        dialogBuilder.setOkListener { dialog, _ ->
                            dialog.dismiss()
                        }
                    }
                    showDialog(dialogBuilder)
                }
            } else if (TextUtils.equals("1116", t.code)) {
                //  1116 （去处理道路运输证按钮 此时有个字段uploadRoadLicense 为“1”）
                if (TextUtils.equals("1", t.data?.uploadRoadLicense)) {
                    var dialog = DialogBuilder()
                    dialog.message = t.msg
                    dialog.setOKTextListener("去处理") { dialog, _ ->
                        dialog.dismiss()
                        ACertificateServer.getPluginServer().startOverdueExpiredUI(context, data.vehicleId, data.plateNumber, t.data?.licenseState)
                    }
                    showDialog(dialog)
                } else {
                    showDialogToast(t.msg)
                }
            } else if (TextUtils.equals("1112", t.code)) {
                setValue("onCheckErrorState", t.data, t.msg)
            } else {
                setValue("onChangeOneError", t.data, t.msg)
            }
        }
    }


    fun checkPlateNumberBeforeChange(
        context: Context,
        check: ReqCheckPlateNumberBeforeChange,
        req: Req3ChangeOne,
        eSignFlag: Boolean
    ) {
        execute(
            true,
            check
        ) { t ->

            var esignFlag = "0"
            if (t.success()) {
                if (eSignFlag) {
                    changeOne(context, req)
                } else {
                    esignCheck(context, req)
                }
            } else if (TextUtils.equals("1111", t.code)) {
                var dialog = DialogBuilder()
                dialog.setMessage(t.msg)
                dialog.setOkListener { dialog, _ ->
                    dialog.dismiss()
                    if (eSignFlag) {
                        changeOne(context, req)
                    } else {
                        //电签流程
                        esignCheck(context, req)
                    }
                }
                showDialog(dialog)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /**
     * 注释：电签流程
     * 时间：2024/3/6 13:33
     * 作者：王家辉
     * */
    fun esignCheck(context: Context, req: Req3ChangeOne) {
        var esignFlag = "0"
        execute(
            true,
            ReqQueryDelistUserElectronicSignState(
                orderId = req.orderId,
            )
        ) { t ->
            if (t.success() && t.data != null) {
                var urlType = ""
                var userType = ""
                if (CommServer.getUserServer().login.relation.isCarrier) {
                    urlType = "#/driver?platform=XXX&isSign=1"
                    userType = "2"
                } else {
                    urlType = "#/carBoss?isSign=1"
                    userType = "10"
                }

                if (TextUtils.equals(
                        "1",
                        t.data!!.electronicSignState
                    ) && TextUtils.equals(
                        "1",
                        t.data!!.cycleState
                    )
                ) {
                    X5WebNoToolBarActivity.startContentUI(
                        context,
                        HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + "#/cycleCommitmentLetter?userType=" + userType + "&plateNumber=" + req.plateNumber + "&heavy=&orderId=" + req.orderId + "&changeFlag=1",
                        "1"
                    )
                    esignFlag = "2"
                } else {
                    X5WebNoToolBarActivity.startContentUI(
                        context,
                        HttpURLConfig.getWebUrl() + "/form_h5/contract/index.html?_t=" + System.currentTimeMillis() + urlType + "&plateNumber=" + req.plateNumber + "&orderId=" + req.orderId + "&changeFlag=1",
                        "1"
                    )
                    if (TextUtils.equals(
                            "0",
                            t.data!!.electronicSignState
                        ) && TextUtils.equals(
                            "0",
                            t.data!!.cycleState
                        )
                    ) {
                        esignFlag = "0"
                    } else {
                        esignFlag = "1"
                    }
                }
                setValue("onSetEsignFlag", esignFlag)
            } else {
                showToast(t.msg)
            }

        }
    }

    fun onChangeOverLoad(orderId: String) {
        val load = ReqChangeOverLoad()
        load.orderId = orderId
        execute(
            true,
            load
        ) { t ->
            if (t.success()) {
                setValue("onChangeOverLoad", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    fun onChangeOverLoadTMS(sourceId: String, consignorUserId: String) {
        val load = ReqChangeOverLoad()
        load.sourceId = sourceId
        load.consignorUserId = consignorUserId
        load.orderId = sourceId
        execute(
            true,
            load
        ) { t ->
            if (t.success()) {
                setValue("onChangeOverLoad", t.data)
            } else {
                showDialogToast(t.msg)
            }
        }
    }

    /**
     * 货主配置车辆排放类型
     * */
    fun ReqQueryDelistOrderConsignorrConfig(req: ReqQueryDelistOrderConsignorrConfig, next: (stadard: String?) -> Unit = {}) {
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                next(t.data?.allowDieselCarWithSpecifiedStandardsForTransport)
            } else {
                showToast(t.msg)
            }
        }
    }

    /**
     * 12吨以下货主摘单限制
     */
    fun checkUnderTwelveVehicleConsignor(context: Context, data: Req3ChangeOne) {
        val req = ReqCheckUnderTwelveVehicleConsignor()
        req.vehicleId = data.vehicleId
        req.consignorUserId = data.consignorUserId
        this.execute(true, req, object : IResult<BaseRsp<ResultData>> {
            override fun onFail(e: HandleException) {
                showDialogToast(e.message)
            }

            @Throws(Exception::class)
            override fun onSuccess(resultDataBaseRsp: BaseRsp<ResultData>) {
                if (resultDataBaseRsp.success()) {
                    changeOne(context, data)
                } else {
                    showDialogToast(resultDataBaseRsp.data?.resultMsg)
                }
            }
        })
    }

    fun queryCarrierOrderCarpooling(req: ReqCheckDelistOrderDocumentExpire) {
        execute(
            req
        ) { rspCheckDelistOrderDocumentExpireBaseRsp: BaseRsp<RspCheckDelistOrderDocumentExpire> ->
            if (rspCheckDelistOrderDocumentExpireBaseRsp.success()) {
                setValue(
                    "queryCarrierOrderCarpoolingSuccess",
                    rspCheckDelistOrderDocumentExpireBaseRsp.data
                )
            } else {
                showToast(rspCheckDelistOrderDocumentExpireBaseRsp.msg)
            }
        }
    }

    /**
     * 变更查询该运单是否有人身安全险
     */
    fun queryOrderHavePolicy(orderId: String) {
        execute(
            true,
            ReqQueryOrderHavePolicy(orderId = orderId)
        ) { t ->
            if (t.success() && TextUtils.equals("1", t.data?.safePolicyIsShow)) {
                setValue("onQueryOrderHavePolicy")
            }
        }
    }

    fun getVehicleEmissionStandard(allowType: String?, vehicle: RspVehicleData?) {
        execute(
            true,
            ReqGetVehicleEmissionStandard(allowType = allowType, vehicleId = vehicle?.vehicleId)
        ) { t ->
            setValue("onGetVehicleEmissionStandard", t.data, vehicle)
        }
    }

    fun checkVehicleCarpooling(
        req: ReqCheckVehicleCarpooling,
        block: (show: RspCheckVehicleCarpooling) -> Unit
    ) {
        this.execute(req) {
            if (it.success()) {
                it.data?.let { it1 -> block(it1) }
            } else {
                showToast(it.msg)
            }
        }
    }

    /**
     * 车辆在途校验
     * */
    fun assignVehicleTransportCheck(req: ReqAssignVehicleTransportCheck, next: (data: BaseRsp<RspAssignVehicleTransportCheck>) -> Unit = {}, nextFail: () -> Unit = {}) {
        execute(false, req) {
            if (it.success()) {
                it?.let { it1 -> next(it1) }
            } else {
                nextFail()
            }
        }
    }

    /**
     * 查询证件过期 冻结 异常 缺失 风险 绑定等校验
     * */
    fun queryWhTmsUserAndVehicleState(req: ReqQueryWhTmsUserAndVehicleState) {
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                setValue("onQueryWhTmsUserAndVehicleState", t.data)
            } else {
                showToast(t.msg)
            }
        }
    }

    fun queryProcessOrderPay(req: ReqQueryProcessOrderPay) {
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                setValue("onRspQueryProcessOrderPay", t)
            } else {
                showToast(t.msg)
            }
        }
    }

    fun queryBeforeChangeDriverAndVehicle(orderId: String?) {
        val req = ReqQueryBeforeChangeDriverAndVehicle(orderId = orderId)
        execute(
            true,
            req
        ) { t ->
            if (t.success()) {
                setValue("onQueryBeforeChangeDriverAndVehicle", t.data)
            } else {
                showToast(t.msg)
            }
        }
    }
}
