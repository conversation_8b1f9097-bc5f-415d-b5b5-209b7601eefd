package com.zczy.plugin.order.supervise.req;
/*=============================================================================================
 * 功能描述:
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/14
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import com.sfh.lib.rx.EmptyResult;
import com.zczy.comm.http.entity.BaseNewRequest;
import com.zczy.comm.http.entity.BaseRsp;
import com.zczy.comm.http.entity.ResultData;

import io.reactivex.disposables.Disposable;

public class ReqAddSdkRecord extends BaseNewRequest<BaseRsp<ResultData>> {
    String orderId;
    //结果信息
    String returnInfo;
    //节点 1.确认发货 2.确认收货
    String nodeType;
    //省平台2.0
    String version = "2";
    //需要缓存运单: 1 缓存 其他不缓存
    String successFlag;

    public ReqAddSdkRecord(String orderId, String returnInfo) {
        super("oms-app/order/receive/addSdkRecord");
        this.orderId = orderId;
        this.returnInfo = returnInfo;
    }

    public ReqAddSdkRecord setSuccessFlag(String successFlag) {
        this.successFlag = successFlag;
        return this;
    }

    public Disposable buildStart() {
        this.nodeType = "1";
        return this.sendRequest(new EmptyResult());
    }

    public Disposable buildStop() {
        this.nodeType = "2";
        return this.sendRequest(new EmptyResult());
    }
}
