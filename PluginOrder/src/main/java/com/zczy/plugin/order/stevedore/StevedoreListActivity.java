package com.zczy.plugin.order.stevedore;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.sfh.lib.mvvm.annotation.LiveDataMatch;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.zczy.comm.CommServer;
import com.zczy.comm.data.role.IRelation;
import com.zczy.comm.http.entity.PageList;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.pulltorefresh.CommEmptyView;
import com.zczy.comm.widget.pulltorefresh.OnLoadingListener2;
import com.zczy.comm.widget.pulltorefresh.SwipeRefreshMoreLayout;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.stevedore.adapter.SteveDoreAdapter;
import com.zczy.plugin.order.stevedore.model.ESteveDore;
import com.zczy.plugin.order.stevedore.model.StevedoreModel;

/**
 * 功能描述:装卸费列表
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/3/5
 */
public class StevedoreListActivity extends AbstractLifecycleActivity<StevedoreModel> implements OnLoadingListener2, BaseQuickAdapter.OnItemChildClickListener, BaseQuickAdapter.OnItemClickListener {

//    public static void startContentUI(Context context) {
//
//        Intent intent = new Intent (context, StevedoreListActivity.class);
//        context.startActivity (intent);
//    }

    public static void startContentUI(Context fragment) {

        AMainServer mainServer = AMainServer.getPluginServer();
        mainServer.openReactNativeActivity(fragment, "StevedoreListPage");
    }


    private AppToolber appToolbar;

    private SwipeRefreshMoreLayout swipeRefreshMoreLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate (savedInstanceState);
        setContentView (R.layout.order_stevedore_list_activity);
        UtilStatus.initStatus (this, ContextCompat.getColor (this,R.color.comm_title_bg));
        initView ();
    }

    private void initView() {

        appToolbar = findViewById (R.id.app_toolbar);
        swipeRefreshMoreLayout = findViewById (R.id.swipe_refresh_more_layout);
        swipeRefreshMoreLayout.setAdapter (new SteveDoreAdapter (), true);
        swipeRefreshMoreLayout.setEmptyView(CommEmptyView.creator(this,R.drawable.comm_recycler_empty_2_nothing_info,"没有找到相关信息"));
        swipeRefreshMoreLayout.setOnLoadListener2 (this);
        swipeRefreshMoreLayout.addOnItemChildClickListener (this);
        swipeRefreshMoreLayout.addOnItemListener (this);
        swipeRefreshMoreLayout.onAutoRefresh ();

        try {
            IRelation relation = CommServer.getUserServer ().getLogin ().getRelation ();
            if (relation.isCarrier ()) {
                //承运人
                appToolbar.getTvRight ().setText ("添加");
                appToolbar.setRightOnClickListener (new View.OnClickListener () {

                    @Override
                    public void onClick(View v) {
                        // 添加
                        StevedoreAddEditActivity.startContentUI (StevedoreListActivity.this, 2, "");
                    }
                });
            }
        } catch (Exception e) {
            showDialogToast (e.getMessage ());
        }
    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {

        if (view.getId () == R.id.tv_look_detail) {
            //详情
            this.onItemClick (adapter, view, position);
        } else if (view.getId () == R.id.tv_edit) {
            // 修改
            ESteveDore data = (ESteveDore) adapter.getItem (position);
            StevedoreAddEditActivity.startContentUI (StevedoreListActivity.this, 3, data.getDetailId ());
        }
    }

    @Override
    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {

        ESteveDore data = (ESteveDore) adapter.getItem (position);
        StevedoreDetailActivity.startContentUI (this, data.getDetailId ());
    }

    @Override
    public void onLoadUI(int i) {

        this.getViewModel ().onPageList (i);
    }

    @LiveDataMatch(tag = "分页")
    public void onPageSuccess(PageList<ESteveDore> pageList) {

        this.swipeRefreshMoreLayout.onRefreshCompale (pageList);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

    }
}
