package com.zczy.plugin.order.designation.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 *    author : zzf
 *    date   : 2023/7/4
 */
data class ReqBigAssignDriverVehicle(
        /**
         * 运单号
         */
        var orderId: String = "",
        /**
         *司机id
         */
        var driverUserId: String = "",
        /**
         *车辆id
         */
        var vehicleId: String = "",
) : BaseOrderRequest<BaseRsp<ResultData>>("oms-app/order/bigCarrier/bigAssignDriverVehicle")
