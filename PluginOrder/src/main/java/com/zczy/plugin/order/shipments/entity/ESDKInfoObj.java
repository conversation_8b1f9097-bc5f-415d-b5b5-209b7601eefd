package com.zczy.plugin.order.shipments.entity;
/*=============================================================================================
 * 功能描述:省平台监管2.0
 *---------------------------------------------------------------------------------------------
 *  涉及业务 & 更新说明:
 *
 *
 *--------------------------------------------------------------------------------------------
 *  <AUTHOR> 孙飞虎  on  2021/12/14
 *  @Company    中储智运科技股份有限公司
 *  @Copyright （版权）中储智运科技股份有限公司所有                                                                                        *
 *=============================================================================================*/

import com.hdgq.locationlib.entity.ShippingNoteInfo;

public class ESDKInfoObj {
    String orderId;
    String startLocationText;
    double startLongitude;
    double startLatitude;
    String despatchProCode;
    String despatchCityCode;
    String despatchDisCode;

    String endLocationText;
    double endLongitude;
    double endLatitude;
    String deliverProCode;
    String deliverCityCode;
    String deliverDisCode;
    String vehicleNumber;
    String driverName;
    String consignorSubsidiaryId;

    String orderState;//7已收货(非APP操作收货)  8已终止
    String changeMacFlag;//    -- 是否变更设备【APP启动使用】
    String changeFlag;//   -- 是否变更地址【收货使用】

    long interval;


    public ShippingNoteInfo getShippingNoteInfo() {

        ShippingNoteInfo info = new ShippingNoteInfo();
        //运单号
        info.setShippingNoteNumber(this.getOrderId());
        //分单号
        info.setSerialNumber("0000");
        //起点地址文字描述
        info.setStartLocationText(this.getStartLocationText());
        //起点位置行政区划代码
        info.setStartCountrySubdivisionCode(this.getDespatchDisCode());
        //起点位置经度
        info.setStartLongitude(this.getStartLongitude());
        //起点位置纬度
        info.setStartLatitude(this.getStartLatitude());
        //到达地址文字描述
        info.setEndLocationText(this.getEndLocationText());
        //到达位置行政区划代码
        info.setEndCountrySubdivisionCode(this.getDeliverDisCode());
        //到达位置经度
        info.setEndLongitude(this.getEndLongitude());
        //到达位置纬度
        info.setEndLatitude(this.getEndLatitude());
        //车牌号
        info.setVehicleNumber(this.getVehicleNumber());
        //司机姓名
        info.setDriverName(this.getDriverName());
        return info;
    }

    @Override
    public String toString() {
        return "ESDKInfoObj{" +
                "orderId='" + orderId + '\'' +
                ", startLocationText='" + startLocationText + '\'' +
                ", startLongitude=" + startLongitude +
                ", startLatitude=" + startLatitude +
                ", despatchProCode='" + despatchProCode + '\'' +
                ", despatchCityCode='" + despatchCityCode + '\'' +
                ", despatchDisCode='" + despatchDisCode + '\'' +
                ", endLocationText='" + endLocationText + '\'' +
                ", endLongitude=" + endLongitude +
                ", endLatitude=" + endLatitude +
                ", deliverProCode='" + deliverProCode + '\'' +
                ", deliverCityCode='" + deliverCityCode + '\'' +
                ", deliverDisCode='" + deliverDisCode + '\'' +
                ", vehicleNumber='" + vehicleNumber + '\'' +
                ", driverName='" + driverName + '\'' +
                ", consignorSubsidiaryId='" + consignorSubsidiaryId + '\'' +
                ", orderState='" + orderState + '\'' +
                ", changeMacFlag='" + changeMacFlag + '\'' +
                ", changeFlag='" + changeFlag + '\'' +
                ", interval=" + interval +
                '}';
    }

    public String getChangeFlag() {
        return changeFlag;
    }

    public void setChangeFlag(String changeFlag) {
        this.changeFlag = changeFlag;
    }

    public String getChangeMacFlag() {
        return changeMacFlag;
    }

    public void setChangeMacFlag(String changeMacFlag) {
        this.changeMacFlag = changeMacFlag;
    }

    public String getOrderState() {
        return orderState;
    }

    public long getInterval() {
        return interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getStartLocationText() {
        return startLocationText;
    }

    public void setStartLocationText(String startLocationText) {
        this.startLocationText = startLocationText;
    }

    public double getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(double startLongitude) {
        this.startLongitude = startLongitude;
    }

    public double getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(double startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getDespatchProCode() {
        return despatchProCode;
    }

    public void setDespatchProCode(String despatchProCode) {
        this.despatchProCode = despatchProCode;
    }

    public String getDespatchCityCode() {
        return despatchCityCode;
    }

    public void setDespatchCityCode(String despatchCityCode) {
        this.despatchCityCode = despatchCityCode;
    }

    public String getDespatchDisCode() {
        return despatchDisCode;
    }

    public void setDespatchDisCode(String despatchDisCode) {
        this.despatchDisCode = despatchDisCode;
    }

    public String getEndLocationText() {
        return endLocationText;
    }

    public void setEndLocationText(String endLocationText) {
        this.endLocationText = endLocationText;
    }

    public double getEndLongitude() {
        return endLongitude;
    }

    public void setEndLongitude(double endLongitude) {
        this.endLongitude = endLongitude;
    }

    public double getEndLatitude() {
        return endLatitude;
    }

    public void setEndLatitude(double endLatitude) {
        this.endLatitude = endLatitude;
    }

    public String getDeliverProCode() {
        return deliverProCode;
    }

    public void setDeliverProCode(String deliverProCode) {
        this.deliverProCode = deliverProCode;
    }

    public String getDeliverCityCode() {
        return deliverCityCode;
    }

    public void setDeliverCityCode(String deliverCityCode) {
        this.deliverCityCode = deliverCityCode;
    }

    public String getDeliverDisCode() {
        return deliverDisCode;
    }

    public void setDeliverDisCode(String deliverDisCode) {
        this.deliverDisCode = deliverDisCode;
    }

    public String getVehicleNumber() {
        return vehicleNumber;
    }

    public void setVehicleNumber(String vehicleNumber) {
        this.vehicleNumber = vehicleNumber;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getConsignorSubsidiaryId() {
        return consignorSubsidiaryId;
    }

    public void setConsignorSubsidiaryId(String consignorSubsidiaryId) {
        this.consignorSubsidiaryId = consignorSubsidiaryId;
    }
}
