package com.zczy.plugin.order.shipments;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.zczy.comm.Const;
import com.zczy.comm.ui.UtilStatus;
import com.zczy.comm.widget.AppToolber;
import com.zczy.comm.widget.tablayout.CommonTabEntity;
import com.zczy.plugin.order.R;
import com.zczy.plugin.order.shipments.fragment.ShipmentCouponFragment;
import com.zczy.plugin.order.shipments.model.ShipmentsCouponModel;
import com.zczy.plugin.order.shipments.model.request.ReqQueryCarrierAdvanceCouponList;

import java.util.ArrayList;

/**
 * 功能描述:发货选择优惠券
 *
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2019/2/27
 */
public class ShipmentsCouponActivity extends AbstractLifecycleActivity<ShipmentsCouponModel> {


    /***
     *
     * @param fragment
     * @param orderId
     * @param money 服务费
     * @param requestCode
     */
    public static void startContentUI(Activity fragment, String orderId, String pbCarrierMoney, String money, String userCouponIds,      String forcePayFlag , int requestCode) {

        Intent intent = new Intent(fragment, ShipmentsCouponActivity.class);
        intent.putExtra("orderId", orderId);
        intent.putExtra("userCouponIds", userCouponIds);
        intent.putExtra("pbCarrierMoney", pbCarrierMoney);
        intent.putExtra("money", money);
        intent.putExtra("forcePayFlag", forcePayFlag);
        fragment.startActivityForResult(intent, requestCode);

    }
    private AppToolber appToolber;

    private CommonTabLayout commonTabLayout;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        this.setContentView(R.layout.order_shipment_coupon_activity);
        UtilStatus.initStatus(this, ContextCompat.getColor(this, R.color.comm_title_bg));
        this.appToolber = findViewById(R.id.appToolber);
        this.commonTabLayout = findViewById(R.id.common_tab_layout);
        this.appToolber.setRightOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View v) {

                DialogBuilder builder = new DialogBuilder();
                builder.setTitle(Html.fromHtml("<font color=\"#3c75ed\">优惠券使用说明</font>"));
                builder.setMessage(Html.fromHtml("<font color=\"#3c75ed\">●</font> 关于因运单违约等原因造成的优惠券返还。返还的优惠券有效期不变，优惠券退回时，可能已超出优惠券的有效期，将无法再使用。你可以在【优惠券】-【已过期优惠券】中找到过期的优惠券。 <br />" +
                        "<font color=\"#3c75ed\">●</font> 关于优惠券有效期。所有券上都会注明“有效期”，该券仅能在注明的有效期内使用。凡运单摘单时间在注明的有效期内，即可使用该券。<br />" +
                        "<font color=\"#3c75ed\">●</font> 用户不得单独或与货主恶意串通，利用中储智运满返券实施欺诈等非法行为以达到套取现金目的。且用户不得利用中储智运满返券进行售卖、兑换或其他营利性活动。如果中储智运发现用户存在前述违法违规行为，中储智运有权拒绝该用户使用中储智运满返券和/或对该用户账号和/或关联账号采取临时或永久禁用等措施，并通过法律途径依法追究该用户的法律责任。<br />" +
                        "<font color=\"#3c75ed\">●</font> 中储智运将不定时地通过在本页面发布更新版本的方式修改使用规则。请用户定期访问本页面并查看最新版本。这些规则对用户均具有约束力。<br />" +
                        "<font color=\"#3c75ed\">●</font> 优惠券最终解释权归中储智运科技股份有限公司所有。如果用户对使用规则有任何疑问或需要任何帮助，请及时与客服联系，联系电话：" + Const.PHONE_SERVER_400 + "。"));
                builder.setOKText("我知道了");
                builder.setHideCancel(true);
                showDialog(builder);
            }
        });


        ArrayList<CustomTabEntity> tabEntitys = new ArrayList<>(2);
        CommonTabEntity tabEntity1 = new CommonTabEntity();
        tabEntity1.title = "可使用优惠券";
        CommonTabEntity tabEntity2 = new CommonTabEntity();
        tabEntity2.title = "不可用优惠券";

        tabEntitys.add(tabEntity1);
        tabEntitys.add(tabEntity2);

        String orderId = getIntent().getStringExtra("orderId");
        String money = getIntent().getStringExtra("money");
        String pbCarrierMoney = getIntent().getStringExtra("pbCarrierMoney");
        String forcePayFlag = getIntent().getStringExtra("forcePayFlag");

        double priceMoney = 0.0f;
        if (!TextUtils.isEmpty(money)) {
            priceMoney = Double.valueOf(money);
        }

        ArrayList<Fragment> fragments = new ArrayList<>(2);

        String userCouponIds = getIntent().getStringExtra("userCouponIds");
        //0 查询可用张数 1 查询可用不可用张数 2 查询可用优惠券列表 3 查询不可用优惠券列表
        ShipmentCouponFragment fragment1 = ShipmentCouponFragment.start(ReqQueryCarrierAdvanceCouponList.TYPE_C, orderId,userCouponIds,pbCarrierMoney, priceMoney,forcePayFlag);
        fragments.add(fragment1);

        ShipmentCouponFragment fragment2 = ShipmentCouponFragment.start(ReqQueryCarrierAdvanceCouponList.TYPE_D, orderId,"",pbCarrierMoney, priceMoney,forcePayFlag);
        fragments.add(fragment2);

        commonTabLayout.setTabData(tabEntitys, this, R.id.frame_layout, fragments);
    }


}
