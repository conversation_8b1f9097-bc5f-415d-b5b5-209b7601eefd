package com.zczy.plugin.order.designation

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import com.sfh.lib.event.RxBusEvent
import com.sfh.lib.mvvm.annotation.LiveDataMatch
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.ui.dialog.DialogBuilder
import com.zczy.comm.CommServer
import com.zczy.comm.config.RouterConfig
import com.zczy.comm.ui.BaseActivity
import com.zczy.comm.utils.RouterUtils
import com.zczy.comm.utils.toJson
import com.zczy.overdue.boss.BossExpiredCertificateManagementActivity
import com.zczy.overdue.cyr.CyrExpiredCertificateManagementActivity
import com.zczy.overdue.cys.CysExpiredCertificateManagementActivity
import com.zczy.plugin.order.ElectSignContractDialog
import com.zczy.plugin.order.R
import com.zczy.plugin.order.designation.model.DesignationVehicleDriverModel
import com.zczy.plugin.order.designation.req.ReqBigAssignDriverVehicle
import com.zczy.plugin.order.designation.req.ReqCarrierLicenseTransitionPeriod
import com.zczy.plugin.order.designation.req.ReqEletcSignContract
import com.zczy.plugin.order.designation.req.ReqQueryUserAndVehicleDelistRiskStates
import com.zczy.plugin.order.designation.req.RspDataCheckEletcSignContract
import com.zczy.plugin.order.designation.req.RspEletcSignContract
import com.zczy.plugin.order.designation.req.RspGetTempToken
import com.zczy.plugin.order.source.list.X5WebNoToolBarActivity
import com.zczy.plugin.order.source.pick.CheckUserAndVehicleStates
import com.zczy.plugin.order.source.pick.OrderDriverListActivity
import com.zczy.plugin.order.source.pick.PickAntiDraudAction
import com.zczy.plugin.order.source.pick.entity.EDriverUser
import com.zczy.plugin.order.source.pick.entity.EVehicle
import com.zczy.plugin.order.source.pick.fragment.OnUserAndVehicleStates

/**
 *    author : zzf
 *    date   : 2023/6/30
 *    desc   :指定车辆和司机-物流企业
 */
class SpecifiedVehicleDriverCysActivity : BaseActivity<DesignationVehicleDriverModel>(),
    View.OnClickListener {

    /**
     * 选择司机
     */
    private val tvSelectDriver by lazy { findViewById<TextView>(R.id.tv_select_driver) }

    /**
     * 派单
     */
    private val tvCommit by lazy { findViewById<TextView>(R.id.tv_commit) }

    /**
     * 选择车辆
     */
    private lateinit var pickCarFragment: SpecifiedOrderCarFragmnet
    private var driverUser: EDriverUser? = null
    private var orderId: String = ""
    private var setId: String = ""

    private var checkPersonCar: OnUserAndVehicleStates? = null
    private var dialog: ElectSignContractDialog? = null

    companion object {
        @JvmStatic
        fun startContentUI(context: Context, orderId: String, setId: String? = "") {
            val intent = Intent(context, SpecifiedVehicleDriverCysActivity::class.java)
            intent.putExtra("orderId", orderId)
            intent.putExtra("setId", setId ?: "")
            context.startActivity(intent)
        }
    }

    override fun getLayout(): Int {
        return R.layout.specified_vehicle_driver_cys_activity
    }

    override fun bindView(bundle: Bundle?) {
        tvCommit.setOnClickListener(this)
        tvSelectDriver.setOnClickListener(this)
        pickCarFragment = supportFragmentManager.findFragmentById(R.id.select_car) as SpecifiedOrderCarFragmnet
        pickCarFragment.setOnEnableSlect(object : SpecifiedOrderCarFragmnet.OnEnableSlect {
            override fun isOK(): Boolean {
                if (driverUser == null || TextUtils.isEmpty(driverUser?.driverId)) {
                    showToast("请先选择司机")
                    return false
                }
                return true
            }
        })
    }

    override fun initData() {
        orderId = intent.getStringExtra("orderId") ?: ""
        setId = intent.getStringExtra("setId") ?: ""
        checkPersonCar = CheckUserAndVehicleStates(
            this@SpecifiedVehicleDriverCysActivity,
            viewModel,
            orderId,
            setId
        )
        pickCarFragment.setOnUserAndVehicleStates(checkPersonCar)
        pickCarFragment.setSelectCarCallback {
            checkVehicleInfo(it)
        }

        viewModel.checkEletcSignContract()
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.tv_commit -> {
                //确认派单
                commitData()
            }

            R.id.tv_select_driver -> {
                //选择司机
                OrderDriverListActivity.startContentUI(this)
            }
        }
    }

    /**
     * 注释：校验车
     * 时间：2024/9/14 0014 11:54
     * 作者：郭翰林
     */
    fun checkVehicleInfo(vehicleInfo: EVehicle) {
        val request = ReqCarrierLicenseTransitionPeriod()
        request.orderId = orderId
        request.vehicleId = vehicleInfo.vehicleId ?: ""
        request.plateNumber = vehicleInfo.plateNumber ?: ""
        request.checkDelistType = "1"

        getViewModel(BaseViewModel::class.java).execute(request) {
            runOnUiThread {
                if (it.success()) {
                    if (!TextUtils.equals("0", it.data?.carrierLicenseState)) {
                        if (TextUtils.equals("1", it.data?.carrierLicenseState)) {
                            //1 提示 不拦截
                            if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.cancelText = "知道了"
                                dialogBuilder.isCancelable = false;
                                dialogBuilder.setCancelListener { dialog, _ ->
                                    dialog.dismiss()
                                    if (!TextUtils.isEmpty(vehicleInfo?.vehicleId)) {
                                        //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                                        queryCheckCarrierAndVehicleCertificate(vehicleInfo?.vehicleId ?: "")
                                    }
                                }
                                dialogBuilder.setOKText("立即更新")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    //车辆证件过期
                                    val params = mutableMapOf<String, Any>()
                                    params["page"] = "VehicleIdCardManagerPage"
                                    params["data"] = mapOf(
                                        Pair("vehicleId", vehicleInfo.vehicleId),
                                    ).toJson()
                                    RouterUtils.skipRouter(RouterConfig.ReactRouter.URI, params)
                                }
                                showDialog(dialogBuilder)
                            } else {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.isCancelable = false;
                                dialogBuilder.setOKText("我知道了")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    if (!TextUtils.isEmpty(vehicleInfo?.vehicleId)) {
                                        //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                                        queryCheckCarrierAndVehicleCertificate(vehicleInfo?.vehicleId ?: "")
                                    }
                                }
                                showDialog(dialogBuilder)
                            }
                        } else {
                            //2 提示 拦截
                            driverUser = null
                            tvSelectDriver.text = "";
                            pickCarFragment?.onClear();

                            if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("立即更新")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    //车辆证件过期
                                    val params = mutableMapOf<String, Any>()
                                    params["page"] = "VehicleIdCardManagerPage"
                                    params["data"] = mapOf(
                                        Pair("vehicleId", vehicleInfo.vehicleId),
                                    ).toJson()
                                    RouterUtils.skipRouter(RouterConfig.ReactRouter.URI, params)
                                }
                                showDialog(dialogBuilder)
                            } else {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("我知道了")
                                showDialog(dialogBuilder)
                            }
                        }
                    } else {
                        if (!TextUtils.isEmpty(vehicleInfo?.vehicleId)) {
                            //最后换车调用 WLHY-9279	【安卓】【汽运】证件风险通过规则调整
                            queryCheckCarrierAndVehicleCertificate(vehicleInfo?.vehicleId ?: "")
                        }
                    }
                } else {
                    showDialogToast(it.msg)
                }
            }
        }
    }

    /**
     * 注释：校验司机
     * 时间：2024/9/14 0014 11:57
     * 作者：郭翰林
     */
    fun checkDriverInfo(driverInfo: EDriverUser, onSuccess: Runnable) {
        val request = ReqCarrierLicenseTransitionPeriod()
        request.orderId = orderId
        request.vehicleId = driverInfo.vehicle?.vehicleId ?: ""
        request.driverUserId = driverInfo.driverId ?: ""
        request.checkDelistType = "0"
        getViewModel(BaseViewModel::class.java).execute(request) {
            runOnUiThread {
                if (it.success()) {
                    if (!TextUtils.equals("0", it.data?.carrierLicenseState)) {
                        if (TextUtils.equals("1", it.data?.carrierLicenseState)) {
                            //1 提示 不拦截
                            if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.cancelText = "知道了"
                                dialogBuilder.setCancelListener { dialog, _ -> dialog.dismiss() }
                                dialogBuilder.setOKText("立即更新")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    //过期管理
                                    val relation = CommServer.getUserServer().login.relation
                                    if (relation.isCarrier) {
                                        CyrExpiredCertificateManagementActivity.jumpPage(SpecifiedVehicleDriverCysActivity@ this)
                                    } else if (relation.isBoss) {
                                        BossExpiredCertificateManagementActivity.jumpPage(SpecifiedVehicleDriverCysActivity@ this)
                                    } else if (relation.isCys) {
                                        CysExpiredCertificateManagementActivity.jumpPage(SpecifiedVehicleDriverCysActivity@ this)
                                    }
                                }
                                showDialog(dialogBuilder)
                            } else {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("我知道了")
                                showDialog(dialogBuilder)
                            }

                            onSuccess.run()

                        } else {
                            //2 提示 拦截
                            if (TextUtils.equals("1", it.data?.updateLicenseFlag)) {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("立即更新")
                                dialogBuilder.setOkListener { dialog, _ ->
                                    dialog.dismiss()
                                    //过期管理
                                    val relation = CommServer.getUserServer().login.relation
                                    if (relation.isCarrier) {
                                        CyrExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
                                    } else if (relation.isBoss) {
                                        BossExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
                                    } else if (relation.isCys) {
                                        CysExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
                                    }
                                }
                                showDialog(dialogBuilder)
                            } else {
                                val dialogBuilder = DialogBuilder()
                                dialogBuilder.title = "提示"
                                dialogBuilder.message = it.data?.resultMsg
                                dialogBuilder.isHideCancel = true
                                dialogBuilder.setOKText("我知道了")
                                showDialog(dialogBuilder)
                            }
                        }
                    } else {
                        onSuccess.run()
                    }
                } else {
                    showDialogToast(it.msg)
                }
            }
        }
    }

    /**
     * 派单
     */
    fun commitData() {
        val vehicleInfo = pickCarFragment.vehicleInfo
        if (vehicleInfo == null) {
            return
        }
        val text = tvSelectDriver.text
        if (TextUtils.isEmpty(text)) {
            showDialogToast("请选择司机!")
            return
        }

        val req = ReqBigAssignDriverVehicle()
        req.orderId = orderId
        req.vehicleId = vehicleInfo.vehicleId
        req.driverUserId = driverUser?.driverId ?: ""
        viewModel?.bigAssignDriverVehicle(req)
    }

    @RxBusEvent(from = "来自司机选择")
    open fun onSelectDriverSuccess(data: EDriverUser) {
        checkDriverInfo(data) {
            driverUser = data
            tvSelectDriver.text = data.driverName
            checkPersonCar?.onSelectPerson(data)
        }
    }

    @LiveDataMatch(tag = "派单成功")
    open fun bigAssignDriverVehicleSuccess(msg: String) {
        showToast(msg)
        finish()
    }

    /**
     * WLHY-9279	【安卓】【汽运】证件风险通过规则调整
     */
    private fun queryCheckCarrierAndVehicleCertificate(
        vehicleId: String,
    ) {

        val reqCheck = ReqQueryUserAndVehicleDelistRiskStates()
        reqCheck.orderId = orderId;
        reqCheck.driverUserId = driverUser?.driverId ?: ""
        reqCheck.vehicleId = vehicleId

        viewModel.execute(reqCheck) {

            it.data?.let {

                if (TextUtils.equals("1", it.userAndVehicleRiskStates)) {

                    var dailog = DialogBuilder()
                    dailog.message = it.resultMsg

                    if (TextUtils.equals("1", it.riskLimitationDelistFlag)) {
                        //限制成交,清空选择车辆与人
                        driverUser = null
                        tvSelectDriver.text = ""
                        pickCarFragment?.onClear()

                        if (TextUtils.equals("1", it.isOperation)) {
                            //完善资料
                            dailog.cancelText = "完善资料"
                            dailog.setCancelListener { dialog, which ->
                                dialog.dismiss()
                                gotoExpiredCertificateManagementActivity()
                            }
                            dailog.setOKText("我知道了")
                        } else {
                            dailog.isHideCancel = true
                            dailog.setOKText("我知道了")
                        }
                    } else {
                        if (TextUtils.equals("1", it.isOperation)) {
                            //完善资料
                            dailog.cancelText = "完善资料"
                            dailog.setCancelListener { dialog, which ->
                                dialog.dismiss()
                                gotoExpiredCertificateManagementActivity()
                            }
                            dailog.setOKText("继续提交")
                        } else {
                            dailog.cancelText = "我知道了"
                            dailog.setOKText("继续提交")
                        }
                    }
                    showDialog(dailog)
                } else {
                    showDialogToast(it.resultMsg)
                }
            }
        }
    }

    fun gotoExpiredCertificateManagementActivity() {
        //过期管理
        val relation = CommServer.getUserServer().login.relation
        if (relation.isCarrier) {
            CyrExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
        } else if (relation.isBoss) {
            BossExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
        } else if (relation.isCys) {
            CysExpiredCertificateManagementActivity.jumpPage(this@SpecifiedVehicleDriverCysActivity)
        }
    }

    @LiveDataMatch
    open fun onCheckEletcSignContractSuccess(data: RspDataCheckEletcSignContract?) {
        if (data != null && TextUtils.equals("0", data.underWayContractSignState)) {
            dialog = ElectSignContractDialog(data, false, viewModel, { reqEletcSignContract: ReqEletcSignContract? ->
                if (reqEletcSignContract != null) {
                    viewModel.eletcSignContract(reqEletcSignContract)
                }
            }, {
                this.finish()
            })
            dialog?.show(this)
        } else {
            //ZCZY-18042  司机摘单时选择确认收货地址
            PickAntiDraudAction(
                this@SpecifiedVehicleDriverCysActivity,
                getViewModel(DesignationVehicleDriverModel::class.java),
                PickAntiDraudAction.NODE_ASSIGN
            )
                .setOrderId(orderId)
                .setSetId(setId)
                .showAntiDraudDialog {

                }
        }
    }

    @LiveDataMatch
    open fun onEletcSignContract(data: RspEletcSignContract?) {
        if (data != null) {
            if (TextUtils.isEmpty(data.redirectUrl)) {
                dialog?.dismiss()
                this.finish()
                showToast("发起签署成功，短信链接已发送到该手机号")
            } else {
                viewModel.getTempToken(data.redirectUrl)
            }
        }
    }

    @LiveDataMatch
    open fun onGetTempToken(data: RspGetTempToken?, url: String) {
        if (data != null) {
            if (dialog != null) {
                dialog?.dismiss()
            }
            X5WebNoToolBarActivity.startContentUI(this, url + data.token + "&originFrom=app_android")
            //ZCZY-18042  司机摘单时选择确认收货地址
            PickAntiDraudAction(
                this@SpecifiedVehicleDriverCysActivity,
                getViewModel(DesignationVehicleDriverModel::class.java),
                PickAntiDraudAction.NODE_ASSIGN
            )
                .setOrderId(orderId)
                .setSetId(setId)
                .showAntiDraudDialog {

                }
        }
    }
}