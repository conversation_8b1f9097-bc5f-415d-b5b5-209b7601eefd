package com.zczy.plugin.order.changecarrier.model

import com.sfh.lib.exception.HandleException
import com.sfh.lib.mvvm.service.BaseViewModel
import com.sfh.lib.rx.IResult
import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.PageList
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.changecarrier.bean.OrderChangeData
import com.zczy.plugin.order.changecarrier.req.Req7QueryChangeOrderPage
import com.zczy.plugin.order.waybill.model.request.ReqCancelCarrierChange

class OrderChangeCarrierMainOrderChangeModel : BaseViewModel() {
    // 运单变更列表
    fun getNetInfo(nowPage: Int) {
        execute(true,
            Req7QueryChangeOrderPage(nowPage),
            object : IResult<BaseRsp<PageList<OrderChangeData>>> {
                override fun onSuccess(t: BaseRsp<PageList<OrderChangeData>>) {
                    if (t.success()) {
                        setValue("onGetNetInfoSuccess", t.data)
                    } else {
                        setValue("onGetNetInfoSuccess", null)
                        showDialogToast(t.msg)
                    }
                }

                override fun onFail(e: HandleException) {
                    setValue("onGetNetInfoSuccess", null)
                    showDialogToast(e.msg)
                }
            })
    }

    /*取消变更
     * */
    open fun cancelCarrierChange(orderId: String?, sourceId: String?, consignorUserId: String?) {
        execute(false, ReqCancelCarrierChange(orderId = orderId, sourceId = sourceId, consignorUserId = consignorUserId), object : IResult<BaseRsp<ResultData>> {
            override fun onSuccess(p0: BaseRsp<ResultData>?) {
                setValue("onCancelCarrierChange", p0?.msg)
            }

            override fun onFail(p0: HandleException?) {
                showDialogToast(p0?.msg)
            }

        })
    }
}
