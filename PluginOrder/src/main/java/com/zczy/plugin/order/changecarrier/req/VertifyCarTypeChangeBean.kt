package com.zczy.plugin.order.changecarrier.req

import com.zczy.comm.http.entity.BaseRsp
import com.zczy.comm.http.entity.ResultData
import com.zczy.plugin.order.BaseOrderRequest

/**
 * 准驾车型校验
 */
data class VertifyCarTypeChangeBeanReq(
    var orderId: String? = null,
    var sourceId: String? = null,
    var consignorUserId: String? = null,
    var vehicleId: String = "",
    var driverUserId: String = ""
) : BaseOrderRequest<BaseRsp<VertifyCarTypeChangeBeanResp>>("/oms-app/carrier/common/checkVehicleType")

data class VertifyCarTypeChangeBeanResp(
    var checkVehicleType: String = ""
) : ResultData()