<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zczy.plugin.order">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application>
        <activity android:name=".source.list.quote.AskPriceQuoteActivity" />

        <activity
            android:name=".source.route.activity.OrderRouteAddOrEditActivity"
            android:label="添加常跑路线"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.route.activity.OrderRouteChoosePathActivity"
            android:label="请选择起始地"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.route.activity.OrderRouteSearchPathActivity"
            android:label="货源广场 选择地址 搜索界面"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- *******************************货源广场******************************* -->
        <activity
            android:name=".source.search.ui.OrderSourceSearchActivity"
            android:label="货源广场--搜索货源"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.detail.OrderSourceDetailActivity"
            android:label="货源广场--货源详情"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.detail.OrderSourceBatchDetailActivity"
            android:label="货源广场--批量货货详情"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.detail.OrderSourceSetDetailActivity"
            android:label="货源广场--抢单转议价详情"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.detail.SpecialAreaDetailActivity"
            android:label="货源广场--专区详情"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.search.ui.OrderSourceSearchResultAllActivity"
            android:label="货运广场 主页的搜索 结果"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.search.ui.OrderSourceSearchResultTypeActivity"
            android:label="货运广场 搜索结果 指定类型的"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 单货主 货源 -->
        <activity
            android:name=".source.onehz.OrderSourceOneHZActivity"
            android:label="单货主 货源 " />
        <!-- *******************************个人中心******************************* -->
        <!-- 个人中心 优惠券 -->
        <activity
            android:name=".coupon.OrderCouponMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 个人中心 快递详情 -->
        <activity
            android:name=".express.OrderExpressDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 个人中心 快递录入界面 -->
        <activity
            android:name=".express.OrderExpressEntryActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 个人中心 录入快递单号界面 -->
        <activity
            android:name=".express.OrderExpressEntryCodeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 个人中心 快递管理 回单押金 -->
        <activity
            android:name=".express.OrderExpressMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- *******************************变更承运******************************* -->
        <!-- 变更承运 主页 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 变更承运 详情 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 变更承运 变更 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierChangeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- 变更承运 变更 (TMS货源)-->
        <activity
            android:name=".changecarrier.OrderChangeCarrierChangeTMSActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".changecarrier.OrderChangeCarrierChooseAllCarActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 变更承运 选择承运人列表 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierChooseAllCarTMSActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 变更承运 选择承运人列表 TMS -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierPeopleListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- 待卸货-变更车辆 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierShipmentsCarActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".changecarrier.OrderChangeCarrierShipmentsCarTMSActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- *******************************违约管理******************************* -->
        <!-- 违约管理 主页 -->
        <activity
            android:name=".violate.OrderViolateMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 变更承运 搜索历史 -->
        <activity
            android:name=".changecarrier.OrderChangeCarrierHistorySearchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 违约管理 详情 违约申请中 -->
<!--        <activity-->
<!--            android:name=".violate.OrderViolateDetailApplyingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 违约管理 详情 违约单详情 &ndash;&gt;-->
        <activity
            android:name=".violate.OrderViolateListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 运单发起违约 -->
        <activity
            android:name=".violate.OrderViolateAddActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- *******************************运单异常******************************* -->
        <activity
            android:name=".waybill.WaybillCodeActivity"
            android:label="运单二维码"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.WaybillCommDetailActivity"
            android:label="运单详情"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!--        <activity-->
        <!--                android:name=".waybill.WaybillStatusActivity"-->
        <!--                android:label="运单状态"-->
        <!--                android:launchMode="singleTask"-->
        <!--                android:screenOrientation="portrait" />-->
        <activity
            android:name=".waybill.WaybillSettlementActivity"
            android:label="待结算运单列表"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
<!--        <activity-->
<!--            android:name=".shipments.ShipmentsSuccessActivity"-->
<!--            android:label="确认发货成功"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".shipments.ShipmentsFailActivity"-->
<!--            android:label="确认发货失败"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".shipments.ShipmentsActivity"-->
<!--            android:label="确认发货"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".shipments.ShipmentsEditActivity"-->
<!--            android:label="发货修改"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".shipments.ReapplyTerracePayActivity"-->
<!--            android:label="重新申请预付"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".waybill.cyr.DisagreeReconsiderActivity"
            android:label="承运方拒绝回单议价"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".bill.BillClockInActivity"
            android:label="卸货打卡"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <!-- *******************************议价摘单******************************* -->
<!--        &lt;!&ndash; 承运人议价 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrOfferActivity"-->
<!--            android:label="承运人议价"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrReferralOfferActivity"-->
<!--            android:label="承运人普通货转议价报价"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrMindOfferActivity"-->
<!--            android:label="承运人智能推荐报价"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.offline.OrderOfflineOfferActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.offline.OrderOfflineCysOfferActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.offline.OrderOfflineBossOfferActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 安全培训检查 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.CheckRecordsLinkActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 承运人摘单 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrZeroAssumePickingActivity"-->
<!--            android:label="零担承运人摘单"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.cyr.OrderCyrContainerPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />&lt;!&ndash; 车老板摘单 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.boss.OrderBossPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.boss.OrderBossOfferMindActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.boss.OrderBossContainerPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />&lt;!&ndash; 车老板议价 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.boss.OrderBossOfferActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.boss.OrderBossReferralOfferActivity"-->
<!--            android:label="车老板普通货转议价"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        &lt;!&ndash; 物流企业摘单 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.cys.OrderCysPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.cys.OrderCysContainerPickingActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 物流企业议价 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.cys.OrderCysOfferActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 选择指定司机 &ndash;&gt;-->
        <activity
            android:name=".source.pick.OrderDriverListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 选择指定车辆 -->
        <activity
            android:name=".source.pick.OrderCarListActivityV1"
            android:screenOrientation="portrait" /> <!-- 摘单成功 -->
<!--        <activity-->
<!--            android:name=".source.pick.OrderPickSuccessActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 议价成功 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.OrderOfferSuccessActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 议价失败 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.OrderPickOfferFailActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; 摘单议价选择优惠券 &ndash;&gt;-->
<!--        <activity-->
<!--            android:name=".source.pick.UseCouponActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.BoundMoneyCouponActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" />-->
<!--        <activity-->
<!--            android:name=".source.pick.InsuranceCouponActivity"-->
<!--            android:launchMode="singleTask"-->
<!--            android:screenOrientation="portrait" /> &lt;!&ndash; *******************************确认卸货******************************* &ndash;&gt;-->
        <!-- 运单回单修改 -->
        <activity
            android:name=".bill.BillNormalEditActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 普通卸货 -->
        <activity
            android:name=".bill.BillNormalActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" /> <!-- 收货付优惠券 -->
        <!--        <activity-->
        <!--                android:name=".bill.DeliverAddOrderLossDetailActivity"-->
        <!--                android:launchMode="singleTask"-->
        <!--                android:screenOrientation="portrait" /> &lt;!&ndash; 亏涨吨规则详情 &ndash;&gt;-->
        <activity
            android:name=".bill.ReturnOrderLossDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 亏涨吨规则详情 -->
        <activity
            android:name=".bill.BillCouponActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 扫描 -->
        <activity
            android:name=".bill.ScanBillActivity"
            android:launchMode="singleTask"
            android:theme="@style/OnePxActivityStyle" /> <!-- 系统对接 -->
        <activity
            android:name=".bill.BillSystemActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 扫描卸货 -->
        <activity
            android:name=".bill.BillScanActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 卸货成功 -->
        <activity
            android:name=".bill.BillSuccessActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 录入装卸费列表 -->
        <activity
            android:name=".stevedore.StevedoreListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 录入装卸费 -->
        <activity
            android:name=".stevedore.StevedoreAddEditActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 录入装卸费成功 -->
        <activity
            android:name=".stevedore.SearchOrderListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 录入装卸费，提供运单搜索列表 -->
        <activity
            android:name=".stevedore.StevedoreSuccessActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 录入装卸费详情 -->
        <activity
            android:name=".stevedore.StevedoreDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 回程货源 -->
        <activity
            android:name=".source.returnsource.OrderReturnSourceActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 更多货源 -->
        <activity
            android:name=".emergency.EmergencyLogisticsListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".emergency.EmergencyLogisticsMessageActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.cyr.WebRiskActivity"
            android:launchMode="singleTask" /> <!-- 合同 -->
        <activity
            android:name=".ZczyWaterActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.WaybillTransportContractDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /><!-- 发货优惠券 -->
        <activity
            android:name=".shipments.ShipmentsCouponActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 运单 再次承运 再来一单 -->
        <activity
            android:name=".onemore.OrderOneMoreMainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity android:name=".onemore.OrderOneMoreSearchActivity" />
        <activity
            android:name=".waybill.WaybillSearchActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 选择快递公司 -->
        <activity
            android:name=".express.OrderExpressComListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 指定司机车辆 -->
        <activity
            android:name=".designation.SpecifiedVehicleDriverActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 安全告知书 -->
        <activity
            android:name=".designation.SpecifiedVehicleDriverCysActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.cyr.ShipmentsSafeActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 运单 任务奖励 货源 列表 -->
        <activity android:name=".rewark.OrderReWardMainActivity" />
        <activity android:name=".search.OrderSearchActivityV2" />
        <activity android:name=".source.convection.OrderConvectionActivity" />
        <activity android:name=".source.prefecture.OrderPrefectureOfflineActivity" />
        <activity android:name=".source.consignormore.SourceConsignorMoreActivity" />
        <activity android:name=".source.history.SourceHistoryActivity" />
        <activity android:name=".source.detail.expired.SourceExpiredDetailActivity" />
        <activity
            android:name=".source.prefecture.PrefectureMoreListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".bill.HWScanActivity"
            android:screenOrientation="portrait" /> <!-- TMS 正大智能排队 -->
        <activity android:name=".lineup.PickOrderActivity" />
        <activity android:name=".lineup.LineUpManageActivity" />
        <activity android:name=".lineup.LineUpListActivity" />
        <activity android:name=".lineup.LineUpListDriverActivity" />
        <!-- 线下专区 -->
        <activity
            android:name=".offlinezone.OfflineZoneListActivity2"
            android:screenOrientation="portrait" />
        <activity
            android:name=".offlinezone.OfflineZoneWebDetailActivity"
            android:screenOrientation="portrait" />
<!--        <activity-->
<!--            android:name=".shipments.OffLineShipmentsActivity"-->
<!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".bill.OffLineBillNormalActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.zczy.plugin.order.source.list.X5WebNoToolBarActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="driver"
                    android:path="/contractFace"
                    android:scheme="zczy" />
            </intent-filter>
        </activity>
<!--        <activity-->
<!--            android:name=".source.prefecture.GrabOrdersActivity"-->
<!--            android:screenOrientation="portrait" />-->
        <activity
            android:name=".bill.ReceiptTypeWebActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".bill.BillReceiptReservationActivity"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.zczy.plugin.order.equity.BuyCardOrderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.zczy.plugin.order.equity.BuyCardActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.zczy.plugin.order.equity.BuyCardRecordListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.zczy.plugin.order.equity.BuyCardSelectActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.zczy.plugin.order.zeroAssume.ZeroAssumeActivity"
            android:screenOrientation="portrait" /><!-- 零担拼车单列表 -->
        <activity
            android:name=".source.prefecture.AskPriceActicity"
            android:label="询价专区"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.cyr.ScanCodeConfirmClaimActivity"
            android:label="扫码确认认领"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.exclusive.SourceExclusiveActivity"
            android:label="专属货源推荐"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.exclusive.SourceBroadcastHistoryActivity"
            android:label="历史播报"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.list.ActionWebActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.WaybillDickerListActivity"
            android:label="议价待处理列表"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.SelectOrderSourceListPage"
            android:label="选择运单页"
            android:screenOrientation="portrait" />
        <activity
            android:name=".waybill.WaybillMavigatorMapListActivity"
            android:label="承运方首页增加导航入口"
            android:screenOrientation="portrait" />
        <activity
            android:name=".shipments.ShipmentSuccessActivity"
            android:label="承运方首页增加导航入口"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.route.activity.RegisterSelectFromActivity"
            android:label="注册货源来源选择"
            android:screenOrientation="portrait" />
        <activity
            android:name=".source.route.activity.RegisterRouteAddActivity"
            android:label="添加常跑路线(账号注册使用)"
            android:screenOrientation="portrait" />
    </application>

</manifest>