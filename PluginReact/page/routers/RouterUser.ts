import MaintenanceManagementListPage from '../user/cardholderservice/pages/MaintenanceManagementListPage';
import HomeClockPage from '../user/clock/HomeClockPage';

export default [
    {
        title: '人身保障管理',
        name: 'PersonalSafetyPage',
        component: require('../user/personalsafety/PersonalSafetyPage').default,
    },
    {title: '装卸货列表', name: 'StevedoreListPage', component: require('../user/stevedore/StevedoreListPage').default},
    {
        title: '装卸货详情',
        name: 'StevedoreDetailPage',
        component: require('../user/stevedore/StevedoreDetailPage').default,
    },
    {
        title: '录入装卸货',
        name: 'StevedoreAddEditPage',
        component: require('../user/stevedore/StevedoreAddEditPage').default,
    },
    {
        title: '选择订单',
        name: 'SearchOrderListPage',
        component: require('../user/stevedore/SearchOrderListPage').default,
    },
    {
        title: '违约管理主页',
        name: 'OrderViolateMainPage',
        component: require('../user/violate/OrderViolateMainPage').default,
    },
    {
        title: '违约管理详情',
        name: 'OrderViolateDetailApplyingPage',
        component: require('../user/violate/OrderViolateDetailApplyingPage').default,
    },
    {
        title: '好友列表 界面',
        name: 'UserFriendListPage',
        component: require('../user/friend/UserFriendListPage').default,
    },
    {
        title: '司机管理列表 界面',
        name: 'CysDriverManagementPage',
        component: require('../user/drivermanager/CysDriverManagementPage').default,
    },
    {
        title: '好友详情 界面',
        name: 'UserFriendDetailPage',
        component: require('../user/friend/UserFriendDetailPage').default,
    },
    {
        title: '趣味活动列表',
        name: 'UserActivitiesPage',
        component: require('../user/activities/UserActivitiesPage').default,
    },
    {
        title: '好友 添加好友 界面',
        name: 'UserFriendAddPage',
        component: require('../user/friend/UserFriendAddPage').default,
    },
    {
        title: '好友 搜索 界面',
        name: 'UserFriendSearchPage',
        component: require('../user/friend/UserFriendSearchPage').default,
    },
    {
        title: '回单押金主页',
        name: 'OrderExpressMainPage',
        component: require('../user/express/OrderExpressMainPage').default,
    },
    {
        title: '评价管理',
        name: 'EvaluateManagerPage',
        component: require('../user/evaluate/EvaluateManagerPage').default,
    },
    {
        title: '评价搜索',
        name: 'EvaluateSearchPage',
        component: require('../user/evaluate/EvaluateSearchPage').default,
    },
    {
        title: '评价详情',
        name: 'EvaluateDetailPage',
        component: require('../user/evaluate/EvaluateDetailPage').default,
    },
    {
        title: '卡友服务',
        name: 'CardholderServicePage',
        component: require('../user/cardholderservice/CardholderServicePage').default,
    },
    {
        title: '卡友服务详情',
        name: 'CardholderDetailPage',
        component: require('../user/cardholderservice/CardholderDetailPage').default,
    },
    {
        title: '填写维修单',
        name: 'FillRepairOrderPage',
        component: require('../user/cardholderservice/FillRepairOrderPage').default,
    },
    {
        title: '查看更多',
        name: 'CardholderMoreInfoPage',
        component: require('../user/cardholderservice/CardholderMoreInfoPage').default,
    },
    {
        title: '新的好友',
        name: 'UserNewFriendPage',
        component: require('../user/friend/UserNewFriendPage').default,
    },
    {
        title: '摘单设置记录',
        name: 'PickSettingMainPage',
        component: require('../user/picksetting/PickSettingMainPage').default,
    },
    {
        title: '摘单设置',
        name: 'PickSettingPage',
        component: require('../user/picksetting/PickSettingPage').default,
    },
    {
        title: '摘单设置复制功能',
        name: 'PickSettingCopyPage',
        component: require('../user/picksetting/PickSettingCopyPage').default,
    },
    {
        title: '我要评价',
        name: 'EvaluatePage',
        component: require('../user/evaluate/EvaluatePage').default,
    },
    {
        title: '问题与反馈',
        name: 'UserTicklingPage',
        component: require('../user/tickling/UserTicklingPage').default,
    },
    {
        title: '个人中心 问题反馈 详情',
        name: 'UserTicklingDetailPage',
        component: require('../user/tickling/UserTicklingDetailPage').default,
    },
    {
        title: '个人中心 问题反馈 新增',
        name: 'UserTicklingAddPage',
        component: require('../user/tickling/UserTicklingAddPage').default,
    },
    {
        title: '账单管理',
        name: 'ReconciliationManagerPage',
        component: require('../reconciliation/ReconciliationManagerPage').default,
    },
    {
        title: '账单详情',
        name: 'ReconciliationDetailPageV1',
        component: require('../reconciliation/ReconciliationDetailPageV1').default,
    },
    {
        title: '个人中心 问题反馈 选择订单',
        name: 'UserTicklingChooseOrderPage',
        component: require('../user/tickling/UserTicklingChooseOrderPage').default,
    },
    {
        title: '申请保障列表',
        name: 'ApplyProtectionMainPage',
        component: require('../user/applyprotection/ApplyProtectionMainPage').default,
    },
    {
        title: '申请货物保障',
        name: 'ApplyGoodInsurancePage',
        component: require('../user/applyprotection/ApplyGoodInsurancePage').default,
    },
    {
        title: '发起申请-人身保障',
        name: 'ApplyPersonInsurancePage',
        component: require('../user/applyprotection/ApplyPersonInsurancePage').default,
    },
    {
        title: '申请进度-人身保障',
        name: 'ApplyProgressPersonPage',
        component: require('../user/applyprotection/ApplyProgressPersonPage').default,
    },
    {
        title: '申请进度-货物保障',
        name: 'ApplyProgressGoodPage',
        component: require('../user/applyprotection/ApplyProgressGoodPage').default,
    },
    {
        title: '申请货物保障-编辑',
        name: 'ApplyEditGoodInsurancePage',
        component: require('../user/applyprotection/ApplyEditGoodInsurancePage').default,
    },
    {
        title: '发起申请-人身保障-编辑',
        name: 'ApplyEditPersonInsurancePage',
        component: require('../user/applyprotection/ApplyEditPersonInsurancePage').default,
    },
    {
        title: '签约运力页面',
        name: 'ContractCapacityPage',
        component: require('../user/contractcapacity/pages/ContractCapacityPage').default,
    },
    {
        title: '发起签约意向',
        name: 'ContractCapacityAddPage',
        component: require('../user/contractcapacity/pages/ContractCapacityAddPage').default,
    },
    {
        title: '保证金明细',
        name: 'ContractMarginDetailsPage',
        component: require('../user/contractcapacity/pages/details/ContractMarginDetailsPage').default,
    },
    {
        title: '合同详情',
        name: 'ContractDetailsPage',
        component: require('../user/contractcapacity/pages/ContractDetailsPage').default,
    },
    {
        title: '合同详情-合同周期内数据达成情况',
        name: 'ContractAchieveDetailsPage',
        component: require('../user/contractcapacity/pages/details/ContractAchieveDetailsPage').default,
    },
    {
        title: '合同详情-违约金明细',
        name: 'ContractLiquidatedDamagesPage',
        component: require('../user/contractcapacity/pages/details/ContractLiquidatedDamagesPage').default,
    },
    {
        title: '合同详情-违约详情',
        name: 'ContractDetailsOfDefaultPage',
        component: require('../user/contractcapacity/pages/details/defaultdetails/ContractDetailsOfDefaultPage').default,
    },

    {
        title: '签约意向详情',
        name: 'ContractCapacityDetailPage',
        component: require('../user/contractcapacity/pages/ContractCapacityDetailPage').default,
    },
    {
        title: '我的预约',
        name: 'MaintenanceManagementListPage',
        component: require('../user/cardholderservice/pages/MaintenanceManagementListPage').default,
    },
    {
        title: '维修预约详情',
        name: 'MaintenanceManagementDetailPage',
        component: require('../user/cardholderservice/pages/MaintenanceManagementDetailPage').default,
    },
    {
        title: '打卡',
        name: 'HomeClockPage',
        component: require('../user/clock/HomeClockPage').default,
    },
];
