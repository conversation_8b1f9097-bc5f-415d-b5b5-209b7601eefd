export default [
    {
        title: '找货设置',
        name: 'SearchGoodSettings',
        component: require('../order/goods/search/SearchGoodSettings').default,
    },
    {
        title: '超限运输车辆通行证',
        name: 'OrderExcessPassFile',
        component: require('../order/excesspass/OrderExcessPassFilePage').default,
    },
    {
        title: '智运排队',
        name: 'LineUpManagePage',
        component: require('../order/lineup/LineUpManagePage').default,
    },
    {
        title: '排队详情(非直属车司机)',
        name: 'LineUpListDriverPage',
        component: require('../order/lineup/LineUpListDriverPage').default,
    },
    {
        title: '排队详情',
        name: 'LineUpListPage',
        component: require('../order/lineup/LineUpListPage').default,
    },
    {
        title: '抢单',
        name: 'PickOrderPage',
        component: require('../order/lineup/PickOrderPage').default,
    },
    {
        title: '线下专区',
        name: 'OfflineZoneListPage',
        component: require('../order/offlinezone/OfflineZoneListPage').default,
    },
    {
        title: '预约货源详情',
        name: 'SourceReservationInfoPage',
        component: require('../order/reservation/SourceReservationInfoPage').default,
    },
    {
        title: '预约货源-失败',
        name: 'SourceReservationInfoFailPage',
        component: require('../order/reservation/SourceReservationInfoFailPage').default,
    },
    {
        title: '预约货源-成功',
        name: 'SourceReservationInfoSuccessPage',
        component: require('../order/reservation/SourceReservationInfoSuccessPage').default,
    },
    {
        title: '我的预约',
        name: 'SourceReservationInfoListPage',
        component: require('../order/reservation/SourceReservationInfoListPage').default,
    },
    {
        title: '入园签到',
        name: 'OrderSignInPickPage',
        component: require('../order/signpick/OrderSignInPickPage').default,
    },
    {
        title: '入园签到成功',
        name: 'OrderSignSuccessPage',
        component: require('../order/signpick/OrderSignSuccessPage').default,
    },
    {
        title: '专属货源推荐页',
        name: 'SourceExclusivePage',
        component: require('../order/goods/search/SourceExclusivePage').default,
    },
    {
        title: 'Trn回单上传',
        name: 'BillNormalTrnPage',
        component: require('../bill/BillNormalTrnPage').default,
    },
    {
        title: 'Trn回单上传成功页',
        name: 'BillSuccessTrnPage',
        component: require('../bill/BillSuccessTrnPage').default,
    },
    {
        title: 'Trn确认发货页',
        name: 'ShipmentsTrnPage',
        component: require('../shipments/delivery/ShipmentsTrnPage').default,
    },
    {
        title: 'Trn确认发货成功页',
        name: 'ShipmentsTrnSuccessPage',
        component: require('../shipments/ShipmentsTrnSuccessPage').default,
    },
    {
        title: '精准装货日期',
        name: 'DetailLoadGoodsPage',
        component: require('../order/loadgoods/DetailLoadGoodsPage').default,
    }, {
        title: '发货单据补传',
        name: 'ShipmentsImgUpdatePage',
        component: require('../shipments/delivery/ShipmentsImgUpdatePage').default,
    },
];
