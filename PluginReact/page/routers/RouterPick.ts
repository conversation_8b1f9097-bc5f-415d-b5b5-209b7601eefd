import OrderBossPickingTMSPage from '../pick/boss/OrderBossPickingTMSPage';
import FreightCouponPage from '../pick/FreightCouponPage';

export default [
    {
        title: '议价成功页',
        name: 'OrderOfferSuccessPage',
        component: require('../pick/OrderOfferSuccessPage').default,
    },
    {
        title: '摘单成功页',
        name: 'OrderPickSuccessPage',
        component: require('../pick/OrderPickSuccessPage').default,
    },
    {
        title: '摘单成功页',
        name: 'OrderPickSuccessTMSPage',
        component: require('../pick/OrderPickSuccessTMSPage').default,
    },
    {
        title: '摘单失败页',
        name: 'OrderPickOfferFailPage',
        component: require('../pick/OrderPickOfferFailPage').default,
    },
    {
        title: '摘单【诚意金优惠券】列表',
        name: 'BoundMoneyCouponPage',
        component: require('../pick/BoundMoneyCouponPage').default,
    },
    {
        title: '摘单【红包优惠券】列表',
        name: 'UseCouponPage',
        component: require('../pick/UseCouponPage').default,
    },
    {
        title: '摘单【货物保障优惠券】列表',
        name: 'InsuranceCouponPage',
        component: require('../pick/InsuranceCouponPage').default,
    },
    {
        title: '技术服务费抵用券',
        name: 'TechServiceCouponPage',
        component: require('../pick/TechServiceCouponPage').default,
    },
    {
        title: '运费险抵用券',
        name: 'FreightCouponPage',
        component: require('../pick/FreightCouponPage').default,
    },
    {
        title: '直摘[承运人]',
        name: 'OrderCyrPickingPage',
        component: require('../pick/cyr/OrderCyrPickingPage').default,
    },
    {
        title: '选择车辆',
        name: 'OrderCarListPage',
        component: require('../pick/OrderCarListPage').default,
    },
    {
        title: '选择车辆',
        name: 'OrderTMSrCarListPage',
        component: require('../pick/OrderTMSrCarListPage').default,
    },
    {
        title: '直摘页面[车老板]',
        name: 'OrderBossPickingPage',
        component: require('../pick/boss/OrderBossPickingPage').default,
    },
    {
        title: '[议价/摘单]指定司机',
        name: 'OrderDriverListPage',
        component: require('../pick/OrderDriverListPage').default,
    },
    {
        title: '简单模式直摘页面[承运人]',
        name: 'OrderCyrPickingSimplePage',
        component: require('../pick/cyr/OrderCyrPickingSimplePage').default,
    },
    {
        title: '直摘页面[物流企业][批量货->集装箱]',
        name: 'OrderCysContainerPickingPage',
        component: require('../pick/cys/OrderCysContainerPickingPage').default,
    },
    {
        title: '直摘页面[物流企业]',
        name: 'OrderCysPickingPage',
        component: require('../pick/cys/OrderCysPickingPage').default,
    },
    {
        title: '报价页面[物流企业]',
        name: 'OrderCysOfferPage',
        component: require('../pick/cys/OrderCysOfferPage').default,
    },
    {
        title: '线下议价[物流企业]',
        name: 'OrderOfflineCysOfferPage',
        component: require('../pick/cys/OrderOfflineCysOfferPage').default,
    },
    {
        title: '我要报价页面[承运人]',
        name: 'OrderCyrOfferPage',
        component: require('../pick/cyr/OrderCyrOfferPage').default,
    },
    {
        title: '我要报价页面[承运人][线下专区]',
        name: 'OrderCyrOfflineOfferPage',
        component: require('../pick/cyr/OrderCyrOfflineOfferPage').default,
    },
    {
        title: '我要报价页面[承运人]转议价',
        name: 'OrderCyrReferralOfferPage',
        component: require('../pick/cyr/OrderCyrReferralOfferPage').default,
    },
    {
        title: '智能推荐我要报价页面[承运人]',
        name: 'OrderCyrMindOfferPage',
        component: require('../pick/cyr/OrderCyrMindOfferPage').default,
    },
    {
        title: '议价[车老板]',
        name: 'OrderBossOfferPage',
        component: require('../pick/boss/OrderBossOfferPage').default,
    },
    {
        title: '直摘页面[车老板][批量货->集装箱]',
        name: 'OrderBossContainerPickingPage',
        component: require('../pick/boss/OrderBossContainerPickingPage').default,
    },
    {
        title: '直摘页面[承运人][批量货->集装箱]',
        name: 'OrderCyrContainerPickingPage',
        component: require('../pick/cyr/OrderCyrContainerPickingPage').default,
    },
    {
        title: '推荐议价[车老板]',
        name: 'OrderBossReferralOfferPage',
        component: require('../pick/boss/OrderBossReferralOfferPage').default,
    },
    {
        title: '智能推荐议价[车老板]',
        name: 'OrderBossOfferMindPage',
        component: require('../pick/boss/OrderBossOfferMindPage').default,
    },
    {
        title: '直摘[承运人] -- 零担',
        name: 'OrderCyrZeroAssumePickingPage',
        component: require('../pick/cyr/OrderCyrZeroAssumePickingPage').default,
    },
    {
        title: '直摘[车老板] -- 零担',
        name: 'OrderBossZeroAssumePickingPage',
        component: require('../pick/boss/OrderBossZeroAssumePickingPage').default,
    },
    {
        title: '直摘[物流企业] -- 零担',
        name: 'OrderCysZeroAssumePickingPage',
        component: require('../pick/cys/OrderCysZeroAssumePickingPage').default,
    },
    {
        title: '线下议价[车老板]',
        name: 'OrderOfflineBossOfferPage',
        component: require('../pick/boss/OrderOfflineBossOfferPage').default,
    },
    {
        title: '线下专区直摘页面',
        name: 'GrabOrdersPage',
        component: require('../pick/GrabOrdersPage').default,
    },
    {
        title: '直摘[承运人]代开票',
        name: 'OrderCyrPickingInvoicePage',
        component: require('../pick/cyr/OrderCyrPickingInvoicePage').default,
    },
    {
        title: '直摘[承运人]代开票',
        name: 'OrderCyrPickingTMSPage',
        component: require('../pick/cyr/OrderCyrPickingTMSPage').default,
    },
    {
        title: '我要报价页面[承运人]代开票',
        name: 'OrderCyrOfferInvoicePage',
        component: require('../pick/cyr/OrderCyrOfferInvoicePage').default,
    },
    {
        title: '直摘页面[车老板]代开票',
        name: 'OrderBossPickingInvoicePage',
        component: require('../pick/boss/OrderBossPickingInvoicePage').default,
    },
    {
        title: '议价[车老板]代开',
        name: 'OrderBossOfferInvoicePage',
        component: require('../pick/boss/OrderBossOfferInvoicePage').default,
    },
    {
        title: '议价[车老板]TMS',
        name: 'OrderBossPickingTMSPage',
        component: require('../pick/boss/OrderBossPickingTMSPage').default,
    },
];
