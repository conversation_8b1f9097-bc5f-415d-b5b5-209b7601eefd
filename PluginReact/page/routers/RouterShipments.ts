export default [
    {
        title: '确认发货',
        name: 'ShipmentsPage',
        component: require('../shipments/delivery/ShipmentsPage').default,
    },
    {
        title: '零担批量确认发货',
        name: 'ShipmentsZeroBatchPage',
        component: require('../shipments/delivery/ShipmentsZeroBatchPage').default,
    },
    {
        title: '零担确认发货',
        name: 'ShipmentsZeroPage',
        component: require('../shipments/delivery/ShipmentsZeroPage').default,
    },
    {
        title: '重新预付',
        name: 'ReapplyTerracePayPage',
        component: require('../shipments/delivery/ReapplyTerracePayPage').default,
    },
    {
        title: '发货后预付申请',
        name: 'PrepaidApplyPage',
        component: require('../shipments/delivery/PrepaidApplyPage').default,
    },
    {
        title: '发货单修改',
        name: 'ShipmentsEditPage',
        component: require('../shipments/delivery/ShipmentsEditPage').default,
    },
    {
        title: '发货单修改TMS',
        name: 'ShipmentsTMSEditPage',
        component: require('../shipments/delivery/ShipmentsTMSEditPage').default,
    },
    {
        title: '线下确认发货',
        name: 'OffLineShipmentsPage',
        component: require('../shipments/delivery/OffLineShipmentsPage').default,
    },
    {
        title: '修改预付发货照片',
        name: 'ShipmentsEditImgPage',
        component: require('../shipments/delivery/ShipmentsEditImgPage').default,
    },
    {
        title: '零担确认发货成功页',
        name: 'ShipZeroSuccessPage',
        component: require('../shipments/ShipZeroSuccessPage').default,
    },
    {
        title: '确认发货成功页',
        name: 'ShipmentsSuccessPage',
        component: require('../shipments/ShipmentsSuccessPage').default,
    },
];
