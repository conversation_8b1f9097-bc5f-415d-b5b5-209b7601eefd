export default [
    {
        title: '交易通首页',
        name: 'WalletScreenPage',
        component: require('../wisdom/WalletScreenPage').default,
    },
    {
        title: '提现',
        name: 'WithdrawalPage',
        component: require('../wisdom/WithdrawalPage').default,
    },
    {
        title: '提现失败页',
        name: 'WithdrawalFailedPage',
        component: require('../wisdom/WithdrawalFailedPage').default,
    },
    {
        title: '提现成功页',
        name: 'WithdrawalSuccessPage',
        component: require('../wisdom/WithdrawalSuccessPage').default,
    },
    {
        title: '支付记录',
        name: 'PayRecordPage',
        component: require('../wisdom/PayRecordPage').default,
    },
    {
        title: '提现记录',
        name: 'WithdrawalRecordPage',
        component: require('../wisdom/WithdrawalRecordPage').default,
    },
    {
        title: '订金记录',
        name: 'DepositIncomeRecordPage',
        component: require('../wisdom/DepositIncomeRecordPage').default,
    },
    {
        title: '已结算运费',
        name: 'SettlementRecordPage',
        component: require('../wisdom/SettlementRecordPage').default,
    },
    {
        title: '收支明细',
        name: 'RevenueRecordPage',
        component: require('../wisdom/RevenueRecordPage').default,
    },
    {
        title: '冻结记录',
        name: 'FreezeRecordPage',
        component: require('../wisdom/FreezeRecordPage').default,
    },
    {
        title: '添加银行卡成功',
        name: 'WalletAddBankSuccessPage',
        component: require('../wisdom/WalletAddBankSuccessPage').default,
    },
    {
        title: '银行卡列表',
        name: 'WalletBankListPage',
        component: require('../wisdom/WalletBankListPage').default,
    },
    {
        title: '绑定招商监管户银行卡',
        name: 'WalletBankSupervisionListPage',
        component: require('../wisdom/WalletBankSupervisionListPage').default,
    },
    {
        title: '监管账户绑定成功',
        name: 'WalletBankSupervisionSuccessPage',
        component: require('../wisdom/WalletBankSupervisionSuccessPage').default,
    },
    {
        title: '银行卡认证-失败',
        name: 'WalletBankVerifyFailedPage',
        component: require('../wisdom/WalletBankVerifyFailedPage').default,
    },
    {
        title: '银行卡认证-成功',
        name: 'WalletBankVerifySuccessPage',
        component: require('../wisdom/WalletBankVerifySuccessPage').default,
    },
    {
        title: '设置支付密码第一步',
        name: 'SettingPayPasswordOne',
        component: require('../wisdom/SettingPayPasswordOne').default,
    },
    {
        title: '设置支付密码第二步',
        name: 'SettingPayPasswordTwo',
        component: require('../wisdom/SettingPayPasswordTwo').default,
    },
    {
        title: '设置支付密码第三步',
        name: 'SettingPayPasswordThree',
        component: require('../wisdom/SettingPayPasswordThree').default,
    },
];
