import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, ScrollView, Text, View} from 'react-native';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

const COMMONMONTH = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28];
const SMALLMONTH = [...COMMONMONTH, 29, 30];
const BIGMONTH = [...COMMONMONTH, 29, 30, 31];
const REAPMONTH = [...COMMONMONTH, 29];
let num = 1;

export interface DatePickerProps {
    value: Date | null | undefined;
    height?: number;
    width?: number | string;
    fontSize?: number;
    textColor?: string;
    startYear?: number;
    endYear?: number;
    markColor?: string;
    markHeight?: number;
    markWidth?: number | string;
    fadeColor?: string;
    format?: string;

    onChange(value: Date): void;
}

export interface DateBlockProps {
    digits: number[];
    value: number;
    type: string;
    height: number;
    fontSize?: number;
    textColor?: string;
    markColor?: string;
    markHeight?: number;
    markWidth?: number | string;
    isF: boolean;

    onChange(type: string, digit: number): void;
}

let date = new Date();

const DatePicker: React.FC<DatePickerProps> = ({value, onChange, height, width, fontSize, textColor, startYear, endYear, markColor, markHeight, markWidth, format}) => {
    const [days, setDays] = useState<number[]>([]);
    const [months, setMonths] = useState<number[]>([]);
    const [years, setYears] = useState<number[]>([]);
    const pickerHeight: number = Math.round(height || Dimensions.get('window').height / 4); //容器高度
    const pickerWidth: any = width || '100%'; //容器宽度
    const unexpectedDate: Date = new Date(years[0], 0, 1);
    const [isF, setIsF] = useState(false);
    date = new Date(value || unexpectedDate);
    useEffect(() => {
        const end = endYear || new Date().getFullYear() + 10; //如果没有设置结束年份,采用当前年份后推10年
        const start = !startYear || startYear > end ? end - 80 : startYear; //同理,前推80年
        setDays([...Array(31)].map((_, index) => index + 1));
        setMonths([...Array(12)].map((_, index) => index + 1));
        setYears([...Array(end - start + 1)].map((_, index) => start + index));
    }, []);
    const isReapYear = (year: number) => {
        return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
    };
    const returnAllMonths = (year: number, month: number) => {
        if ([1, 3, 5, 7, 8, 10, 12].includes(month)) {
            return BIGMONTH;
        } else if ([4, 6, 9, 11].includes(month)) {
            return SMALLMONTH;
        } else if (isReapYear(year)) {
            return REAPMONTH;
        } else {
            return COMMONMONTH;
        }
    };
    const flash = () => {
        if ([2, 4, 6, 9, 11].includes(date.getMonth() + 1)) {
            setIsF(!isF);
        }
    };
    const changeHandle = (type: string, digit: number): void => {
        switch (type) {
            case 'day':
                date.setDate(digit);
                break;
            case 'month':
                date.setMonth(digit - 1);
                flash();
                break;
            case 'year':
                date.setFullYear(digit);
                if (isReapYear(digit)) {
                    flash();
                }
                break;
        }
        onChange(date);
    };

    // 根据年月日来渲染不同列
    const getOrder = () => {
        return (format || 'yyyy-mm-dd').split('-').map((type, index) => {
            switch (type) {
                case 'dd':
                    return {
                        name: 'day',
                        digits: returnAllMonths(date.getFullYear(), date.getMonth() + 1),
                        value: date.getDate(),
                    };
                case 'mm':
                    return {name: 'month', digits: months, value: date.getMonth() + 1};
                case 'yyyy':
                    return {name: 'year', digits: years, value: date.getFullYear()};
                default:
                    return {
                        name: ['year', 'month', 'day'][index],
                        digits: [years, months, days][index],
                        value: [date.getFullYear(), date.getMonth() + 1, date.getDate()][index],
                    };
            }
        });
    };

    return (
        <View
            style={{
                flexDirection: 'row',
                height: pickerHeight,
                width: pickerWidth,
                backgroundColor: '#fff',
            }}>
            {getOrder().map((item, index) => {
                return (
                    <DateBlock
                        digits={item.digits}
                        value={item.value}
                        type={item.name}
                        onChange={changeHandle}
                        height={pickerHeight}
                        fontSize={fontSize}
                        textColor={textColor}
                        markColor={markColor}
                        markHeight={markHeight}
                        markWidth={markWidth}
                        key={index}
                        isF={isF}
                    />
                );
            })}
        </View>
    );
};

function DateBlock({value, digits, type, onChange, height, fontSize, textColor, markColor, markHeight, markWidth, isF}) {
    const dHeight: number = Math.round(height / 4); //日期高度
    const mHeight: number = markHeight || Math.min(dHeight, 65); //蒙层高度
    const mWidth: any = markWidth || '100%'; //蒙层长度
    const offsets = digits.map((_: number, index: number) => index * dHeight); //偏移量(顶部到吸附点的垂直距离),当前的日期下标和日期高度的乘积
    const scrollRef = useRef<any>(null);
    const snapScrollToIndex = (index: number) => {
        scrollRef?.current?.scrollTo({y: dHeight * index, animated: true});
    };
    const [isScrolling, setIsScrolling] = useState(false);
    useEffect(() => {
        snapScrollToIndex(value - digits[0]);
    }, [scrollRef.current, isF]);
    const handleMomentumScrollEnd = ({nativeEvent}: any) => {
        if (!isScrolling) {
            setIsScrolling(true);

            const digit = Math.round(nativeEvent.contentOffset.y / dHeight + digits[0]);
            onChange(type, digit);
            setTimeout(() => setIsScrolling(false), 1);
        }
    };

    return (
        <View
            style={{
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
            }}>
            {/*蒙层*/}
            <View
                style={{
                    position: 'absolute',
                    borderRadius: 0,
                    top: (height - mHeight) / 2,
                    backgroundColor: markColor || 'rgba(0, 0, 0, 0.05)',
                    height: mHeight,
                    width: mWidth,
                }}
            />

            <ScrollView ref={scrollRef} style={{width: '100%'}} snapToOffsets={offsets} showsVerticalScrollIndicator={false} scrollEventThrottle={16} onMomentumScrollEnd={handleMomentumScrollEnd}>
                {digits.map((value: number, index: number) => {
                    return (
                        <UITouchableOpacity
                            key={index}
                            onPress={() => {
                                //支持点击选择日期
                                onChange(type, digits[index]);
                                snapScrollToIndex(index);
                            }}>
                            <Text
                                style={{
                                    textAlign: 'center',
                                    fontSize: fontSize || 16,
                                    color: textColor || '#666',
                                    marginBottom: index === digits.length - 1 ? (height - dHeight) / 2 : 0,
                                    marginTop: index === 0 ? (height - dHeight) / 2 : 0,
                                    lineHeight: dHeight,
                                    height: dHeight,
                                }}>
                                {value +
                                    (() => {
                                        switch (type) {
                                            case 'year':
                                                return '年';
                                            case 'month':
                                                return '月';
                                            case 'day':
                                                return '日';
                                            default:
                                                return '';
                                        }
                                    })()}
                            </Text>
                        </UITouchableOpacity>
                    );
                })}
            </ScrollView>
        </View>
    );
}

export default DatePicker;
