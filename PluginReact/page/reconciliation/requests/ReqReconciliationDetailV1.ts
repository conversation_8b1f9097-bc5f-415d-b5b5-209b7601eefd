import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import PageList from '../../http/PageList';
import TextUtils from '../../util/TextUtils';

/**
 * 注释: 账单详情
 * 时间: 2024/7/3 8:44
 * wiki: http://wiki.zczy56.com/pages/viewpage.action?pageId=********
 * <AUTHOR>
 */

export class ReqReconciliationDetailV1 extends BaseRequest {
    checkId?: string; // 账单ID
    public async request(): Promise<BaseResponse<PageList<RspReconciliationDetailV1>>> {
        this.params = {
            checkId: this.checkId,
        };
        return super.post('ams-app/accountCheckPackingApp/detail', PageList<RspReconciliationDetailV1>);
    }
}

export class RspReconciliationDetailV1 extends ResultData {
    checkId?: string; //   账单ID
    checkNo?: string; // 	账单编号
    carrierName?: string; // 	主用户名称
    subsidiaryName?: string; // 	结算平台
    subsidiaryId?: string; // 	结算平台ID
    orderType?: string; // 	1 运单 2 结算调整单
    feeType?: string; // 	费用类型 1 运费 2 保费
    generationTime?: string; // 	制单时间
    generationTimeStr?: string; // 	制单时间
    packingNum?: string; // 	账单数量
    totalAmountSum?: string; // 	应付总额/应开票数
    settleAmountSum?: string; // 已付总额
    settleMoney?: string; // 	应付余额
    packRemark?: string; // 	账单备注
    settleMoneyTotal?: string; // 	应付余额合计
    packingNumTotal?: string; // 	应付余额合计
    totalAmountSumTotal?: string; // 应开票数合计

    showOrderType() {
        return TextUtils.equals('1', this.orderType) ? '运单' : '结算调整单';
    }
}

export class ReqInvoiceSubsidiaryInfoDto extends BaseRequest {
    subsidiaryId?: string; // 账单ID
    public async request(): Promise<BaseResponse<RspInvoiceSubsidiaryInfoDtoV1>> {
        this.params = {
            subsidiaryId: this.subsidiaryId,
        };
        return super.post('ams-app/accountCheckPackingApp/InvoiceSubsidiaryInfoDto', RspInvoiceSubsidiaryInfoDtoV1);
    }
}

export class RspInvoiceSubsidiaryInfoDtoV1 extends ResultData {
    InvoiceSubsidiaryInfoDto?: RspInvoiceSubsidiaryInfoDto; //   公司名称
}

export class RspInvoiceSubsidiaryInfoDto {
    subsidiaryName?: string; //   公司名称
    taxpayerIdentificationNumber?: string; // 	纳税人识别号
    companyAddress?: string; // 	地址
    companyMobile?: string; // 	电话
    companyBank?: string; // 	开户行
    companyBankNumber?: string; // 	账号
}

export class ReqBillConfirm extends BaseRequest {
    checkId?: string; // 账单id
    operateName?: string; // 操作人
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            checkId: this.checkId,
            operateName: this.operateName,
        };
        return super.post('ams-app/accountCheckPackingApp/billConfirm', ResultData);
    }
}

export class ReqBillReject extends BaseRequest {
    checkId?: string; // 账单id
    operateName?: string; // 操作人
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            checkId: this.checkId,
            operateName: this.operateName,
        };
        return super.post('ams-app/accountCheckPackingApp/billReject', ResultData);
    }
}

export class ReqConfirmQuerySum extends BaseRequest {
    carrierUserId?: string; // 用户ID
    packingState?: string; // 账单状态： 2 待确认 3 已确认
    businessType?: string; // 业务类型 1 汽运（承运商账单） 2 水运（船东账单）
    generationTimeS?: string; // 制单日期开始时间
    generationTimeE?: string; // 制单日期结束时间
    public async request(): Promise<BaseResponse<RspConfirmQuerySumV1>> {
        this.params = {
            carrierUserId: this.carrierUserId,
            packingState: this.packingState,
            businessType: this.businessType,
            generationTimeS: this.generationTimeS,
            generationTimeE: this.generationTimeE,
        };
        return super.post('ams-app/accountCheckPackingApp/confirmQuerySum', RspConfirmQuerySumV1);
    }
}

export class RspConfirmQuerySumV1 extends ResultData {
    AccountConfirmingSumDto?: RspConfirmQuerySum; //   汇总数据
}

export class RspConfirmQuerySum {
    packCountSum?: string; //   账单数量
    totalAmountSum?: string; // 	应付合计
    settledAmountSum?: string; // 	已付合计
    settleMoneySum?: string; // 	应付余额
}
