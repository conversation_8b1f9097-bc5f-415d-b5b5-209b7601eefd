import {Dimensions, StyleSheet, View} from 'react-native';
import React from 'react';
import BaseCommPage from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {DP, gScreen_statusBarHeight, gScreen_width} from '../util/scaled-style';
import {SceneMap, TabBar, TabView} from 'react-native-tab-view';
import UITitleView from '../widget/UITitleView';
import ReconciliationPageView from './view/ReconciliationPageView';
import {Method} from '../util/NativeModulesTools';
import {http} from '../const.global';

interface State {}

const first = () => (
    <View style={{flex: 1}}>
        <ReconciliationPageView billType={ReconciliationManagerPage.BILL_TYPE_1} />
    </View>
);

const second = () => (
    <View style={{flex: 1}}>
        <ReconciliationPageView billType={ReconciliationManagerPage.BILL_TYPE_2} />
    </View>
);
/**
 * 注释: 账单管理
 * 时间: 2024/7/26 10:03
 * <AUTHOR>
 */
export default class ReconciliationManagerPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    static BILL_TYPE_1 = '2'; // 待确认
    static BILL_TYPE_2 = '3'; // 已确认
    constructor(props) {
        super(props);
        this.state = {};
        this.renderTabBar = this.renderTabBar.bind(this);
    }
    static formatNumber = (number) => {
        return new Intl.NumberFormat('en-US').format(number);
    };
    render() {
        return (
            <View style={{flex: 1}}>
                {/*toolbar*/}
                <UITitleView 
                    title={'账单管理'} 
                    rightText={'操作手册'}
                    rightTextStyle={{color: '#3c75ed'}}
                    clickRight={() => Method.openWebView(`${http.url()}/form_h5/documents/reconciliation/app_help.html`, '操作手册')}
                />
                {/*tab切换*/}
                {this.renderUiTabView()}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释:  tab切换
     * 时间:  2024/3/5 16:09
     * <AUTHOR>
     */
    private renderUiTabView() {
        let index = 0;
        //界面title
        let routes: {key: string; title: string}[];
        //tabs页面
        let renderScene: any;
        routes = [
            {key: 'first', title: '待确认'},
            {key: 'second', title: '已确认'},
        ];
        renderScene = SceneMap({first, second});
        return (
            <TabView
                navigationState={{index, routes}}
                renderScene={renderScene}
                onIndexChange={(tabIndex) => {
                    index = tabIndex;
                }}
                renderTabBar={this.renderTabBar}
                initialLayout={{width: Dimensions.get('window').width}}
            />
        );
    }

    /**
     * 注释:  tab样式
     * 时间:  2024/3/5 16:24
     * <AUTHOR>
     * @param props
     */
    private renderTabBar(props) {
        return <TabBar {...props} activeColor={'#333333'} labelStyle={{fontSize: 15, color: '#999999', fontWeight: 'bold'}} indicatorStyle={{backgroundColor: '#5086fc'}} style={{backgroundColor: '#ffffff'}} />;
    }
}

const styles = StyleSheet.create({
    title_body: {paddingTop: gScreen_statusBarHeight, backgroundColor: '#ffffff', width: gScreen_width},
    title_mid: {height: DP(90), flexDirection: 'row', alignSelf: 'center', alignItems: 'center'},
    title_left: {flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center'},
    title_content: {flex: 2.2, fontFamily: '', color: '#333333', textAlign: 'center', fontWeight: 'bold'},
    title_line: {width: gScreen_width, height: DP(0.5), backgroundColor: '#e3e3e3'},
    title_right: {flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end'},
    manualButton: {color: '#3c75ed', fontSize: 16, fontWeight: 'bold', marginRight: 15},
});
