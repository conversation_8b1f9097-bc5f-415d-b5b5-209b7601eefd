import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import {gScreen_width} from '../util/scaled-style';
import {ReqInvoiceSubsidiaryInfoDto, ReqReconciliationDetailV1, RspInvoiceSubsidiaryInfoDto, RspReconciliationDetailV1} from './requests/ReqReconciliationDetailV1';
import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';
import ReconciliationManagerPage from './ReconciliationManagerPage';

interface State extends BaseState {
    mRspReconciliationDetailV1?: RspReconciliationDetailV1[];
    mRspInvoiceSubsidiaryInfoDto?: RspInvoiceSubsidiaryInfoDto;
    mRspReconciliationDetailV1_1?: RspReconciliationDetailV1;
}

/**
 * 注释: 账单详情
 * 时间: 2024/7/29 11:20
 * <AUTHOR>
 */
export default class ReconciliationDetailPageV1 extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    checkId: string;
    billType: string;
    subsidiaryId: string;
    constructor(props) {
        super(props);
        this.state = {};
        this.checkId = this.getParams().checkId ?? '';
        this.billType = this.getParams().billType ?? '';
        this.subsidiaryId = this.getParams().subsidiaryId ?? '';
    }

    componentDidMount() {
        super.componentDidMount();
        //获取评价详情
        let req = new ReqReconciliationDetailV1();
        req.checkId = this.checkId;
        Method.showWaitDialog();
        req.request().then((response) => {
            Method.hideWaitDialog();
            if (response.isSuccess()) {
                this.setState({mRspReconciliationDetailV1: response.data?.rootArray});
                this.setState({mRspReconciliationDetailV1_1: response.data?.rootArray[0]});
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
        //获取评价详情
        let reqInvoiceSubsidiaryInfoDto = new ReqInvoiceSubsidiaryInfoDto();
        reqInvoiceSubsidiaryInfoDto.subsidiaryId = this.subsidiaryId;
        Method.showWaitDialog();
        reqInvoiceSubsidiaryInfoDto.request().then((response) => {
            Method.hideWaitDialog();
            if (response.isSuccess()) {
                this.setState({mRspInvoiceSubsidiaryInfoDto: response.data?.InvoiceSubsidiaryInfoDto});
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }
    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                {/*toolbar*/}
                <UITitleView title={'账单详情'} />
                <ScrollView style={{paddingBottom: 10, flex: 1}}>
                    <View style={{backgroundColor: '#FFFFFF', height: 8}} />
                    {this.renderComView(this.state.mRspReconciliationDetailV1_1?.checkNo ?? '', TextUtils.equals(this.billType, ReconciliationManagerPage.BILL_TYPE_1) ? '待确认' : '已确认', true)}
                    <View style={{backgroundColor: '#FFFFFF', height: 8}} />
                    <View style={styles.line} />
                    {this.renderView1()}
                    {this.state.mRspReconciliationDetailV1?.map((item, index) => {
                        return (
                            <View>
                                <View style={{backgroundColor: '#EFF0F3', height: 7}} />
                                <View style={{backgroundColor: '#FFFFFF', paddingVertical: 7}}>
                                    <Text style={[styles.comView, {color: '#333333', fontSize: 16, fontWeight: 'bold'}]}>{TextUtils.equals('1', item.orderType) ? '运单' : '结算调整单'}</Text>
                                    {this.renderComView('费用类型：', TextUtils.equals(item.feeType, '1') ? '运费' : '保费')}
                                    {this.renderComView('明细数：', item.packingNum)}
                                    {this.renderComView('本期结算金额：', ReconciliationManagerPage.formatNumber(item.settleMoney))}
                                </View>
                            </View>
                        );
                    })}
                    <View style={{backgroundColor: '#EFF0F3', height: 7}} />
                    {this.renderView3()}
                    <Text style={[styles.comView, {color: '#333333', fontSize: 16, fontWeight: 'bold', paddingVertical: 10, marginTop: 7}]}>开票信息</Text>
                    {this.renderComView('公司名称：', this.state.mRspInvoiceSubsidiaryInfoDto?.subsidiaryName)}
                    {this.renderComView('纳税人识别号：', this.state.mRspInvoiceSubsidiaryInfoDto?.taxpayerIdentificationNumber)}
                    {this.renderComView('地址：', this.state.mRspInvoiceSubsidiaryInfoDto?.companyAddress)}
                    {this.renderComView('电话：', this.state.mRspInvoiceSubsidiaryInfoDto?.companyMobile)}
                    {this.renderComView('开户行：', this.state.mRspInvoiceSubsidiaryInfoDto?.companyBank)}
                    {this.renderComView('账号：', this.state.mRspInvoiceSubsidiaryInfoDto?.companyBankNumber)}
                    <View style={{backgroundColor: '#EFF0F3', height: 7}} />
                </ScrollView>
            </View>
        );
    }
    renderView3() {
        return (
            <View style={{backgroundColor: '#FFFFFF', paddingVertical: 10}}>
                {this.renderComView('明细数合计：', this.state.mRspReconciliationDetailV1_1?.packingNumTotal, true)}
                {this.renderComView('本期结算金额合计：', ReconciliationManagerPage.formatNumber(this.state.mRspReconciliationDetailV1_1?.settleMoneyTotal), true)}
                {this.renderComView('账单备注：', this.state.mRspReconciliationDetailV1_1?.packRemark, false)}
            </View>
        );
    }
    renderView1() {
        return (
            <View style={{backgroundColor: '#FFFFFF', paddingVertical: 10}}>
                {this.renderComView('主用户名称：', this.state.mRspReconciliationDetailV1_1?.carrierName)}
                {this.renderComView('结算平台：', this.state.mRspReconciliationDetailV1_1?.subsidiaryName)}
                {this.renderComView('制单日期：', this.state.mRspReconciliationDetailV1_1?.generationTimeStr)}
            </View>
        );
    }
    renderComView(title: string, titleContent?: string, orange: boolean = false) {
        return (
            <View style={styles.comView}>
                <Text style={styles.comViewLeft}>{title}</Text>
                <Text style={[styles.comViewRight, {color: orange ? '#FF6F4D' : '#333333'}]}>{titleContent}</Text>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    line: {
        width: gScreen_width,
        height: 0.5,
        backgroundColor: '#e3e3e3',
    },
    comView: {
        backgroundColor: '#FFFFFF',
        justifyContent: 'space-between',
        flexDirection: 'row',
        paddingHorizontal: 15,
        paddingVertical: 3,
    },
    comViewLeft: {
        color: '#666666',
        fontSize: 14,
    },
    comViewRight: {
        color: '#333333',
        fontSize: 14,
    },
});
