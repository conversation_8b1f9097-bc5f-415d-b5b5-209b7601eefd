import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import UIFlatListViewV1, {UIFlatListViewV1Ref} from '../../widget/UIFlatListViewV1';
import {gScreen_height, gScreen_width} from '../../util/scaled-style';
import UIImage from '../../widget/UIImage';
import EventBus from '../../util/EventBus';
import TextUtils from '../../util/TextUtils';
import LinearGradient from 'react-native-linear-gradient';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import {ReqBillConfirm, ReqBillReject, ReqConfirmQuerySum, RspReconciliationDetailV1} from '../requests/ReqReconciliationDetailV1';
import {Method} from '../../util/NativeModulesTools';
import UIProcuredTimePicker from './UIProcuredTimePicker';
import {Constant} from '../../base/Constant';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

interface Props {
    billType: string;
}

/**
 * 注释:
 * 时间: 2024/7/26 10:21
 * <AUTHOR>
 * @param props
 * @param ref
 * @constructor
 */
export default function ReconciliationPageView(props: Props) {
    /** 控件 列表对象 */
    let d = (new Date().getFullYear() + '-' + (new Date().getMonth() + 1)).toString();
    const refListView = React.useRef<UIFlatListViewV1Ref>(null);
    let billType = props.billType;
    const [showTime, setShowTime] = useState(false);
    const [settleMoneySum, setRspInvoiceSubsidiaryInfoDto] = useState('');
    let nowMonthDays = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate();
    const startDate = useRef(new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-1 00:00:00');
    const endDate = useRef(new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + nowMonthDays + ' 23:59:59');
    const [date, setDate] = useState(d);
    const BILL_TYPE_1 = '2'; // 待确认
    const BILL_TYPE_2 = '3'; // 已确认

    const formatNumber = (number) => {
        return new Intl.NumberFormat('en-US').format(number);
    };

    useEffect(() => {
        switch (billType) {
            case BILL_TYPE_2: {
                confirmQuerySum();
            }
        }
        const flashListener = ({key, value1, value2, value3}) => {
            switch (key) {
                case 'flash':
                    if (value1 == true && props.billType === BILL_TYPE_2) {
                        startDate.current = value2;
                        endDate.current = value3;
                        refListView.current?.onRefresh({
                            businessType: '1',
                            packingState: billType,
                            generationTimeS: startDate.current,
                            generationTimeE: endDate.current,
                        });
                        confirmQuerySum();
                    }
                    setShowTime(false);
                    break;
                default:
                    break;
            }
        };
        const timeRefreshListener = ({key, value}) => {
            switch (key) {
                case 'getDay':
                    setDate(value);
                    break;
                default:
                    break;
            }
        };
        EventBus.getInstance().addListener(Constant.event_flash_month_day, flashListener);
        EventBus.getInstance().addListener(Constant.event_get_date, timeRefreshListener);
        return () => {
            EventBus.getInstance().removeListener(flashListener);
            EventBus.getInstance().removeListener(timeRefreshListener);
        };
    }, []);

    /**
     * 注释: 空视图
     * 时间: 2024/6/19 16:49
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    const renderEmptyView = () => {
        return (
            <View style={{height: gScreen_height * 0.8, alignItems: 'center', justifyContent: 'center'}}>
                <UIImage source={'icon_pick_setting_empty'} style={{width: 200, height: 134}} />
                <Text style={{fontSize: 13, color: '#999'}}>没有找到相关信息</Text>
            </View>
        );
    };

    const confirmQuerySum = () => {
        let req = new ReqConfirmQuerySum();
        switch (billType) {
            case BILL_TYPE_2: {
                req.generationTimeS = startDate.current;
                req.generationTimeE = endDate.current;
                break;
            }
            default:
                break;
        }
        req.packingState = billType;
        req.businessType = '1';
        req.request().then((response) => {
            if (response.isSuccess()) {
                setRspInvoiceSubsidiaryInfoDto(response.data?.AccountConfirmingSumDto?.settleMoneySum ?? '');
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    };

    const reject = (checkId: string) => {
        let login = Method.getLogin();
        let req = new ReqBillReject();
        req.checkId = checkId;
        req.operateName = login.memberName;
        Method.showWaitDialog();
        req.request().then((response) => {
            Method.hideWaitDialog();
            if (response.isSuccess()) {
                refListView.current?.onRefresh({
                    businessType: '1',
                    packingState: billType,
                    generationTimeS: billType == BILL_TYPE_2 ? startDate.current : null,
                    generationTimeE: billType == BILL_TYPE_2 ? endDate.current : null,
                });
            } else {
                Method.showDialog({message: response.getMsg()});
            }
        });
    };

    const confirm = (checkId: string) => {
        let login = Method.getLogin();
        let req = new ReqBillConfirm();
        req.operateName = login.memberName;
        req.checkId = checkId;
        Method.showWaitDialog();
        req.request().then((response) => {
            Method.hideWaitDialog();
            if (response.isSuccess()) {
                refListView.current?.onRefresh({
                    businessType: '1',
                    packingState: billType,
                    generationTimeS: billType == BILL_TYPE_2 ? startDate.current : null,
                    generationTimeE: billType == BILL_TYPE_2 ? endDate.current : null,
                });
            } else {
                Method.showDialog({message: response.getMsg()});
            }
        });
    };

    const renderButtonView = (item: RspReconciliationDetailV1) => {
        return (
            <View>
                <View style={styles.line} />
                <View
                    style={{
                        flex: 1,
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                        paddingVertical: 10,
                        paddingEnd: 15,
                    }}>
                    <Text
                        style={styles.leftBtn}
                        onPress={() => {
                            Method.showDialog({
                                title: '提示',
                                message: '是否确定驳回此账单？',
                                okText: '确定',
                                hideCancel: false,
                                okAction: () => {
                                    reject(item.checkId ?? '');
                                },
                            });
                        }}>
                        {'驳回'}
                    </Text>
                    <LinearGradient
                        style={{
                            marginStart: 15,
                            borderWidth: 0,
                            borderRadius: 5,
                            borderColor: '#556CF0',
                        }}
                        colors={['#87A3FF', '#556CF0']}
                        useAngle={true}
                        angle={90}>
                        <Text
                            style={styles.rightBtn}
                            onPress={() => {
                                Method.showDialog({
                                    title: '提示',
                                    message: '账单已核对无误？',
                                    okText: '确定',
                                    hideCancel: false,
                                    okAction: () => {
                                        confirm(item.checkId ?? '');
                                    },
                                });
                            }}>
                            {'确认'}
                        </Text>
                    </LinearGradient>
                </View>
            </View>
        );
    };

    const renderItemView = (item: RspReconciliationDetailV1) => {
        return (
            <View style={{backgroundColor: '#EFF0F3'}}>
                <UITouchableOpacity
                    onPress={() => {
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {
                            page: 'ReconciliationDetailPageV1',
                            data: {
                                checkId: item.checkId,
                                billType: billType,
                                subsidiaryId: item.subsidiaryId,
                            },
                        });
                    }}>
                    <View style={styles.item_content}>
                        <Text style={{paddingHorizontal: 10, paddingVertical: 12, flex: 1}}>{item.checkNo}</Text>
                        <View style={styles.line} />
                        <View style={{flexDirection: 'row', paddingHorizontal: 10}}>
                            <Text style={{paddingVertical: 5, color: '#333333'}}>{'结算平台：'}</Text>
                            <Text
                                style={{
                                    paddingVertical: 5,
                                    color: '#333333',
                                    flex: 1,
                                    paddingRight: 10,
                                }}>
                                {item.subsidiaryName}
                            </Text>
                        </View>
                        <View style={{flexDirection: 'row', paddingHorizontal: 10}}>
                            <Text style={{paddingVertical: 5, color: '#666666'}}>{'本期结算金额：'}</Text>
                            <Text
                                style={{
                                    paddingVertical: 5,
                                    color: '#FF6032',
                                    flex: 1,
                                    paddingRight: 10,
                                }}>{`￥${formatNumber(item.settleMoney)}`}</Text>
                        </View>
                        <View style={{flexDirection: 'row', paddingHorizontal: 10, alignItems: 'center'}}>
                            <Text style={{paddingVertical: 5, color: '#666666'}}>{'制单日期：'}</Text>
                            <Text style={{color: '#666666'}}>{`${item.generationTime}`}</Text>
                        </View>
                        {TextUtils.equals(BILL_TYPE_1, billType) && renderButtonView(item)}
                    </View>
                </UITouchableOpacity>
            </View>
        );
    };

    return (
        <View style={{flex: 1}}>
            {TextUtils.equals(billType, BILL_TYPE_1) ? (
                <View style={{backgroundColor: '#FFFFFF'}}>
                    <Text
                        style={{
                            backgroundColor: '#F6F9FF',
                            paddingHorizontal: 15,
                            paddingTop: 7,
                            paddingBottom: 5,
                            margin: 10,
                        }}>
                        {'尊敬的客户，请认真核对账单信息，确认完毕后，将以此为依据进行结算、开票！如对账单信息有异议，可详询平台客服，电话400-088-5566。更多明细请登陆PC端下载账单查看。'}
                    </Text>
                </View>
            ) : (
                <View style={{backgroundColor: '#FFFFFF', height: 75, marginTop: 4, paddingTop: 15}}>
                    <View style={{flexDirection: 'row', paddingHorizontal: 25}}>
                        <Text style={{}}>{'账单总计'}</Text>
                        <Text style={{flex: 1, marginStart: 10}}>{settleMoneySum}</Text>
                    </View>
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            setShowTime(true);
                        }}>
                        <Text style={{paddingStart: 25, paddingTop: 5}}>{date}</Text>
                        <UIImage source={'base_arrow_down'} style={{width: 6, height: 4.5, paddingStart: 15, marginTop: 7}} />
                    </UITouchableOpacity>
                    <UIImage
                        source={'reconciliation_icon_1'}
                        style={{
                            width: gScreen_width * 0.95,
                            height: 60,
                            marginHorizontal: 10,
                            top: 7.5,
                            flex: 1,
                            zIndex: -1,
                            position: 'absolute',
                        }}
                    />
                </View>
            )}
            <UIFlatListViewV1
                ref={refListView}
                path={'ams-app/accountCheckPackingApp/confirmQuery'}
                params={{
                    businessType: '1',
                    packingState: billType,
                    generationTimeS: billType == BILL_TYPE_2 ? startDate.current : null,
                    generationTimeE: billType == BILL_TYPE_2 ? endDate.current : null,
                }}
                pageSize={10}
                estimatedItemSize={50}
                autoRefresh={true}
                emptyComponentView={renderEmptyView()}
                renderItemView={(item, index) => {
                    return renderItemView(item);
                }}
            />
            <UIProcuredTimePicker showMark={showTime} />
        </View>
    );
}

const styles = StyleSheet.create({
    item_content: {
        backgroundColor: '#FFFFFF',
        borderRadius: 7,
        flexDirection: 'column',
        marginTop: 7,
    },
    line: {
        width: gScreen_width,
        height: 0.5,
        backgroundColor: '#e3e3e3',
    },
    bottomContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        marginTop: 10,
        backgroundColor: '#5086FC',
        borderColor: '#5086FC',
        borderRadius: 6,
        marginRight: 14,
        paddingVertical: 5,
        width: 77,
        alignSelf: 'flex-end',
    },
    leftBtn: {
        color: '#3D85FF',
        fontSize: 14,
        borderRadius: 5,
        paddingHorizontal: 23,
        paddingVertical: 3,
        borderColor: '#C2D9FF',
        borderWidth: 1,
    },
    rightBtn: {
        color: '#fff',
        fontSize: 14,
        paddingHorizontal: 23,
        paddingVertical: 3,
    },
});
