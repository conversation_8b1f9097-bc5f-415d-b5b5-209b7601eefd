import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import UIButton from '../../widget/UIButton';
import EventBus from '../../util/EventBus';
import {Constant} from '../../base/Constant';
import ADatePicker from '../utils/ADatePicker';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {Method} from '../../util/NativeModulesTools';
interface Props {
    showMark: boolean;
}

/**
 * 注释:
 * 时间: 2024/7/22 16:48
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIProcuredTimePicker(props: Props) {
    const [startTime, setStartTime] = useState(new Date());
    const [endTime, setEndTime] = useState(new Date());
    const [nowTime, setNowTime] = useState(new Date());
    const [chooseMonthOrDay, setChooseMonthOrDay] = useState(true);
    const [chooseInputDay, setChooseInp] = useState(true);
    let start = startTime.getFullYear() + '-' + (startTime.getMonth() + 1) + '-' + startTime.getDate();
    let end = endTime.getFullYear() + '-' + (endTime.getMonth() + 1) + '-' + endTime.getDate();
    let nowMonthDays = new Date(nowTime.getFullYear(), nowTime.getMonth() + 1, 0).getDate();
    let nowDate = nowTime.getFullYear() + '-' + (nowTime.getMonth() + 1);
    if (props.showMark) {
        return (
            <View style={{width: '100%', height: '100%', position: 'absolute', bottom: 0, zIndex: 1}}>
                <View
                    style={{
                        height: '100%',
                        width: '100%',
                        backgroundColor: '#666',
                        opacity: 0.5,
                    }}
                />
                <View
                    style={{
                        backgroundColor: '#fff',
                        bottom: 0,
                        position: 'absolute',
                        zIndex: 1000,
                    }}>
                    <View style={{flexDirection: 'row', height: 40, alignItems: 'center', paddingHorizontal: 15}}>
                        <UITouchableOpacity
                            style={[
                                styles.touchStyle,
                                {
                                    borderBottomColor: chooseMonthOrDay ? '#377efa' : '#fff',
                                },
                            ]}
                            onPress={() => {
                                if (!chooseMonthOrDay) {
                                    setChooseMonthOrDay(!chooseMonthOrDay);
                                }
                            }}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: chooseMonthOrDay ? '#377efa' : '#333',
                                }}>
                                {' 按月选择 '}
                            </Text>
                        </UITouchableOpacity>
                        <UITouchableOpacity
                            style={[
                                styles.touchStyle,
                                {
                                    borderBottomColor: chooseMonthOrDay ? '#fff' : '#377efa',
                                },
                            ]}
                            onPress={() => {
                                if (chooseMonthOrDay) {
                                    setChooseMonthOrDay(!chooseMonthOrDay);
                                }
                            }}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: chooseMonthOrDay ? '#333' : '#377efa',
                                    marginStart: 15,
                                }}>
                                {'按日选择'}
                            </Text>
                        </UITouchableOpacity>
                    </View>

                    <View style={{marginBottom: !chooseMonthOrDay ? 30 : 0}}>
                        <View style={styles.inputStyle} />

                        {!chooseMonthOrDay && (
                            <View style={{marginTop: 10, marginBottom: 10}}>
                                <View style={{width: '100%', height: 1, borderBottomColor: '#dcdcdc'}} />

                                <View style={{flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                                    <UITouchableOpacity
                                        onPress={() => {
                                            if (!chooseInputDay) {
                                                setChooseInp(!chooseInputDay);
                                            }
                                        }}>
                                        <View
                                            style={[
                                                styles.textContainer,
                                                {
                                                    backgroundColor: chooseInputDay ? '#eef5ff' : '#f3f3f3',
                                                    right: 5,
                                                },
                                            ]}>
                                            <Text>{start}</Text>
                                        </View>
                                    </UITouchableOpacity>

                                    <View style={styles.lineStyle} />
                                    <UITouchableOpacity
                                        onPress={() => {
                                            if (chooseInputDay) {
                                                setChooseInp(!chooseInputDay);
                                            }
                                        }}>
                                        <View
                                            style={[
                                                styles.textContainer,
                                                {
                                                    backgroundColor: chooseInputDay ? '#f3f3f3' : '#eef5ff',
                                                    left: 5,
                                                },
                                            ]}>
                                            <Text>{end}</Text>
                                        </View>
                                    </UITouchableOpacity>
                                </View>
                            </View>
                        )}
                    </View>
                    {/*时间选择器*/}
                    <ADatePicker
                        value={nowTime}
                        onChange={(value) => {
                            setNowTime(value);
                            if (chooseInputDay) {
                                setStartTime(value);
                            } else {
                                setEndTime(value);
                            }
                        }}
                        format={chooseMonthOrDay ? 'yyyy-mm' : 'yyyy-mm-dd'}
                    />
                    <UIButton
                        text={'确定'}
                        style={{
                            marginTop: 10,
                            width: '60%',
                            borderRadius: 20,
                            marginBottom: 20,
                            alignSelf: 'center',
                        }}
                        onPress={() => {
                            let startMonth = startTime.getFullYear() + '-' + (startTime.getMonth() + 1);
                            if (!chooseMonthOrDay && endTime < startTime) {
                                Method.showDialogToast('结束时间不能小于开始时间');
                            } else {
                                if (!chooseMonthOrDay && startTime.getFullYear() != endTime.getFullYear()) {
                                    Method.showDialogToast('开始时间和结束时间需在同一个自然年度');
                                    return;
                                }
                                const diff = endTime.getTime() - startTime.getTime();
                                const diffInMonths = diff / (1000 * 60 * 60 * 24 * 30.44);
                                if (diffInMonths > 12) {
                                    Method.showDialogToast('时间跨度不能超过一年');
                                    return;
                                }
                                //自定义功能
                                EventBus.getInstance().fireEvent(Constant.event_show_hide_date_components, false);
                                EventBus.getInstance().fireEvent(Constant.event_get_date, {
                                    key: 'getDay',
                                    value: chooseMonthOrDay ? startMonth : start + '至' + end,
                                });
                                EventBus.getInstance().fireEvent(Constant.event_flash_month_day, {
                                    key: 'flash',
                                    value1: true,
                                    value2: chooseMonthOrDay ? nowDate + '-1 00:00:00' : start + ' 00:00:00',
                                    value3: chooseMonthOrDay ? nowDate + '-' + nowMonthDays + ' 23:59:59' : end + ' 23:59:59',
                                });
                            }
                        }}
                    />
                </View>
            </View>
        );
    } else {
        return <></>;
    }
}

const styles = StyleSheet.create({
    textContainer: {
        position: 'relative',
        width: 162,
        height: 29,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
    },
    lineStyle: {
        width: 8.5,
        height: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
    },
    inputStyle: {
        width: Dimensions.get('screen').width,
        height: 1,
        borderBottomWidth: 1,
        borderBottomColor: '#dcdcdc',
    },
    touchStyle: {
        borderBottomWidth: 2,
        justifyContent: 'center',
    },
});
