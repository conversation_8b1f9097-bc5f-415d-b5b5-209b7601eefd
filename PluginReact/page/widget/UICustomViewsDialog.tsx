import React from 'react';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width} from '../util/scaled-style';

interface Props {
    dismiss?: () => void;
    views?: JSX.Element; //富文本
    canBack?: boolean; //是否响应系统返回，和点击背景遮罩层
}

/**
 * 注释: 全自定义View通用弹窗
 * @param props
 * @constructor
 */
export default function UICustomViewsDialog(props: Props) {
    let canBack = props.canBack ?? true;
    return (
        <Modal
            animationIn={'zoomIn'}
            animationOut={'zoomOut'}
            backdropOpacity={0.3}
            isVisible={true}
            hideModalContentWhileAnimating={true}
            deviceWidth={gScreen_width}
            deviceHeight={gScreen_height}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            onBackButtonPress={canBack ? props.dismiss : () => {}} // 响应返回键
            onBackdropPress={canBack ? props.dismiss : () => {}} // 点击背景遮罩层
        >
            <>{props.views}</>
        </Modal>
    );
}
