import {ScrollView, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {ReactElement, useEffect, useState} from 'react';
import Modal from 'react-native-modal';
import {ECity} from '../http/ECity';
import CityInfo from '../http/CityInfo';
import UITouchableOpacity from './UITouchableOpacity';
import {ArrayUtils} from '../util/ArrayUtils';
import UIImage from './UIImage';
import TextUtils from '../util/TextUtils';
import {Constant} from '../base/Constant';
import {Method} from '../util/NativeModulesTools';
import {plainToInstance} from 'class-transformer';
import UIButton from './UIButton';

interface Props {
    // 关闭事件
    onClose?: Function;
    //样式
    style?: ViewStyle;
    //城市列表
    cityList?: ECity[];
    //已经选中的城市
    selectedCityList?: CityInfo[];
    //0:单选,1:多选
    selectMode?: number;
    //最大选择数量
    maxSelectNum?: number;
    //是否显示目的地
    showDestinationView?: boolean;
    // 选中回调
    onSelect?: Function;
    //是否显示确认按钮
    showOkButton?: boolean;
    //     展示文案
    title?: string;
    //     是否需要历史记录
    hasHistory?: boolean;
}

/**
 * 注释: 城市选择控件
 * 时间: 2024/12/27 星期五 9:43
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UISelectCityView(props: Props) {
    //选中的城市
    const [selectedCityList, setSelectedCityList] = useState<CityInfo[]>(props.selectedCityList ?? []);

    // 历史记录
    const [historyCityList, setHistoryCityList] = useState<CityInfo[]>([]);

    //当前层级城市
    const [levelCityList, setLevelCityList] = useState<ECity[]>([]);

    //选中的层级列表
    const [selectCityInfo, setSelectCityInfo] = useState<CityInfo>(new CityInfo());

    //当前选择的城市
    const [selectCity, setSelectCity] = useState<ECity>();

    // 初始化
    useEffect(() => {
        //获取历史记录
        let historyCityListJson = Method.getStringExtra('cache_history_cities_v2');
        if (TextUtils.isNoEmpty(historyCityListJson)) {
            let list: CityInfo[] = plainToInstance<CityInfo, object>(CityInfo, JSON.parse(historyCityListJson));
            setHistoryCityList(list?.splice(0, 3) ?? []);
        }
        //初始化当前城市层级
        let first = new ECity();
        first.areaCode = '000000';
        first.areaName = '全国';
        first.displayType = '-1';
        setLevelCityList([first, ...(props.cityList ?? [])]);
    }, []);

    //选中的城市
    useEffect(() => {
        setSelectedCityList(props.selectedCityList ?? []);
    }, [props.selectedCityList]);

    /**
     * 注释: 绘制目的地页
     * 时间: 2024/12/27 星期五 11:06
     * <AUTHOR>
     */
    function renderDestinationView() {
        let views: Array<ReactElement> = [];
        selectedCityList.map((value, index) => {
            views.push(renderDestinationItem(value, index));
        });
        return (
            <View style={styles.destinationViewStyle}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text style={{fontSize: 14, color: '#666', marginRight: 5}}>{props.title ?? '目的地'}</Text>
                    {(props.maxSelectNum ?? 0) > 0 && <Text style={{fontSize: 14, color: '#999'}}>{`(最多可选择${props.maxSelectNum}个)`}</Text>}
                </View>
                <View style={{flexDirection: 'row', marginTop: 5}}>{views}</View>
            </View>
        );
    }

    /**
     * 注释: 绘制历史记录
     * 时间: 2024/12/27 星期五 11:51
     * <AUTHOR>
     */
    function renderHistoryView() {
        let views: Array<ReactElement> = [];
        historyCityList.map((value, index) => {
            views.push(renderHistoryItem(value, index));
        });
        return (
            <View style={styles.destinationViewStyle}>
                {/*标题*/}
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: 10,
                    }}>
                    <Text style={{fontSize: 14, color: '#666', marginRight: 5}}>历史记录</Text>
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            setHistoryCityList([]);
                            Method.removeStringExtra('cache_history_cities_v2');
                        }}>
                        <UIImage source={'base_close_x_blue_2'} style={{width: 9, height: 9}} />
                        <Text style={{fontSize: 14, color: '#5086FC', marginLeft: 5}}>清空</Text>
                    </UITouchableOpacity>
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap'}}>{views}</View>
            </View>
        );
    }

    /**
     * 注释: 绘制选中的目标城市
     * 时间: 2023/8/28 0028 11:54
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderDestinationItem(item: CityInfo, index: number): JSX.Element {
        return (
            <View key={`key_destination_item_${index}`}>
                <UITouchableOpacity
                    onPress={() => {
                        let results = ArrayUtils.remove(selectedCityList, item);
                        setSelectedCityList(results);
                    }}
                    style={{
                        padding: 5,
                        zIndex: 99,
                        alignSelf: 'flex-end',
                        transform: [{translateX: -5}],
                    }}>
                    <UIImage
                        source={'icon_close'}
                        style={{
                            width: 15,
                            height: 15,
                        }}
                    />
                </UITouchableOpacity>
                <View style={styles.destinationItemStyle}>
                    <Text style={{fontSize: 14, color: '#fff', maxWidth: 100}} ellipsizeMode={'tail'} numberOfLines={1}>
                        {getAreaName(item)}
                    </Text>
                </View>
            </View>
        );
    }

    /**
     * 注释: 获取已经选中的城市列表
     * 时间: 2024/12/30 星期一 20:16
     * <AUTHOR>
     * @param selectedCityList
     * @returns {ECity[]}
     */
    function getSelectedECityList(selectedCityList: CityInfo[]): ECity[] {
        let cityList: ECity[] = [];
        for (let i = 0; i < selectedCityList.length; i++) {
            let area = selectedCityList[i].area;
            let city = selectedCityList[i].city;
            let pro = selectedCityList[i].pro;
            if (area) {
                cityList.push(area);
                continue;
            }
            if (city) {
                cityList.push(city);
                continue;
            }
            if (pro) {
                cityList.push(pro);
            }
        }
        return cityList;
    }

    /**
     * 注释: 根据CityInfo获取ECity
     * 时间: 2025/1/7 星期二 11:17
     * <AUTHOR>
     */
    function getECityFromCityInfo(cityInfo: CityInfo) {
        if (cityInfo.area) {
            return cityInfo.area;
        } else if (cityInfo.city) {
            return cityInfo.city;
        } else if (cityInfo.pro) {
            return cityInfo.pro;
        }
    }

    /**
     * 注释: 绘制历史记录Item
     * 时间: 2023/8/28 0028 11:54
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderHistoryItem(item: CityInfo, index: number): JSX.Element {
        return (
            <UITouchableOpacity
                style={styles.itemStyle}
                key={`key_item_history_${index}`}
                onPress={() => {
                    if (props.selectMode == 0) {
                        Method.putStringExtra('cache_history_cities_v2', JSON.stringify([item]));
                        props.onSelect && props.onSelect([item]);
                        props.onClose && props.onClose();
                        return;
                    }
                    let results = selectedCityList.filter((cityInfo) => {
                        return TextUtils.equals(getAreaName(item), getAreaName(cityInfo));
                    });
                    if (ArrayUtils.isEmpty(results)) {
                        //替换全国、全省、全市
                        let city = getECityFromCityInfo(item);
                        if (city?.displayType == '-1') {
                            let results = selectedCityList.filter((cityInfo) => {
                                return !(city?.areaCode == cityInfo.area?.areaCode || city?.areaCode == cityInfo.city?.areaCode || city?.areaCode == cityInfo.pro?.areaCode);
                            });
                            setSelectedCityList([...results, item]);
                        } else {
                            setSelectedCityList([...selectedCityList, item]);
                        }
                    } else {
                        let results = ArrayUtils.remove(selectedCityList, item);
                        setSelectedCityList(results);
                    }
                }}>
                <Text style={{fontSize: 14, color: '#333', maxWidth: 100}} ellipsizeMode={'tail'} numberOfLines={1}>
                    {getAreaName(item)}
                </Text>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 绘制层级选择视图
     * 时间: 2024/12/27 星期五 14:48
     * <AUTHOR>
     */
    function renderLevelsView() {
        let views: Array<ReactElement> = [];
        let selectECityList = getSelectedECityList(selectedCityList);
        levelCityList.map((value, index) => {
            let isSelect = selectECityList.find((item) => item.areaCode == value.areaCode) != undefined;
            views.push(isSelect ? renderSelectItem(value) : renderItem(value));
        });
        let showAreaName = convertAreaName(selectCity?.areaName ?? '');
        if (TextUtils.isEmpty(showAreaName)) {
            showAreaName = '全国';
        }
        return (
            <View style={styles.destinationViewStyle}>
                <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                    <Text>{`选择：${showAreaName}`}</Text>
                    {(selectCityInfo.pro != undefined || selectCityInfo.city != undefined) && (
                        <UITouchableOpacity
                            onPress={() => {
                                if (selectCityInfo.city != undefined && selectCityInfo.pro != undefined) {
                                    //退回省级
                                    let childList = selectCityInfo.pro.childList ?? [];
                                    let first = new ECity();
                                    first.areaCode = selectCityInfo.pro.areaCode;
                                    first.areaType = selectCityInfo.pro.areaType;
                                    first.areaName = '全省';
                                    first.displayType = '-1';
                                    first.childList = childList;
                                    let cityInfo = plainToInstance(CityInfo, JSON.parse(JSON.stringify(selectCityInfo)));
                                    cityInfo.city = undefined;
                                    setSelectCityInfo(cityInfo);
                                    setLevelCityList([first, ...childList]);
                                } else {
                                    //退回全国
                                    let first = new ECity();
                                    first.areaCode = '000000';
                                    first.areaName = '全国';
                                    first.displayType = '-1';
                                    setSelectCityInfo(new CityInfo());
                                    setLevelCityList([first, ...(props.cityList ?? [])]);
                                }
                            }}>
                            <Text style={{color: '#5086FC', fontSize: 14}}>返回上一级</Text>
                        </UITouchableOpacity>
                    )}
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center', flexWrap: 'wrap', marginTop: 15}}>{views}</View>
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2023/8/28 0028 11:54
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderItem(item: ECity): JSX.Element {
        return (
            <UITouchableOpacity
                style={styles.itemStyle}
                key={'key_item_' + item?.areaCode}
                onPress={() => {
                    if (item.displayType == '-1') {
                        //选中全国、全省或者全市
                        if (TextUtils.equals('全国', item.areaName)) {
                            let cityInfo = new CityInfo();
                            cityInfo.pro = item;
                            if (props.selectMode == 0) {
                                Method.putStringExtra('cache_history_cities_v2', JSON.stringify([cityInfo]));
                                props.onSelect && props.onSelect([cityInfo]);
                                props.onClose && props.onClose();
                                return;
                            }
                            setSelectedCityList([cityInfo]);
                            setSelectCity(item);
                            Method.showToast('【全国】已替换所有下级地区');
                        } else if (TextUtils.equals('全省', item.areaName)) {
                            //去除选择的全国
                            let filterCityList = selectedCityList.filter((cityInfo) => {
                                return !TextUtils.equals('全国', cityInfo.pro?.areaName);
                            });
                            //去除选中的省级地级市
                            filterCityList = filterCityList.filter((cityInfo) => {
                                let cityChildList = item.childList ?? [];
                                for (let i = 0; i < cityChildList?.length; i++) {
                                    if (cityChildList[i].areaCode == cityInfo.pro?.areaCode || cityChildList[i].areaCode == cityInfo.city?.areaCode || cityChildList[i].areaCode == cityInfo.area?.areaCode) {
                                        return false;
                                    } else {
                                        let areaChildList = cityChildList[i].childList ?? [];
                                        for (let j = 0; j < areaChildList?.length; j++) {
                                            if (areaChildList[j].areaCode == cityInfo.pro?.areaCode || areaChildList[j].areaCode == cityInfo.city?.areaCode || areaChildList[j].areaCode == cityInfo.area?.areaCode) {
                                                return false;
                                            }
                                        }
                                    }
                                }
                                return true;
                            });
                            let cityInfo = new CityInfo();
                            let pro = plainToInstance(ECity, JSON.parse(JSON.stringify(selectCityInfo.pro)));
                            pro.displayType = '-1';
                            cityInfo.pro = pro;
                            if (props.selectMode == 1 && filterCityList.length + 1 > (props.maxSelectNum ?? 0)) {
                                Method.showDialog({
                                    message: '最多只能选择' + (props.maxSelectNum ?? 0) + '个城市',
                                    hideCancel: true,
                                });
                                return;
                            }
                            if (props.selectMode == 0) {
                                Method.putStringExtra('cache_history_cities_v2', JSON.stringify([...filterCityList, cityInfo]));
                                props.onSelect && props.onSelect([...filterCityList, cityInfo]);
                                props.onClose && props.onClose();
                                return;
                            }
                            setSelectedCityList([...filterCityList, cityInfo]);
                            setSelectCity(pro);
                            Method.showToast('【全省】已替换所有下级地区');
                        } else if (TextUtils.equals('全市', item.areaName)) {
                            //去除选择的全国
                            let filterCityList = selectedCityList.filter((cityInfo) => {
                                return !TextUtils.equals('全国', cityInfo.pro?.areaName);
                            });
                            //去除选中的省级地级市
                            filterCityList = filterCityList.filter((cityInfo) => {
                                return !(cityInfo.pro?.displayType == '-1' && cityInfo.city == undefined && cityInfo.pro?.areaCode == selectCityInfo.pro?.areaCode);
                            });
                            //去除选中的市级地级区
                            filterCityList = filterCityList.filter((cityInfo) => {
                                let city = item?.childList?.find((city) => {
                                    return city.areaCode == cityInfo.pro?.areaCode || city.areaCode == cityInfo.city?.areaCode || city.areaCode == cityInfo.area?.areaCode;
                                });
                                return city == undefined;
                            });
                            let cityInfo = new CityInfo();
                            cityInfo.pro = selectCityInfo.pro;
                            if (selectCityInfo.city) {
                                let city = plainToInstance(ECity, JSON.parse(JSON.stringify(selectCityInfo.city)));
                                city.displayType = '-1';
                                cityInfo.city = city;
                                setSelectCity(city);
                            }
                            if (props.selectMode == 1 && filterCityList.length + 1 > (props.maxSelectNum ?? 0)) {
                                Method.showDialog({
                                    message: '最多只能选择' + (props.maxSelectNum ?? 0) + '个城市',
                                    hideCancel: true,
                                });
                                return;
                            }
                            if (props.selectMode == 0) {
                                Method.putStringExtra('cache_history_cities_v2', JSON.stringify([...filterCityList, cityInfo]));
                                props.onSelect && props.onSelect([...filterCityList, cityInfo]);
                                props.onClose && props.onClose();
                                return;
                            }
                            setSelectedCityList([...filterCityList, cityInfo]);
                            Method.showToast('【全市】已替换所有下级地区');
                        }
                    } else {
                        if (ArrayUtils.isNoEmpty(item.childList)) {
                            //正常下钻
                            let childList = item.childList ?? [];
                            let first = new ECity();
                            let cityInfo = plainToInstance(CityInfo, JSON.parse(JSON.stringify(selectCityInfo)));
                            if (item.displayType == '1') {
                                first.areaCode = item.areaCode;
                                first.areaType = item.areaType;
                                first.areaName = '全省';
                                first.displayType = '-1';
                                first.childList = childList;
                                cityInfo.pro = item;
                            } else if (item.displayType == '2') {
                                first.areaCode = item.areaCode;
                                first.areaType = item.areaType;
                                first.areaName = '全市';
                                first.displayType = '-1';
                                first.childList = childList;
                                if (cityInfo.pro) {
                                    cityInfo.city = item;
                                } else {
                                    cityInfo.pro = item;
                                }
                            }
                            setLevelCityList([first, ...childList]);
                            setSelectCityInfo(cityInfo);
                        } else {
                            //最后一级选中
                            if (props.selectMode == 1 && selectedCityList.length >= (props.maxSelectNum ?? 0)) {
                                Method.showDialog({
                                    message: '最多只能选择' + (props.maxSelectNum ?? 0) + '个城市',
                                    hideCancel: true,
                                });
                                return;
                            }
                            item.isSelect = '1';
                            let cityInfo = plainToInstance(CityInfo, JSON.parse(JSON.stringify(selectCityInfo)));
                            cityInfo.area = item;
                            //去除已经选中的全市或者全省
                            let filterCityList = selectedCityList.filter((selectedCityInfo) => {
                                let pro = selectedCityInfo.pro;
                                let city = selectedCityInfo.city;
                                if (pro?.displayType == '-1' || city?.displayType == '-1') {
                                    if (cityInfo.city?.areaCode == city?.areaCode || cityInfo.pro?.areaCode == pro?.areaCode) {
                                        Method.showToast(`【${getAreaName(cityInfo)}】已经替换【${getAreaName(selectedCityInfo)}】`);
                                        return false;
                                    }
                                }
                                return true;
                            });
                            if (props.selectMode == 0) {
                                Method.putStringExtra('cache_history_cities_v2', JSON.stringify([...filterCityList, cityInfo]));
                                props.onSelect && props.onSelect([...filterCityList, cityInfo]);
                                props.onClose && props.onClose();
                                return;
                            }
                            setSelectCityInfo(cityInfo);
                            setSelectedCityList([...filterCityList, cityInfo]);
                            setSelectCity(item);
                        }
                    }
                }}>
                <Text style={{fontSize: 14, color: '#333', maxWidth: 100}} ellipsizeMode={'tail'} numberOfLines={1}>
                    {convertAreaName(item?.areaName ?? '')}
                </Text>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 绘制被选中的城市
     * 时间: 2024/12/27 星期五 14:30
     * <AUTHOR>
     * @param item
     * @returns {JSX.Element}
     */
    function renderSelectItem(item: ECity): JSX.Element {
        return (
            <UITouchableOpacity
                style={styles.selectItemStyle}
                key={'key_item_' + item?.areaCode}
                onPress={() => {
                    let filterCityList = selectedCityList.filter((cityInfo) => {
                        return cityInfo.pro?.areaCode != item?.areaCode && cityInfo.city?.areaCode != item?.areaCode && cityInfo.area?.areaCode != item?.areaCode;
                    });
                    setSelectedCityList(filterCityList);
                }}>
                <Text style={{fontSize: 14, color: '#fff', maxWidth: 100}} ellipsizeMode={'tail'} numberOfLines={1}>
                    {convertAreaName(item?.areaName ?? '')}
                </Text>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 获取节点地区名称
     * 时间: 2023/9/5 0005 17:23
     * <AUTHOR>
     * @param cityInfo
     */
    function getAreaName(cityInfo: CityInfo) {
        if (TextUtils.isNoEmpty(cityInfo?.area?.areaName)) {
            return convertAreaName(cityInfo?.area?.areaName ?? '');
        } else if (TextUtils.isNoEmpty(cityInfo?.city?.areaName)) {
            return convertAreaName(cityInfo?.city?.areaName ?? '');
        } else {
            return convertAreaName(cityInfo?.pro?.areaName ?? '');
        }
    }

    /**
     * 注释: 转换地区名称
     * 时间: 2024/12/27 星期五 15:43
     * <AUTHOR>
     * @param areaName
     */
    function convertAreaName(areaName: string) {
        switch (areaName) {
            case '内蒙古自治区':
                return '内蒙古';
            case '宁夏回族自治区':
                return '宁夏';
            case '新疆维吾尔自治区':
                return '新疆';
            case '香港特别行政区':
                return '香港';
            case '澳门特别行政区':
                return '澳门';
            case '西藏自治区':
                return '西藏';
            case '广西壮族自治区':
                return '广西';
            case '香港岛中西区':
                return '中西区';
            case '北京市':
                return '北京';
            case '天津市':
                return '天津';
            case '重庆市':
                return '重庆';
            case '上海市':
                return '上海';
            default:
                if (areaName.endsWith('省') && areaName != '全省') {
                    return areaName.substring(0, areaName.length - 1);
                }
                return areaName;
        }
    }

    return (
        <Modal
            style={[{justifyContent: 'flex-start', padding: 0, margin: 0}, props.style]}
            animationIn={'slideInDown'}
            backdropOpacity={0}
            useNativeDriver={true}
            isVisible={true}
            onBackButtonPress={() => {
                props.onClose && props.onClose();
            }}
            onBackdropPress={() => {
                props.onClose && props.onClose();
            }}>
            <View style={{backgroundColor: '#eff0f3', flexDirection: 'column'}}>
                {/*选中的目的地*/}
                {props.showDestinationView && renderDestinationView()}
                <ScrollView style={{maxHeight: 350}}>
                    {/*历史记录*/}
                    {(props.hasHistory ?? true) && renderHistoryView()}
                    {/*层级选择*/}
                    {renderLevelsView()}
                </ScrollView>
                {/*确认选择*/}
                {props.showOkButton && (
                    <View style={{backgroundColor: '#fff', paddingVertical: 10}}>
                        <UIButton
                            text={'确认选择'}
                            style={{marginHorizontal: 20}}
                            onPress={() => {
                                Method.putStringExtra('cache_history_cities_v2', JSON.stringify(selectedCityList));
                                props.onSelect && props.onSelect(selectedCityList);
                                props.onClose && props.onClose();
                            }}
                        />
                    </View>
                )}
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    destinationItemStyle: {
        padding: 5,
        minWidth: 75,
        height: 30,
        marginRight: 15,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Constant.color_5086fc,
        borderRadius: 4,
        transform: [{translateY: -15}],
    },
    selectItemStyle: {
        padding: 5,
        minWidth: 65,
        height: 30,
        marginRight: 15,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Constant.color_5086fc,
        borderRadius: 4,
    },
    itemStyle: {
        borderColor: '#333',
        borderWidth: 0.5,
        borderRadius: 4,
        minWidth: 65,
        height: 30,
        marginRight: 15,
        marginTop: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    destinationViewStyle: {
        flexDirection: 'column',
        backgroundColor: '#fff',
        paddingVertical: 10,
        paddingHorizontal: 15,
        marginBottom: 10,
    },
});
