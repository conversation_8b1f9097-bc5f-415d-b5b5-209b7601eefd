import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import TextUtils from '../util/TextUtils';
import UIImagePostItem from './UIImagePostItem';
import {Method} from '../util/NativeModulesTools';
/**
 * 多图片上传(一次可选择多个图片文件上传)
 */

interface Props {
    imgs?: string[]; //图片集合
    takePhoto?: boolean; //只拍照
    onChange?: Function; //图片改变回调
    edit?: boolean; //是否可编辑
    max: number; //最大上传数量
    rowSize: number;

    transformUrl(url: string): string;

    showLoading(show: boolean): void;
}

function UIImageMultipleFileView(props: Props, ref) {
    //当前运行状态
    const running = useRef<boolean>(true);

    const [imgsList, setImageList] = useState(props.imgs ?? []);

    const imgsListRef = useRef<string[]>(props.imgs ?? []);

    useEffect(() => {
        //监听图片列表
        const list = props.imgs ?? [];
        setImageList(list);
        imgsListRef.current = list;

        return () => {
            running.current = false;
        };
    }, [props.imgs]);

    const getImages = () => {
        //获取数据
        return imgsList;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({getImages}));

    const lookImagePage = (item, imageUrl) => {
        //查看大图
        Method.onLookImageClick2(imageUrl);
    };

    const delPic = (index, item, imageUrl) => {
        //删除
        let newList = imgsList.filter((value) => !TextUtils.equals(value, item));
        setImageList(newList);
        imgsListRef.current = newList;

        Method.showToast('已删除图片');
        props.onChange && props.onChange(newList);
    };

    /***上传文件*/
    const upFile = async (imageWater, imageOld, compateCallback = () => {}) => {
        let json = await Method.upFileNew(imageWater);
        let result = JSON.parse(json);

        if (TextUtils.equals('200', result.code)) {
            let newList = [...imgsListRef.current, result.url];
            //更新显示
            imgsListRef.current = newList;
            setImageList(newList);
            props.onChange && props.onChange(newList);
        }

        compateCallback();
        console.log('上传图片==结束====' + imageWater);
    };

    /***接收返回拍照或选择图片(无水印)*/
    const activityNoWaterResultFile = (code, json) => {
        try {
            let result = JSON.parse(json);
            if (code == 200) {
                if (imgsList.length >= props.max) {
                    Method.showToast('图片数量已达上限！');
                    return;
                }
                //上传图片文件是集合  显示loading
                props.showLoading && props.showLoading(true);
                let count = 0;
                let compateCallback = () => {
                    count++;
                    if (count >= result.length) {
                        //隐藏loading
                        props.showLoading && props.showLoading(false);
                    }
                };
                result.map(async (item) => {
                    await upFile(item, '', compateCallback);
                });
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            props.showLoading && props.showLoading(false);
            Method.showToast('请重新选择上传文件(R1001');
        }
    };

    /**
     * 选择图片
     */
    const choosePic = () => {
        let count = props.max - imgsList.length;
        //不加水印
        Method.openCameraAndLibNew(props.takePhoto ?? false, count > 0 ? count : 1, activityNoWaterResultFile);
    };

    return <UIImagePostItem item={imgsList} edit={props.edit ?? true} max={props.max} rowSize={props.rowSize} delPic={delPic} lookImagePage={lookImagePage} choosePic={choosePic} transformUrl={props.transformUrl} />;
}

export interface ImageMultipleFileViewRef {
    getImages: () => string[];
}

export default forwardRef<ImageMultipleFileViewRef, Props>(UIImageMultipleFileView);
