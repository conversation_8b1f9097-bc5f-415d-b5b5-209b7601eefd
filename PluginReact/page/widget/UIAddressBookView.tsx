import {FlatList, SectionList, StyleSheet, Text, View} from 'react-native';
import React, {createRef, useEffect, useRef, useState} from 'react';
// import pinyin from 'pinyin';
import TextUtils from '../util/TextUtils';
import UIImage from './UIImage';
import {http} from '../const.global';
import {ArrayUtils} from '../util/ArrayUtils';
import UITouchableOpacity from './UITouchableOpacity';

export class LetterList {
    title: string;
    data: any[];
}

interface Props {
    friendsData: any[]; //列表数据
    onclick?: Function; //Item点击事件
    onLongClickItem?: Function; //Item长按事件
}

/**
 * 注释: 通讯录视图
 * 时间: 2023/12/19 15:43
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIAddressBookView(props: Props) {
    //列表Ref
    const sectionListRef = createRef<SectionList>();
    //首字母数组
    const letterArr = useRef(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '#']);
    //实际能点击的首字母
    const acLetterArr = useRef<string[]>([]);
    //好友数组
    const [sections, setSections] = useState<any[]>([]);
    //用户首字母
    const [] = useState<[]>();

    useEffect(() => {
        getFriendList(props.friendsData);
    }, [props.friendsData]);

    /**
     * 注释: 设置联系人列表
     * 时间: 2023/12/19 13:58
     * <AUTHOR>
     */
    async function getFriendList(friendsData) {
        let sectionTemp: LetterList[] = [];

        letterArr.current.map((letter) => {
            sectionTemp.push({
                title: letter,
                data: [],
            });
        });
        for (let listItem of friendsData) {
            // @ts-ignore
            let letter = await CommonUtils.translatePinyin(listItem.userName);
            acLetterArr.current.push(letter);
            acLetterArr.current = [...new Set(acLetterArr.current)].sort();
            sectionTemp.map((item) => {
                // @ts-ignore
                if (item.title.toUpperCase() == letter) {
                    // @ts-ignore
                    item.data.push(listItem);
                }
            });
        }
        //删除空数组，防止已有的联系人数据不会渲染
        let section = sectionTemp.filter((item) => {
            return ArrayUtils.isNoEmpty(item.data);
        });
        setSections(section);
    }

    /**
     * 注释: 字母关联分组跳转
     * 时间: 2023/12/19 15:01
     * <AUTHOR>
     * @param key
     */
    function onSectionSelect(key) {
        if (acLetterArr.current.includes(key.toUpperCase())) {
            sectionListRef.current &&
                sectionListRef.current.scrollToLocation({
                    itemIndex: 0,
                    sectionIndex: acLetterArr.current.indexOf(key),
                    viewOffset: 0,
                });
        }
    }

    /**
     * 注释: 加密手机号
     * 时间: 2023/12/21 8:37
     * <AUTHOR>
     * @param phoneNumber
     * @returns {any}
     */
    function encryptPhoneNumber(phoneNumber) {
        if (phoneNumber.length !== 11) {
            return phoneNumber; // 如果手机号长度不符合要求，直接返回原始手机号
        }

        const firstPart = phoneNumber.slice(0, 3); // 获取前三位
        const encryptedPart = '****'; // 加密替换的部分
        const lastPart = phoneNumber.slice(7); // 获取后四位

        return firstPart + encryptedPart + lastPart; // 拼接加密后的手机号
    }

    /**
     * 注释: 分组列表的头部
     * 时间: 2023/12/19 15:01
     * <AUTHOR>
     * @param sectionItem
     * @returns {JSX.Element}
     */
    function renderSectionHeader(sectionItem) {
        const {section} = sectionItem;
        return (
            <View style={{height: 35, justifyContent: 'center', alignItems: 'flex-start', paddingLeft: 14}}>
                <Text style={{}}>{section.title.toUpperCase()}</Text>
            </View>
        );
    }

    /**
     * 注释: 分组列表的renderItem
     * 时间: 2023/12/19 15:01
     * <AUTHOR>
     * @param item
     * @param index
     * @returns {JSX.Element}
     */
    function renderItem(item, index) {
        let imgUrl = '';
        if (`${item.headUrl}`.startsWith('http')) {
            imgUrl = item.headUrl;
        } else {
            if (TextUtils.isNoEmpty(item.headUrl)) {
                imgUrl = http.imagUrl(item.headUrl);
            } else {
                imgUrl = 'base_user_def';
            }
        }
        let showStateMsg = false;
        if (TextUtils.equals('0', item.state)) {
            showStateMsg = true;
        }
        return (
            <UITouchableOpacity
                style={{height: 64, backgroundColor: '#fff', justifyContent: 'center'}}
                activeOpacity={0.75}
                onLongPress={() => {
                    props.onLongClickItem && props.onLongClickItem(item);
                }}
                onPress={() => {
                    props.onclick && props.onclick(item);
                }}>
                <View style={{flexDirection: 'row', paddingLeft: 14}}>
                    <UIImage source={imgUrl} style={{width: 43, height: 43, borderRadius: 20}} resizeMode={'stretch'} />
                    <View style={{marginLeft: 10}}>
                        <Text style={{fontSize: 17, color: '#333'}}>{item.customerName}</Text>
                        <Text style={{fontSize: 14, color: '#666', marginTop: 3}}>{encryptPhoneNumber(item.mobile)}</Text>
                    </View>
                    {showStateMsg && <Text style={{fontSize: 14, color: '#999', alignSelf: 'flex-end', marginLeft: 50}}>待好友验证通过</Text>}
                </View>
            </UITouchableOpacity>
        );
    }

    return (
        <View style={{flex: 1}}>
            <SectionList
                // @ts-ignore
                ref={sectionListRef}
                renderItem={({item, index}) => renderItem(item, index)}
                renderSectionHeader={renderSectionHeader}
                sections={sections}
                keyExtractor={(item, index) => item + index}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
            />

            {/*右侧字母栏*/}
            <View
                style={{
                    width: 20,
                    flex: 1,
                    alignItems: 'center',
                    position: 'absolute',
                    right: 7,
                    top: 30,
                    justifyContent: 'center',
                }}>
                <FlatList
                    data={letterArr.current}
                    keyExtractor={(item, index) => index.toString()}
                    renderItem={({item, index}) => (
                        <UITouchableOpacity
                            onPress={() => {
                                onSectionSelect(item);
                            }}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    width: 24,
                                    height: 20,
                                    textAlign: 'center',
                                    color: '#5086fc',
                                }}>
                                {item.toUpperCase()}
                            </Text>
                        </UITouchableOpacity>
                    )}
                />
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    separator: {
        height: 1,
        backgroundColor: '#eff0f3',
    },
});
