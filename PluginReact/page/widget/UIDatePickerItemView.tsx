import {StyleProp, StyleSheet, Text, TextStyle, View, ViewStyle} from 'react-native';
import React, {useEffect, useState} from 'react';
import UIImage from './UIImage';
import UIDatePicker from './UIDatePicker';
import {dateFormat} from '../util/DateUtil';
import TextUtils from '../util/TextUtils';
import UITouchableOpacity from './UITouchableOpacity';
import { gScreen_width } from '../util/scaled-style';

interface Props {
    //标题
    title: string;
    //副标题
    subtitle?: string;
    //主标题样式
    titleStyle?: StyleProp<TextStyle>;
    //副标题样式
    subtitleStyle?: StyleProp<TextStyle>;
    //选择时间样式
    dateStyle?: StyleProp<TextStyle>;
    //选择时间文案
    dataText?: string;
    //额外样式
    style?: ViewStyle;
    //选中时间回调
    onSelect?: Function;
    //默认时间
    defaultTime?: Date;
    //最小值
    minDate?: Date;
    //最大值
    maxDate?: Date;
    //选择时间格式化
    dataFormat?: string;
    //分钟间隔
    minuteStep?: number;
    //是否可以编辑
    enable?: boolean;
    //点击事件
    onClick?: Function;
    //调用来源 0 : 报价 1 其他
    source?: number;
}

/**
 * 注释: 时间选择器ItemView
 * 时间: 2023/11/23 0023 11:08
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIDatePickerItemView(props: Props) {
    const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
    const [selectDate, setSelectDate] = useState<Date>();

    useEffect(() => {
        if (props.defaultTime) {
            setSelectDate(props.defaultTime);
            props.onSelect && props.onSelect(props.defaultTime);
        }
    }, [props.defaultTime]);

    //WLHY-2106 承运方议价单报价有效期组件优化
    const getStepNowDate = (time?: Date): Date => {
        let now = time ? time : new Date();
        let minutes = now.getMinutes();
        if (minutes <= 5) {
            now.setMinutes(5);
        } else if (minutes <= 10) {
            now.setMinutes(10);
        } else if (minutes <= 15) {
            now.setMinutes(15);
        } else if (minutes <= 20) {
            now.setMinutes(20);
        } else if (minutes <= 25) {
            now.setMinutes(25);
        } else if (minutes <= 30) {
            now.setMinutes(30);
        } else if (minutes <= 35) {
            now.setMinutes(35);
        } else if (minutes <= 40) {
            now.setMinutes(40);
        } else if (minutes <= 45) {
            now.setMinutes(45);
        } else if (minutes <= 50) {
            now.setMinutes(50);
        } else if (minutes <= 55) {
            now.setMinutes(55);
        } else {
            now.setMinutes(60);
        }
        return now;
    };

    const getStepNowDateV2 = (time?: Date): Date => {
        //取整数
        let now = time ? time : new Date();
        now.setMinutes(60);
        return now;
    };
    return (
        <View style={[styles.mainView, props.style]}>
            <View>
                <Text style={[{fontSize: 16, color: '#333'}, props.titleStyle]}>{props.title}</Text>
                {TextUtils.isNoEmpty(props.subtitle) && <Text style={[{fontSize: 12, color: '#999', marginTop: 5}, props.subtitleStyle]}>{props.subtitle}</Text>}
            </View>
            <UITouchableOpacity
                style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}
                onPress={() => {
                    props.onClick && props.onClick();
                    if (props.enable) {
                        setShowDatePicker(true);
                    }
                }}>
                <Text
                    style={[
                        {
                            fontSize: 16,
                            color: selectDate ? '#333' : '#999',
                            flex: 1,
                            textAlign: 'right',
                        },
                        props.dateStyle,
                    ]}>{`${selectDate ? dateFormat(selectDate, props.dataFormat ?? 'yyyy-MM-dd HH:mm:00') : '请选择时间'}`}</Text>
                <UIImage source={'base_right_arrow_gray'} style={{marginLeft: 10, width: 10, height: 18}} />
            </UITouchableOpacity>
            {/*时间选择器*/}
            {showDatePicker && (
                <UIDatePicker
                    title={props.title}
                    onHideEvent={() => {
                        setShowDatePicker(false);
                    }}
                    mode={'datetime'}
                    onSelectEvent={(value) => {
                        setSelectDate(value);
                        props.onSelect && props.onSelect(value);
                    }}
                    defaultTime={selectDate ?? getStepNowDate(props.minDate)}
                    minDate={props.source == 0 ? getStepNowDateV2(props.minDate) : getStepNowDate(props.minDate)}
                    maxDate={props.maxDate}
                    minuteStep={props.minuteStep}
                    style={{marginRight:props.source == 0 ? gScreen_width/-5 : 0}}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    mainView: {
        backgroundColor: '#fff',
        padding: 14,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
});
