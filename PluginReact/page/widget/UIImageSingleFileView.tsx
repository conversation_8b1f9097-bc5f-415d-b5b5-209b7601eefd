import React, {useImperativeHandle} from 'react';
import {http} from '../const.global';
import TextUtils from '../util/TextUtils';
import {ImageStyle, StyleProp, View} from 'react-native';
import UIImage from './UIImage';
import {gScreen_width} from '../util/scaled-style';
import UITouchableOpacity from './UITouchableOpacity';
import {Method} from '../util/NativeModulesTools';
import {ResizeMode} from 'react-native-fast-image';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';

/**
 * 单图片上传(选择，拍照，上传,删除，查看大图)
 */
export interface ViewCallback {
    openCameraAndClear: () => void;
    delPic: () => void;
}

export type Props = {
    ref?: React.Ref<UIImageSingleFileViewRef>;
    url: string; //图片
    defaultValue?: string; //默认图片
    takePhoto?: boolean; //只拍照
    edit?: boolean; //是否可编辑
    imgStyle?: StyleProp<ImageStyle>; //图片样式
    onDelPic?: () => void; //删除
    onPostSuccess?: (url: string, nextCallback?: ViewCallback) => void; //上传成功回调
    transformUrl?: (url: string) => string; //转换可以显示完整路径
    showLoading?: (show: boolean) => void; //上传显示loading
    onSelectFile?: (callback: (file: string) => void) => void; //自定义选择文件
    //显示模式
    resizeMode?: ResizeMode;
    canDelete?: boolean;
};

function UIImageSingleFileView({url, defaultValue = 'certification_cys_take_picture', takePhoto = false, edit = true, canDelete = true, imgStyle, ...restProps}: Props, ref: React.Ref<UIImageSingleFileViewRef>) {
    const [imgUrl, setImgUrl] = React.useState(url);
    React.useEffect(() => {
        if (!TextUtils.equals(imgUrl, url)) {
            console.log('更新图片');
            setImgUrl(url);
        }
    }, [url]);
    //当前运行状态
    const running = React.useRef<boolean>(true);

    React.useEffect(() => {
        return () => {
            running.current = false;
        };
    }, []);

    /***上传文件*/
    const upFile = React.useCallback(async (file) => {
        //上传图片文件是集合  显示loading
        restProps.showLoading && restProps.showLoading(true);

        let json = await Method.upFileNew(file);
        if (running.current) {
            let result = JSON.parse(json);
            if (TextUtils.equals('200', result.code)) {
                //更新显示
                console.log('上传图片');
                setImgUrl(result.url);
                restProps.onPostSuccess && restProps.onPostSuccess(result.url);
            } else {
                Method.showToast(result.msg);
            }
            //隐藏loading
            restProps.showLoading && restProps.showLoading(false);
        }
    }, []);

    /***接收返回拍照或选择图片(无水印)*/
    const activityNoWaterResultFile = React.useCallback((code, json) => {
        try {
            let result = JSON.parse(json);
            if (code == 200) {
                result.map(async (item: string) => await upFile(item));
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            Method.showToast('请重新选择上传文件(R1001');
        }
    }, []);

    const onClickImg = () => {
        if (edit && TextUtils.isEmpty(imgUrl)) {
            if (restProps.onSelectFile) {
                restProps.onSelectFile((file: string) => {
                    upFile(file).then((r) => {});
                });
            } else {
                //不加水印
                Method.openCameraAndLibNew(takePhoto, 1, activityNoWaterResultFile);
            }
        } else if (TextUtils.isNoEmpty(imgUrl)) {
            //查看大图
            let imageUrl = restProps.transformUrl ? restProps.transformUrl(imgUrl) : imgUrl;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ImagePreViewPage',
                data: {urlList: [imageUrl], canDelete: edit && canDelete && TextUtils.isNoEmpty(imageUrl)},
                callBack: () => {
                    //删除照片回调
                    setImgUrl('');
                    restProps.onDelPic && restProps.onDelPic();
                },
            });
        }
    };

    // 触发上传的方法
    const triggerUpload = React.useCallback(() => {
        if (edit && TextUtils.isEmpty(imgUrl)) {
            if (restProps.onSelectFile) {
                restProps.onSelectFile((file: string) => {
                    upFile(file).then((r) => {});
                });
            } else {
                //不加水印
                Method.openCameraAndLibNew(takePhoto, 1, activityNoWaterResultFile);
            }
        }
    }, [edit, imgUrl, takePhoto, restProps, upFile, activityNoWaterResultFile]);

    //对外抛出方法
    useImperativeHandle(ref, () => ({
        clear: () => setImgUrl(''),
        triggerUpload: triggerUpload
    }));

    return (
        <View>
            <UITouchableOpacity activeOpacity={0.9} onPress={onClickImg}>
                <UIImage
                    resizeMode={restProps.resizeMode ?? 'stretch'}
                    style={[{width: gScreen_width, height: 150, alignSelf: 'center'}, imgStyle]}
                    source={TextUtils.isEmpty(imgUrl) ? defaultValue : restProps.transformUrl ? restProps.transformUrl(imgUrl) : http.imagUrl(imgUrl)}
                />
            </UITouchableOpacity>
        </View>
    );
}

export interface UIImageSingleFileViewRef {
    clear: () => void;
    triggerUpload: () => void;
}

export default React.memo(React.forwardRef<UIImageSingleFileViewRef, Props>(UIImageSingleFileView));
