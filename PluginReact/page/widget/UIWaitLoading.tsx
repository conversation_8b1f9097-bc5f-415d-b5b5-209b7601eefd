import React from 'react';
import {StyleSheet, View} from 'react-native';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width} from '../util/scaled-style';
import Lottie from 'lottie-react-native';

/**
 * 等待对话框
 */
interface Props {
    visible?: boolean;
    dismiss?: Function;
}

export default function UIWaitLoading(props: Props) {
    const [isVisible, setIsVisible] = React.useState(false);

    const onClose = React.useCallback(() => {
        props.dismiss && props.dismiss();
    },[]);
    React.useEffect(() => {
        setIsVisible(props.visible??false);
    }, [props.visible]);
    return (
        <Modal
            animationIn={'zoomIn'}
            animationOut={'zoomOut'}
            backdropOpacity={0}
            isVisible={isVisible}
            hideModalContentWhileAnimating={true}
            deviceWidth={gScreen_width}
            deviceHeight={gScreen_height}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            onBackButtonPress={onClose} // 响应返回键
            onBackdropPress={onClose} // 点击背景遮罩层
        >
            <View style={styles.modalBackground}>
                <Lottie
                    source={require('../../src/main/assets/loading.json')}
                    autoPlay={true}
                    style={styles.lottie}
                    renderMode="SOFTWARE"
                    loop={true}
                    speed={1.0}
                    resizeMode="contain"
                />
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    modalBackground: {
        display: 'flex',
        flex: 1,
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'space-around',
    },
    lottie:{width: 50, height: 50}
});
