import {StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import React, {useEffect, useRef, useState} from 'react';
import {gScreen_width} from '../util/scaled-style';
import UIImage from './UIImage';
import TextUtils from '../util/TextUtils';
import UITouchableOpacity from './UITouchableOpacity';
import {Method} from '../util/NativeModulesTools';
/**
 * 视频预览图
 *
 * @param item 数据结合
 * @param transformUrl 转换图片地址
 * @param edit 是否可编辑
 * @param lookImagePage 查看大图(item,imageUrl) imageUrl 网络地址
 * @param delPic 删除(index,item,imageUrl) imageUrl 网络地址
 * @param choosePic 选择图片或拍照
 * @param max 最多数量
 * @param rowSize 每行显示
 * @constructor
 */

interface Props {
    items: any[]; // 数据结合
    transformUrl: Function; //自定义转换可显示图片完整地址
    lookImagePage: Function; //查看大图(item,imageUrl) imageUrl 图片完整地址
    delPic: Function; //删除(index,item,imageUrl) imageUrl图片完整地址
    choosePic: Function; //选择图片或拍照
    max: number; //最多数量
    rowSize: number; //每行显示
    edit: boolean; //是否可编辑
}

class VideoThum {
    public data: any;
    public thum: string;
}

export default function UIVideoThumPostItem(props: Props) {
    //图片大小 = (屏幕宽度-（14 * (每行数量-1 + 左右两边margin值）) / 每行数量
    const size = (gScreen_width - 12 * (props.rowSize - 1 + 2)) / props.rowSize;
    const left = (index: number) => (index % props.rowSize == 0 ? 0 : 7);
    const [videos, setVideos] = useState<any[]>([]);

  useEffect(() => {
    setVideos(props.items)
  },[props.items])

    return (
        <View style={styles.body}>
            {videos.map((item, index) =><UIVideoThumItem item={item} transformUrl={props.transformUrl} lookImagePage={props.lookImagePage} delPic={props.delPic} choosePic={props.choosePic} edit={props.edit} left={left} index={index} size={size}/>)}
            {videos.length < props.max && props.edit && (
                <UITouchableOpacity activeOpacity={0.9} style={{marginLeft: left(videos.length), marginBottom: 7}} onPress={() => props.choosePic()} key={'selector_view_photo_add' + props.max}>
                    <UIImage style={{width: size, height: size}} source={'base_selector_view_photo_2'} key={'selector_view_photo_i_add' + props.max} />
                </UITouchableOpacity>
            )}
        </View>
    );
}
interface PropsItem {
    item: any; // 数据结合
    transformUrl: Function; //自定义转换可显示图片完整地址
    lookImagePage: Function; //查看大图(item,imageUrl) imageUrl 图片完整地址
    delPic: Function; //删除(index,item,imageUrl) imageUrl图片完整地址
    choosePic: Function; //选择图片或拍照
    edit: boolean; //是否可编辑
    left: Function;
    index: number;
    size: number;
}
export const UIVideoThumItem = (props: PropsItem) => {
    const [videoThum, setVideoThum] = useState<VideoThum>();
    const running = React.useRef(true);

    useEffect(() => {
        running.current = true;
        //刷新视频显示
        Method.transferNativeMethod('CommonReactNativeExtension', 'getWebVideoThumbnail', {url: props.transformUrl(props.item)})
        .then(thumResult=>{
            if (!running.current) {
                return;
            }
            let newItem = new VideoThum();
            newItem.data = props.item
            if (TextUtils.equals('200', thumResult.code)) {
                newItem.thum = thumResult.data;
            } else {
                Method.showToast(thumResult.msg);
                newItem.thum = 'base_selector_view_photo_2';
            }
            setVideoThum(newItem);
        });
        return () => {
            running.current = false;
        };
    }, [props.item]);

    return props.edit ? (
        <View style={{marginLeft: props.left(props.index), marginBottom: 7}} key={'video' + props.index}>
            <UITouchableOpacity activeOpacity={0.9} onPress={() => props.lookImagePage(videoThum?.data, videoThum?.thum)} style={styles.center}>
                <FastImage style={{width: props.size, height: props.size}} source={{uri: videoThum?.thum}} />
                <UIImage style={styles.play_iocn} source={'jz_play_normal'} />
            </UITouchableOpacity>
            <UITouchableOpacity onPress={() => props.delPic(props.index, videoThum?.data, videoThum?.thum)} style={styles.edit_icon}>
                <UIImage style={styles.size} source={'review_alterdialog_skip'} />
            </UITouchableOpacity>
        </View>
    ) : (
        <View style={{marginLeft: props.left(props.index), marginBottom: 7}} key={'video' + props.index}>
            <UITouchableOpacity activeOpacity={0.9} onPress={() => props.lookImagePage(videoThum?.data, videoThum?.thum)} style={styles.center}>
                <FastImage style={{width: props.size, height: props.size}} source={{uri: videoThum?.thum}} />
                <UIImage style={styles.play_iocn} source={'jz_play_normal'} />
            </UITouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    body: {flexDirection: 'row', flexWrap: 'wrap', marginLeft: 7, marginRight: 7, marginTop: 7},
    play_iocn: {width: 27, height: 27, position: 'absolute', alignSelf: 'center'},
    size: {width: 27, height: 27},
    edit_icon: {alignSelf: 'flex-end', position: 'absolute'},
    center: {justifyContent: 'center'},
});
