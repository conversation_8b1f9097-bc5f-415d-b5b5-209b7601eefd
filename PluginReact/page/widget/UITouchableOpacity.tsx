import {GestureResponderEvent, StyleProp, TouchableOpacity, ViewStyle} from 'react-native';
import React, {ReactNode} from 'react';
import _ from 'lodash';

interface Props {
    //子控件
    children?: ReactNode;
    onPress?: Function;
    onLongPress?: ((event: GestureResponderEvent) => void) | undefined;
    onPressOut?: ((event: GestureResponderEvent) => void) | undefined;
    style?: StyleProp<ViewStyle> | undefined;
    disabled?: boolean;
    activeOpacity?: number;
}

/**
 * 注释: 防抖点击事件
 * 时间: 2024/11/18 星期一 10:16
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UITouchableOpacity(props: Props) {
    const handlePress = _.debounce(() => {
        props.onPress && props.onPress();
    }, 300);
    return (
        <TouchableOpacity onPress={handlePress} style={props.style} disabled={props.disabled} activeOpacity={props.activeOpacity} onLongPress={props.onLongPress} onPressOut={props.onPressOut}>
            {props.children}
        </TouchableOpacity>
    );
}
