import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {BackHandler, StyleSheet, Text, View} from 'react-native';
import {gScreen_statusBarHeight, gScreen_width} from '../util/scaled-style';
import {gLog} from '../const.global';
import UIImage from './UIImage';
import UITouchableOpacity from './UITouchableOpacity';
import TextUtils from '../util/TextUtils';

/**
 * 标题栏
 * title 标题内容
 * rightText 右侧标题内容
 * clickBack 左侧返回事件
 * clickRight 右侧点击事件
 * @param {*} param0
 * @returns
 */
interface Props {
    title: string;
    rightText?: string;
    clickBack?: Function;
    clickRight?: Function;
    showBack?: boolean;
    isBlack?: boolean; //字体颜色是否为黑色
    isShowCustomer?: boolean; //是否显示在线客服
}

export default function UITitleWhiteView(props: Props) {
    const navigation = useNavigation();

    const showBack = props.showBack ?? true;

    const isShowCustomer = props.isShowCustomer ?? true;

    const leftOnClick = () => {
        //左边点击回调
        gLog('--------左边点击回调--------');
        if (props.clickBack) {
            props.clickBack();
        } else if (navigation.canGoBack()) {
            //是否可以返回
            navigation.goBack();
        } else {
            BackHandler.exitApp();
        }
    };

    const rightOnClick = () => {
        //右侧边点击回调
        if (props.clickRight) {
            props.clickRight();
        }
    };

    return (
        <View style={style.title_body}>
            <View style={style.title_mid}>
                {showBack && (
                    <UITouchableOpacity activeOpacity={0.9} style={style.title_left} onPress={leftOnClick}>
                        <UIImage style={{width: 10, height: 18}} source={props.isBlack ? 'base_back_black' : 'base_back_white'} />
                        <Text style={{fontFamily: '', fontSize: 16, marginLeft: 5, color: props.isBlack ? '#333' : '#fff'}}>返回</Text>
                    </UITouchableOpacity>
                )}
                <Text style={[style.title_content, {color: props.isBlack ? '#333' : '#fff'}]}>{props.title}</Text>
                {isShowCustomer && (
                    <UITouchableOpacity style={style.rightContainer} onPress={rightOnClick}>
                        <UIImage source={'http://img.zczy56.com/202401241507181931834.png'} style={style.rightContainer_icon} />
                        <Text style={style.title_right}>{props.rightText}</Text>
                    </UITouchableOpacity>
                )}
                {!isShowCustomer && TextUtils.isNoEmpty(props.rightText) && (
                    <UITouchableOpacity style={style.rightContainer} onPress={rightOnClick}>
                        <Text style={style.title_right2}>{props.rightText}</Text>
                    </UITouchableOpacity>
                )}
            </View>
        </View>
    );
}

const style = StyleSheet.create({
    title_body: {paddingTop: gScreen_statusBarHeight, width: gScreen_width},
    title_mid: {height: 45, flexDirection: 'row', alignSelf: 'center', alignItems: 'center'},
    title_left: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        paddingLeft: 15,
        zIndex: 2,
    },
    title_content: {
        width: gScreen_width,
        position: 'absolute',
        color: '#fff',
        fontSize: 16,
        textAlign: 'center',
        fontWeight: 'bold',
        zIndex: 1,
    },
    title_right: {
        fontFamily: '',
        color: '#fff',
        fontSize: 16,
        textAlign: 'right',
    },
    title_right2: {
        fontFamily: '',
        color: '#333',
        fontSize: 16,
        textAlign: 'right',
    },
    title_line: {width: gScreen_width, height: 0.5, backgroundColor: '#e3e3e3'},
    rightContainer: {
        flex: 1,
        marginRight: 15,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        zIndex: 2,
    },
    rightContainer_icon: {width: 18, height: 18, marginRight: 7},
});
