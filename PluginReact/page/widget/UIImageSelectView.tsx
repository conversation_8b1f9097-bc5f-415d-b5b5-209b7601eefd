import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import UIImagePostItem from './UIImagePostItem';
import {Method} from '../util/NativeModulesTools';

/**
 * 图片组件
 *
 */
interface Props {
    imgs: any[]; //图片集合
    onChange?: Function; //图片改变回调
    takePhoto: boolean; //只拍照
    edit: boolean; //是否可编辑
    max: number; //最大上传数量
    rowSize: number; //每行数量
    transformData: Function; //上传url数据转为自定义对象
    transformUrl: Function; //自定义对象转图片可以显示url
    showLoading?: Function; //上传图片loading回调
}

function UIImageSelectView(props: Props, ref: React.Ref<UIImageSelectViewRef>) {
    // //当前运行状态
    const running = useRef<boolean>(true);
    //图片
    const [imgList, setImageList] = useState(props.imgs);

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({
        getImages: () => {
            return imgList;
        },
    }));

    /***查看大图*/
    const lookImagePage = (item, imageUrl) => {
        Method.onLookImageClick2(imageUrl);
    };

    /***删除*/
    const delPic = (index, item, imageUrl) => {
        let newImgs = imgList.filter((_, i) => i != index);
        setImageList(newImgs);

        Method.showToast('已删除图片');
        props.onChange && props.onChange(newImgs);
    };

    /***上传文件*/
    const upFile = (file) => {
        //显示loading
        props.showLoading && props.showLoading(true);

        Method.upFile(file, (code, json) => {
            if (running.current) {
                //隐藏loading
                props.showLoading && props.showLoading(false);
                //当前界面正常运行
                let result = JSON.parse(json);
                if (code == 200) {
                    if (imgList.length >= props.max) {
                        Method.showToast('图片数量已达上限！');
                        return;
                    }

                    if (props.transformData) {
                        //转换自定义对象
                        let data = props.transformData(result.url);
                        let newImgs = [...imgList, data];

                        setImageList(newImgs);

                        props.onChange && props.onChange(newImgs);
                    }
                } else {
                    Method.showToast(result.msg);
                }
            }
        });
    };

    /***接收返回拍照或选择图片*/
    const activityResultFile = (code, json) => {
        let result = JSON.parse(json);
        if (code == 200) {
            if (imgList.length >= props.max) {
                Method.showToast('图片数量已达上限！');
                return;
            }
            upFile(result[0]);
        } else if (code == -2 || code == -3) {
            Method.showToast(result.msg);
        }
    };

    /*** 选择图片*/
    const choosePic = () => {
        if (props.takePhoto) {
            //拍照
            Method.openCamera(activityResultFile);
        } else {
            //拍照与图库
            Method.openCameraAndLib(1, true, activityResultFile);
        }
    };

    /***Item 对象转图片可以显示url */
    const transformUrl = (item) => {
        return props.transformUrl && props.transformUrl(item);
    };

    useEffect(() => {
        return () => {
            //当前界面被停止
            running.current = false;
        };
    }, []);

    return <UIImagePostItem item={imgList} edit={props.edit} max={props.max} rowSize={props.rowSize} delPic={delPic} lookImagePage={lookImagePage} choosePic={choosePic} transformUrl={transformUrl} />;
}

export interface UIImageSelectViewRef {
    getImages: () => any[];
}

export default forwardRef<UIImageSelectViewRef, Props>(UIImageSelectView);
