/**全局常量 */
import {NativeModules} from 'react-native';
import './util/scaled-style';
import * as Sentry from '@sentry/react-native';
import TextUtils from './util/TextUtils';

//JS 异常监听
const defaultHander = ErrorUtils.getGlobalHandler();

ErrorUtils.setGlobalHandler((error, isFatal) => {
    Sentry.captureException(error);
    defaultHander(error, isFatal);
});

//RN 版本
export const gVersion = {
    versionCode: 9000,
    aliasName: 'bundle',
    pluginPackageName: 'com.zczy.cyfrn.single',
};

//日志
export const gLog = (msg) => {
    if (__DEV__) {
        console.log(msg);
    }
};

//原生对象声明
export const gOpenNativeModule = NativeModules.OpenNativeModule;

export const http = {
    //请求服务地址
    url: () => gOpenNativeModule.getUrl(),
    webUrl: () => gOpenNativeModule.getWebUrl && gOpenNativeModule.getWebUrl(),
    //图片文件服务地址
    // imagUrl: () => gOpenNativeModule.getImageUrl(),
    imagUrl: (img) => {
        if (TextUtils.isNoEmpty(img) && (img.indexOf('http') != -1 || img.indexOf('https') != -1)) {
            return img;
        }
        return gOpenNativeModule.getImageUrl() + img;
    },
    //数据加密
    checkcode: (data) => gOpenNativeModule.checkcode(data),
    //数据解密
    decheckcode: (data) => gOpenNativeModule.decheckcode(data),
    //白盒秘钥=加密
    aesEncryptStringWithBase64: (data) => gOpenNativeModule.aesEncryptStringWithBase64(data),
    //白盒秘钥=解密
    aesDecryptStringWithBase64: (data) => gOpenNativeModule.aesDecryptStringWithBase64(data),
    //判断数据是否成功
    success: (data) => data && (data.code === '200' || data.code === 200) && data.data && data.data.resultCode === '0000',
    //接口返回信息
    msg: (data) => (data.code === '200' || data.code === 200 ? (data.data ? data.data.resultMsg : '') : data.msg),
    //接口返回code
    code: (data) => (data.code === '200' || data.code === 200 ? (data.data ? data.data.resultCode : '') : data.code),
};

// /**
//  *  注释: 原生UI相关Api
//  * 时间: 2023/6/14 0014 18:32
//  * <AUTHOR>
//  */
// export const UI = {
//     showToast(msg) {
//         gOpenNativeModule.showToast(`${msg}`);
//     },
//     /***
//      * 对话框提示（Android 原生提供支持）
//      */
//     showDialogToast(msg, okAction = () => {}) {
//         let dialog = {title: '提示', message: msg, cancelText: '', okText: '确定', hideCancel: true, cancelable: true};
//         gOpenNativeModule.showDialog(JSON.stringify(dialog), () => {}, okAction);
//     },

//     /***
//      * 对话框提示（Android 原生提供支持）
//      */
//     showDialog({title = '提示', message = '', cancelText = '取消', okText = '确定', hideCancel = false, cancelable = true, cancelAction = () => {}, okAction = () => {}}) {
//         gOpenNativeModule.showDialog(
//             JSON.stringify({
//                 title: title,
//                 message: message,
//                 cancelText: cancelText,
//                 okText: okText,
//                 hideCancel: hideCancel,
//                 cancelable: cancelable,
//             }),
//             cancelAction,
//             okAction,
//         );
//     },

//     /***
//      * 显示等待（Android 原生提供支持）
//      */
//     showLoading() {
//         gOpenNativeModule.showLoading();
//     },

//     /**
//      * 隐藏等待（Android 原生提供支持）
//      */
//     hideLoading() {
//         gOpenNativeModule.hideLoading();
//     },

//     /**
//      * 查看大图
//      * @param {*} files  集合（网络地址）
//      * @param {*} position 显示第几个
//      */
//     onLookImageClick(files, position = 0) {
//         gOpenNativeModule.onLookImageClick(files, position);
//     },

//     /**
//      * 查看大图
//      * @param {*} file 网络地址
//      */
//     onLookImageClick2(file) {
//         gOpenNativeModule.onLookImageClick2(file);
//     },

//     /***
//      * 直接拍照（Android 原生提供支持）
//      * 不建议使用（推荐使用openCameraAndLibNew）
//      */
//     openCamera(callback = (code, json) => {}) {
//         gOpenNativeModule.openCamera(callback);
//     },

//     /***
//      * 相册与拍照（Android 原生提供支持）
//      * 不建议使用（推荐使用openCameraAndLibNew）
//      */
//     openCameraAndLib(selectCount = 1, capture = true, callback = (code, json) => {}) {
//         gOpenNativeModule.openCameraAndLib(selectCount, capture, callback);
//     },
//     /***
//      *  相册与拍照
//      * @param onlyCamera
//      * @param orderId
//      * @param selectCount
//      * @param callback
//      */
//     openCameraAndLibNew(onlyCamera = false, selectCount = 1, callback = (code, json) => {}) {
//         gOpenNativeModule.openCameraAndLibNew(onlyCamera, selectCount, callback);
//     },

//     /***
//      * 图片加印（可以图片一起加印）
//      * @param onlyCamera
//      * @param orderId
//      * @param selectCount
//      * @param callback
//      */
//     openCameraAndLibWater(onlyCamera = false, orderId, selectCount = 1, callback = (code, json) => {}) {
//         gOpenNativeModule.openCameraAndLibWater(onlyCamera, orderId, selectCount, callback);
//     },

//     /***
//      * 打开水印（Android 原生提供支持）
//      * @param orderId
//      * @param file
//      * @param callback
//      */
//     openWaterActivity(orderId, file, callback = (code, json) => {}) {
//         gOpenNativeModule.openWaterActivity(orderId, file, callback);
//     },

//     /***
//      *  获取原生界面传值（Android 原生提供支持）
//      * @return
//      */
//     getIntent(key) {
//         return gOpenNativeModule.getIntent(key);
//     },

//     /***
//      * 打开拨号界面（Android 原生提供支持）
//      * @param phone
//      */
//     callPhone(phone) {
//         gOpenNativeModule.callPhone(phone);
//     },

//     /***
//      * 打开在线客服（Android 原生提供支持）
//      */
//     openLineServer() {
//         gOpenNativeModule.openLineServer();
//     },

//     /***
//      * 打开原生Activty（Android 原生提供支持）
//      * @param pkgCls 类名
//      * @param params 参数
//      * @param callback 回调
//      */
//     openNativeActivity(pkgCls, params, callback) {
//         gOpenNativeModule.openNativeActivity(pkgCls, params, callback);
//     },

//     /***
//      * 打开登录（Android 原生提供支持）
//      */
//     openLogin() {
//         gOpenNativeModule.openLogin();
//     },

//     /***
//      * 打开原生Web（Android 原生提供支持）
//      * @param title
//      * @param url
//      */
//     openWeb(title, url) {
//         gOpenNativeModule.openWeb(title, url);
//     },

//     /***
//      * 打开原生Web（Android 原生提供支持）
//      * @param url
//      */
//     openWebNoTitle(url) {
//         gOpenNativeModule.openWebNoTitle(url);
//     },

//     /***
//      * 跳转首页菜单TAB（Android 原生提供支持）
//      */
//     openHomeTab(type) {
//         gOpenNativeModule.openHomeTab(type);
//     },

//     /***
//      * 分享（Android 原生提供支持）
//      * @param title
//      * @param url
//      */
//     openShareDialog(title, url) {
//         gOpenNativeModule.openShareDialog(title, url);
//     },

//     /***
//      * 用户认证（Android 原生提供支持）
//      */
//     userAuthent() {
//         gOpenNativeModule.userAuthent();
//     },

//     /***
//      * 权限申请（Android 原生提供支持）
//      * @param tip
//      * @param permissions
//      * @param callback
//      */
//     checkPermissions(tip, permissions, callback = (code, json) => {}) {
//         gOpenNativeModule.checkPermissions(tip, permissions, callback);
//     },

//     /***
//      * 城市区域选择（Android 原生提供支持）
//      * @param callback
//      */
//     openCity(callback = (code, json) => {}) {
//         gOpenNativeModule.openCity(callback);
//     },

//     /**
//      * 扫码
//      * @param {*} callback
//      */
//     openScanActivity(callback = (code, data) => {}) {
//         gOpenNativeModule.openScanActivity(callback);
//     },

//     /**
//      * 退出整个RN
//      */
//     finish() {
//         BackHandler.exitApp();
//     },

//     /***
//      * 设置返回数据 ReadableMap
//      */
//     setResult(data) {
//         gOpenNativeModule.setResult(data);
//     },

//     /***
//      * 设置返回数据 ReadableMap 并退出
//      */
//     setResultAndFinish(data) {
//         gOpenNativeModule.setResultAndFinish(data);
//     },

//     /***
//      * 打开线路规划（Android 原生提供支持）
//      */
//     openOilMapDetailActivity(data) {
//         gOpenNativeModule.openOilMapDetailActivity(data);
//     },
//     /**
//      * 视频录制
//      * @param toast
//      * @param time
//      * @param cameraAroundState 0 后置 1 前置
//      */
//     startVideoRecordAction: async (toast, time, cameraAroundState = '0') => {
//         return (await gOpenNativeModule.startVideoRecordAction) && gOpenNativeModule.startVideoRecordAction(toast, time, cameraAroundState);
//     },

//     /***
//      * 打开高德地址选择列表
//      * code:200 成功 data:{},msg:''
//      * @returns {Promise<*>}
//      */
//     openSelectMapArddess: async () => {
//         if (!gOpenNativeModule.onOpenSelectAddress) {
//             UI.showToast('请使用7.0.0版本及其以上');
//         } else {
//             return await gOpenNativeModule.onOpenSelectAddress();
//         }
//     },

//     /**
//      * 打开运单搜索
//      * */
//     openWaybillSearch: async (plateNumber) => {
//         if (!gOpenNativeModule.openWaybillSearch) {
//             UI.showToast('当前版本过低，请更新app');
//         } else {
//             return await gOpenNativeModule.openWaybillSearch(plateNumber);
//         }
//     },

//     /**
//      * 语音播报
//      * @param message
//      */
//     readText: (message) => {
//         gOpenNativeModule.readText && gOpenNativeModule.readText(message);
//     },

//     /**
//      * 认证Ocr识别
//      */
//     idCardScan: async (type, viewType) => {
//         return (await gOpenNativeModule.openScanOcr) && gOpenNativeModule.openScanOcr(type, viewType);
//     },
// };

// export const Method = {
//     /***
//      * 保存数据（Android 原生提供支持）
//      * @param key
//      * @param value
//      */
//     putStringExtra: (key, value) => {
//         gOpenNativeModule.putStringExtra(key, value);
//     },

//     /***
//      * 获取保存数据（Android 原生提供支持）
//      * @param key
//      */
//     getStringExtra: (key) => {
//         return gOpenNativeModule.getStringExtra(key);
//     },

//     /***
//      * 清除保存的数据（Android 原生提供支持）
//      * @param key
//      */
//     removeStringExtra: (key) => {
//         gOpenNativeModule.removeStringExtra(key);
//     },

//     /***
//      * 获取定位（Android 原生提供支持）
//      */
//     loaction: (callback = (code, json) => {}) => {
//         gOpenNativeModule.loaction(callback);
//     },

//     /***
//      * 文件上传（Android 原生提供支持）
//      * @param file
//      * @param callback
//      */
//     upFile: (file, callback = (code, json) => {}) => {
//         gOpenNativeModule.upFile(file, callback);
//     },
//     /***
//      * 文件上传（Android 原生提供支持）
//      * @param file
//      * @param callback
//      */
//     upFileNew: async (file) => {
//         return await gOpenNativeModule.upFileNew(file);
//     },
//     /***
//      * 文件上传（Android 原生提供支持） 环境/产线/xxx.png
//      * @param file
//      * @param callback
//      */
//     upFileNewWithPath: async (file) => {
//         return await gOpenNativeModule.upFileNewWithPath(file);
//     },
//     /***
//      * 文件上传（Android 原生提供支持）
//      * @param files 多集合
//      * @param callback
//      */
//     upFileList: (files, callback = (code, json) => {}) => {
//         gOpenNativeModule.upFileList(files, callback);
//     },
//     /**
//      * 文件上传（Android 原生提供支持） 全部上传完成之后回调
//      * @param files
//      * @param callback
//      */
//     upFileListNew: (files, callback = (code, json) => {}) => {
//         gOpenNativeModule.upFileListNew(files, callback);
//     },
//     /***
//      * 下载文件（Android 原生提供支持）
//      * @param url
//      * @param fileName
//      * @param callback
//      */
//     downFile: (url, fileName, callback = (code, json) => {}) => {
//         gOpenNativeModule.downFile(url, fileName, callback);
//     },

//     /**
//      *
//      * @returns 获取登录信息
//      */
//     getLogin: () => {
//         let login = gOpenNativeModule.getLogin();
//         return JSON.parse(login);
//     },

//     /***
//      * 退出应用
//      */
//     onLoseToken: (code = '', msg = '') => {
//         gOpenNativeModule.onLoseToken(code, msg);
//     },

//     /***
//      * 网络请求
//      */
//     httpReuest: async (url, path, method, params, mediaType, header, encryption, decheck) => {
//         return await gOpenNativeModule.httpReuest(url, path, method, params, mediaType, header, encryption, decheck);
//     },

//     /***
//      * 获取宿主版本
//      * @return
//      */
//     getHostVersion: () => {
//         return parseInt(gOpenNativeModule.getHostVersion());
//     },
//     /***
//      * 获取宿主版本名称
//      * @return
//      */
//     getHostVersionName: () => {
//         return gOpenNativeModule.getHostVersionName();
//     },

//     /**
//      * 注释: 获取App缓存路径
//      * 时间: 2023/7/24 0024 16:59
//      * <AUTHOR>
//      * @returns {*}
//      */
//     getAppCachePath: () => {
//         return gOpenNativeModule.getAppCachePath();
//     },

//     /***
//      * 原生交互通用方法
//      * @param methodName
//      * @param params
//      * @returns {Promise<any>}
//      * @param key
//      */
//     transferNativeMethod: async (key, methodName, params = {}) => {
//         //原生返回的是WritableMap
//         return await gOpenNativeModule.transferNativeMethod(key, methodName, params);
//     },

//     /**
//      * 检查RN 版本更新
//      * @param version
//      */
//     onCheckVersion: (version) => {
//         gLog('----------------------检查RN 版本更新---------------------------');
//         if (gOpenNativeModule.onCheckVersion) {
//             gLog('----------------------检查RN 版本更新---1------------原生方法存在------------');
//             gOpenNativeModule.onCheckVersion(JSON.stringify(version));
//         } else {
//             gLog('----------------------检查RN 版本更新--------2--------原生方法不存在-----------');
//         }
//     },
// };
