// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class RsqQueryOilActListFrame extends ResultData {
    /**
     * 油站信息列表
     */
    oilInfoList?: OilInfoList[];
    /**
     * 油气余额
     */
    oilUserAmount?: string;
}

export class OilInfoList {
    /**
     * 角标
     */
    awardTipPic?: string;
    /**
     * 满X元减Y元中的   Y值
     */
    discountMoney?: string;
    /**
     * 满X元前Z升中的   Z值
     */
    discountShowNum?: string;
    /**
     * 优惠价格
     */
    oilStationDiscountPrice?: string;
    /**
     * 油站id
     */
    oilStationId?: string;
    /**
     * 油站价
     */
    oilStationPrice?: string;
    /**
     * 油站地址
     */
    stationAddress?: string;
    /**
     * 油站距离
     */
    stationDistance?: string;
    /**
     * 纬度
     */
    stationLat?: string;
    /**
     * 经度
     */
    stationLng?: string;
    /**
     * 油站名称
     */
    stationName?: string;
    /**
     * 满X元减Y元中的   X值
     */
    useThreshold?: string;

    /**
     * 单位
     */
    stationPriceType?: string;

    /**
     * #1 展示 0 不展示
     */
    showCouponType?: string;
}
