import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import EWaybill from '../pick/models/EWaybill';
import ELogin from '../user/personalsafety/models/ELogin';
import {plainToInstance} from 'class-transformer';
import {ReqQueryOrderInfoForAppSkip} from '../pick/requests/ReqQueryOrderInfoForAppSkip';
import LanguageType from '../util/language/LanguageType';
import UISteps, {StepData} from '../widget/UISteps';
import {gScreen_width} from '../util/scaled-style';
import PublicWelfareActivitiesView from '../pick/views/PublicWelfareActivitiesView';
import OrderPickReactExtension from '../pick/utils/OrderPickReactExtension';
import LinearGradient from 'react-native-linear-gradient';
import UIImage from '../widget/UIImage';
import TextUtils from '../util/TextUtils';
import UIButton from '../widget/UIButton';
import {ReqQueryDictConfigScore} from '../bill/requests/ReqQueryDictConfigScore';
import {Constant} from '../base/Constant';
import {UserType} from '../user/models/UserType';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {onEvent} from '../base/native/UITrackingAction';
import {Method} from '../util/NativeModulesTools';
import {OrderCoordinate, ReqOrderCoordinate} from '../pick/requests/ReqOrderCoordinate';
import {RouterUtils} from '../util/RouterUtils';
import ShipmentRecommendOilGas from './views/ShipmentRecommendOilGas';
import {ReqOffLineBeforeDeliverCargoQuery} from './delivery/requests/ReqOffLineBeforeDeliverCargoQuery';
import {ReqShipmentsEGoodInfo} from './delivery/requests/ReqShipmentsEGoodInfo';

interface State extends BaseState {
    showSimpleTips: boolean;
    receiveFenceDistance?: string;
}

/**
 * 注释:  发货成功页
 * 时间: 2024/11/25 星期一 10:45
 * <AUTHOR>
 */
export default class ShipmentsSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    orderId: string;
    endAddress: string;
    eWaybill: EWaybill | undefined;
    isPreOperate: boolean;
    eLogin: ELogin;
    title: string;
    isAdvance: boolean;
    activityShowFlag: string;
    activityInfo: string;
    promptFlag: string;
    consignorPrompt: string;
    platFormCargoPrompt: string;
    detailId: string;
    type: string;
    orderCoordinate: OrderCoordinate | undefined;

    constructor(props) {
        super(props);
        this.state = {showSimpleTips: true};
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.detailId = this.pageParams.detailId;
        this.endAddress = this.pageParams.endAddress;
        this.eLogin = plainToInstance(ELogin, Method.getLogin());
        this.isPreOperate = this.pageParams.scene == '1' || this.pageParams.scene == '2';
        this.isAdvance = this.pageParams.isAdvance ?? false;
        this.activityShowFlag = this.pageParams.activityShowFlag;
        this.activityInfo = this.pageParams.activityInfo;
        this.promptFlag = this.pageParams.promptFlag;
        this.consignorPrompt = this.pageParams.consignorPrompt;
        this.platFormCargoPrompt = this.pageParams.platFormCargoPrompt;
        this.type = this.pageParams.type;
        this.title = this.isPreOperate ? LanguageType.getTxt('预付申请成功') : UserType.isCarrier() ? LanguageType.getTxt('装货成功') : LanguageType.getTxt('确认发货');
    }

    componentDidMount() {
        super.componentDidMount();
        //显示活动弹窗
        OrderPickReactExtension.showEActivityInfo(this.activityShowFlag, this.activityInfo);
        //请求运单信息
        this.queryReqQueryOrderInfo();
        // 防诈骗提示
        this.queryDictConfigScore();
        // 查询目的地
        this.queryReqOrderCoordinate();
        //     查询卸货范围
        this.queryReqOrderDeliverCargoQuery();
    }

    async queryReqOrderDeliverCargoQuery() {
        let request = new ReqShipmentsEGoodInfo();
        request.orderId = this.orderId;
        request.detailId = this.detailId;
        request.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                this.setState({
                    receiveFenceDistance: response.data?.receiveFenceDistance ?? '',
                });
            }
        });
    }

    /**
     * 注释: 请求运单信息
     * 时间: 2024/11/25 星期一 16:17
     * <AUTHOR>
     */
    queryReqQueryOrderInfo() {
        let request = new ReqQueryOrderInfoForAppSkip();
        request.orderId = this.orderId;
        request.request().then((rsq) => (this.eWaybill = rsq.data));
    }

    /**
     * 注释: 查询目的地
     * 时间: 2024/11/25 星期一 16:02
     * <AUTHOR>
     */
    queryReqOrderCoordinate() {
        let request = new ReqOrderCoordinate();
        request.orderId = this.orderId;
        request.request().then((res) => {
            this.orderCoordinate = res.data;
        });
    }

    /**
     * 注释: 显示简版提示
     * 时间: 2024/7/30 0030 17:27
     * <AUTHOR>
     */
    queryDictConfigScore() {
        let req = new ReqQueryDictConfigScore();
        req.request().then((res) => {
            if (res.isSuccess() && TextUtils.equals('0', res.data?.value)) {
                this.setState({showSimpleTips: true});
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3', alignItems: 'center'}}>
                <UITitleView title={this.title} />
                <ScrollView>
                    {/*绘制步骤条*/}
                    {this.renderStepView()}
                    <View style={{backgroundColor: '#fff', padding: 15, alignItems: 'center', marginTop: 10}}>
                        {/*提示视图*/}
                        {this.renderTipsView()}
                        {/*卸货要求*/}
                        {TextUtils.equals('1', this.promptFlag) && this.renderConsignorPromptView()}
                    </View>
                    {/*推荐油站列表*/}
                    <ShipmentRecommendOilGas type={this.type} />
                    {/*导航按钮*/}
                    <UIButton
                        text={'导航去卸货地'}
                        width={260}
                        height={45}
                        style={{alignSelf: 'center', marginTop: 10, marginBottom: 50}}
                        onPress={() => {
                            Method.openMapNaviPage(this.orderCoordinate?.deliverCoordinateY, this.orderCoordinate?.deliverCoordinateX, this.orderCoordinate?.deliverProCityDisPlace, '2', JSON.stringify(this.eWaybill));
                            RouterUtils.skipPop();
                        }}
                    />
                    {/*活动信息*/}
                    {this.isAdvance && <PublicWelfareActivitiesView starNode={'5'} orderId={this.orderId} />}
                </ScrollView>
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制提示视图
     * 时间: 2024/11/25 星期一 14:21
     * <AUTHOR>
     */
    renderTipsView() {
        let showTwo: boolean = UserType.isCarrier() && TextUtils.isNoEmpty(this.state.receiveFenceDistance) && parseFloat(this.state.receiveFenceDistance ?? '') >= 0;
        // 简版提示
        if (this.state.showSimpleTips) {
            return (
                <View style={{alignItems: 'center'}}>
                    {!this.isPreOperate && <Text style={styles.labelStyle}>{UserType.isCarrier() ? '装货成功!' : '发货成功!'}</Text>}
                    <Text style={{fontSize: 16, color: '#333333', marginTop: 10, marginLeft: 5}}>
                        1、为保障您的权益，请您在运输途中<Text style={{color: '#5086FC'}}>全程保持APP前台运行</Text>
                        ，到达目的地卸货后请及时【确认卸货】，否则会触发异常影响结算，如遇货主金额不足导致无法卸货请联系货主。
                    </Text>
                    {showTwo && (
                        <Text
                            style={{
                                color: '#5086FC',
                                marginTop: 10,
                                fontSize: 16,
                            }}>
                            2、货主要求必须在目的地{this.state.receiveFenceDistance}公里内进行卸货，否则无法卸货，请注意卸货地点
                        </Text>
                    )}
                    {TextUtils.equals('0', this.type) && (
                        <UITouchableOpacity
                            onPress={() => {
                                onEvent({
                                    pageId: 'ShipmentsSuccessPage',
                                    tableId: 'skipMiniProgram#gh_171fe8ef4ec4#/packageIndex/publicQRCode/publicQRCode',
                                });
                                Method.openMiniProgram('gh_171fe8ef4ec4', '/packageIndex/publicQRCode/publicQRCode');
                            }}>
                            <Text
                                style={{
                                    fontSize: 16,
                                    color: '#333333',
                                    marginTop: 10,
                                    marginLeft: 5,
                                }}>
                                {showTwo ? 3 : 2}、您的预付申请需要平台审核，若想及时了解预付审核进度信息，请关注微信公众号【中储智运红运驿站】。<Text style={{color: '#5086FC'}}>一键关注</Text>
                            </Text>
                        </UITouchableOpacity>
                    )}
                </View>
            );
        }
        return (
            <View style={{alignItems: 'center'}}>
                <Text style={[styles.labelStyle, {color: '#f00'}]}>谨防诈骗!</Text>
                <Text
                    style={{
                        fontSize: 16,
                        color: '#333333',
                        marginTop: 10,
                    }}>
                    {TextUtils.isNoEmpty(this.endAddress) ? `1、您的运输目的地为：${(<Text style={{color: '#FF3434'}}>{this.endAddress}</Text>)},运输过程中，如您被告知` : '1、运输过程中，如您被告知'}
                    <Text style={{color: '#FF3434'}}>变更卸货目的地</Text>未在平台APP收到变更申请，请及时<Text style={{color: '#FF3434'}}>联系平台客服</Text>核实确认（<Text style={{color: '#FF3434'}}>4000885566</Text>），谨防诈骗分子
                    <Text style={{color: '#FF3434'}}>冒充货主、收货人诈骗。</Text>未按平台要求操作，由此产生的损失将由您自行承担。
                </Text>
                <Text
                    style={{
                        fontSize: 16,
                        color: '#333333',
                        marginTop: 10,
                    }}>
                    2、为保障您的权益，请您在运输途中<Text style={{color: '#5086FC'}}>全程保持APP前台运行</Text>，到达目的地卸货后请及时【确认卸货】，否则会触发异常影响结算，如遇货主金额不足导致无法卸货请联系货主。
                </Text>
                {TextUtils.equals('0', this.type) && (
                    <UITouchableOpacity
                        onPress={() => {
                            onEvent({
                                pageId: 'ShipmentsSuccessPage',
                                tableId: 'skipMiniProgram#gh_171fe8ef4ec4#/packageIndex/publicQRCode/publicQRCode',
                            });
                            Method.openMiniProgram('gh_171fe8ef4ec4', '/packageIndex/publicQRCode/publicQRCode');
                        }}>
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#333333',
                                marginTop: 10,
                            }}>
                            3、您的预付申请需要平台审核，若想及时了解预付审核进度信息，请关注微信公众号【中储智运红运驿站】。<Text style={{color: '#5086FC'}}>一键关注</Text>
                        </Text>
                    </UITouchableOpacity>
                )}
            </View>
        );
    }

    /**
     * 注释: 绘制卸货要求
     * 时间: 2024/11/25 星期一 13:38
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderConsignorPromptView() {
        return (
            <LinearGradient
                colors={['#E6F2FE', '#FBFDFF']}
                style={{
                    paddingVertical: 10,
                    paddingHorizontal: 8,
                    borderRadius: 4,
                    marginTop: 20,
                    width: '100%',
                }}>
                <View>
                    <View style={{flexDirection: 'row'}}>
                        <UIImage source={'icon_shipments_success_toast'} style={{width: 17, marginRight: 5}} />
                        <Text style={{fontSize: 14, color: '#4F6485', fontWeight: 'bold'}}>装卸货要求</Text>
                    </View>
                    <View
                        style={{
                            backgroundColor: '#fff',
                            borderRadius: 4,
                            paddingVertical: 15,
                            paddingHorizontal: 12,
                            marginTop: 8,
                        }}>
                        {TextUtils.isNoEmpty(this.consignorPrompt) && <Text style={{fontSize: 14, color: '#666666'}}>{this.consignorPrompt}</Text>}
                        {TextUtils.isNoEmpty(this.platFormCargoPrompt) && (
                            <View>
                                <UIImage source={'deliver_dotted_line_black'} style={{width: '100%', height: 1, marginVertical: 5}} />
                                <Text style={{fontSize: 14, color: '#666666'}}>{this.platFormCargoPrompt}</Text>
                            </View>
                        )}
                    </View>
                </View>
            </LinearGradient>
        );
    }

    /**
     * 注释: 绘制步骤
     * 时间: 2024/11/25 星期一 13:37
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderStepView() {
        let dataList: StepData[] = [];
        dataList.push({
            title: LanguageType.getTxt(this.isPreOperate || UserType.isCarrier() ? '确认装货' : '确认发货'),
            complete: true,
            first: true,
            showTip: true,
            itemWidth: (gScreen_width - 80) / 4,
        });
        dataList.push({
            title: LanguageType.getTxt('确认卸货'),
            complete: false,
            itemWidth: (gScreen_width - 80) / 4,
        });
        dataList.push({
            title: LanguageType.getTxt('货主回单确认'),
            complete: false,
            itemWidth: (gScreen_width - 80) / 3.5,
        });
        dataList.push({
            title: LanguageType.getTxt('结算回款'),
            complete: false,
            last: true,
            itemWidth: (gScreen_width - 80) / 4,
        });
        return (
            <View style={styles.floatContainer}>
                {/*提示悬浮框*/}
                <View
                    style={{
                        alignItems: 'center',
                        alignSelf: 'flex-start',
                        marginLeft: (gScreen_width + 30) / 7,
                    }}>
                    <View style={styles.tipContent}>
                        <Text style={{fontSize: 12, color: '#fff'}}>{LanguageType.getTxt('等待卸货')}</Text>
                    </View>
                    <View style={styles.arrow} />
                </View>
                {/*步骤条*/}
                <UISteps dataList={dataList} />
            </View>
        );
    }
}

const styles = StyleSheet.create({
    floatContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingBottom: 5,
        paddingTop: 5,
    },
    tipContent: {
        backgroundColor: '#5086FC',
        padding: 7,
        borderRadius: 8,
    },
    arrow: {
        marginTop: -0.5,
        width: 0,
        height: 0,
        borderStyle: 'solid',
        borderWidth: 6,
        borderTopColor: '#5086FC', //下箭头颜色
        borderLeftColor: '#fff', //右箭头颜色
        borderBottomColor: '#fff', //上箭头颜色
        borderRightColor: '#fff', //左箭头颜色
    },
    labelStyle: {
        fontSize: 18,
        color: Constant.color_5086fc,
        fontWeight: 'bold',
        marginTop: 10,
    },
});
