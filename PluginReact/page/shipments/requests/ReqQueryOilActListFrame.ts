import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {RsqQueryOilActListFrame} from '../models/RsqQueryOilActListFrame';

/**
 * 注释: 请求附件油气
 * 时间: 2024/11/26 星期二 14:57
 * <AUTHOR>
 */
export class ReqQueryOilActListFrame extends BaseRequest {
    async request(): Promise<BaseResponse<RsqQueryOilActListFrame>> {
        this.params = {};
        return super.post('ims-app/activityOils/queryOilActListFrame', RsqQueryOilActListFrame);
    }
}
