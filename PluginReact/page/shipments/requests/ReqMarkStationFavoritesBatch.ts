import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';

/**
 * 注释: 批量收藏油站列表
 * 时间: 2024/12/18 星期三 11:15
 * <AUTHOR>
 */
export class ReqMarkStationFavoritesBatch extends BaseRequest {
    stationIdList: string[];

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {stationIdList: this.stationIdList};
        return super.post('/oilcard-app/oilcard/markStationFavoritesBatch', ResultData);
    }
}
