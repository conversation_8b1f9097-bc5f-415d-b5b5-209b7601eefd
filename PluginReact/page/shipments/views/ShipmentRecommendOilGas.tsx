import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {useEffect, useState} from 'react';
import UIImage from '../../widget/UIImage';
import TextUtils from '../../util/TextUtils';
import {ArrayUtils} from '../../util/ArrayUtils';
import LinearGradient from 'react-native-linear-gradient';
import PagerView from 'react-native-pager-view';
import UICursor from '../../widget/UICursor';
import RecommendOilGasItem from './RecommendOilGasItem';
import {ReqQueryOilActListFrame} from '../requests/ReqQueryOilActListFrame';
import {OilInfoList} from '../models/RsqQueryOilActListFrame';
import {ReqMarkStationFavoritesBatch} from '../requests/ReqMarkStationFavoritesBatch';
import {Method} from '../../util/NativeModulesTools';
import {RouterUtils} from '../../util/RouterUtils';

interface Props {
    type?: string;
    style?: StyleProp<ViewStyle>;
}

/**
 * 注释: 推荐油站列表视图
 * 时间: 2024/11/25 星期一 19:31
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function ShipmentRecommendOilGas(props: Props) {
    // 油站列表
    const [oilGasList, setOilGasList] = useState<Array<OilInfoList>>([]);
    const [current, setCurrent] = useState<number>(0);
    const [oilUserAmount, setOilUserAmount] = useState<string>();
    const [pageViewHeight, setPageViewHeight] = useState<number>(120);

    // 初始化
    useEffect(() => {
        queryOilGasList();
    }, []);

    /**
     * 注释: 查询油站列表
     * 时间: 2024/11/25 星期一 19:35
     * <AUTHOR>
     */
    function queryOilGasList() {
        let request = new ReqQueryOilActListFrame();
        request.request().then((res) => {
            // setOilUserAmount('580.00');
            // let oilInfoList = [];
            // for (let i = 0; i < 10; i++) {
            //     let item = new OilInfoList();
            //     item.awardTipPic = '合作油站';
            //     item.discountMoney = '15';
            //     item.discountShowNum = '30';
            //     item.oilStationDiscountPrice = '6.22';
            //     item.oilStationPrice = '7.22';
            //     item.stationAddress = '汉中市汉台区宗营镇下寨村口处延长壳牌加油';
            //     item.stationDistance = '0.7';
            //     item.stationLat = '';
            //     item.stationLng = '';
            //     item.stationName = '汉中天汉大道北加油站8号';
            //     item.useThreshold = '300';
            //     item.stationPriceType = '升';
            //     oilInfoList.push(item);
            // }
            // setOilGasList(oilInfoList);
            // if (oilInfoList.length == 1) {
            //     setPageViewHeight(120);
            // } else if (oilInfoList.length == 2) {
            //     setPageViewHeight(240);
            // } else {
            //     setPageViewHeight(320);
            // }
            if (res.isSuccess() && ArrayUtils.isNoEmpty(res.data?.oilInfoList)) {
                setOilUserAmount(res.data?.oilUserAmount);
                setOilGasList(res.data?.oilInfoList ?? []);
                if (res.data?.oilInfoList?.length == 1) {
                    setPageViewHeight(120);
                } else if (res.data?.oilInfoList?.length == 2) {
                    setPageViewHeight(240);
                } else {
                    setPageViewHeight(320);
                }
            }
        });
    }

    // 返回油站列表视图
    if (ArrayUtils.isNoEmpty(oilGasList)) {
        return (
            <View style={[{paddingVertical: 15, paddingHorizontal: 10}, props.style]}>
                {/*可用余额*/}
                <LinearGradient colors={['#FFEAD4', '#FFFDFC']} style={{paddingHorizontal: 10, paddingVertical: 13}}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={'icon_oil'} style={{width: 19, height: 19}} />
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#555',
                                marginLeft: 5,
                                textAlign: 'center',
                            }}>
                            可用油气余额
                        </Text>
                        <LinearGradient
                            colors={['#FF3716', '#FF8631']}
                            useAngle={true}
                            angle={270}
                            style={{
                                paddingHorizontal: 5,
                                borderRadius: 3,
                                marginHorizontal: 3,
                            }}>
                            <Text style={{color: '#fff'}}> {oilUserAmount}</Text>
                        </LinearGradient>
                        <Text style={{fontSize: 14, color: '#555', textAlign: 'center'}}>元，为您推荐油气站点</Text>
                    </View>
                </LinearGradient>
                {/*油站列表*/}
                <PagerView
                    style={{width: '100%', height: pageViewHeight, marginTop: -5}}
                    pageMargin={15}
                    onPageSelected={(event) => {
                        setCurrent(event.nativeEvent.position);
                    }}>
                    {ArrayUtils.splitArrayIntoGroups(oilGasList, 3).map((value, index) => {
                        return (
                            <View style={{flexDirection: 'column'}} key={`${index}`}>
                                {value.map((item) => {
                                    return (
                                        <RecommendOilGasItem
                                            data={item}
                                            onClick={() => {
                                                let req = new ReqMarkStationFavoritesBatch();
                                                req.stationIdList = oilGasList.map((item) => {
                                                    return item.oilStationId ?? '';
                                                });
                                                req.request().then((res) => {
                                                    if (res.isSuccess()) {
                                                        Method.openOilCollectionList();
                                                        RouterUtils.skipPop();
                                                    }
                                                });
                                            }}
                                        />
                                    );
                                })}
                            </View>
                        );
                    })}
                </PagerView>
                {/*游标*/}
                {oilGasList.length > 3 && <UICursor totalPage={oilGasList.length / 3} currentPage={current} />}
            </View>
        );
    }
    // 正常返回点赞图片
    return <UIImage source={TextUtils.equals('0', props.type ?? '0') ? 'order_success_icon' : 'base_tip_success'} style={{width: 163, height: 129, marginTop: 30, alignSelf: 'center'}} />;
}

const styles = StyleSheet.create({});
