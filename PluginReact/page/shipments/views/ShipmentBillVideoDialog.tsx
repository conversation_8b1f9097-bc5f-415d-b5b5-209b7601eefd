import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import React, {useImperativeHandle} from 'react';
import UIImage from '../../widget/UIImage';
import TextUtils from '../../util/TextUtils';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width} from '../../util/scaled-style';
import UIVideoThumPostItem from '../../widget/UIVideoThumPostItem';
import {Method} from '../../util/NativeModulesTools';
import UIButton from '../../widget/UIButton';
import {PeopleVehicleVideoArr} from '../../shipments/delivery/models/ShipmentsEGoodInfoRsp';
import {http} from '../../const.global';
import UIPopup from '../../widget/UIPopup';

interface Props {
    data?: PeopleVehicleVideoArr[]; //视频地址
    remark?: string;
    onClose?: (ok: boolean) => void;
    max: number; //最大
    edit: boolean;
    onCallback: (txt: string, data: PeopleVehicleVideoArr[]) => void;
}

/**
 * 注释:	WLHY-12652 🏷 【汽运】承运方支持上传视频
 * @constructor
 */
export default function ShipmentBillVideoDialog(props: Props, ref) {
    //视频预览地址
    const [videoUrl, setVideoUrl] = React.useState<PeopleVehicleVideoArr[]>(props.data ?? []);
    const [txt, setTxt] = React.useState<string | undefined>(props.remark);
    const [showSelectFileDilaog, setShowSelectFileDilaog] = React.useState(false);

    const lookVideoPage = (item, imageUr) => {
        //查看播放视频
        Method.transferNativeMethod('CommonReactNativeExtension', 'videoPlay', {url: http.imagUrl(item.videoUrl)});
    };

    const delPic = (index, item, imageUrl) => {
        //删除
        let newList = videoUrl.filter((value,i) => index != i);
        setVideoUrl(newList);
    };
    const chooseVideo = async () => {
        //拍摄视频
        setShowSelectFileDilaog(false);
        let result = await Method.startVideoRecordAction('', '30', '0');
        if (TextUtils.equals('200', result.code)) {
            upFile(result.data);
        } else if (TextUtils.equals('-1', result.code)) {
            Method.showToast(result.msg);
        }
    };
    const selectVideo = async () => {
        //选择视频
        setShowSelectFileDilaog(false);
        let result = await Method.startOpenVideoSelectAction(false, 1, true);
        if (result.code == 200) {
            let list = JSON.parse(result.data);
            console.log('file:' + result.data);
            upFile(list[0]);
        }
    };

    const upFile = async (file) => {
        //上传文件
        Method.showWaitDialog();
        let fileResult = await Method.upFilExtensionNew(file, 'shoufahuozuozhengshipin/', false);
        if (TextUtils.equals('200', fileResult.code)) {
            //视频返回
            console.log("上传之后sigleUrl="+fileResult.sigleUrl);
            
            let newData = new PeopleVehicleVideoArr();
            newData.videoUrl = fileResult.sigleUrl;
            setVideoUrl(videoUrl.concat(newData));
        } else {
            Method.showToast(fileResult.msg);
        }
        Method.hideWaitDialog();
    };
    return (
        <Modal
            style={{margin: 0}}
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.5}
            isVisible={true}
            useNativeDriver={true}
            onBackdropPress={() => {
                props.onClose?.(false);
            }}
            onBackButtonPress={() => {
                props.onClose?.(false);
            }}>
            <View style={{flex: 1}} />
            <View style={{backgroundColor: '#fff', borderTopLeftRadius: 8, borderTopRightRadius: 8, width: gScreen_width}}>
                <Text style={{color: '#333', fontSize: 18, fontWeight: 'bold', textAlign: 'center', marginTop: 10}}>备注说明</Text>
                <TouchableOpacity style={{right: 15, marginTop: 15, position: 'absolute'}} onPress={() => props.onClose?.(false)}>
                    <UIImage source={'certification_carowner_tip_hide'} style={{width: 18, height: 18}} />
                </TouchableOpacity>

                <View style={{height: 1, width: gScreen_width, backgroundColor: '#e3e3e3', marginTop: 10}} />

                <View style={{backgroundColor: '#F5F6F9', borderRadius: 5, padding: 10, marginLeft: 14, marginRight: 14, marginTop: 14}}>
                    <TextInput
                        style={{color: '#333', fontSize: 14}}
                        maxLength={50}
                        multiline={true}
                        numberOfLines={4}
                        textAlignVertical={'top'}
                        value={txt}
                        placeholder="请输入备注信息"
                        onChangeText={(txt) => {
                            setTxt(txt);
                        }}></TextInput>
                    <Text style={{color: '#BBB', fontSize: 14, position: 'absolute', bottom: 10, right: 10}}>{`${txt?.length ?? 0}/50`}</Text>
                </View>
                <Text style={{color: '#333', fontSize: 17, marginLeft: 14, marginTop: 20}}>
                    补充视频佐证 <Text style={{color: '#999', fontSize: 14}}>{`最多上传${props.max}个`}</Text>
                </Text>
                <View style={{marginLeft: 10, marginRight: 10}}>
                    <UIVideoThumPostItem items={videoUrl} edit={true} max={props.max} rowSize={4} delPic={delPic} lookImagePage={lookVideoPage} choosePic={() => setShowSelectFileDilaog(true)} transformUrl={(item) => http.imagUrl(item.videoUrl)} />
                </View>
                <UIButton
                    style={{marginLeft: 14, marginRight: 14, marginTop: 20, marginBottom: 10}}
                    text={'保存'}
                    onPress={() => {
                        props.onCallback(txt ?? '', videoUrl);
                    }}></UIButton>
                {showSelectFileDilaog && (
                    <UIPopup title="请选择" onClose={() => setShowSelectFileDilaog(false)}>
                        {
                            <>
                                <View style={{height: 1, width: gScreen_width, backgroundColor: '#e3e3e3', marginTop: 5}} />
                                <Text onPress={chooseVideo} style={{fontSize: 18, color: '#333', textAlign: 'center', padding: 10}}>
                                    拍摄
                                </Text>
                                <View style={{height: 1, width: gScreen_width, backgroundColor: '#e3e3e3', marginTop: 10}} />
                                <Text onPress={selectVideo} style={{fontSize: 18, color: '#333', textAlign: 'center', padding: 10}}>
                                    相册
                                </Text>
                                <View style={{height: 1, width: gScreen_width, backgroundColor: '#e3e3e3', marginTop: 10}} />
                            </>
                        }
                    </UIPopup>
                )}
            </View>
        </Modal>
    );
}
