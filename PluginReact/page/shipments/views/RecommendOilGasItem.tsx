import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import UIImage from '../../widget/UIImage';
import {OilInfoList} from '../models/RsqQueryOilActListFrame';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import TextUtils from '../../util/TextUtils';

interface Props {
    data: OilInfoList;
    onClick: Function;
}

/**
 * 注释: 油站Item视图
 * 时间: 2024/11/26 星期二 9:40
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function RecommendOilGasItem(props: Props) {
    return (
        <UITouchableOpacity
            style={styles.mainStyle}
            onPress={() => {
                props.onClick && props.onClick();
            }}>
            {/*角标*/}
            <LinearGradient colors={['#FFF5EF', '#FFF0D6']} style={styles.tagStyle} useAngle={true} angle={90}>
                <Text style={{color: '#704627', fontSize: 10}} numberOfLines={1} ellipsizeMode={'tail'}>
                    {props.data.awardTipPic}
                </Text>
            </LinearGradient>
            {/*油价描述*/}
            <View
                style={{
                    flexDirection: 'column',
                    backgroundColor: '#FF14140F',
                    paddingHorizontal: 6,
                    borderRadius: 5,
                    marginTop: 12,
                    marginRight: 10,
                    paddingVertical: 5,
                }}>
                {TextUtils.equals('1', props.data.showCouponType) ? (
                    <>
                        <Text
                            style={{
                                color: '#FD1F1F',
                                fontSize: 11,
                            }}>{`满${props.data.useThreshold}前${props.data.discountShowNum}升`}</Text>
                        <Text
                            style={{
                                color: '#FF170E',
                                fontSize: 21,
                                fontWeight: 'bold',
                            }}>
                            {`¥${props.data.oilStationDiscountPrice}`}
                            <Text style={{fontSize: 10}}>{`/${props.data.stationPriceType}`}</Text>
                        </Text>
                        <Text
                            style={{
                                fontSize: 10,
                                color: '#777',
                            }}>{`油站价 ¥${props.data.oilStationPrice}/${props.data.stationPriceType}`}</Text>
                    </>
                ) : (
                    <Text
                        style={{
                            color: '#FF170E',
                            fontSize: 21,
                            fontWeight: 'bold',
                        }}>
                        {`¥${props.data.oilStationPrice}`}
                        <Text style={{fontSize: 10}}>{`/${props.data.stationPriceType}`}</Text>
                    </Text>
                )}
            </View>
            {/*位置*/}
            <View style={{flexDirection: 'column', flex: 1}}>
                <Text
                    style={{
                        fontSize: 15,
                        color: '#333',
                        fontWeight: 'bold',
                    }}
                    numberOfLines={1}
                    ellipsizeMode={'tail'}>
                    {props.data.stationName}
                </Text>
                <Text
                    style={{
                        fontSize: 12,
                        color: '#777',
                        marginTop: 3,
                    }}
                    numberOfLines={2}
                    ellipsizeMode={'tail'}>
                    {props.data.stationAddress}
                </Text>
                <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 8}}>
                    {TextUtils.equals('1', props.data.showCouponType) && (
                        <LinearGradient colors={['#FF8D4E', '#FF2F18']} style={styles.activityTag} useAngle={true} angle={90}>
                            <Text
                                style={{
                                    fontSize: 10,
                                    color: '#fff',
                                }}
                                numberOfLines={1}
                                ellipsizeMode={'tail'}>{`满${props.data.useThreshold}减${props.data.discountMoney}`}</Text>
                        </LinearGradient>
                    )}
                    <LinearGradient
                        colors={['#F6A364', '#FF753A']}
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            borderRadius: 10,
                            paddingHorizontal: 5,
                            paddingVertical: 2,
                        }}
                        useAngle={true}
                        angle={90}>
                        <Text
                            style={{
                                fontSize: 11,
                                color: '#fff',
                                marginRight: 8,
                            }}>{`${props.data.stationDistance}km`}</Text>
                        <UIImage source={'icon_navigation'} style={{width: 11, height: 11}} />
                    </LinearGradient>
                </View>
            </View>
        </UITouchableOpacity>
    );
}

const styles = StyleSheet.create({
    mainStyle: {
        backgroundColor: '#fff',
        padding: 10,
        borderRadius: 5,
        flexDirection: 'row',
        marginBottom: 8,
    },
    tagStyle: {
        width: 55,
        height: 18,
        justifyContent: 'center',
        alignItems: 'center',
        borderTopLeftRadius: 5,
        position: 'absolute',
        top: 0,
        left: 0,
    },
    activityTag: {
        minWidth: 60,
        height: 18,
        paddingHorizontal: 5,
        borderRadius: 2,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
