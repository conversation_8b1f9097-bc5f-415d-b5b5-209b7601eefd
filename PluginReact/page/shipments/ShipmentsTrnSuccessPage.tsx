import {View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import EWaybill from '../pick/models/EWaybill';
import {ReqQueryOrderInfoForAppSkip} from '../pick/requests/ReqQueryOrderInfoForAppSkip';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import {Method} from '../util/NativeModulesTools';
import {OrderCoordinate} from '../pick/requests/ReqOrderCoordinate';
import {RouterUtils} from '../util/RouterUtils';
import {ReqQueryTmsAddressInfoBySourceId} from '../pick/requests/ReqQueryTmsAddressInfoBySourceId';

interface State extends BaseState {}

/**
 * 注释:  发货成功页
 */
export default class ShipmentsTrnSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    orderId: string;
    sourceId: string;
    eWaybill: EWaybill | undefined;
    orderCoordinate: OrderCoordinate | undefined;

    constructor(props) {
        super(props);
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.sourceId = this.pageParams.sourceId;
    }

    componentDidMount() {
        super.componentDidMount();
        //请求运单信息
        this.queryReqQueryOrderInfo();
        // 查询目的地
        this.queryReqOrderCoordinate();
    }

    /**
     * 注释: 请求运单信息
     */
    queryReqQueryOrderInfo() {
        let request = new ReqQueryOrderInfoForAppSkip();
        request.orderId = this.orderId;
        request.sourceId = this.sourceId;
        request.request().then((rsq) => (this.eWaybill = rsq.data));
    }

    /**
     * 注释: 查询目的地
     * <AUTHOR>
     */
    queryReqOrderCoordinate() {
        let request = new ReqQueryTmsAddressInfoBySourceId();
        request.sourceId = this.sourceId;
        request.request().then((res) => {
            this.orderCoordinate = res.data;
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3', alignItems: 'center'}}>
                <UITitleView title={'装货成功'} />
                <UIImage source={'order_success_icon'} style={{width: 200, height: 150, marginTop: 15}} />
                {/*导航按钮*/}
                <UIButton
                    text={'导航去卸货地'}
                    width={260}
                    height={45}
                    style={{alignSelf: 'center', marginTop: 10, marginBottom: 50}}
                    onPress={() => {
                        Method.openMapNaviPage(this.orderCoordinate?.deliverCoordinateY, this.orderCoordinate?.deliverCoordinateX, this.orderCoordinate?.deliverProCityDisPlace, '2', JSON.stringify(this.eWaybill));
                        RouterUtils.skipPop();
                    }}
                />
                {this.initCommView()}
            </View>
        );
    }
}
  
