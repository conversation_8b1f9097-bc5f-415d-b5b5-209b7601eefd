import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UISteps, {StepData} from '../widget/UISteps';
import LanguageType from '../util/language/LanguageType';
import {gScreen_width} from '../util/scaled-style';
import UIImage from '../widget/UIImage';
import {EShipmentsSuccess} from './delivery/models/EShipmentsSuccess';

/**
 * 注释: 零担发货成功
 */
export default class ShipZeroSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, BaseState> {
    //页面传参
    mEShipmentsSuccess: EShipmentsSuccess;
    endAddress: string;
    //订单Id
    orderId: string;
    //订单详情Id
    detailId: string;

    constructor(props) {
        super(props);
        let params = this.getParams();
        this.mEShipmentsSuccess = params.shipmentsSuccess;
        this.endAddress = params.endAddress;
        this.orderId = params.orderId;
        this.detailId = params.detailId;
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={LanguageType.getTxt('确认发货')} />
                <ScrollView>
                    {/*步骤条*/}
                    {this.renderStepView()}
                    <View style={styles.mainContainer}>
                        {/*谨防诈骗图片*/}
                        <UIImage source={'icon_success_title'} style={styles.imgScam} />
                        {/*诈骗提示视图*/}
                        {this.renderTips()}
                        <UIImage source={'order_success_icon'} style={styles.imgGood} />
                    </View>

                    {/*成功回单数视图*/}
                    {this.renderListView()}
                </ScrollView>
                {/*初始化基础组件*/}
                {this._initCommView()}
            </View>
        );
    }

    renderListView = () => {
        return (
            <View style={{paddingLeft: 10, paddingRight: 10, marginBottom: 10, marginTop: 7}}>
                {this.mEShipmentsSuccess.successList && (
                    <Text style={{fontSize: 16, color: '#333', alignSelf: 'center'}}>
                        成功发货<Text style={{color: '#5086fc'}}>{this.mEShipmentsSuccess.successList?.length ?? ''}</Text>单 <Text style={{color: '#5086fc'}}>{this.mEShipmentsSuccess.totalWeightOfUnit1 ?? ''}</Text>吨{' '}
                        <Text style={{color: '#5086fc'}}>{this.mEShipmentsSuccess.totalWeightOfUnit2 ?? ''}</Text>方
                    </Text>
                )}
                {this.mEShipmentsSuccess.successList?.map((value, index) => (
                    <View style={{backgroundColor: '#fff', padding: 10, borderRadius: 7, marginTop: 7}} key={'index_' + value.orderId}>
                        <Text style={{color: '#666', fontSize: 13}}>运单编号 {value.orderId}</Text>
                        <Text style={{fontSize: 16, color: '#333', marginTop: 5}} numberOfLines={1} ellipsizeMode={'tail'}>
                            <UIImage source={'icon_start_new'} style={{width: 14, height: 14}} />
                            {` ${value.deliverCity} ${value.deliverDis}`}
                        </Text>
                        <UIImage source={'order_dot_vertical'} style={{width: 1, height: 15, marginHorizontal: 6}} />
                        <Text style={{fontSize: 16, color: '#333'}} numberOfLines={1} ellipsizeMode={'tail'}>
                            <UIImage source={'icon_end_new'} style={{width: 14, height: 14, marginRight: 5}} />
                            {` ${value.despatchCity} ${value.despatchDis}`}
                        </Text>
                        <UIImage source={'order_shipment_success'} style={{width: 60, height: 60, position: 'absolute', top: '20%', right: 10}} />
                    </View>
                ))}
                {this.mEShipmentsSuccess.failList?.map((value, index) => (
                    <View style={{backgroundColor: '#fff', padding: 10, borderRadius: 7, marginTop: 7}}>
                        <Text style={{color: '#666', fontSize: 13}}>运单编号 {value.orderId}</Text>
                        <Text style={{fontSize: 16, color: '#333', marginTop: 5}} numberOfLines={1} ellipsizeMode={'tail'}>
                            <UIImage source={'icon_start_new'} style={{width: 14, height: 14, marginRight: 5}} />
                            {` ${value.deliverCity} ${value.deliverDis}`}
                        </Text>
                        <UIImage source={'order_dot_vertical'} style={{width: 1, height: 15, marginHorizontal: 6}} />
                        <Text style={{fontSize: 16, color: '#333'}} numberOfLines={1} ellipsizeMode={'tail'}>
                            <UIImage source={'icon_end_new'} style={{width: 14, height: 14, marginRight: 5}} />
                            {` ${value.despatchCity} ${value.despatchDis}`}
                        </Text>
                        <UIImage source={'order_shipment_fail'} style={{width: 60, height: 60, position: 'absolute', top: '20%', right: 10}} />
                        <View
                            style={{
                                backgroundColor: '#FFF3F0',
                                paddingHorizontal: 10,
                                paddingVertical: 7,
                                borderRadius: 6,
                                marginTop: 13,
                            }}>
                            <Text style={{fontSize: 13, color: '#FD5353'}}>{`原因：${value.message}`}</Text>
                        </View>
                    </View>
                ))}
            </View>
        );
    };

    /**
     * 注释: 步骤条
     */
    renderStepView = () => {
        let dataList: StepData[] = [];
        dataList.push({
            title: LanguageType.getTxt('确认发货'),
            complete: true,
            first: true,
            showTip: true,
            itemWidth: (gScreen_width - 80) / 4,
        });
        dataList.push({
            title: LanguageType.getTxt('确认卸货'),
            complete: false,

            itemWidth: (gScreen_width - 80) / 4,
        });
        dataList.push({title: LanguageType.getTxt('货主回单确认'), complete: false, itemWidth: (gScreen_width - 80) / 3.5});
        dataList.push({
            title: LanguageType.getTxt('结算回款'),
            complete: false,
            last: true,
            itemWidth: (gScreen_width - 80) / 4,
        });
        return (
            <View style={styles.floatContainer}>
                {/*提示悬浮框*/}
                <View
                    style={{
                        alignItems: 'center',
                        alignSelf: 'flex-start',
                        marginLeft: (gScreen_width + 30) / 7,
                    }}>
                    <View style={styles.tipContent}>
                        <Text style={{fontSize: 12, color: '#fff'}}>{LanguageType.getTxt('等待卸货')}</Text>
                    </View>
                    <View style={styles.arrow} />
                </View>
                {/*步骤条*/}
                <UISteps dataList={dataList} />
            </View>
        );
    };

    /**
     * 注释: 诈骗提示视图
     */
    renderTips = () => {
        return (
            <View style={{padding: 19}}>
                <Text style={styles.tipType}>
                    1、您的运输目的地为：<Text style={{color: '#FF3434'}}>{this.endAddress}</Text>,运输过程中，如您被告知
                    <Text style={{color: '#FF3434'}}>变更卸货目的地</Text>未在平台APP收到变更申请，请及时<Text style={{color: '#FF3434'}}>联系平台客服</Text>
                    核实确认（<Text style={{color: '#FF3434'}}>4000885566</Text>），谨防诈骗分子<Text style={{color: '#FF3434'}}>冒充货主、收货人诈骗。</Text>
                    未按平台要求操作，由此产生的损失将由您自行承担。
                </Text>
                <Text style={styles.tipType}>
                    2、为保障您的权益，请您在运输途中 <Text style={{color: '#FF3434'}}>全程保持APP前台运行</Text>,到达目的地卸货后请及时【确认卸货】，否则会触发异常影响结算，如遇货主金额不足导致无法卸货请联系货主。
                </Text>
            </View>
        );
    };
}

const styles = StyleSheet.create({
    floatContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff',
        paddingBottom: 5,
        paddingTop: 5,
    },
    mainContainer: {
        marginTop: 7,
        backgroundColor: '#fff',
        alignItems: 'center',
    },
    tipContent: {
        backgroundColor: '#5086FC',
        padding: 7,
        borderRadius: 8,
    },
    arrow: {
        marginTop: -0.5,
        width: 0,
        height: 0,
        borderStyle: 'solid',
        borderWidth: 6,
        borderTopColor: '#5086FC', //下箭头颜色
        borderLeftColor: '#fff', //右箭头颜色
        borderBottomColor: '#fff', //上箭头颜色
        borderRightColor: '#fff', //左箭头颜色
    },

    tipType: {
        fontSize: 16,
        color: '#333',
    },
    imgScam: {
        width: 83,
        height: 19,
        marginTop: 20,
    },
    imgGood: {
        width: 152,
        height: 121,
        marginTop: 15,
    },
});
