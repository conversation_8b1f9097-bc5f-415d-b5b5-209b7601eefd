import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../../widget/UITitleView';
import {ReqQueryOrderDataBeforeUpdateDeliverInfo} from './requests/ReqQueryOrderDataBeforeUpdateDeliverInfo';
import ShipmentsEGoodInfoRsp from './models/ShipmentsEGoodInfoRsp';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import TextUtils from '../../util/TextUtils';
import UILabel from '../../widget/UILabel';
import {ArrayUtils} from '../../util/ArrayUtils';
import {ShipmentUI} from './models/ShipmentUI';
import {ReqUpdateDeliverInfoBeforeReceive} from './requests/ReqUpdateDeliverInfoBeforeReceive';
import UIDialog from '../../widget/UIDialog';
import {ReqUpdateDeliverLTLInfoBeforeReceive} from './requests/ReqUpdateDeliverLTLInfoBeforeReceive';
import {gStyle} from '../../util/comm-style';
import UIImage from '../../widget/UIImage';
import {DP} from '../../util/scaled-style';
import ShipmentsPersonCarGoodsImageSelectView from './view/ShipmentsPersonCarGoodsImageSelectView';
import {Method} from '../../util/NativeModulesTools';
import ShipmentVideoRemarkView, {ShipmentVideoRemarkViewRef} from './view/ShipmentVideoRemarkView';

interface State extends BaseState {
    //发货单信息
    shipmentsEGoodInfo?: ShipmentsEGoodInfoRsp;

    //获取信息变更提示
    goodInfoChangeTip?: JSX.Element;

    //显示货物信息更改提示
    showTip: boolean;
}

/**
 * 注释: 修改预付发货照片
 * 时间: 2023/7/10 0010 15:51
 * <AUTHOR>
 */
export default class ShipmentsEditImgPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    orderId: string;
    detailId: string;
    pageParams: any;
    shipmentUI: ShipmentUI;

    //单据照片索引
    private refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();

    //人货照片索引
    private refPeopleVehicleImgView = React.createRef<ShipmentsImageSelectViewRef>();
    //挂车车牌合影索引
    private refTrailerImgView = React.createRef<ShipmentsImageSelectViewRef>();
    //备注视图索引
    private refShipmentsRemarksView = React.createRef<ShipmentVideoRemarkViewRef>();

    constructor(props) {
        super(props);
        this.state = {
            showTip: false,
        };
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.data?.orderId ?? '';
        this.detailId = this.pageParams.data?.detailId ?? '';
        this.shipmentUI = new ShipmentUI();
        this.shipmentUI.orderId = this.orderId;
        this.shipmentUI.detailId = this.detailId;
    }

    componentDidMount() {
        super.componentDidMount();
        this._showWaitDialog();
        //请求发货后订单信息
        let req = new ReqQueryOrderDataBeforeUpdateDeliverInfo();
        req.orderId = this.orderId;
        req.detailId = this.detailId;
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess() && response.data) {
                //回单打回时之前输入数量 or 当前输入内容(旧值用于对比数据是否发生修改,本地字段)
                response.data.rootArray = response.data?.rootArray.map((value) => {
                    value.oldBeforeDeliverCargoWeight = value.beforeDeliverCargoWeight ?? '';
                    return value;
                });
                this.setState({shipmentsEGoodInfo: response.data}, () => {
                    this.shipmentUI.shipmentsEGoodInfo = this.state.shipmentsEGoodInfo!;
                    this.shipmentUI.goodInfos = this.state.shipmentsEGoodInfo!.rootArray;
                });
            } else {
                this._showToast(response.getMsg());
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'修改发货信息'} />
                <ScrollView style={{flex: 1}}>
                    {/*绘制运单编号*/}
                    {this.renderOrderNo()}
                    {/*预付方式*/}
                    {this.renderTerracePayView()}
                    {/*绘制发货信息*/}
                    {this.state.shipmentsEGoodInfo && this.renderDeliveryInfoView()}
                    {this.state.shipmentsEGoodInfo && this.renderRemarksView()}
                </ScrollView>
                {/* 底部按钮 */}
                {this.renderBottomView()}
                {/*基础组件初始化*/}
                {this.initCommView()}
                {/*货物信息更改提示*/}
                {this.state.showTip && this.renderGoodInfoChangeDialog()}
            </View>
        );
    }

    /**
     * 注释: 预付方式图示
     * 时间: 2024/3/21 14:13
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    private renderTerracePayView() {
        return (
            <View style={[gStyle.view_padding, gStyle.view_row, {minHeight: 37, justifyContent: 'center'}]}>
                <UIImage style={{width: DP(37), height: DP(37), marginRight: 8}} source={'icon_pre'} />
                <Text style={gStyle.txt_333333_24}> 预付方式 </Text>
                <Text style={[gStyle.text_flex_right, gStyle.txt_333333_24, {fontWeight: 'bold'}]}>{this.getTerraceWay(this.state.shipmentsEGoodInfo?.advanceType)}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制运单编号
     * 时间: 2023/7/10 0010 16:07
     * <AUTHOR>
     * @return {JSX.Element}
     */
    renderOrderNo() {
        return (
            <View
                style={{
                    paddingLeft: 14,
                    backgroundColor: '#fff',
                    height: 42,
                    marginBottom: 7,
                    justifyContent: 'center',
                }}>
                <Text style={{fontSize: 16}}>{`运单编号：${this.orderId}`}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制发货信息
     * 时间: 2023/7/10 0010 16:18
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderDeliveryInfoView() {
        return (
            <View style={{marginTop: 5}}>
                <UILabel
                    title={'发货信息'}
                    style={{
                        paddingLeft: 14,
                        paddingRight: 14,
                        paddingTop: 10,
                    }}
                />
                {/*绘制单据照片*/}
                {this.renderOrderImageView()}
                {/*绘制人车照片*/}
                {this.renderPeopleCarView()}
                {/*绘制挂车合影照片*/}
                {this.renderTrailerView()}
            </View>
        );
    }
    /**
     * 注释: 绘制发货备注视图
     */
    renderRemarksView() {
        return <ShipmentVideoRemarkView ref={this.refShipmentsRemarksView} max={3} edit={true} deliverVideoArray={this.state.shipmentsEGoodInfo?.deliverVideoJsonArr} remark={this.state.shipmentsEGoodInfo?.carrierDeliverRemark} />;
    }
    /**
     * 注释: 绘制单据照片
     * 时间: 2023/7/10 0010 16:18
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderOrderImageView() {
        let orderImgObj = this.state.shipmentsEGoodInfo?.orderImgObj;
        return (
            <ShipmentsImageSelectView
                ref={this.refImagesWaybillView}
                orderId={this.state.shipmentsEGoodInfo!.orderId}
                imgs={this.state.shipmentsEGoodInfo!.pageImageJsonObjArr}
                title={orderImgObj?.title ?? ''}
                toast={`最多上传${orderImgObj?.limitCount}张`}
                warning={orderImgObj?.warningMsg ?? ''}
                showReadToastIcon={TextUtils.equals('1', orderImgObj?.uploadFlag)}
                waterMarkFlag={orderImgObj?.waterMarkFlag ?? ''}
                takePhoto={TextUtils.equals('1', orderImgObj?.takePhotoFlag)}
                edit={true}
                type={'1'}
                max={orderImgObj?.limitCount ?? 8}
            />
        );
    }

    /**
     * 注释: 绘制人车合影
     * 时间: 2023/7/10 0010 16:19
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderPeopleCarView() {
        let peopleVehicleImgObj = this.state.shipmentsEGoodInfo!.peopleVehicleImgObj;
        if (TextUtils.equals('1', peopleVehicleImgObj.showUploadFlag)) {
            return (
                <ShipmentsPersonCarGoodsImageSelectView
                    ref={this.refPeopleVehicleImgView}
                    style={{marginTop: 5}}
                    orderId={this.state.shipmentsEGoodInfo!.orderId}
                    imgs={this.state.shipmentsEGoodInfo!.deliverImageJsonObjArr}
                    title={peopleVehicleImgObj?.title ?? ''}
                    toast={''}
                    warning={peopleVehicleImgObj?.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', peopleVehicleImgObj?.uploadFlag)}
                    waterMarkFlag={peopleVehicleImgObj?.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', peopleVehicleImgObj?.takePhotoFlag)}
                    edit={true}
                    max={peopleVehicleImgObj?.limitCount ?? 8}
                    min={this.state.shipmentsEGoodInfo?.minLimitCount ?? 2}
                    demoImg="http://img.zczy56.com/202504181430024132410.png"
                />
            );
        }
    }
    /**
     * 发货挂车车牌合影照片
     */
    renderTrailerView() {
        let trailerImageObj = this.state.shipmentsEGoodInfo!.trailerImageObj;
        if (TextUtils.equals('1', trailerImageObj.showUploadFlag)) {
            return (
                <ShipmentsPersonCarGoodsImageSelectView
                    ref={this.refTrailerImgView}
                    style={{marginTop: 5}}
                    key={'imagesCarPerson'}
                    orderId={this.state.shipmentsEGoodInfo!.orderId}
                    imgs={this.state.shipmentsEGoodInfo!.trailerImageJsonObjArr}
                    title={trailerImageObj?.title ?? ''}
                    toast={''}
                    warning={trailerImageObj?.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', trailerImageObj?.uploadFlag)}
                    waterMarkFlag={trailerImageObj?.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', trailerImageObj?.takePhotoFlag)}
                    edit={true}
                    max={trailerImageObj?.limitCount ?? 8}
                    min={trailerImageObj?.minLimitCount ?? 2}
                    demoImg='http://img.zczy56.com/202505271511252225712.png'
                />
            );
        } 
    }
    /**
     * 注释: 确认修改按钮
     * 时间: 2023/7/10 0010 16:03
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderBottomView() {
        return (
            <Text style={styles.bottom_view_right} onPress={() => this.checkAndPost(this.shipmentUI)}>
                确认修改
            </Text>
        );
    }

    /**
     * 注释: 绘制货物信息修改弹窗
     * 时间: 2023/7/11 0011 14:35
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderGoodInfoChangeDialog() {
        return (
            <UIDialog
                title={'修改提示'}
                views={this.state.goodInfoChangeTip}
                dismiss={() => {
                    this.setState({showTip: false});
                }}
                rightOnClick={() => {
                    this.setState({showTip: false});
                    if (TextUtils.isNoEmpty(this.state.shipmentsEGoodInfo?.orderCarpoolingId)) {
                        //零担
                        this.updateDeliverLTLInfoBeforeReceive(this.shipmentUI);
                    } else {
                        this.updateDeliverInfoBeforeReceive(this.shipmentUI);
                    }
                }}
                leftOnClick={() => {
                    this.setState({showTip: false});
                }}
            />
        );
    }

    /**
     * 注释: 更新订单信息
     * 时间: 2023/7/11 0011 10:25
     * <AUTHOR>
     * @param shipmentUI
     */
    checkAndPost(shipmentUI: ShipmentUI) {
        //表单检测
        let ok = true;
        if (ok && this.refImagesWaybillView.current) {
            ok = this.refImagesWaybillView.current.check(shipmentUI);
        }
        if (ok && this.refPeopleVehicleImgView.current) {
            ok = this.refPeopleVehicleImgView.current.check(shipmentUI);
        }
        if (ok && this.refShipmentsRemarksView.current) {
            ok = this.refShipmentsRemarksView.current.check(shipmentUI);
        }
        if (ok && this.refTrailerImgView.current) {
            ok = this.refTrailerImgView.current.check(shipmentUI);
        }
        if (!ok) {
            return;
        }
        if (TextUtils.isNoEmpty(this.state.shipmentsEGoodInfo?.orderCarpoolingId)) {
            //零担
            this.updateDeliverLTLInfoBeforeReceive(this.shipmentUI);
        } else {
            this.updateDeliverInfoBeforeReceive(this.shipmentUI);
        }
    }

    /**
     * 注释: 订单变更接口调用
     * 时间: 2023/7/11 0011 11:02
     * <AUTHOR>
     * @param shipmentUI
     */
    updateDeliverInfoBeforeReceive(shipmentUI: ShipmentUI) {
        this._showWaitDialog();
        let request = new ReqUpdateDeliverInfoBeforeReceive();
        request.orderId = shipmentUI.orderId;
        request.detailId = shipmentUI.detailId;
        request.signalType = Method.getNetworkType();
        request.remark = shipmentUI.remark;
        request.deliverVideoArray = shipmentUI.deliverVideoArray;

        if (ArrayUtils.isNoEmpty(this.refImagesWaybillView.current?.getImages())) {
            request.orderPicArray = this.refImagesWaybillView.current?.getImages()!;
        }
        if (TextUtils.equals('1', this.state.shipmentsEGoodInfo!.peopleVehicleImgObj.showUploadFlag)) {
            if (ArrayUtils.isNoEmpty(this.refPeopleVehicleImgView.current?.getImages())) {
                request.carPeoplePicArray = this.refPeopleVehicleImgView.current?.getImages()!;
            }
        } else {
            request.carPeoplePicArray = this.state.shipmentsEGoodInfo!.deliverImageJsonObjArr;
        }

        //挂车合影
        if (ArrayUtils.isNoEmpty(this.refTrailerImgView.current?.getImages())) {
            request.trailerPicUrlsJsonArr = this.refTrailerImgView.current?.getImages()!;
        }
        

        request.request().then((response) => {
            this._dismissWait();
            if (TextUtils.isNoEmpty(response.data?.resultMsg)) {
                //显示修改成功弹窗
                Method.showDialog({
                    title: '提示',
                    hideCancel: true,
                    message: response.data?.resultMsg,
                    cancelable: false,
                    okAction: () => {
                        this._finish();
                    },
                });
            }
        });
    }

    /**
     * 注释: 订单变更接口调用
     * @param shipmentUI
     */
    updateDeliverLTLInfoBeforeReceive(shipmentUI: ShipmentUI) {
        this._showWaitDialog();
        let request = new ReqUpdateDeliverLTLInfoBeforeReceive();
        request.orderId = shipmentUI.orderId;
        request.detailId = shipmentUI.detailId;
        request.weightDetails = shipmentUI.getCargoIdWeightData();
        request.signalType = Method.getNetworkType();
        request.remark = shipmentUI.remark;
        request.deliverVideoArray = shipmentUI.deliverVideoArray;

        if (ArrayUtils.isNoEmpty(this.refImagesWaybillView.current?.getImages())) {
            request.orderPicArray = this.refImagesWaybillView.current?.getImages()!;
        }
        if (TextUtils.equals('1', this.state.shipmentsEGoodInfo!.peopleVehicleImgObj.showUploadFlag)) {
            if (ArrayUtils.isNoEmpty(this.refPeopleVehicleImgView.current?.getImages())) {
                request.carPeoplePicArray = this.refPeopleVehicleImgView.current?.getImages()!;
            }
        } else {
            request.carPeoplePicArray = this.state.shipmentsEGoodInfo!.deliverImageJsonObjArr;
        }
        request.request().then((response) => {
            this._dismissWait();
            if (TextUtils.isNoEmpty(response.data?.resultMsg)) {
                //显示修改成功弹窗
                Method.showDialog({
                    title: '提示',
                    hideCancel: true,
                    message: response.data?.resultMsg,
                    cancelable: false,
                    okAction: () => {
                        this._finish();
                    },
                });
            }
        });
    }

    /**
     * 注释: 获取预付方式
     * 时间: 2024/3/21 14:16
     * <AUTHOR>
     */
    getTerraceWay(advanceType?: string) {
        switch (advanceType) {
            case '1':
                return '现金预付';
            case '2':
                return '油品预付';
            case '3':
                return '油品+现金预付';
            default:
                return '';
        }
    }
}

const styles = StyleSheet.create({
    bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
});
