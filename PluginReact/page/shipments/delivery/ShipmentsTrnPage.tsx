import React from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import UITitleView from '../../widget/UITitleView';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import TextUtils from '../../util/TextUtils';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import {gStyle} from '../../util/comm-style';
import {gScreen_width} from '../../util/scaled-style';
import ShipmentsEGoodInfoRsp from './models/ShipmentsEGoodInfoRsp';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import ShipmentsGoodsView, {ShipmentsGoodsViewRef} from './view/ShipmentsGoodsView';
import {ShipmentsModel} from './ShipmentsModel';
import {PermissionType, PermissionUtil} from '../../util/PermissionUtil';
import LanguageType from '../../util/language/LanguageType';
import {onEvent} from '../../base/native/UITrackingAction';
import {Method} from '../../util/NativeModulesTools';
import ShowTrnIntegrityView, { ShowTrnIntegrityViewRef } from './view/ShowTrnIntegrityView';
import { ShipmentUI } from './models/ShipmentUI';

interface State extends BaseState {
    init: boolean;
}

/**
 * 注释: Trn确认发货页
 * <AUTHOR>
 */
export default class ShipmentsTrnPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    /*** 货物明细（EGoodInfo,包含输入发货吨位）控件对象*/
    private refGoodInfoView = React.createRef<ShipmentsGoodsViewRef>();
    /***  运单图片 控件对象*/
    private refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();
    /** 协议视图 控件对线 */
    private refShowIntegrityView = React.createRef<ShowTrnIntegrityViewRef>();

    private shipmentsModel = new ShipmentsModel();

    //原生端传过来的值
    public waybill: any;
    //摘单信息
    public shipmentsEGoodInfo = new ShipmentsEGoodInfoRsp();
    //选择阅读协议
    private checkIntegrity?: boolean;

    constructor(props) {
        super(props);
        this.waybill = this.getParams();
        this.state = {
            init: false,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        //请求货物信息
        this.shipmentsModel.openTrnScene(this, this.waybill, (data) => {
            if (data.code == 200) {
                this.shipmentsEGoodInfo = data.shipmentsEGoodInfo;
                this.setState({init: true});
            } else {
                this._showToast(data.msg);
            }
        });
    }
    postShipments = () => {
        if (!Method.isOpenGPS()) {
            //打开GPS 设置
            Method.openGPS();
            return;
        }
        PermissionUtil.applyPermission(
            '中储智运需申请您的地址位置权限,以便为您确认实际到达收发货地点服务。拒绝或取消授权不影响使用其他服务',
            (code, msg) => {
                if (code == 200) {
                    this.checkAndPost();
                } else {
                    this._showToast(msg);
                }
            },
            PermissionType.LOCATION,
        );
        //发货埋点
        onEvent({pageId: 'ShipmentsPage', tableId: '#tv_postShipments'});
    };

    async checkAndPost() {
        //确认发货
        let shipmentUI = new ShipmentUI();
        shipmentUI.sourceId = this.waybill.sourceId;
        shipmentUI.orderId = this.shipmentsEGoodInfo.orderId;
        shipmentUI.shipmentsEGoodInfo = this.shipmentsEGoodInfo;
        //SDK是否可打开
        shipmentUI.haveOpenSdk = false;
        let ok = true;
        if (ok && this.refGoodInfoView.current) {
            //货物明细
            ok = this.refGoodInfoView.current.check(shipmentUI);
        }
        if (ok && this.refImagesWaybillView.current) {
            //运单图片
            ok = this.refImagesWaybillView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesWaybill = this.refImagesWaybillView.current.getImages();
            }
        }
        if (!ok) {
            return;
        }
        if (!this.checkIntegrity) {
            this._showToast('必须勾选我已阅读，才能确认发货');
            return;
        }

        this.shipmentsModel.doTrnConfirmDeliver(this, shipmentUI);
    }
    render() {
        //头部
        return (
            <View style={{flex: 1}}>
                <UITitleView title={LanguageType.getTxt('确认发货')} />
                <ScrollView style={{flex: 1}}>
                    {this.state.init && this.renderTopView()}
                    {/*1. 货物明细*/}
                    {this.state.init && this.renderGoodInfo()}
                    {/*3. 装卸货要求是否展示*/}
                    {this.state.init && this.renderConsignorPrompt()}
                    {/*4.单据照片*/}
                    {this.state.init && this.renderOrderImg()}
                    {/*6.必需显示内容 协议*/}
                    {this.state.init && this.renderIntegrity()}
                </ScrollView>
                {/* 底部按钮 */}
                {this.renderBottomView()}
                {/*基础组件初始化*/}
                {this.initCommView()}
            </View>
        );
    }

    renderTopView() {
        return (
            <Text key={'Top_view_3'} style={styles._top_orderId}>
                运单编号: {this.shipmentsEGoodInfo.orderId}
            </Text>
        );
    }

    renderGoodInfo() {
        //1.货物明细
        return <ShipmentsGoodsView ref={this.refGoodInfoView} key={'ShipmentsGoodsView_1'} shipmentsEGoodInfo={this.shipmentsEGoodInfo} sceneAdvance={0} />;
    }
    renderConsignorPrompt() {
        //3. 装卸货要求是否展示	 1:展示0：不展示
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.promptFlag)) {
            return (
                <View style={[gStyle.view_padding, {marginTop: 2}]} key={'promptFlag'}>
                    <Text style={gStyle.txt_333333_34}>装卸货要求</Text>
                    {/* 货主发单时的装卸货要求 */}
                    {TextUtils.isNoEmpty(this.shipmentsEGoodInfo.consignorPrompt) && <Text style={gStyle.txt_555555_24}>{this.shipmentsEGoodInfo.consignorPrompt}</Text>}
                    {/* 平台货物的装卸货要求 */}
                    {TextUtils.isNoEmpty(this.shipmentsEGoodInfo.platFormCargoPrompt) && <Text style={[gStyle.input_bg, gStyle.txt_666666_24]}>{this.shipmentsEGoodInfo.platFormCargoPrompt}</Text>}
                </View>
            );
        }
        return <></>;
    }
    renderOrderImg() {
        //4.单据照片
        let orderImgObj = this.shipmentsEGoodInfo.orderImgObj;

        if (orderImgObj && TextUtils.equals('1', orderImgObj.showUploadFlag)) {
            return (
                <ShipmentsImageSelectView
                    ref={this.refImagesWaybillView}
                    key={'imagesWaybill'}
                    style={{marginTop: 5}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.imageJsonObjArr}
                    title={orderImgObj.title ?? ''}
                    toast={`最多上传${orderImgObj.limitCount}张`}
                    warning={orderImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', orderImgObj.uploadFlag)}
                    waterMarkFlag={orderImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', orderImgObj.takePhotoFlag)}
                    edit={true}
                    type={'1'}
                    max={orderImgObj.limitCount ?? 8}
                />
            );
        }
        return <></>;
    }

    renderIntegrity() {
        //必需显示内容 协议
        return (
            <ShowTrnIntegrityView
                ref={this.refShowIntegrityView}
                key={'ShowIntegrityView'}
                shipmentsEGoodInfo={this.shipmentsEGoodInfo}
                onCheck={(check) => (this.checkIntegrity = check)}
            />
        );
    }

    renderBottomView() {
        return (
            <Text key={'Bottom_view_1'} onPress={this.postShipments} style={styles._bottom_view_right}>
                {TextUtils.equals('2', this.waybill.advanceState) ? '重新确认发货' : LanguageType.getTxt('确认发货')}
            </Text>
        );
    }
}

const styles = StyleSheet.create({
    _bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
    _top_advanceReason_txt: {
        width: gScreen_width,
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
        paddingRight: 15,
        backgroundColor: '#fff',
        color: '#ffe6a1',
    },
    _top_advanceReason_img: {
        width: 15,
        height: 15,
    },
    _top_policy: {
        width: gScreen_width,
        paddingLeft: 14,
        paddingTop: 7,
        paddingBottom: 7,
        paddingRight: 14,
        backgroundColor: '#fef6d9',
        fontSize: 14,
    },

    _top_orderId: {
        backgroundColor: '#fff',
        fontSize: 14,
        color: '#666666',
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
    },
});
