import React from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import UITitleView from '../../widget/UITitleView';
import BaseCommPage, {BaseState, DialogBuilder, DialogViewBuilder} from '../../base/BaseCommPage';
import TextUtils from '../../util/TextUtils';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import {gStyle} from '../../util/comm-style';
import {gScreen_height, gScreen_width} from '../../util/scaled-style';
import ShipmentsEGoodInfoRsp from './models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp, {EAdvanceType} from './models/AdvanceInfoRsp';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import ShipmentsContainerGoodsView from './view/ShipmentsContainerGoodsView';
import ShipmentsGoodsView, {ShipmentsGoodsViewRef} from './view/ShipmentsGoodsView';
import ShipmentTeaveFactoryTimeView, {ShipmentTeaveFactoryTimeViewRef} from './view/ShipmentTeaveFactoryTimeView';
import ShowIntegrityView, {ShowIntegrityViewRef} from './view/ShowIntegrityView';
import EventBus from '../../util/EventBus';
import {Constant} from '../../base/Constant';
import {ShipmentsModel} from './ShipmentsModel';
import {ShipmentUI} from './models/ShipmentUI';
import {EAdvanceInfoBeforeCommit, ReqQueryAdvanceInfoBeforeCommit} from './requests/ReqQueryAdvanceInfoBeforeCommit';
import ShipmentsTerracePay, {ShipmentsTerracePayRef} from './view/ShipmentsTerracePay';
import {PermissionType, PermissionUtil} from '../../util/PermissionUtil';
import UIImage from '../../widget/UIImage';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import AntiDraudDialog from '../../pick/views/AntiDraudDialog';
import LanguageType from '../../util/language/LanguageType';
import {onEvent} from '../../base/native/UITrackingAction';
import OrderShipmentInsuranceView, {OrderShipmentInsuranceViewRef} from './view/OrderShipmentInsuranceView';
import {EAgreementType} from '../../pick/views/AgreementView';
import ShipmentsPersonCarGoodsImageSelectView from './view/ShipmentsPersonCarGoodsImageSelectView';
import {FaceInfo, ReqFaceRecognitionForUpgrade} from './requests/ReqFaceRecognitionForUpgrade';
import {plainToInstance} from 'class-transformer';
import UIImageBackground from '../../widget/UIImageBackground';
import {Method} from '../../util/NativeModulesTools';
import UIButton from '../../widget/UIButton';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import ShipmentVideoRemarkView, {ShipmentVideoRemarkViewRef} from './view/ShipmentVideoRemarkView';

interface State extends BaseState {
    init: boolean;
    //计算货主预付费用
    hzPayMoney: number;
    /**** 司机是否选择了非指定预付款 1 选择 2 未选择*/
    isAdvanceButtonOn: boolean;
    /**** 预付方式*/
    advanceWay?: EAdvanceType;
    /*** 油气比例值 */
    oilRatio: string;
    //是否为大货件(ZCZY-16095)
    isBulkCargo: boolean;
    //防诈骗对话框
    showFullConsignorPrompt: boolean;
    isConsignorPromptCollapsible: boolean;
}

/**
 * 注释: 确认发货页
 * 时间: 2023/7/10 0010 10:59
 * <AUTHOR>
 */
export default class ShipmentsPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    private shipmentsModel = new ShipmentsModel();

    //原生端传过来的值
    waybill: any;
    //摘单信息
    public shipmentsEGoodInfo = new ShipmentsEGoodInfoRsp();
    public advanceInfo = new AdvanceInfoRsp();

    /*** 预付 控件对象*/
    private refShipmentsTerracePay = React.createRef<ShipmentsTerracePayRef>();
    /*** 货物明细（EGoodInfo,包含输入发货吨位）控件对象*/
    private refGoodInfoView = React.createRef<ShipmentsGoodsViewRef>();
    /***发货出厂时间时间*/
    private refTeaveFactoryTimeView = React.createRef<ShipmentTeaveFactoryTimeViewRef>();
    /***  运单图片 控件对象*/
    private refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();
    /***  人车货照片 控件对象*/
    private refIImagesCarPersonView = React.createRef<ShipmentsImageSelectViewRef>();
    /***  挂车车牌合影图片 控件对象*/
    private refIImagesTrailerView = React.createRef<ShipmentsImageSelectViewRef>();
    //备注视图索引
    private refShipmentsRemarksView = React.createRef<ShipmentVideoRemarkViewRef>();
    /** 货保障服务 控件对象 */
    private refOrderPickOfferInsuranceView = React.createRef<OrderShipmentInsuranceViewRef>();
    /** 协议视图 控件对线 */
    private refShowIntegrityView = React.createRef<ShowIntegrityViewRef>();

    //是否使用立减优惠
    private userPaymentMoney?: string;
    //选择阅读协议
    private checkIntegrity?: boolean;

    //预付服务更改回调
    eventBusListener: Function;

    //人脸识别标记
    private faceIdentifyFlag?: string;
    //人脸信息
    private faceIdentifyInfo?: FaceInfo;

    constructor(props) {
        super(props);
        this.faceIdentifyFlag = '';
        this.waybill = this.getParams();
        // @ts-ignore
        this.state = {
            init: false,
            isAdvanceButtonOn: true,
            isBulkCargo: false,
            showFullConsignorPrompt: false,
            isConsignorPromptCollapsible: false,
        };
        //EventBus回调
        this.eventBusListener = ({key, value}) => {
            //数据回调
            switch (key) {
                case 'isAdvanceButtonOn': {
                    //选择是否需要预付
                    //必须放在赋值前面
                    if (this.state.isAdvanceButtonOn == false && value == true) {
                        //发货时，司机从不需要预付修改为需要预付，则清空司机已上传的单据照片和人车货合影
                        this.refIImagesCarPersonView.current?.clear();
                        this.refImagesWaybillView.current?.clear();
                    }
                    this.setState({isAdvanceButtonOn: value});
                    break;
                }
                //计算货主预付费用
                case 'hzPayMoney': {
                    this.setState({hzPayMoney: value});
                    break;
                }
                //选择油比例
                case 'selectOilRatio': {
                    this.setState({
                        oilRatio: value,
                    });
                    break;
                }
                //切换预付方式
                case 'setAdvanceWay': {
                    this.setState({advanceWay: value});
                    break;
                }
            }
        };
    }

    postShipments = () => {
        if (!Method.isOpenGPS()) {
            //打开GPS 设置
            Method.openGPS();
            return;
        }
        PermissionUtil.applyPermission(
            '中储智运需申请您的地址位置权限,以便为您确认实际到达收发货地点服务。拒绝或取消授权不影响使用其他服务',
            (code, msg) => {
                if (code == 200) {
                    if (TextUtils.equals('1', this.shipmentsEGoodInfo.haveAuthorize)) {
                        //ZCZY-6636 【互联互通】兰鑫钢铁系统对接
                        let dialog = new DialogViewBuilder();
                        dialog.views = (
                            <View
                                style={{
                                    borderTopLeftRadius: 6,
                                    borderTopRightRadius: 6,
                                    position: 'absolute',
                                    backgroundColor: '#fff',
                                    bottom: -gScreen_width * 0.1,
                                    padding: 0,
                                    left: -gScreen_width * 0.1,
                                    alignItems: 'center',
                                    width: gScreen_width * 1.1,
                                }}>
                                <UIImage
                                    source={'http://img.zczy56.com/202507181611065463417.png'}
                                    style={{
                                        width: '100%',
                                        height: '120%',
                                        position: 'absolute',
                                        bottom: -gScreen_height * 0.01,
                                        left: -gScreen_width * 0.05,
                                    }}
                                />
                                <View
                                    style={{
                                        paddingVertical: 20,
                                        paddingHorizontal: 20,
                                        borderTopLeftRadius: 6,
                                        borderTopRightRadius: 6,
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                    }}>
                                    <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold'}}>场区通行顺畅须知</Text>
                                </View>
                                <Text
                                    style={{
                                        paddingHorizontal: 20,
                                        paddingVertical: 15,
                                        fontSize: 15,
                                        color: '#666',
                                        lineHeight: 22,
                                    }}>
                                    为便于<Text style={{color: '#478AFE'}}>您在该货主场区顺利作业</Text>，平台将通过系统对接形式向货主提供您的身份证号。若您充分理解上述信息的收集，请点击同意。
                                </Text>
                                <View style={{paddingHorizontal: 20, paddingBottom: 20}}>
                                    <UIButton
                                        text={'同意并发货'}
                                        fontSize={17}
                                        textColor={'#fff'}
                                        fontWeight={true}
                                        style={{
                                            width: 200,
                                            backgroundColor: '#5086FC',
                                            borderRadius: 6,
                                            marginBottom: 10,
                                            height: 44,
                                        }}
                                        onPress={() => {
                                            dialog.onDismiss();
                                            //发货检查参数
                                            this.checkAndPost();
                                        }}
                                    />
                                    <UITouchableOpacity
                                        style={{
                                            alignItems: 'center',
                                            paddingVertical: 10,
                                        }}
                                        onPress={() => {
                                            dialog.onDismiss();
                                        }}>
                                        <Text style={{fontSize: 15, color: '#999'}}>放弃发货</Text>
                                    </UITouchableOpacity>
                                </View>
                            </View>
                        );
                        this._showViewDialog(dialog);
                    } else {
                        //发货检查参数
                        this.checkAndPost();
                    }
                } else {
                    this._showToast(msg);
                }
            },
            PermissionType.LOCATION,
        );
        //发货埋点
        onEvent({pageId: 'ShipmentsPage', tableId: '#tv_postShipments'});
    };

    async checkAndPost() {
        //确认发货
        let shipmentUI = new ShipmentUI();
        shipmentUI.scene = '0';
        shipmentUI.autoGuidFlag = this.shipmentsEGoodInfo.autoGuidFlag;

        if (TextUtils.equals('1', this.shipmentsEGoodInfo.faceIdentifyFlag) && Method.getHostVersion() >= 1900) {
            //大于等于804版本，需要进行人脸识别
            if (TextUtils.isEmpty(this.faceIdentifyFlag)) {
                //当前没有进行人脸识别
                let response = await RouterUtils.skipRouter(RouterUrl.FaceRecognitionPage);
                if (response != null) {
                    this.faceIdentifyFlag = response.code;
                    if (TextUtils.equals('1', this.faceIdentifyFlag)) {
                        let data = response.data;
                        if (data != null) {
                            this.faceIdentifyInfo = plainToInstance(FaceInfo, data);
                            let req = new ReqFaceRecognitionForUpgrade();
                            req.idCardNo = this.faceIdentifyInfo.idCardNo ?? '';
                            req.idCardName = this.faceIdentifyInfo.idCardName ?? '';
                            req.orderNo = this.shipmentsEGoodInfo.orderId;
                            req.functionType = '15';
                            req.request().then((_response) => {});
                        }
                    }
                }
            }
        }
        shipmentUI.orderId = this.shipmentsEGoodInfo.orderId;
        shipmentUI.detailId = this.shipmentsEGoodInfo.detailId;
        shipmentUI.faceIdentifyFlag = this.faceIdentifyFlag;
        shipmentUI.shipmentsEGoodInfo = this.shipmentsEGoodInfo;
        shipmentUI.advanceInfo = this.advanceInfo;
        //SDK是否可打开
        shipmentUI.haveOpenSdk = TextUtils.equals('1', this.shipmentsEGoodInfo.haveOpenSdk);
        shipmentUI.floodSafeTipFlag = this.shipmentsEGoodInfo.floodSafeTipFlag;

        let ok = true;

        if (ok && this.refShipmentsTerracePay.current) {
            //预付
            ok = this.refShipmentsTerracePay.current.check(shipmentUI);
        }
        if (ok && this.refGoodInfoView.current) {
            //货物明细
            ok = this.refGoodInfoView.current.check(shipmentUI);
        }
        if (ok && this.refTeaveFactoryTimeView.current) {
            //发货出厂时间时间
            ok = this.refTeaveFactoryTimeView.current?.check(shipmentUI);
        }

        if (ok && this.refImagesWaybillView.current) {
            //运单图片
            ok = this.refImagesWaybillView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesWaybill = this.refImagesWaybillView.current.getImages();
            }
        }
        if (ok && this.refIImagesCarPersonView.current) {
            //人车货图片
            ok = this.refIImagesCarPersonView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesCarPerson = this.refIImagesCarPersonView.current.getImages();
            }
        }
        if (ok && this.refIImagesTrailerView.current) {
            //挂车车牌合影图片
            ok = this.refIImagesTrailerView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesTrailer = this.refIImagesTrailerView.current.getImages();
            }
        }
        if (ok && this.refOrderPickOfferInsuranceView.current) {
            ok = this.refOrderPickOfferInsuranceView.current.check(shipmentUI);
        }
        if (ok && this.refShipmentsRemarksView.current) {
            ok = this.refShipmentsRemarksView.current.check(shipmentUI);
        }
        if (!ok) {
            return;
        }
        if (!this.checkIntegrity) {
            this._showToast('必须勾选我已阅读，才能确认发货');
            return;
        }
        this.shipmentsModel.cancelAdvance = () => {
            this.refShipmentsTerracePay.current?.notAdvanceOn();
        };
        if (TextUtils.equals('1', shipmentUI.isAdvanceButtonOn)) {
            if (TextUtils.equals('2', this.advanceInfo.advanceType)) {
                //2.个体司机非指定订单
                await this.queryAdvanceInfoBeforeCommit(shipmentUI);
            } else {
                //预付款
                let dialog = new DialogBuilder();
                dialog.title = '提交预付款审核';
                if (TextUtils.equals('4', this.advanceInfo.advanceType)) {
                    //"提交后平台会进行审核，审核通过后会将预付运费打款至您关联的车老板智运宝账户"
                    dialog.msg = TextUtils.equals('2', shipmentUI.advanceWay)
                        ? '提交后平台会进行审核，审核通过会将预付运费打款至您关联的车老板智运油卡账户'
                        : TextUtils.equals('3', shipmentUI.advanceWay)
                        ? '提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本和智运油卡账户中进行查询'
                        : '提交后平台会进行审核，审核通过后您关联的车老板可在其智运账本中进行查询';
                } else {
                    //1:预付现金;2:预付油品;3:预付油品+现金
                    dialog.views = TextUtils.equals('2', shipmentUI.advanceWay) ? (
                        <Text style={styles.dialogContent}>
                            提交后平台会进行审核，预计处理时长<Text style={{color: '#FF5E1C'}}>30分钟</Text>，审核结果会通过APP消息告知，请您耐心等待。{'\n审核通过后您可在智运油卡账户中进行查询。'}
                        </Text>
                    ) : TextUtils.equals('3', shipmentUI.advanceWay) ? (
                        <Text style={styles.dialogContent}>
                            提交后平台会进行审核，预计处理时长<Text style={{color: '#FF5E1C'}}>30分钟</Text>，审核结果会通过APP消息告知，请您耐心等待。{'\n审核通过后您可在智运账本和智运油卡账户中进行查询。'}
                        </Text>
                    ) : (
                        <Text style={styles.dialogContent}>
                            提交后平台会进行审核，预计处理时长<Text style={{color: '#FF5E1C'}}>30分钟</Text>，审核结果会通过APP消息告知，请您耐心等待。{'\n审核通过后您可在智运账本中进行查询。'}
                        </Text>
                    );
                }
                dialog.onCancelEvent = () => {
                    onEvent({pageId: 'ShipmentsPage', tableId: 'bt#onCancelEvent'});
                };
                dialog.onOkEvent = () => {
                    this.shipmentsModel.confrimOrderDeliver(this, shipmentUI);
                    onEvent({pageId: 'ShipmentsPage', tableId: 'bt#onOkEvent'});
                };
                this._showDialog(dialog);
            }
        } else {
            this.shipmentsModel.confrimOrderDeliver(this, shipmentUI);
        }
    }

    async queryAdvanceInfoBeforeCommit(shipmentUI: ShipmentUI) {
        //发货之前选择预付时提示信息
        this._showWaitDialog();

        let req = new ReqQueryAdvanceInfoBeforeCommit();
        req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
        req.weightDetails = shipmentUI.getCargoIdWeightData();
        req.advanceType = shipmentUI.advanceWay;
        req.advanceOilRatio = shipmentUI.oilRatio;

        let result = await req.request();

        this._dismissWait();

        if (result.isSuccess() && result.data != undefined) {
            let data = result.data;
            this.showAdvanceDialog(shipmentUI, data);
        } else {
            this._showMsgDialog(result.getMsg());
        }
    }

    componentDidMount() {
        //EventBus注册
        super.componentDidMount();
        EventBus.getInstance().addListener(Constant.event_shipments_page, this.eventBusListener);

        //请求货物信息和预付信息
        this.shipmentsModel.openScene(this, this.waybill, (data) => {
            if (data.code == 200) {
                this.shipmentsEGoodInfo = data.shipmentsEGoodInfo;
                if (TextUtils.equals(this.shipmentsEGoodInfo.deliverPolicyInfo?.canBuyPolicy, '1')) {
                    this.refShowIntegrityView.current?.showKey(EAgreementType.INSURANCE);
                }
                this.advanceInfo = data.advanceInfo;
                this.setState({
                    init: true,
                    isBulkCargo: TextUtils.equals('1', data.shipmentsEGoodInfo.bulkCargoTipFlag),
                });
            } else {
                this._showToast(data.msg);
            }
        });
        //请求防诈骗对话框
    }

    componentWillUnmount() {
        //EventBus移除监听
        super.componentWillUnmount();
        EventBus.getInstance().removeListener(this.eventBusListener);
    }

    showAdvanceDialog(shipmentUI: ShipmentUI, data: EAdvanceInfoBeforeCommit) {
        let dialog = new DialogBuilder();
        dialog.title = '提交预付款审核';

        //{"resultCode":"0000","changRatioFlag":"0",
        // "totalMsg":"提交后平台会进行审核，审核通过后您可在智运账本中进行查询\n平台预付额度上限为300元，本次现金预付金额为300元，预付服务费为0.71元",
        // "tipFlag":"1","attributeMsg":"平台预付额度上限为300元，本次现金预付金额为300元，预付服务费为0.71元","resultMsg":"查询成功"}}
        if (TextUtils.equals('1', data.tipFlag)) {
            let attributeMsg = data.attributeMsg ?? '';
            let totalMsg = data.totalMsg ?? '';
            let txts = totalMsg.split(attributeMsg);

            dialog.views = (
                <Text style={{padding: 7}}>
                    {txts.map((value, index) => (
                        <Text key={index}>
                            {index > 0 && <Text style={{color: '#5086FC'}}>{attributeMsg}</Text>}
                            {value}
                        </Text>
                    ))}
                </Text>
            );
        } else {
            dialog.msg = data?.totalMsg ?? '';
        }
        dialog.onCancelEvent = () => {
            if (TextUtils.equals('1', data.changRatioFlag)) {
                // 预付比例是否变更 1:变更,变动界面比例
                if (this.refShipmentsTerracePay.current != null) {
                    this.refShipmentsTerracePay.current.setOilRatio(data?.advanceRatio ?? '');
                }
            }
            onEvent({pageId: 'ShipmentsPage', tableId: 'bt#onCancelEvent'});
        };
        dialog.onOkEvent = () => {
            //确认发货
            this.shipmentsModel.confrimOrderDeliver(this, shipmentUI);
            onEvent({pageId: 'ShipmentsPage', tableId: 'bt#onOkEvent'});
        };
        this._showDialog(dialog);
    }

    render() {
        //头部
        return (
            <View style={{flex: 1}}>
                <UITitleView title={LanguageType.getTxt('确认发货')} />
                <ScrollView style={{flex: 1}}>
                    {/*大件货提示*/}
                    {this.state.isBulkCargo && this.renderBulkCargoTipView()}
                    {this.state.init && this.renderTopView()}
                    {/*指定预付提示文字*/}
                    {this.state.init && this.renderSpanAdvanceView()}
                    {/*绘制预付*/}
                    {this.state.init && this.renderTerracePay()}
                    {/*1. 货物明细*/}
                    {this.state.init && this.renderGoodInfo()}
                    {/*2. 货物出厂时间 ZCZY-7729 冀东定制化需求*/}
                    {this.state.init && this.renderTeaveFactoryTime()}
                    {/*3. 装卸货要求是否展示*/}
                    {this.state.init && this.renderConsignorPrompt()}
                    {/*4.单据照片*/}
                    {this.state.init && this.renderOrderImg()}
                    {/*5.人车货合影*/}
                    {this.state.init && this.renderPeopleVehicleImg()}
                    {/*挂车车牌合影图片 */}
                    {this.state.init && this.renderTrailerImage()}
                    {/*货保服务视图*/}
                    {this.state.init && this.renderShipmentInsuranceView()}
                    {/*备注视图*/}
                    {this.state.init && this.renderRemarksView()}
                    {/*6.必需显示内容 协议*/}
                    {this.state.init && this.renderIntegrity()}
                    {/*7.防诈骗对话框*/}
                    {this.state.init && this.renderAntiDraudDialog()}
                </ScrollView>
                {/* 底部按钮 */}
                {this.renderBottomView()}
                {/*基础组件初始化*/}
                {this.initCommView()}
            </View>
        );
    }

    renderSpanAdvanceView() {
        if (this.advanceInfo && TextUtils.equals('1', this.advanceInfo.advanceType) && TextUtils.equals('1', this.advanceInfo.isAdvance)) {
            //指定预付提示信息
            return (
                <View
                    style={{
                        paddingLeft: 14,
                        paddingRight: 14,
                        paddingTop: 5,
                        paddingBottom: 5,
                        backgroundColor: '#fff',
                        marginTop: 7,
                    }}>
                    <Text style={{fontSize: 17, color: '#333333'}}>预付服务</Text>
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#999999',
                        }}>
                        您的预付申请需要货主方审核，请耐心等待。审核通过后我们会消息通知您。
                    </Text>
                </View>
            );
        }
    }

    /**
     * 注释: 大件货提示（ZCZY-16095）
     * 时间: 2023/10/23 16:39
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderBulkCargoTipView() {
        return (
            <View style={{backgroundColor: '#fff', paddingBottom: 7, paddingTop: 7, marginBottom: 5}}>
                <UIImageBackground source={'http://img.zczy56.com/202412121004563126370.png'} style={{width: gScreen_width - 20, height: 80, paddingTop: 20, alignSelf: 'center'}} resizeMode="contain">
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#666',
                            padding: 7,
                            marginTop: 5,
                        }}>
                        您正在确认发货大件货物运单，请在卸货前上传《超限运输车辆通行证》，否则将影响运单结算
                    </Text>
                </UIImageBackground>
            </View>
        );
    }

    /**
     * 注释: 绘制发货备注视图
     * 时间: 2023/7/31 0031 19:06
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderRemarksView() {
        return <ShipmentVideoRemarkView ref={this.refShipmentsRemarksView} max={3} edit={true} deliverVideoArray={this.shipmentsEGoodInfo.deliverVideoJsonArr} remark={this.shipmentsEGoodInfo.carrierDeliverRemark} />;
    }

    renderTopView() {
        return (
            <View>
                {TextUtils.equals('2', this.shipmentsEGoodInfo.advanceState) && TextUtils.isNoEmpty(this.shipmentsEGoodInfo.advanceReason) && (
                    <Text key={'Top_view_1'} style={styles._top_advanceReason_txt}>
                        <UIImage style={styles._top_advanceReason_img} source={'base_message_black_ic'} />
                        未通过原因：{this.shipmentsEGoodInfo.advanceReason}
                    </Text>
                )}
                {TextUtils.equals('1', this.shipmentsEGoodInfo.orderPolicyFlag) && <Text style={styles._top_policy}>{`温馨提示：此单已购买保障服务，预计扣除${this.shipmentsEGoodInfo.orderPolicyMoney}元货物保障服务费`}</Text>}
                <Text key={'Top_view_3'} style={styles._top_orderId}>
                    运单编号: {this.shipmentsEGoodInfo.orderId}
                </Text>
            </View>
        );
    }

    renderTerracePay() {
        let show = !TextUtils.equals('2', this.shipmentsEGoodInfo.goodsSource) && (TextUtils.equals('2', this.advanceInfo.advanceType) || TextUtils.equals('4', this.advanceInfo.advanceType)) && !TextUtils.equals('1', this.waybill.modeType);
        if (show) {
            //2.个体司机非指定订单 => 走平台预付
            // 4.个体司机关联车老板模式 => 走平台预付
            return (
                <ShipmentsTerracePay
                    ref={this.refShipmentsTerracePay}
                    isAdvanceButtonOn={this.state.isAdvanceButtonOn}
                    shipmentsEGoodInfo={this.shipmentsEGoodInfo}
                    advanceInfo={this.advanceInfo}
                    sceneAdvance={0}
                    advanceState={this.waybill.advanceState}
                />
            );
        }
    }

    renderGoodInfo() {
        //1.货物明细
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.goodsSource)) {
            //ZCZY-7768 批量货按箱发布（针对广州中物储）(界面优先判断集装箱)
            //1.集装箱货物明细(View)
            //1.货物明细
            return <ShipmentsContainerGoodsView ref={this.refGoodInfoView} key={'ShipmentsGoodsView_1'} shipmentsEGoodInfo={this.shipmentsEGoodInfo} advanceInfo={this.advanceInfo} sceneAdvance={0} />;
        } else {
            return <ShipmentsGoodsView ref={this.refGoodInfoView} key={'ShipmentsGoodsView_1'} shipmentsEGoodInfo={this.shipmentsEGoodInfo} advanceInfo={this.advanceInfo} sceneAdvance={0} />;
        }
    }

    renderTeaveFactoryTime() {
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.deliverOutStageFlag)) {
            //2.货物出厂时间  非货后预付申请 && ZCZY-7729 冀东定制化需求
            return <ShipmentTeaveFactoryTimeView key={'ShipmentTeaveFactoryTimeView'} ref={this.refTeaveFactoryTimeView} />;
        }
        return <></>;
    }

    renderConsignorPrompt() {
        //3. 装卸货要求是否展示	 1:展示0：不展示
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.promptFlag)) {
            const consignorPrompt = this.shipmentsEGoodInfo.consignorPrompt;
            const platFormCargoPrompt = this.shipmentsEGoodInfo.platFormCargoPrompt;

            const showConsignorPrompt = TextUtils.isNoEmpty(consignorPrompt);
            const showPlatFormCargoPrompt = TextUtils.isNoEmpty(platFormCargoPrompt);

            if (!showConsignorPrompt && !showPlatFormCargoPrompt) {
                return <></>;
            }

            // 拼接文本内容，如果两个都存在，则添加换行符
            const fullText = (showConsignorPrompt ? consignorPrompt : '') + (showConsignorPrompt && showPlatFormCargoPrompt ? '\n' : '') + (showPlatFormCargoPrompt ? platFormCargoPrompt : '');

            const maxLines = 3; // 设置最大显示行数

            return (
                <View style={[gStyle.view_padding, {marginTop: 2}]} key={'promptFlag'}>
                    <Text style={gStyle.txt_333333_34}>装卸货要求</Text>
                    <Text
                        style={gStyle.txt_555555_24}
                        numberOfLines={this.state.showFullConsignorPrompt ? undefined : maxLines}
                        onTextLayout={(event) => {
                            if (!this.state.isConsignorPromptCollapsible && event.nativeEvent.lines.length > maxLines) {
                                this.setState({isConsignorPromptCollapsible: true});
                            }
                        }}>
                        {fullText}
                    </Text>
                    {this.state.isConsignorPromptCollapsible && (
                        <Text style={{color: '#5086FC'}} onPress={() => this.setState((prevState) => ({showFullConsignorPrompt: !prevState.showFullConsignorPrompt}))}>
                            {this.state.showFullConsignorPrompt ? '收起' : '...展开更多'}
                        </Text>
                    )}
                </View>
            );
        }
        return <></>;
    }

    renderOrderImg() {
        //4.单据照片
        let orderImgObj = this.shipmentsModel.selectOrderImgObj(this.state.isAdvanceButtonOn, this.shipmentsEGoodInfo, this.advanceInfo);

        if (orderImgObj && TextUtils.equals('1', orderImgObj.showUploadFlag)) {
            return (
                <ShipmentsImageSelectView
                    ref={this.refImagesWaybillView}
                    key={'imagesWaybill'}
                    style={{marginTop: 5}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.imageJsonObjArr}
                    title={orderImgObj.title ?? ''}
                    toast={`最多上传${orderImgObj.limitCount}张`}
                    warning={orderImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', orderImgObj.uploadFlag)}
                    waterMarkFlag={orderImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', orderImgObj.takePhotoFlag)}
                    edit={true}
                    type={'1'}
                    max={orderImgObj.limitCount ?? 8}
                />
            );
        }
        return <></>;
    }

    renderPeopleVehicleImg() {
        //人车货合影
        let peopleVehicleImgObj = this.shipmentsModel.selectPeopleVehicleImgObj(this.state.isAdvanceButtonOn, this.shipmentsEGoodInfo, this.advanceInfo);

        if (peopleVehicleImgObj && TextUtils.equals('1', peopleVehicleImgObj.showUploadFlag)) {
            return (
                <ShipmentsPersonCarGoodsImageSelectView
                    ref={this.refIImagesCarPersonView}
                    key={'imagesCarPerson'}
                    style={{marginTop: 5}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.imageJsonObjArr2}
                    title={peopleVehicleImgObj.title ?? ''}
                    toast={''}
                    warning={peopleVehicleImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', peopleVehicleImgObj.uploadFlag)}
                    waterMarkFlag={peopleVehicleImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', peopleVehicleImgObj.takePhotoFlag)}
                    edit={true}
                    max={peopleVehicleImgObj.limitCount ?? 8}
                    min={this.shipmentsEGoodInfo.minLimitCount ?? 2}
                    demoImg="http://img.zczy56.com/202504181430024132410.png"
                />
            );
        }
    }

    renderTrailerImage() {
        //挂车车牌合影
        let vehicleImgObj = this.shipmentsModel.selectTrailerImageObj(this.state.isAdvanceButtonOn, this.shipmentsEGoodInfo, this.advanceInfo);

        if (vehicleImgObj && TextUtils.equals('1', vehicleImgObj.showUploadFlag)) {
            return (
                <ShipmentsPersonCarGoodsImageSelectView
                    ref={this.refIImagesTrailerView}
                    key={'refIImagesTrailerView'}
                    style={{marginTop: 5}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.trailerImageJsonObjArr}
                    title={vehicleImgObj.title ?? ''}
                    toast={''}
                    warning={vehicleImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', vehicleImgObj.uploadFlag)}
                    waterMarkFlag={vehicleImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', vehicleImgObj.takePhotoFlag)}
                    edit={true}
                    max={vehicleImgObj.limitCount ?? 8}
                    min={vehicleImgObj.minLimitCount ?? 2}
                    demoImg="http://img.zczy56.com/202505271511252225712.png"
                />
            );
        }
    }

    /**
     * 注释: 发货货物保障服务
     * 时间: 2024/3/26 11:23
     * <AUTHOR>
     */
    renderShipmentInsuranceView() {
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.deliverPolicyInfo?.canBuyPolicy)) {
            return (
                <OrderShipmentInsuranceView
                    style={{marginTop: 7}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    shipmentsEGoodInfo={this.shipmentsEGoodInfo}
                    ref={this.refOrderPickOfferInsuranceView}
                    onSelect={(selected) => {
                        if (selected) {
                            this.refShowIntegrityView.current?.showKey(EAgreementType.INSURANCE);
                        } else {
                            this.refShowIntegrityView.current?.hiddenKey(EAgreementType.INSURANCE);
                        }
                    }}
                />
            );
        }
    }

    renderIntegrity() {
        //是否显示货物保障协议
        let showHW = TextUtils.equals('1', this.shipmentsEGoodInfo.deliverPolicyInfo?.canBuyPolicy);
        //必需显示内容 协议
        return (
            <ShowIntegrityView
                ref={this.refShowIntegrityView}
                key={'ShowIntegrityView'}
                isAdvanceButtonOn={this.state.isAdvanceButtonOn}
                shipmentsEGoodInfo={this.shipmentsEGoodInfo}
                advanceInfo={this.advanceInfo}
                dlOilCardRatio={this.state.oilRatio}
                userPaymentMoney={this.userPaymentMoney ?? ''}
                advanceWay={this.state.advanceWay}
                onCheck={(check) => (this.checkIntegrity = check)}
                isHuoWu={showHW}
            />
        );
    }

    renderBottomView() {
        return this.advanceInfo && this.advanceInfo.advanceType === '1' && this.advanceInfo.isAdvance === '1' ? (
            //个体司机指定订单 && 预付 => 走货主预付款
            <View key={'Bottom_view_1'} style={styles._bottom_view}>
                <View style={styles._bottom_view_left}>
                    <Text style={gStyle.txt_333333_34}>预付运费 ¥</Text>
                    <Text style={styles._bottom_view_left_txt}>{this.state.hzPayMoney}</Text>
                </View>
                <View style={{flex: 1}}>
                    <Text onPress={this.postShipments} style={styles._bottom_view_right}>
                        {TextUtils.equals('2', this.waybill.advanceState) ? '重新确认发货' : LanguageType.getTxt('确认发货')}
                    </Text>
                </View>
            </View>
        ) : (
            <Text key={'Bottom_view_1'} onPress={this.postShipments} style={styles._bottom_view_right}>
                {TextUtils.equals('2', this.waybill.advanceState) ? '重新确认发货' : LanguageType.getTxt('确认发货')}
            </Text>
        );
    }

    renderAntiDraudDialog() {
        //防诈骗
        return <AntiDraudDialog orderId={this.waybill.orderId} setId={this.waybill.setId} selectNode={'5'} />;
    }
}

const styles = StyleSheet.create({
    //底部样式
    _bottom_view: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 50,
    },

    _bottom_view_left: {
        flex: 1,
        paddingLeft: 10,
        backgroundColor: '#fff',
        flexDirection: 'row',
        alignItems: 'center',
        height: 50,
    },

    _bottom_view_left_txt: {
        fontSize: 17,
        color: '#FB6B40',
    },
    _bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
    _top_advanceReason_txt: {
        width: gScreen_width,
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
        paddingRight: 15,
        backgroundColor: '#fff',
        color: '#ffe6a1',
    },
    _top_advanceReason_img: {
        width: 15,
        height: 15,
    },
    _top_policy: {
        width: gScreen_width,
        paddingLeft: 14,
        paddingTop: 7,
        paddingBottom: 7,
        paddingRight: 14,
        backgroundColor: '#fef6d9',
        fontSize: 14,
    },

    _top_orderId: {
        backgroundColor: '#fff',
        fontSize: 14,
        color: '#666666',
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
    },
    dialogContent: {
        textAlign: 'left',
        textAlignVertical: 'top',
        fontSize: 16,
        color: '#4A4A4A',
        paddingHorizontal: 15,
        paddingBottom: 15,
    },
});
