import {<PERSON><PERSON><PERSON><PERSON>, NativeEventSubscription, ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState, DialogBuilder} from '../../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../../widget/UITitleView';
import {ReqOffLineBeforeDeliverCargoQuery} from './requests/ReqOffLineBeforeDeliverCargoQuery';
import OffLineShipmentsEGoodInfo from './models/OffLineShipmentsEGoodInfo';
import TextUtils from '../../util/TextUtils';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import {PermissionType, PermissionUtil} from '../../util/PermissionUtil';
import {ReqConfirmOrderDeliverOffLine} from './requests/ReqConfirmOrderDeliverOffLine';
import {ArrayUtils} from '../../util/ArrayUtils';
import EventBus from '../../util/EventBus';
import {Constant} from '../../base/Constant';
import {Method} from '../../util/NativeModulesTools';
interface State extends BaseState {
    offLineShipmentsEGoodInfo?: OffLineShipmentsEGoodInfo;
    editable?: boolean;
    deliveryTonnage?: string;
}

/**
 * 注释: 确认发货【线下专区专用】
 * 时间: 2023/7/12 0012 13:56
 * <AUTHOR>
 */
export default class OffLineShipmentsPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    orderId: string;
    detailId: string;
    pageParams: any;

    //纸质单据照片上传索引
    private refImageSelectView = React.createRef<ShipmentsImageSelectViewRef>();

    //返回键监听
    private backHandle: NativeEventSubscription;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.detailId = this.pageParams.detailId;
    }

    componentDidMount() {
        super.componentDidMount();
        this._showWaitDialog();
        //网络请求
        let request = new ReqOffLineBeforeDeliverCargoQuery();
        request.orderId = this.orderId;
        request.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                let editable = !TextUtils.equals('1', response.data?.LTCantUpdate);
                this.setState({
                    offLineShipmentsEGoodInfo: response.data,
                    editable: editable,
                    deliveryTonnage: response.data?.LTTotalMoney,
                });
            }
        });
        this.backHandle = BackHandler.addEventListener('hardwareBackPress', () => {
            let dialogBuilder = new DialogBuilder();
            dialogBuilder.title = '提示';
            dialogBuilder.msg = '确认发货未完成，您确认要离开？';
            dialogBuilder.onOkEvent = () => {
                this._finish();
            };
            this._showDialog(dialogBuilder);
            return true;
        });
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        this.backHandle.remove();
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'确认发货'} />
                <ScrollView style={{flex: 1}}>
                    {/*绘制运单编号*/}
                    {this.renderOrderNo()}
                    {/*绘制实际发货吨数*/}
                    {this.renderRealDeliveryTonnage()}
                    {/*纸质运单上传*/}
                    {this.renderImageUpload()}
                </ScrollView>
                {/* 底部按钮 */}
                {this.renderBottomView()}
                {/*初始化基础组件*/}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制运单编号
     * 时间: 2023/7/10 0010 16:07
     * <AUTHOR>
     * @return {JSX.Element}
     */
    renderOrderNo() {
        return (
            <View
                style={{
                    paddingLeft: 14,
                    backgroundColor: '#fff',
                    height: 42,
                    marginBottom: 7,
                    justifyContent: 'center',
                }}>
                <Text style={{fontSize: 16}}>{`运单编号：${this.orderId}`}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制实际发货吨数
     * 时间: 2023/7/12 0012 14:36
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderRealDeliveryTonnage() {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: '#fff',
                    paddingHorizontal: 14,
                    marginBottom: 7,
                }}>
                <Text style={{fontSize: 16, color: '#333'}}>实际发货吨数</Text>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <TextInput
                        style={{fontSize: 16, textAlign: 'right'}}
                        editable={this.state.editable}
                        clearButtonMode="while-editing"
                        keyboardType="numeric"
                        defaultValue={this.state.deliveryTonnage}
                        placeholder={'请输入'}
                        onChangeText={(text) => {
                            this.setState({deliveryTonnage: text});
                        }}
                    />
                    <Text style={{fontSize: 16, color: '#333'}}>吨</Text>
                </View>
            </View>
        );
    }

    /**
     * 注释: 纸质运单上传
     * 时间: 2023/7/12 0012 15:32
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderImageUpload() {
        return (
            <ShipmentsImageSelectView
                ref={this.refImageSelectView}
                key={'imagesUpload'}
                style={{marginTop: 5}}
                orderId={this.orderId}
                title={'纸质运单照片（选填）'}
                toast={'最多上传3张'}
                showReadToastIcon={false}
                takePhoto={true}
                edit={true}
                type={'1'}
                max={3}
            />
        );
    }

    /**
     * 注释: 确认修改按钮
     * 时间: 2023/7/10 0010 16:03
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderBottomView() {
        return (
            <Text style={styles.bottom_view_right} onPress={() => this.checkAndPost()}>
                确认发货
            </Text>
        );
    }

    /**
     * 注释: 确认发货
     * 时间: 2023/7/12 0012 16:12
     * <AUTHOR>
     */
    checkAndPost() {
        if (TextUtils.isEmpty(this.state.deliveryTonnage)) {
            this._showToast('请输入发货吨位');
            return;
        }
        if (parseFloat(`${this.state.deliveryTonnage ?? 0}`) < 0) {
            this._showToast('请输入正确的发货吨位');
            return;
        }
        if (!Method.isOpenGPS()) {
            //打开GPS 设置
            Method.openGPS();
            return;
        }
        PermissionUtil.applyPermission(
            '中储智运需申请您的地址位置权限,以便为您确认实际到达收发货地点服务。拒绝或取消授权不影响使用其他服务',
            (code, msg) => {
                if (code == 200) {
                    this.confirmOrderDeliverOffLine();
                } else {
                    this._showToast(msg);
                }
            },
            PermissionType.LOCATION,
        );
    }

    /**
     * 注释: 提交确认发货
     * 时间: 2023/7/12 0012 17:00
     * <AUTHOR>
     */
    confirmOrderDeliverOffLine() {
        let request = new ReqConfirmOrderDeliverOffLine();
        request.orderId = this.orderId;
        request.detailId = this.detailId;
        request.deliverWeight = `${this.state.deliveryTonnage}`;
        let images = this.refImageSelectView.current?.getImages();
        if (ArrayUtils.isNoEmpty(images)) {
            request.picUrlsJsonArr = images!;
        }
        request.request().then((response) => {
            if (response.isSuccess()) {
                //开启定位服务
                Method.startBillShipmentsServer('1', `${this.detailId}`, true, true);
                //线下专区 发货成功 -》待卸货【菜单】
                EventBus.getInstance().fireEvent(Constant.event_stevedore_page, {
                    key: 'setIndex',
                    value: 2,
                });
                this._showToast('发货成功');
                this._goBack();
            } else {
                this._showToast(response.getMsg());
            }
        });
    }
}

const styles = StyleSheet.create({
    bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
});
