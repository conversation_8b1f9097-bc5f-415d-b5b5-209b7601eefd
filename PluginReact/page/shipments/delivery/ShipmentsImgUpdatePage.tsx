import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../../widget/UITitleView';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import {ShipmentUI} from './models/ShipmentUI';
import {DialogNewBuilder} from '../../base/Dialog';
import {ReqSupplemenbtaryDeliverPage} from './requests/ReqSupplemenbtaryDeliverPage';

interface State extends BaseState {}

/**
 * 注释: 发货单据补传
 * WLHY-18019 🏷 【汽运】预付审核通过运单支持新增发货单据
 */
export default class ShipmentsImgUpdatePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    orderId: string;
    detailId: string;
    pageParams: any;
    shipmentUI: ShipmentUI;

    //单据照片索引
    private refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId ?? '';
        this.detailId = this.pageParams.detailId ?? '';
        this.shipmentUI = new ShipmentUI();
        this.shipmentUI.orderId = this.orderId;
        this.shipmentUI.detailId = this.detailId;
    }

    componentDidMount() {
        super.componentDidMount();
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'发货单据补传'} />
                {/*绘制运单编号*/}
                {this.renderOrderNo()}
                {/*绘制单据照片*/}
                {this.renderOrderImageView()}
                {/* 底部按钮 */}
                <View style={{flex:1}}></View>
                {this.renderBottomView()}
                {/*基础组件初始化*/}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制运单编号
     * @return {JSX.Element}
     */
    renderOrderNo = () => {
        return (
            <View
                style={{
                    paddingLeft: 14,
                    backgroundColor: '#fff',
                    height: 42,
                    marginBottom: 7,
                    justifyContent: 'center',
                }}>
                <Text style={{fontSize: 16}}>{`运单编号：${this.orderId}`}</Text>
            </View>
        );
    };

    /**
     * 注释: 绘制单据照片
     * @returns {JSX.Element}
     */
    renderOrderImageView = () => {
        return (
            <ShipmentsImageSelectView
                ref={this.refImagesWaybillView}
                orderId={this.orderId}
                imgs={[]}
                title={'发货单据照片'}
                toast={`最多上传8张`}
                warning={'请检查发货单是否上传完整、清晰、正确'}
                showReadToastIcon={true}
                waterMarkFlag={'1'}
                takePhoto={false}
                edit={true}
                type={'1'}
                max={8}

            />
        );
    };

    /**
     * 注释: 确认修改按钮
     * @returns {JSX.Element}
     */
    renderBottomView() {
        return (
            <Text style={styles.bottom_view_right} onPress={this.checkAndPost}>
                确认上传
            </Text>
        );
    }

    /**
     * 注释: 更新订单信息
     * @param shipmentUI
     */
    checkAndPost = () => {
        //表单检测
        let ok = true;
        if (ok && this.refImagesWaybillView.current) {
            ok = this.refImagesWaybillView.current.check(this.shipmentUI);
        }
        if (!ok) {
            return;
        }
        var dialog = new DialogNewBuilder();
        dialog.title = '提示';
        dialog.msg = '确定要修改运单信息吗？';
        dialog.rightOnClick = (dialog) => {
            dialog?.dismiss()
            this._showWaitDialog();
            var req = new ReqSupplemenbtaryDeliverPage();
            req.orderId = this.orderId;
            req.dataList = this.refImagesWaybillView.current?.getImages();
            req.request().then((_response) => {
                this._dismissWait();
                this._showToast(_response.getMsg());
                if (_response.isSuccess()) {
                    this._goBack();
                }
            });
        };
        dialog.show();
    };
}

const styles = StyleSheet.create({
    bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
});
