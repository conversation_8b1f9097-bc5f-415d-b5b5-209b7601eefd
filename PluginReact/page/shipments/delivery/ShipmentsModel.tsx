import React from 'react';
import TextUtils from '../../util/TextUtils';
import {gLog} from '../../const.global';
import {ReqAdvanceInfo} from './requests/ReqAdvanceInfo';
import {ReqShipmentsEGoodInfo} from './requests/ReqShipmentsEGoodInfo';
import {ShipmentUI} from './models/ShipmentUI';
import {ReqConfrimOrderDeliver} from './requests/ReqConfrimOrderDeliver';
import {EShipmentsSuccess} from './models/EShipmentsSuccess';
import {BaseResponse} from '../../http/BaseResponse';
import {ReqContainerDeliverFlag} from './requests/ReqContainerDeliverFlag';
import {ReqContainerConfirmDeliver} from './requests/ReqContainerConfirmDeliver';
import BaseCommPage, {DialogBuilder, DialogViewBuilder} from '../../base/BaseCommPage';
import ShipmentsEGoodInfoRsp from './models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp from './models/AdvanceInfoRsp';
import {ReqDoAdvanceApplyByDeliverAndNotReceive} from './requests/ReqDoAdvanceApplyByDeliverAndNotReceive';
import {ReqQueryStartPlaceLocationBetweenOrderPlaceWarningFlag} from './requests/ReqQueryStartPlaceLocationBetweenOrderPlaceWarningFlag';
import {onEvent} from '../../base/native/UITrackingAction';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import FloodSeasonDialog from './view/FloodSeasonDialog';
import {Method} from '../../util/NativeModulesTools';
import {ReqQueryTrnOrderInfoBeforeDeliver} from './requests/ReqQueryTrnOrderInfoBeforeDeliver';
import {ReqTrnConfrimOrderDeliver} from './requests/ReqTrnConfrimOrderDeliver';
import {ReqOrderCoordinate} from '../../pick/requests/ReqOrderCoordinate';
import {ReqQueryOrderInfoForAppSkip} from '../../pick/requests/ReqQueryOrderInfoForAppSkip';

export class ShipmentsModel {
    public mLongitude: string;
    public mLatitude: string;
    public cancelAdvance?: Function;

    /**
     * 请求发货信息
     * @param {*} waybill
     * @param page
     * @param callback
     */
    public openScene(page: BaseCommPage<any, any>, waybill, callback = (data) => {}, querySource = '') {
        page._showWaitDialog();
        //异步定位一次,清除上次定位数据
        Method.removeStringExtra('shipmen_bill_location');

        const orderId = waybill.orderId;
        const detailId = waybill.detailId;

        Method.locationAndonLooper(
            async (code, json) => {
                //接口使用新网络框架重写
                let request = new ReqShipmentsEGoodInfo();
                request.orderId = orderId;
                request.detailId = detailId;
                request.querySource = querySource;
                let shipmentsEGoodInfoRsp = await request.request();

                if (shipmentsEGoodInfoRsp.isSuccess()) {
                    let request = new ReqAdvanceInfo();
                    request.orderId = orderId;
                    request.detailId = detailId;
                    request.queryAdvanceInfoSource = waybill.queryAdvanceInfoSource
                    
                    if (code == 200) {
                        //定位成功
                        let location = JSON.parse(json);
                        this.mLongitude = location.longitude;
                        this.mLatitude = location.latitude;

                        request.longitude = location.longitude;
                        request.latitude = location.latitude;

                        Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                    }

                    let advanceInfoRsp = await request.request();
                    if (advanceInfoRsp.isSuccess()) {
                        let shipmentsEGoodInfo = shipmentsEGoodInfoRsp.data;

                        if (shipmentsEGoodInfo) {
                            shipmentsEGoodInfo.orderId = orderId;
                            shipmentsEGoodInfo.detailId = detailId;
                            shipmentsEGoodInfo.advanceState = waybill.advanceState;
                            //回单打回时之前输入数量 or 当前输入内容(旧值用于对比数据是否发生修改,本地字段)
                            shipmentsEGoodInfo.rootArray = shipmentsEGoodInfo?.rootArray.map((value) => {
                                if (TextUtils.equals('1', shipmentsEGoodInfo?.LTCantUpdate)) {
                                    ///龙腾特钢 使用指定值 龙腾特钢一期需求 总重量
                                    value.beforeDeliverCargoWeight = shipmentsEGoodInfo?.LTTotalMoney ?? '';
                                }
                                value.oldBeforeDeliverCargoWeight = value.beforeDeliverCargoWeight ?? '';
                                return value;
                            });
                        }

                        page._dismissWait();
                        callback &&
                            callback({
                                code: 200,
                                shipmentsEGoodInfo: shipmentsEGoodInfo,
                                advanceInfo: advanceInfoRsp.data,
                            });
                    } else {
                        page._dismissWait();
                        page._showMsgDialog(advanceInfoRsp.getMsg());
                    }
                } else {
                    page._dismissWait();
                    page._showMsgDialog(shipmentsEGoodInfoRsp.getMsg());
                }
            },
            (code, json) => {
                if (code == 200) {
                    let location = JSON.parse(json);
                    this.mLongitude = location.longitude;
                    this.mLatitude = location.latitude;
                    Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                }
            },
        );
    }

    public selectOrderImgObj(isAdvanceButtonOn: boolean, shipmentsEGoodInfo: ShipmentsEGoodInfoRsp, advanceInfo: AdvanceInfoRsp) {
        //4.单据照片
        if (TextUtils.equals('1', shipmentsEGoodInfo.goodsSource) || TextUtils.equals('2', shipmentsEGoodInfo.goodsSource)) {
            //ZCZY-7768 批量货按箱发布（针对广州中物储）(界面优先判断集装箱) or 零担
            return shipmentsEGoodInfo.orderImgObj;
        } else {
            switch (advanceInfo.advanceType) {
                case '2':
                case '4': {
                    gLog('----------------------走平台预付-------------------------');
                    //2.个体司机非指定订单 => 走平台预付
                    // 4.个体司机关联车老板模式 => 走平台预付
                    return isAdvanceButtonOn && TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.orderImgObj : shipmentsEGoodInfo.orderImgObj;
                }

                case '1': {
                    //个体司机指定订单
                    //true  => 走货主预付款 false  => 走普通发货
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.orderImgObj : shipmentsEGoodInfo.orderImgObj;
                }
                case '3': {
                    // 车老板自己摘牌订单 => 车老板预付
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.orderImgObj : shipmentsEGoodInfo.orderImgObj;
                }
                case '5': {
                    //承运商指派订单 => 走普通发货
                    return shipmentsEGoodInfo.orderImgObj;
                }
                default: {
                    return shipmentsEGoodInfo.orderImgObj;
                }
            }
        }
    }

    /***
     * 获取人车货合影配置
     * @param isAdvanceButtonOn
     * @param shipmentsEGoodInfo
     * @param advanceInfo
     */
    public selectPeopleVehicleImgObj(isAdvanceButtonOn: boolean, shipmentsEGoodInfo: ShipmentsEGoodInfoRsp, advanceInfo: AdvanceInfoRsp) {
        //人车货合影
        if (TextUtils.equals('1', shipmentsEGoodInfo.goodsSource) || TextUtils.equals('2', shipmentsEGoodInfo.goodsSource)) {
            //ZCZY-7768 批量货按箱发布（针对广州中物储）(界面优先判断集装箱) or 零担
            return shipmentsEGoodInfo.peopleVehicleImgObj;
        } else {
            switch (advanceInfo.advanceType) {
                case '2':
                case '4': {
                    gLog('----------------------走平台预付-------------------------');
                    //2.个体司机非指定订单 => 走平台预付
                    // 4.个体司机关联车老板模式 => 走平台预付
                    return isAdvanceButtonOn && TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.peopleVehicleImgObj : shipmentsEGoodInfo.peopleVehicleImgObj;
                }

                case '1': {
                    //个体司机指定订单
                    //true  => 走货主预付款 false  => 走普通发货
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.peopleVehicleImgObj : shipmentsEGoodInfo.peopleVehicleImgObj;
                }
                case '3': {
                    // 车老板自己摘牌订单 => 车老板预付
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.peopleVehicleImgObj : shipmentsEGoodInfo.peopleVehicleImgObj;
                }
                case '5': {
                    //承运商指派订单 => 走普通发货
                    return shipmentsEGoodInfo.peopleVehicleImgObj;
                }
                default: {
                    return shipmentsEGoodInfo.peopleVehicleImgObj;
                }
            }
        }
    }

    /***
     * 挂车车牌合影图片
     * @param isAdvanceButtonOn
     * @param shipmentsEGoodInfo
     * @param advanceInfo
     */
    public selectTrailerImageObj(isAdvanceButtonOn: boolean, shipmentsEGoodInfo: ShipmentsEGoodInfoRsp, advanceInfo: AdvanceInfoRsp) {
        //人车货合影
        if (TextUtils.equals('1', shipmentsEGoodInfo.goodsSource) || TextUtils.equals('2', shipmentsEGoodInfo.goodsSource)) {
            //ZCZY-7768 批量货按箱发布（针对广州中物储）(界面优先判断集装箱) or 零担
            return shipmentsEGoodInfo.trailerImageObj;
        } else {
            switch (advanceInfo.advanceType) {
                case '2':
                case '4': {
                    gLog('----------------------走平台预付-------------------------');
                    //2.个体司机非指定订单 => 走平台预付
                    // 4.个体司机关联车老板模式 => 走平台预付
                    return isAdvanceButtonOn && TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.trailerImageObj : shipmentsEGoodInfo.trailerImageObj;
                }

                case '1': {
                    //个体司机指定订单
                    //true  => 走货主预付款 false  => 走普通发货
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.trailerImageObj : shipmentsEGoodInfo.trailerImageObj;
                }
                case '3': {
                    // 车老板自己摘牌订单 => 车老板预付
                    return TextUtils.equals('1', advanceInfo.isAdvance) ? advanceInfo.trailerImageObj : shipmentsEGoodInfo.trailerImageObj;
                }
                case '5': {
                    //承运商指派订单 => 走普通发货
                    return shipmentsEGoodInfo.trailerImageObj;
                }
                default: {
                    return shipmentsEGoodInfo.trailerImageObj;
                }
            }
        }
    }

    /***
     * 确认发货
     */
    public async confrimOrderDeliver(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI) {
        if (TextUtils.equals('1', shipmentUI.floodSafeTipFlag)) {
            //5677	WLHY-6150	【安卓】【加急】【汽运】汛期安全运输提醒
            let dialog = new DialogViewBuilder();
            dialog.views = (
                <FloodSeasonDialog
                    onNext={() => {
                        dialog.onDismiss();
                        this.confrimOrderDeliver1(page, shipmentUI);
                    }}
                />
            );
            page._showViewDialog(dialog);
        } else {
            this.confrimOrderDeliver1(page, shipmentUI);
        }
    }

    /***
     * 确认发货
     */
    private async confrimOrderDeliver1(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI) {
        page._showWaitDialog();

        let req = new ReqQueryStartPlaceLocationBetweenOrderPlaceWarningFlag();
        req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
        req.latitude = this.mLatitude;
        req.longitude = this.mLongitude;

        let resp = await req.request();
        page._dismissWait();

        if (resp.isSuccess()) {
            if (TextUtils.equals('1', resp.data?.distanceWarningFlag)) {
                let dialog = new DialogBuilder();
                dialog.title = '操作规范提示';
                dialog.model = 2;
                dialog.msg = '系统检测到您距离发货地较远，为保证运费顺利结算，请您确保在发货地操作。';
                dialog.cancelTxt = '继续确认';
                dialog.okTxt = '取消';
                dialog.onCancelEvent = () => {
                    this.confrimOrderDeliver2(page, shipmentUI);
                    onEvent({pageId: 'ShipmentActivity', tableId: '#tv_distanceWarning_ok', event: 'click'});
                };
                dialog.onOkEvent = () => {
                    onEvent({pageId: 'ShipmentActivity', tableId: '#tv_distanceWarning_cancel', event: 'click'});
                };
                page._showDialog(dialog);
            } else {
                await this.confrimOrderDeliver2(page, shipmentUI);
            }
        } else {
            page._showMsgDialog(resp.getMsg());
        }
    }

    /***
     * 确认发货
     */
    private async confrimOrderDeliver2(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI) {
        page._showWaitDialog();

        if (TextUtils.equals('1', shipmentUI.shipmentsEGoodInfo.goodsSource)) {
            //1集装箱货源(界面优先判断集装箱)
            //集装箱确认发货之前提示
            let req = new ReqContainerDeliverFlag();
            req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
            req.containerNoJsonArray = shipmentUI.getEContainer();
            let respone = await req.request();

            if (respone.isSuccess()) {
                let req = new ReqContainerConfirmDeliver();
                req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
                req.detailId = shipmentUI.shipmentsEGoodInfo.detailId;
                req.weightDetails = shipmentUI.getCargoIdWeightData();
                //确认收货图片名称连接字符串
                req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
                //确认人车图片名称连接字符串()
                req.picUrls2JsonArrayObj = shipmentUI.imagesCarPerson;
                req.signalType = Method.getNetworkType();
                req.containerNoJsonArray = shipmentUI.getEContainer();
                req.remark = shipmentUI.remark;
                req.deliverVideoArray = shipmentUI.deliverVideoArray;

                let result = await req.request();
                this.onSuccess(page, shipmentUI, result);
            } else {
                page._dismissWait();
                page._showMsgDialog(respone.getMsg());
            }
        } else {
            let req = new ReqConfrimOrderDeliver();
            req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
            req.detailId = shipmentUI.shipmentsEGoodInfo.detailId;
            req.remark = shipmentUI.remark;
            req.deliverVideoArray = shipmentUI.deliverVideoArray;

            //司机是否选择了非指定预付款 1 选择 2 未选择
            if (shipmentUI.isAdvanceButtonOn == null || TextUtils.isEmpty(shipmentUI.isAdvanceButtonOn)) {
                req.isAdvanceButtonOn = '2';
            } else {
                req.isAdvanceButtonOn = shipmentUI.isAdvanceButtonOn;
            }
            //个体司机信用分
            req.creditPoint = shipmentUI.advanceInfo.creditPoint;
            //拼装字符串 货物ID:重量,货物Id:重量
            req.weightDetails = shipmentUI.getCargoIdWeightData();
            //确认收货图片名称连接字符串
            req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
            //确认人车图片名称连接字符串()
            req.picUrls2JsonArrayObj = shipmentUI.imagesCarPerson;
            //优惠券ID
            req.userCouponId = shipmentUI.userCouponIds;
            req.couponMoney = shipmentUI.couponMoney;
            req.couponAmountType = shipmentUI.couponAmountType;

            req.cutAdvanceCouponId = shipmentUI.cutAdvanceCouponId;
            //申请预付标识 null普通发货，1预付申请
            req.applyAdvance = shipmentUI.applyAdvance;
            //预付方式
            req.advanceWay = shipmentUI.advanceWay;
            req.dlOilCardRatio = shipmentUI.oilRatio;
            //网络制式
            req.signalType = Method.getNetworkType();
            req.longitude = this.mLongitude;
            req.latitude = this.mLatitude;
            //毛重 皮重
            req.grossWeightDetails = shipmentUI.getGrossWeightDetails();
            req.tareWeightDetails = shipmentUI.getTareWeightDetails();
            //出厂时间
            req.outStageTime = shipmentUI.leaveFactoryTime;
            //是否购买智运折扣卡;1:是;0否
            req.discountCardBuyFlag = shipmentUI.discountCardBuyFlag;
            //是否使用智运折扣卡;1:是;0否:
            req.discountCardUseFlag = shipmentUI.discountCardUseFlag;
            //智运折卡号
            req.discountCardNum = shipmentUI.discountCardNum;
            //活动id
            req.activityId = shipmentUI.activityId;
            req.faceIdentifyFlag = shipmentUI.faceIdentifyFlag;

            req.peopleVehicleVideoArrayObj = shipmentUI.peopleVehicleVideoArrayObj;
            //货保服务
            req.buyPolicyCouponId = shipmentUI.buyPolicyCouponId;
            req.buyPolicyFlag = shipmentUI.buyPolicyFlag;
            //WLHY-15998 发货 挂车车牌合影照
            req.trailerPicUrlsJsonArr = shipmentUI.imagesTrailer;

            let result = await req.request();

            page._dismissWait();

            this.onSuccess(page, shipmentUI, result);
        }
    }

    /***
     * 发货后预付申请
     */
    public async doAdvanceApplyByDeliverAndNotReceive(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI) {
        page._showWaitDialog();

        let req = new ReqDoAdvanceApplyByDeliverAndNotReceive();
        req.orderId = shipmentUI.shipmentsEGoodInfo.orderId;
        req.detailId = shipmentUI.shipmentsEGoodInfo.detailId;
        //个体司机信用分
        req.creditPoint = shipmentUI.advanceInfo.creditPoint;
        //确认收货图片名称连接字符串
        req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
        //确认人车图片名称连接字符串()
        req.picUrls2JsonArrayObj = shipmentUI.imagesCarPerson;

        //优惠券ID
        req.userCouponId = shipmentUI.userCouponIds;
        req.couponMoney = shipmentUI.couponMoney;
        req.couponAmountType = shipmentUI.couponAmountType;
        req.cutAdvanceCouponId = shipmentUI.cutAdvanceCouponId;
        //预付方式
        req.advanceWay = shipmentUI.advanceWay;
        req.dlOilCardRatio = shipmentUI.oilRatio;
        //网络制式
        req.signalType = Method.getNetworkType();
        req.longitude = this.mLongitude;
        req.latitude = this.mLatitude;

        //出厂时间
        req.outStageTime = shipmentUI.leaveFactoryTime;

        //是否购买智运折扣卡;1:是;0否
        req.discountCardBuyFlag = shipmentUI.discountCardBuyFlag;
        //是否使用智运折扣卡;1:是;0否:
        req.discountCardUseFlag = shipmentUI.discountCardUseFlag;
        //智运折卡号
        req.discountCardNum = shipmentUI.discountCardNum;
        //活动id
        req.activityId = shipmentUI.activityId;
        req.remark = shipmentUI.remark;
        req.deliverVideoArray = shipmentUI.deliverVideoArray;
        req.oilOnPrePolicyId = shipmentUI.oilOnPrePolicyId;
        //WLHY-15998 发货 挂车车牌合影照
        req.trailerPicUrlsJsonArr = shipmentUI.imagesTrailer;

        let result = await req.request();

        page._dismissWait();

        this.onSuccess(page, shipmentUI, result);
    }

    onSuccess(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI, result: BaseResponse<EShipmentsSuccess>) {
        if (result.isSuccess()) {
            let json = {
                payTerrace: TextUtils.equals('1', shipmentUI.isAdvanceButtonOn) ? '1' : '0',
                advanceWay: shipmentUI.advanceWay,
                shipmentsEGoodInfo: shipmentUI.shipmentsEGoodInfo,
                advanceInfo: shipmentUI.advanceInfo,
                haveOpenSdk: shipmentUI.haveOpenSdk ? '1' : '0',
                synUploadLocationFlag: result.data?.synUploadLocationFlag, //是否需要异步上传定位 1:是，其余否
                scene: shipmentUI.scene ?? '',
            };
            if (TextUtils.equals('1', shipmentUI.autoGuidFlag)) {
                this.queryReqOrderCoordinate(page, shipmentUI.orderId);
            } else {
                page._finish();
                Method.onShipmentSuccess(JSON.stringify(json));
            }
        } else {
            if (TextUtils.equals('2021', result.getCode())) {
                let dialog = new DialogBuilder();
                dialog.title = '确认发货失败';
                dialog.model = 2;
                dialog.msg = result.getMsg();
                dialog.cancelTxt = '联系客服';
                dialog.onCancelEvent = () => {
                    Method.openLineServer();
                };
                page._showDialog(dialog);
            } else if (TextUtils.equals('31032', result.getCode())) {
                let dialog = new DialogBuilder();
                dialog.title = '提示';
                dialog.model = 2;
                dialog.msg = result.getMsg();
                dialog.cancelTxt = '联系客服';
                dialog.onCancelEvent = () => {
                    Method.openLineServer();
                };
                page._showDialog(dialog);
            } else if (TextUtils.equals('6000', result.getCode())) {
                let dialog = new DialogBuilder();
                dialog.title = '提示';
                dialog.model = 1;
                dialog.msg = '该货物已不支持购买货物保障服务';
                dialog.okTxt = '知道了';
                dialog.canBack = false;
                dialog.onOkEvent = () => {
                    let json = {
                        payTerrace: TextUtils.equals('1', shipmentUI.isAdvanceButtonOn) ? '1' : '0',
                        advanceWay: shipmentUI.advanceWay,
                        shipmentsEGoodInfo: shipmentUI.shipmentsEGoodInfo,
                        advanceInfo: shipmentUI.advanceInfo,
                        haveOpenSdk: shipmentUI.haveOpenSdk ? '1' : '0',
                        synUploadLocationFlag: result.data?.synUploadLocationFlag, //是否需要异步上传定位 1:是，其余否
                    };
                    page._finish();
                    if (TextUtils.equals('1', shipmentUI.autoGuidFlag)) {
                        this.queryReqOrderCoordinate(page, shipmentUI.orderId);
                    } else {
                        Method.onShipmentSuccess(JSON.stringify(json));
                    }
                };
                page._showDialog(dialog);
            } else if (TextUtils.equals('21161', result.getCode())) {
                let dialog = new DialogBuilder();
                dialog.title = '提示';
                dialog.model = 2;
                dialog.msg = '您的账户存在欠款，还款后才能继续使用预付服务。';
                dialog.okTxt = '对账还款';
                dialog.onOkEvent = () => {
                    //对账支付
                    RouterUtils.skipRouter(RouterUrl.WisdomReconciliationRouter);
                };
                page._showDialog(dialog);
            } else if (TextUtils.equals('1', result.data?.advanceErrorFlag)) {
                let dialog = new DialogBuilder();
                dialog.title = '提示';
                dialog.model = 2;
                dialog.msg = result.getMsg();
                dialog.okTxt = '不使用预付';
                dialog.onOkEvent = () => {
                    this.cancelAdvance && this.cancelAdvance();
                };
                page._showDialog(dialog);
            } else {
                page._showMsgDialog(result.getMsg());
            }

            //错误信息上报
            if (TextUtils.isNoEmpty(result.data?.reqNo ?? '')) {
                let error = {
                    reqNo: result.data?.reqNo,
                    detailId: shipmentUI.shipmentsEGoodInfo.detailId,
                    type: '1', //发货
                };
                Method.onBillShipmentsServer(JSON.stringify(error));
            }
        }
    }

    /**
     * Trn请求发货信息
     * @param {*} waybill
     * @param page
     * @param callback
     */
    public openTrnScene(page: BaseCommPage<any, any>, waybill, callback = (data) => {}, querySource = '') {
        page._showWaitDialog();
        //异步定位一次,清除上次定位数据
        Method.removeStringExtra('shipmen_bill_location');

        const sourceId = waybill.sourceId;

        Method.locationAndonLooper(
            async (code, json) => {
                if (code == 200) {
                    //定位成功
                    let location = JSON.parse(json);
                    this.mLongitude = location.longitude;
                    this.mLatitude = location.latitude;
                    Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                }

                //接口使用新网络框架重写
                let request = new ReqQueryTrnOrderInfoBeforeDeliver();
                request.sourceId = sourceId;
                let shipmentsEGoodInfoRsp = await request.request();

                if (shipmentsEGoodInfoRsp.isSuccess()) {
                    let shipmentsEGoodInfo = shipmentsEGoodInfoRsp.data;

                    if (shipmentsEGoodInfo) {
                        shipmentsEGoodInfo.orderId = waybill.orderId;
                        shipmentsEGoodInfo.advanceState = waybill.advanceState;
                        //回单打回时之前输入数量 or 当前输入内容(旧值用于对比数据是否发生修改,本地字段)
                        shipmentsEGoodInfo.rootArray = shipmentsEGoodInfo?.rootArray.map((value) => {
                            value.oldBeforeDeliverCargoWeight = value.beforeDeliverCargoWeight ?? '';
                            return value;
                        });
                    }

                    page._dismissWait();
                    callback &&
                        callback({
                            code: 200,
                            shipmentsEGoodInfo: shipmentsEGoodInfo,
                        });
                } else {
                    page._dismissWait();
                    page._showMsgDialog(shipmentsEGoodInfoRsp.getMsg());
                }
            },
            (code, json) => {
                if (code == 200) {
                    let location = JSON.parse(json);
                    this.mLongitude = location.longitude;
                    this.mLatitude = location.latitude;
                    Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                }
            },
        );
    }

    /***
     * Trn确认发货
     */
    public async doTrnConfirmDeliver(page: BaseCommPage<any, any>, shipmentUI: ShipmentUI) {
        page._showWaitDialog();

        let req = new ReqTrnConfrimOrderDeliver();
        req.orderId = shipmentUI.orderId;
        req.sourceId = shipmentUI.sourceId;
        //拼装字符串 货物ID:重量,货物Id:重量
        req.weightDetails = shipmentUI.getCargoIdWeightData();
        //确认收货图片名称连接字符串
        req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
        req.longitude = this.mLongitude;
        req.latitude = this.mLatitude;
        let result = await req.request();

        page._dismissWait();

        if (result.isSuccess()) {
            Method.onEventShipmentsBillSuccess(0);

            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ShipmentsTrnSuccessPage',
                data: {
                    orderId: shipmentUI.orderId,
                    sourceId: shipmentUI.sourceId,
                },
                action: RouterUtils.ACTION_REPLACE,
            });
        } else if (TextUtils.equals('2021', result.getCode())) {
            let dialog = new DialogBuilder();
            dialog.title = '确认发货失败';
            dialog.model = 2;
            dialog.msg = result.getMsg();
            dialog.cancelTxt = '联系客服';
            dialog.onCancelEvent = () => {
                Method.openLineServer();
            };
            page._showDialog(dialog);
        } else if (TextUtils.equals('21161', result.getCode())) {
            let dialog = new DialogBuilder();
            dialog.title = '提示';
            dialog.model = 2;
            dialog.msg = '您的账户存在欠款，还款后才能继续使用预付服务。';
            dialog.okTxt = '对账还款';
            dialog.onOkEvent = () => {
                //对账支付
                RouterUtils.skipRouter(RouterUrl.WisdomReconciliationRouter);
            };
            page._showDialog(dialog);
        } else {
            page._showMsgDialog(result.getMsg());
        }
    }

    /**
     * 注释: 查询目的地
     */
    queryReqOrderCoordinate(page: BaseCommPage<any, any>, orderId?: string) {
        let request = new ReqOrderCoordinate();
        request.orderId = orderId ?? '';
        page._showWaitDialog();
        request.request().then((res) => {
            if (res.isSuccess()) {
                let request2 = new ReqQueryOrderInfoForAppSkip();
                request2.orderId = orderId ?? '';
                request2.request().then((rsq) => {
                    page._dismissWait();
                    if (rsq.isSuccess()) {
                        page._finish();
                        Method.openMapNaviPage(res.data?.deliverCoordinateY, res.data?.deliverCoordinateX, res.data?.deliverProCityDisPlace, '2', JSON.stringify(rsq.data));
                    } else {
                        page._showMsgDialog(rsq.getMsg());
                    }
                });
            } else {
                page._dismissWait();
                page._showMsgDialog(res.getMsg());
            }
        });
    }
}
