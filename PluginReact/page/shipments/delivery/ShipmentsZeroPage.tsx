import React from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import UITitleView from '../../widget/UITitleView';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import TextUtils from '../../util/TextUtils';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import {gStyle} from '../../util/comm-style';
import {gScreen_width} from '../../util/scaled-style';
import ShipmentsEGoodInfoRsp from './models/ShipmentsEGoodInfoRsp';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import ShipmentsGoodsView, {ShipmentsGoodsViewRef} from './view/ShipmentsGoodsView';
import {ShipmentUI} from './models/ShipmentUI';
import {PermissionType, PermissionUtil} from '../../util/PermissionUtil';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import AntiDraudDialog from '../../pick/views/AntiDraudDialog';
import LanguageType from '../../util/language/LanguageType';
import AgreementView, {AgreementViewRef, EAgreementType} from '../../pick/views/AgreementView';
import {ShipmentsZeroDto} from './models/ShipmentsZeroDto';
import {Method} from '../../util/NativeModulesTools';
import ShipmentVideoRemarkView, {ShipmentVideoRemarkViewRef} from './view/ShipmentVideoRemarkView';

interface State extends BaseState {
    init: boolean;
}

/**
 * 注释: 确认发货页(单个零担)
 * <AUTHOR>
 */
export default class ShipmentsZeroPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    //原生端传过来的值
    waybill: any;

    private shipmentsZeroDto = new ShipmentsZeroDto(this);
    //摘单信息
    public shipmentsEGoodInfo = new ShipmentsEGoodInfoRsp();

    /*** 货物明细（EGoodInfo,包含输入发货吨位）控件对象*/
    private refGoodInfoView = React.createRef<ShipmentsGoodsViewRef>();
    /***  运单图片 控件对象*/
    private refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();
    /***  人车货照片 控件对象*/
    private refIImagesCarPersonView = React.createRef<ShipmentsImageSelectViewRef>();
    //备注视图索引
    private refShipmentsRemarksView = React.createRef<ShipmentVideoRemarkViewRef>();
    //协议Ref
    private refAgreementView = React.createRef<AgreementViewRef>();

    //人脸识别标记
    private faceIdentifyFlag?: string;

    constructor(props) {
        super(props);
        this.waybill = this.getParams();
        this.faceIdentifyFlag = '';
        this.state = {
            init: false,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        //请求货物信息和预付信息
        this.shipmentsZeroDto.openScene(this.waybill, (data) => {
            this.shipmentsEGoodInfo = data ?? new ShipmentsEGoodInfoRsp();
            this.setState({init: true});
        });
    }

    postShipments = () => {
        if (!Method.isOpenGPS()) {
            //打开GPS 设置
            Method.openGPS();
            return;
        }
        PermissionUtil.applyPermission(
            '中储智运需申请您的地址位置权限,以便为您确认实际到达收发货地点服务。拒绝或取消授权不影响使用其他服务',
            (code, msg) => {
                if (code == 200) {
                    //发货检查参数
                    this.checkAndPost();
                } else {
                    this._showMsgDialog(msg);
                }
            },
            PermissionType.LOCATION,
        );
    };

    checkAndPost = async () => {
        if (TextUtils.equals('1', this.shipmentsEGoodInfo.faceIdentifyFlag) && Method.getHostVersion() >= 1900) {
            //大于等于804版本，需要进行人脸识别
            if (TextUtils.isEmpty(this.faceIdentifyFlag)) {
                //当前没有进行人脸识别
                let response = await RouterUtils.skipRouter(RouterUrl.FaceRecognitionPage);
                if (response != null) {
                    this.faceIdentifyFlag = response.code;
                }
            }
        }
        //确认发货
        let shipmentUI = new ShipmentUI();
        shipmentUI.orderId = this.shipmentsEGoodInfo.orderId;
        shipmentUI.detailId = this.shipmentsEGoodInfo.detailId;
        shipmentUI.shipmentsEGoodInfo = this.shipmentsEGoodInfo;
        shipmentUI.faceIdentifyFlag = this.faceIdentifyFlag;
        //SDK是否可打开
        shipmentUI.haveOpenSdk = TextUtils.equals('1', this.shipmentsEGoodInfo.haveOpenSdk);

        let ok = true;

        if (ok && this.refGoodInfoView.current) {
            //货物明细
            ok = this.refGoodInfoView.current.check(shipmentUI);
        }

        if (ok && this.refImagesWaybillView.current) {
            //运单图片
            ok = this.refImagesWaybillView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesWaybill = this.refImagesWaybillView.current.getImages();
            }
        }
        if (ok && this.refIImagesCarPersonView.current) {
            //人车货图片
            ok = this.refIImagesCarPersonView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesCarPerson = this.refIImagesCarPersonView.current.getImages();
            }
        }
        if (ok && this.refShipmentsRemarksView.current) {
            ok = this.refShipmentsRemarksView.current.check(shipmentUI);
        }
        if (!ok) {
            return;
        }
        //协议校验
        if (!this.refAgreementView.current?.check()) {
            return;
        }
        await this.shipmentsZeroDto.confrimOrderDeliver(shipmentUI);
    };

    render() {
        //头部
        return (
            <View style={{flex: 1}}>
                <UITitleView title={LanguageType.getTxt('确认发货')} />
                <ScrollView style={{flex: 1}}>
                    {this.state.init && this.renderTopView()}
                    {/*1. 货物明细*/}
                    {this.state.init && this.renderGoodInfo()}
                    {/*3. 装卸货要求是否展示*/}
                    {this.state.init && this.renderConsignorPrompt()}
                    {/*4.单据照片*/}
                    {this.state.init && this.renderOrderImg()}
                    {/*5.人车货合影*/}
                    {this.state.init && this.renderPeopleVehicleImg()}
                    {/*备注视图*/}
                    {this.state.init && this.renderRemarksView()}
                    {/*6.必需显示内容 协议*/}
                    {this.state.init && <AgreementView ref={this.refAgreementView} style={{marginHorizontal: 17, marginVertical: 8, marginBottom: 10}} keys={[EAgreementType.SHIP, EAgreementType.LOCAL]} />}
                    {/*7.防诈骗对话框*/}
                    {this.state.init && <AntiDraudDialog orderId={this.shipmentsEGoodInfo.orderId} selectNode={'5'} />}
                </ScrollView>
                {/* 底部按钮 */}
                {this.renderBottomView()}
                {/*基础组件初始化*/}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制发货备注视图
     * 时间: 2023/7/31 0031 19:06
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderRemarksView = () => {
        return <ShipmentVideoRemarkView ref={this.refShipmentsRemarksView} max={3} edit={true} deliverVideoArray={this.shipmentsEGoodInfo.deliverVideoJsonArr} remark={this.shipmentsEGoodInfo.carrierDeliverRemark} />;
    };

    renderTopView = () => {
        return (
            <View>
                {TextUtils.equals('1', this.shipmentsEGoodInfo.orderPolicyFlag) && <Text style={styles._top_policy}>{`温馨提示：此单已购买保障服务，预计扣除${this.shipmentsEGoodInfo.orderPolicyMoney}元货物保障服务费`}</Text>}
                <Text key={'Top_view_3'} style={styles._top_orderId}>
                    运单编号: {this.shipmentsEGoodInfo.orderId}
                </Text>
            </View>
        );
    };

    renderGoodInfo = () => {
        //1.货物明细
        return <ShipmentsGoodsView ref={this.refGoodInfoView} key={'ShipmentsGoodsView_1'} shipmentsEGoodInfo={this.shipmentsEGoodInfo} sceneAdvance={0} />;
    };

    renderConsignorPrompt = () => {
        //3. 装卸货要求是否展示	 1:展示0：不展示
        return (
            TextUtils.equals('1', this.shipmentsEGoodInfo.promptFlag) && (
                <View style={[gStyle.view_padding, {marginTop: 2}]} key={'promptFlag'}>
                    <Text style={gStyle.txt_333333_34}>装卸货要求</Text>
                    {/* 货主发单时的装卸货要求 */}
                    {TextUtils.isNoEmpty(this.shipmentsEGoodInfo.consignorPrompt) && <Text style={gStyle.txt_555555_24}>{this.shipmentsEGoodInfo.consignorPrompt}</Text>}
                    {/* 平台货物的装卸货要求 */}
                    {TextUtils.isNoEmpty(this.shipmentsEGoodInfo.platFormCargoPrompt) && <Text style={[gStyle.input_bg, gStyle.txt_666666_24]}>{this.shipmentsEGoodInfo.platFormCargoPrompt}</Text>}
                </View>
            )
        );
    };

    renderOrderImg = () => {
        //4.单据照片
        let orderImgObj = this.shipmentsEGoodInfo.orderImgObj;

        return (
            orderImgObj &&
            TextUtils.equals('1', orderImgObj.showUploadFlag) && (
                <ShipmentsImageSelectView
                    ref={this.refImagesWaybillView}
                    key={'imagesWaybill'}
                    style={{marginTop: 5}}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.imageJsonObjArr}
                    title={orderImgObj.title ?? ''}
                    toast={`最多上传${orderImgObj.limitCount}张`}
                    warning={orderImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', orderImgObj.uploadFlag)}
                    waterMarkFlag={orderImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', orderImgObj.takePhotoFlag)}
                    edit={true}
                    type={'1'}
                    max={orderImgObj.limitCount ?? 8}
                />
            )
        );
    };

    renderPeopleVehicleImg = () => {
        //人车货合影
        let peopleVehicleImgObj = this.shipmentsEGoodInfo.peopleVehicleImgObj;
        return (
            peopleVehicleImgObj &&
            TextUtils.equals('1', peopleVehicleImgObj.showUploadFlag) && (
                <ShipmentsImageSelectView
                    ref={this.refIImagesCarPersonView}
                    key={'imagesCarPerson'}
                    orderId={this.shipmentsEGoodInfo.orderId}
                    imgs={this.shipmentsEGoodInfo.imageJsonObjArr2}
                    title={peopleVehicleImgObj.title ?? ''}
                    toast={`最多上传${peopleVehicleImgObj.limitCount}张`}
                    warning={peopleVehicleImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', peopleVehicleImgObj.uploadFlag)}
                    waterMarkFlag={peopleVehicleImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', peopleVehicleImgObj.takePhotoFlag)}
                    edit={true}
                    type={'2'}
                    max={peopleVehicleImgObj.limitCount ?? 8}
                />
            )
        );
    };

    renderBottomView = () => {
        return (
            <Text key={'Bottom_view_1'} onPress={this.postShipments} style={styles._bottom_view_right}>
                {TextUtils.equals('2', this.shipmentsEGoodInfo.advanceState) ? '重新确认发货' : LanguageType.getTxt('确认发货')}
            </Text>
        );
    };
}

const styles = StyleSheet.create({
    //底部样式
    _bottom_view: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 50,
    },

    _bottom_view_left: {
        flex: 1,
        paddingLeft: 10,
        backgroundColor: '#fff',
        flexDirection: 'row',
        alignItems: 'center',
        height: 50,
    },

    _bottom_view_left_txt: {
        fontSize: 17,
        color: '#FB6B40',
    },
    _bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
    _top_advanceReason_txt: {
        width: gScreen_width,
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
        paddingRight: 15,
        backgroundColor: '#fff',
        color: '#fef6d9',
    },
    _top_advanceReason_img: {
        width: 15,
        height: 15,
    },
    _top_policy: {
        width: gScreen_width,
        paddingLeft: 14,
        paddingTop: 7,
        paddingBottom: 7,
        paddingRight: 14,
        backgroundColor: '#fef6d9',
        fontSize: 14,
    },

    _top_orderId: {
        backgroundColor: '#fff',
        fontSize: 14,
        color: '#666666',
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
    },
});
