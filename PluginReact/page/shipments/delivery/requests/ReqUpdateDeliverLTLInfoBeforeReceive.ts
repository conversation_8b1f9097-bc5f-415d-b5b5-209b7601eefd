import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
import {EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 注释: 更新(零担)
 */
export class ReqUpdateDeliverLTLInfoBeforeReceive extends BaseRequest {
    //订单id
    orderId: string;

    //运输详情ID
    detailId: string;

    // 拼装字符串 货物ID:重量,货物Id:重量
    weightDetails: string;

    //网络制式
    signalType: string;

    //确认人车图片名称连接字符串
    carPeoplePicArray: EWater[];

    //单据照片
    orderPicArray: EWater[];
    //备注
    public remark?: string;
    //WLHY-12652 🏷 【汽运】承运方支持上传视频
    public deliverVideoArray?: PeopleVehicleVideoArr[];
    
    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            weightDetails: this.weightDetails,
            signalType: this.signalType,
            carPeoplePicArray: this.carPeoplePicArray,
            orderPicArray: this.orderPicArray,
            remark: this.remark,
            deliverVideoArray: this.deliverVideoArray,
        };
        return super.post('oms-app/order/deliver/updateDeliverLTLInfoBeforeReceive', ResultData);
    }
}
