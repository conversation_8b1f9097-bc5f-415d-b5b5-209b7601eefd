import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {OrderCoordinate} from '../models/OrderCoordinate';

/***
 * 推荐路线
 */
export class ReqOrderCoordinate extends BaseRequest {
    public orderId: string;

    constructor(orderId: string) {
        super();
        this.orderId = orderId;
    }

    public async request(): Promise<BaseResponse<OrderCoordinate>> {
        this.params = {
            orderId: this.orderId,
        };
        return super.post('oms-app/order/common/queryOrderCoordinate', OrderCoordinate);
    }
}
