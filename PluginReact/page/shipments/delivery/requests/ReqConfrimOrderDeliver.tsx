import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from '../models/EShipmentsSuccess';
import {EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 确认发货[普通货]
 */
export class ReqConfrimOrderDeliver extends BaseRequest {
    /**
     * 订单id
     */
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;

    /**
     * 拼装字符串 货物ID:重量,货物Id:重量
     */
    public weightDetails?: string;

    /***
     * 司机是否选择了非指定预付款 1 选择 2 未选择
     */
    public isAdvanceButtonOn?: string;

    /***
     * 使用优惠券
     */
    public userCouponId?: string;

    /***个体司机信用分*/
    public creditPoint?: string;

    /***    预付业务立减金额 如果用户使用了预付业务则传，金额的传金额，折扣的传折扣数*/
    public couponMoney?: string;

    /***预付业务立减 1:固定额度;2:固定金额折扣;3:随机减*/
    public couponAmountType?: string;
    //预付业务立减Id
    public cutAdvanceCouponId?: string;

    /***申请预付标识 null普通发货，1预付申请*/
    public applyAdvance?: string;

    /***预付方式*/
    public advanceWay?: string;
    /**油气比例*/
    public dlOilCardRatio?: string;
    /*网络制式*/
    public signalType?: string;

    /*** 经度*/
    public longitude?: string;

    /*** 纬度*/
    public latitude?: string;

    /***毛重 拼装字符串 货物ID:毛重,货物Id:毛重*/
    public grossWeightDetails?: string;
    /*** 皮重 拼装字符串 货物ID:皮重,货物Id:皮重*/
    public tareWeightDetails?: string;

    //确认收货图片名称连接字符串 2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段
    public picUrlsJsonArrayObj?: EWater[];
    //确认人车图片名称连接字符串
    public picUrls2JsonArrayObj?: EWater[];

    //	出厂时间	public	否	ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public outStageTime?: string;

    //是否购买智运折扣卡;1:是;0否
    public discountCardBuyFlag?: string;
    //是否使用智运折扣卡;1:是;0否:
    public discountCardUseFlag?: string;
    //智运折卡号
    public discountCardNum?: string;
    //活动id
    public activityId?: string;

    /**
     * 人脸识别标记
     * @type {string}
     */
    public faceIdentifyFlag?: string;
    //FBIS-975 人车货视频拍摄功能
    public peopleVehicleVideoArrayObj?: PeopleVehicleVideoArr[];
    //是否否买保险 1： 购买
    public buyPolicyFlag?: string;
    //货保活动id
    public buyPolicyCouponId?: string;
    //备注
    public remark?: string;
    //WLHY-12652 🏷 【汽运】承运方支持上传视频
    public deliverVideoArray?: PeopleVehicleVideoArr[];
    //WLHY-15998 发货 挂车车牌合影照
    public trailerPicUrlsJsonArr?: EWater[];

    async request(): Promise<BaseResponse<EShipmentsSuccess>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            weightDetails: this.weightDetails,
            isAdvanceButtonOn: this.isAdvanceButtonOn,
            userCouponId: this.userCouponId,
            creditPoint: this.creditPoint,
            couponMoney: this.couponMoney,
            couponAmountType: this.couponAmountType,
            cutAdvanceCouponId: this.cutAdvanceCouponId,
            applyAdvance: this.applyAdvance,
            advanceWay: this.advanceWay,
            dlOilCardRatio: this.dlOilCardRatio,
            signalType: this.signalType,
            longitude: this.longitude,
            latitude: this.latitude,
            grossWeightDetails: this.grossWeightDetails,
            tareWeightDetails: this.tareWeightDetails,
            picUrlsJsonArrayObj: this.picUrlsJsonArrayObj,
            picUrls2JsonArrayObj: this.picUrls2JsonArrayObj,
            outStageTime: this.outStageTime,
            discountCardBuyFlag: this.discountCardBuyFlag,
            discountCardUseFlag: this.discountCardUseFlag,
            discountCardNum: this.discountCardNum,
            activityId: this.activityId,
            remark: this.remark,
            faceIdentifyFlag: this.faceIdentifyFlag,
            peopleVehicleVideoArrayObj: this.peopleVehicleVideoArrayObj,
            buyPolicyFlag: this.buyPolicyFlag,
            buyPolicyCouponId: this.buyPolicyCouponId,
            deliverVideoArray:this.deliverVideoArray,
            trailerPicUrlsJsonArr:this.trailerPicUrlsJsonArr
        };
        return super.post('oms-app/order/deliver/doConfirmDeliver', EShipmentsSuccess);
    }
}
