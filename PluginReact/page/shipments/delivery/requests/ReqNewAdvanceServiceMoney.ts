import {BaseRequest} from '../../../http/BaseRequest';
import {AdvanceServiceMoney} from '../models/AdvanceServiceMoney';
import {BaseResponse} from '../../../http/BaseResponse';

/***
 * 预付服务费提示
 */
export class ReqNewAdvanceServiceMoney extends BaseRequest {
    public orderId: string;
    public detailId: string;
    public advanceWay?: string;
    public dlOilCardRatio?: string;

    public async request(): Promise<BaseResponse<AdvanceServiceMoney>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            advanceWay: this.advanceWay,
            dlOilCardRatio: this.dlOilCardRatio,
        };
        return super.post('oms-app/order/deliver/queryNewAdvanceServiceMoney', AdvanceServiceMoney);
    }
}
