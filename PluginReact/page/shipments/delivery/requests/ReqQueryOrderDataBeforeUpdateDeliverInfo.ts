import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';

/**
 * 注释:  确认发货后修改发货信息时查询运单信息
 * 时间: 2023/7/10 0010 18:04
 * <AUTHOR>
 */
export class ReqQueryOrderDataBeforeUpdateDeliverInfo extends BaseRequest {
    /**
     * 订单id
     */
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;

    async request(): Promise<BaseResponse<ShipmentsEGoodInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
        };
        return super.post('oms-app/order/deliver/queryOrderDataBeforeUpdateDeliverInfo', ShipmentsEGoodInfoRsp);
    }
}
