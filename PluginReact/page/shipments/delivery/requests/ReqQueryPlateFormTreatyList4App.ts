import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import Integrity from '../models/Integrity';
import {UserType} from '../../../user/models/UserType';

/***
 * 发货协议请求
 */
export class ReqQueryPlateFormTreatyList4App extends BaseRequest {
    private userTypeAlias: string;

    public async request(): Promise<BaseResponse<Integrity>> {
        if (UserType.isCarrier()) {
            this.userTypeAlias = '700007';
        } else if (UserType.isBoss()) {
            this.userTypeAlias = '700008';
        } else if (UserType.isCys()) {
            this.userTypeAlias = '700009';
        }
        this.params = {
            platform: '700001', //平台	Integer	是	中储智运：700001；安徽：700002；天津：700003
            businessFormat: '700004', //业务类型	Integer	是 汽运：700004；水运：700005；多式联运：700006
            userTypeAlias: this.userTypeAlias, //个体司机：700007；车老板：700008；物流企业：700009；货主：700010；加盟运力：700011；加盟商：700012；单位船舶：700013
        };
        return super.post('mms-app/plateFormTreaty/queryPlateFormTreatyList4App', Integrity);
    }
}
