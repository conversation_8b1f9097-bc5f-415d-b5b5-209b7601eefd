import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from '../models/EShipmentsSuccess';
import {EContainer, EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 确认发货[集装箱发货]
 */
export class ReqContainerConfirmDeliver extends BaseRequest {
    /**
     * 订单id
     */
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;

    /**
     * 拼装字符串 货物ID:重量,货物Id:重量
     */
    public weightDetails: string;

    /*网络制式*/
    public signalType: string;

    //确认收货图片名称连接字符串 2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段
    public picUrlsJsonArrayObj?: EWater[];
    //确认人车图片名称连接字符串
    public picUrls2JsonArrayObj?: EWater[];

    //ZCZY-7768 批量货按箱发布（针对广州中物储）
    public containerNoJsonArray: EContainer[];
    //备注
    public remark?: string;
    //WLHY-12652 🏷 【汽运】承运方支持上传视频
    public deliverVideoArray?: PeopleVehicleVideoArr[];

    async request(): Promise<BaseResponse<EShipmentsSuccess>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            weightDetails: this.weightDetails,
            signalType: this.signalType,
            picUrlsJsonArrayObj: this.picUrlsJsonArrayObj,
            picUrls2JsonArrayObj: this.picUrls2JsonArrayObj,
            containerNoJsonArray: this.containerNoJsonArray,
            remark:this.remark,
            deliverVideoArray:this.deliverVideoArray,
        };
        return super.post('oms-app/order/deliver/doContainerConfirmDeliver', EShipmentsSuccess);
    }
}
