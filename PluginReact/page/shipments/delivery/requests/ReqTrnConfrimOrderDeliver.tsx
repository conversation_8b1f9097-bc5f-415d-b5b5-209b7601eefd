import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from '../models/EShipmentsSuccess';
import {EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 确认发货（src运单）
 * wiki:http://wiki.zczy56.com/pages/viewpage.action?pageId=78545559
 * 对接人：陈鹏宇
 */
export class ReqTrnConfrimOrderDeliver extends BaseRequest {
  
    /*** 经度*/
    public longitude?: string;

    /*** 纬度*/
    public latitude?: string;

    /**
     * 订单id
     */
    public orderId: string;

    public sourceId: string;
    //货主Id
    public consignorUserId: string;
    /**
     * 拼装字符串 货物ID:重量,货物Id:重量
     */
    public weightDetails?: string;
    //确认收货图片名称连接字符串 2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段
    public picUrlsJsonArrayObj?: EWater[];


    async request(): Promise<BaseResponse<EShipmentsSuccess>> {
        this.params = {
            orderId: this.orderId,
            sourceId: this.sourceId,
            weightDetails: this.weightDetails,
            longitude: this.longitude,
            latitude: this.latitude,
            picUrlsJsonArrayObj: this.picUrlsJsonArrayObj,
        };
        return super.post('oms-app/order/deliverOfTms/doTrnConfirmDeliver', EShipmentsSuccess);
    }
}
