import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
import {EWater} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 注释: 线下确认发货提交接口
 * 时间: 2023/7/12 0012 16:57
 * <AUTHOR>
 */
export class ReqConfirmOrderDeliverOffLine extends BaseRequest {
    public orderId: string;
    public detailId: string;
    public deliverWeight: string;
    public picUrlsJsonArr: EWater[];

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            deliverWeight: this.deliverWeight,
            picUrlsJsonArr: this.picUrlsJsonArr,
        };
        return super.post('oms-app/order/tenderDeliver/doConfirmDeliver', ResultData);
    }
}
