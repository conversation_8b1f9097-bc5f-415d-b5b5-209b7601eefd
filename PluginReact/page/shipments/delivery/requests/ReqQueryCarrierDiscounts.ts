import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import PolicyActivityCouponMoney from '../models/PolicyActivityCouponMoney';

/**
 * 注释: 功能描述:ZCZY-12503 生态业务挽回
 * 时间: 2023/6/26 0026 11:44
 * <AUTHOR>
 */
export class ReqQueryCarrierDiscounts extends BaseRequest {
    public orderId: string;
    public advanceServiceMoney: string;
    public advanceType: string;

    public async request(): Promise<BaseResponse<PolicyActivityCouponMoney>> {
        this.params = {
            orderId: this.orderId,
            advanceServiceMoney: this.advanceServiceMoney,
            advanceType: this.advanceType,
        };
        return super.post('oms-app/order/deliver/queryCarrierDiscounts', PolicyActivityCouponMoney);
    }
}
