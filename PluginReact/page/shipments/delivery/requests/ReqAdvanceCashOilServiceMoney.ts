import {BaseRequest} from '../../../http/BaseRequest';
import {AdvanceServiceMoney} from '../models/AdvanceServiceMoney';
import {BaseResponse} from '../../../http/BaseResponse';

/**
 * 预付服务费提示--50804新加
 */
export class ReqAdvanceCashOilServiceMoney extends BaseRequest {
    public orderId: string;
    public dlOilCardRatio: string;
    public advanceWay: string;

    public async request(): Promise<BaseResponse<AdvanceServiceMoney>> {
        this.params = {
            orderId: this.orderId,
            dlOilCardRatio: this.dlOilCardRatio,
            advanceWay: this.advanceWay,
        };
        return super.post('oms-app/order/deliver/queryAdvanceCashOilServiceMoney', AdvanceServiceMoney);
    }
}
