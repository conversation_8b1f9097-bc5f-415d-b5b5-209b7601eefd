import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';

/**
 * 注释:  确认发货后修改发货信息时查询运单信息
 */
export class ReqQueryOrderDataBeforeUpdateDeliverInfoTMS extends BaseRequest {
    /**
     * 订单id
     */
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;
    /**
     * TMS
     */
    public sourceId: string;
    async request(): Promise<BaseResponse<ShipmentsEGoodInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            sourceId:this.sourceId
        };
        return super.post('oms-app/order/deliverOfTms/queryOrderDataBeforeUpdateDeliverInfo', ShipmentsEGoodInfoRsp);
    }
}
