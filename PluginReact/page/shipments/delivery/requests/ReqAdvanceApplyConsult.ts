import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
/**
 * 注释：FBIS-9865预付轨迹校验提示优化并增加回访
 * 时间：2024/12/11 13:54
 * 作者：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=76153841
 * 对接：周豪
 * 成功0000 已存在记录 22008
 * */
export class ReqAdvanceApplyConsult extends BaseRequest {
    orderId?: string; //运单号
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
        };
        return super.post('oms-app/order/deliver/advanceApplyConsult', ResultData);
    }
}
