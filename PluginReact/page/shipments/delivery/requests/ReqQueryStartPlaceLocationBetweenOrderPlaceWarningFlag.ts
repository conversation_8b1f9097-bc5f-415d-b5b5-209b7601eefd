import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';

/** 功能描述: 发货前判断定位是否超过50km并给予提示信息
 http://wiki.zczy56.com/pages/viewpage.action?pageId=65179922
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 */
export class ReqQueryStartPlaceLocationBetweenOrderPlaceWarningFlag extends BaseRequest {
    public orderId: string;
    public longitude?: String;
    public latitude?: String;
    public async request(): Promise<BaseResponse<RespLocationBetweenOrderPlaceWarningFlag>> {
        this.params = {
            orderId: this.orderId,
            longitude: this.longitude,
            latitude: this.latitude,
        };
        return super.post('oms-app/order/deliver/queryStartPlaceLocationBetweenOrderPlaceWarningFlag', RespLocationBetweenOrderPlaceWarningFlag);
    }
}

export class RespLocationBetweenOrderPlaceWarningFlag extends ResultData {
    public distanceWarningFlag?: String; //是否提示标识;1:提醒;0:不提醒
}
