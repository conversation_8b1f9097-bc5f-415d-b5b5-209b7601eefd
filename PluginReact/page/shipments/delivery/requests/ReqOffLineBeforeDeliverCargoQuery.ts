import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
import OffLineShipmentsEGoodInfo from '../models/OffLineShipmentsEGoodInfo';

export class ReqOffLineBeforeDeliverCargoQuery extends BaseRequest {
    public orderId: string;

    async request(): Promise<BaseResponse<OffLineShipmentsEGoodInfo>> {
        this.params = {
            orderId: this.orderId,
        };
        return super.post('oms-app/order/tenderDeliver/beforeDeliverCargoQuery', OffLineShipmentsEGoodInfo);
    }
}
