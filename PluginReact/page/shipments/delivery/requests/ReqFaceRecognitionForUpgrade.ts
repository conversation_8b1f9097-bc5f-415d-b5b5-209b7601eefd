import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
/**
 * 注释:
 * 时间: 2024/6/20 14:48
 * <AUTHOR>
 */

export class ReqFaceRecognitionForUpgrade extends BaseRequest {
    idCardNo?: string; //身份证号码
    idCardName?: string; //身份证姓名
    orderNo?: string; //
    functionType?: string; // 发货：15
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            idCardNo: this.idCardNo,
            idCardName: this.idCardName,
            orderNo: this.orderNo,
            functionType: this.functionType,
        };
        return super.post('mms-app/platform/authentication/faceRecognitionForUpgrade', ResultData);
    }
}

export class FaceInfo {
    public idCardNo: string; //身份证号码
    public idCardName: string; //身份证姓名
    public bestImageUrl: string; //人脸识别照片
    public envImageUrl: string; //全景照片
    public delta: string; //在配合MegLive SDK使用时，用于校验上传数据的校验字符串，此字符串会由MegLive SDK直接返回。
}
