import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from '../models/EShipmentsSuccess';
import {EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';
import EBillDetailZeroAssume from '../../../bill/models/EBillDetailZeroAssume';
import {BacthZeroGoodInfo} from '../models/ShipmentsEZeroGoodInfoRsp';

/**
 * 确认发货[零担货]合并
 */
export class ReqDoConfirmLTLListDeliver extends BaseRequest {
    /**
     * 订单id
     */
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;

    /*网络制式*/
    public signalType?: string;

    /*** 经度*/
    public longitude?: string;

    /*** 纬度*/
    public latitude?: string;

    //确认收货图片名称连接字符串 2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段
    public picUrlsJsonArrayObj?: EWater[];
    //确认人车图片名称连接字符串
    public picUrls2JsonArrayObj?: EWater[];
    //备注
    public remark?: string;

    public topOrderArray?: BacthZeroGoodInfo[];

    public ltlCargoArray?: BacthZeroGoodInfo[];
    //拼车单ID
    public orderCarpoolingId?: string;

    public faceIdentifyFlag?: string;
  //WLHY-12652 🏷 【汽运】承运方支持上传视频
  public deliverVideoArray?: PeopleVehicleVideoArr[];

    async request(): Promise<BaseResponse<EShipmentsSuccess>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            signalType: this.signalType,
            longitude: this.longitude,
            latitude: this.latitude,
            picUrlsJsonArrayObj: this.picUrlsJsonArrayObj,
            picUrls2JsonArrayObj: this.picUrls2JsonArrayObj,
            remark: this.remark,
            topOrderArray: this.topOrderArray,
            ltlCargoArray: this.ltlCargoArray,
            orderCarpoolingId: this.orderCarpoolingId,
            faceIdentifyFlag: this.faceIdentifyFlag,
            deliverVideoArray: this.deliverVideoArray,
            
        };
        return super.post('oms-app/order/deliver/doConfirmLTLListDeliver', EShipmentsSuccess);
    }
}
