import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from '../models/EShipmentsSuccess';
import {EWater, PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';

/**
 * 发货后预付申请
 */
export class ReqDoAdvanceApplyByDeliverAndNotReceive extends BaseRequest {
    /*** 订单id*/
    public orderId: string;

    /**
     * 运输详情ID
     */
    public detailId: string;

    /***预付方式*/
    public advanceWay?: string;
    /**油气比例*/
    public dlOilCardRatio?: string;
    /***  预付业务立减金额 如果用户使用了预付业务则传，金额的传金额，折扣的传折扣数*/
    public couponMoney?: string;

    /***预付业务立减 1:固定额度;2:固定金额折扣;3:随机减*/
    public couponAmountType?: string;

    /*** 使用优惠券*/
    public userCouponId?: string;

    /***个体司机信用分*/
    public creditPoint?: string;

    /*网络制式*/
    public signalType?: string;

    //确认收货图片名称连接字符串 2021-03-08 ZCZY-5531 因移动端图片展示是根据","切割展示的，如果图片旋转过，则会有问题，故新增数组字段
    public picUrlsJsonArrayObj?: EWater[];
    //确认人车图片名称连接字符串
    public picUrls2JsonArrayObj?: EWater[];
    //	出厂时间	String	否	ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public outStageTime?: string;
    //预付业务立减Id
    public cutAdvanceCouponId?: string;

    //是否购买智运折扣卡;1:是;0否
    public discountCardBuyFlag?: string;
    //是否使用智运折扣卡;1:是;0否:
    public discountCardUseFlag?: string;
    //智运折卡号
    public discountCardNum?: string;
    //活动id
    public activityId?: string;
    //油品在途预付活动id
    public oilOnPrePolicyId?: string;
    /*** 经度*/
    public longitude?: string;
    /*** 纬度*/
    public latitude?: string;
    //备注
    public remark?: string;
    //WLHY-12652 🏷 【汽运】承运方支持上传视频
    public deliverVideoArray?: PeopleVehicleVideoArr[];
    //WLHY-15998 发货 挂车车牌合影图片
    public trailerPicUrlsJsonArr?: EWater[];

    async request(): Promise<BaseResponse<EShipmentsSuccess>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            advanceWay: this.advanceWay,
            dlOilCardRatio: this.dlOilCardRatio,
            couponMoney: this.couponMoney,
            couponAmountType: this.couponAmountType,
            userCouponId: this.userCouponId,
            creditPoint: this.creditPoint,
            signalType: this.signalType,
            picUrlsJsonArrayObj: this.picUrlsJsonArrayObj,
            picUrls2JsonArrayObj: this.picUrls2JsonArrayObj,
            outStageTime: this.outStageTime,
            cutAdvanceCouponId: this.cutAdvanceCouponId,
            discountCardBuyFlag: this.discountCardBuyFlag,
            discountCardUseFlag: this.discountCardUseFlag,
            discountCardNum: this.discountCardNum,
            activityId: this.activityId,
            longitude: this.longitude,
            latitude: this.latitude,
            remark: this.remark,
            deliverVideoArray: this.deliverVideoArray,
            oilOnPrePolicyId: this.oilOnPrePolicyId,
            trailerPicUrlsJsonArr:  this.trailerPicUrlsJsonArr,
        };
        return super.post('oms-app/order/deliver/doAdvanceApplyByDeliverAndNotReceive', EShipmentsSuccess);
    }
}
