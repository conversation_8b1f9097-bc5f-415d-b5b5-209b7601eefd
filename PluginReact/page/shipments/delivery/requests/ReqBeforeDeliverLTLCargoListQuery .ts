import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import ShipmentsEZeroGoodInfoRsp from '../models/ShipmentsEZeroGoodInfoRsp';

/**
 * 货物信息
 */
export class ReqBeforeDeliverLTLCargoListQuery extends BaseRequest {
    public orderId: string;
    public detailId: string;

    public async request(): Promise<BaseResponse<ShipmentsEZeroGoodInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
        };
        return super.post('oms-app/order/deliver/beforeDeliverLTLCargoListQuery', ShipmentsEZeroGoodInfoRsp);
    }
}
