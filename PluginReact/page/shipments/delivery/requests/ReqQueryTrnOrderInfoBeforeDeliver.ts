import {BaseRequest} from '../../../http/BaseRequest';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import {BaseResponse} from '../../../http/BaseResponse';

/**
 * 确认发货前查询货物信息(src运单)
 * wiki:http://wiki.zczy56.com/pages/viewpage.action?pageId=78545550
 * 对接人：陈鹏宇
 */
export class ReqQueryTrnOrderInfoBeforeDeliver extends BaseRequest {
    public sourceId: string;

    public async request(): Promise<BaseResponse<ShipmentsEGoodInfoRsp>> {
        this.params = {
            sourceId: this.sourceId,
        };
        return super.post('oms-app/order/deliverOfTms/queryTrnOrderInfoBeforeDeliver', ShipmentsEGoodInfoRsp);
    }
}
