import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import PageList from '../../../http/PageList';
import {EContainer} from '../models/ShipmentsEGoodInfoRsp';

/**
 *根据订单号查询集装箱号
 */
export class ReqQueryContainerDetailListByOrderId extends BaseRequest {
    public orderId: string;

    constructor(orderId: string) {
        super();
        this.orderId = orderId;
    }

    public async request(): Promise<BaseResponse<PageList<EContainer>>> {
        this.params = {
            orderId: this.orderId,
            nowPage: 1,
            pageSize: 100,
        };
        return super.post('oms-app/carrier/common/queryContainerDetailListByOrderId', PageList<EContainer>);
    }
}
