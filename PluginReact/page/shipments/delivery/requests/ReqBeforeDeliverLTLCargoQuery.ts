import {BaseRequest} from '../../../http/BaseRequest';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import {BaseResponse} from '../../../http/BaseResponse';

/**
 * 货物信息
 */
export class ReqBeforeDeliverLTLCargoQuery extends BaseRequest {
    public orderId: string;
    public detailId: string;

    public async request(): Promise<BaseResponse<ShipmentsEGoodInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
        };
        return super.post('oms-app/order/deliver/beforeDeliverLTLCargoQuery', ShipmentsEGoodInfoRsp);
    }
}
