import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import AdvanceInfoRsp from '../models/AdvanceInfoRsp';

/**
 * 预付场景接口
 */
export class ReqAdvanceInfo extends BaseRequest {
    public orderId: string;
    public detailId: string;
    public longitude: string;
    public latitude: string;
    public queryAdvanceInfoSource?:string;//  1:油品广告跳转查询
    async request(): Promise<BaseResponse<AdvanceInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            longitude: this.longitude,
            latitude: this.latitude,
            queryAdvanceInfoSource:this.queryAdvanceInfoSource
        };
        return super.post('oms-app/order/deliver/queryAdvanceInfo', AdvanceInfoRsp);
    }
}
