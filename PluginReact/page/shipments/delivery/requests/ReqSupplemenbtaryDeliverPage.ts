import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
import { EWater } from '../models/ShipmentsEGoodInfoRsp';
/**
 * 预付单-确认发货后回单确认前补传发货单据
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=91586738
 * 对接：陈鹏宇
 * */
export class ReqSupplemenbtaryDeliverPage extends BaseRequest {
    public orderId?: string; //运单号
    public dataList?: EWater[];
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
            dataList:this.dataList
        };
        return super.post('oms-app/order/deliver/supplemenbtaryDeliverPage', ResultData);
    }
}
