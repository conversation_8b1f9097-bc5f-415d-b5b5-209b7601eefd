import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import PageList from '../../../http/PageList';
import {EPickUserCoupon} from '../models/EPickUserCoupon';
import {Method} from '../../../util/NativeModulesTools';

/**
 * 发货可用优惠券
 */
export class ReqQueryCarrierAdvanceCouponList extends BaseRequest {
    /***0 查询可用张数 1 查询可用不可用张数 2 查询可用优惠券列表 3 查询不可用优惠券列表*/
    public type?: string;
    /*** 服务费*/
    public money?: string;
    public size: number = 10;
    public pbCarrierMoney;
    public forcePayFlag?: string;
    public async request(): Promise<BaseResponse<PageList<EPickUserCoupon>>> {
        let login = Method.getLogin();
        this.params = {
            usedTypes: '2', //2 确认发货 3 确认卸货
            type: this.type,
            size: this.size,
            money: this.money,
            pbCarrierMoney: this.pbCarrierMoney,
            userId: login.userId,
            userTypes: login.userType,
            forcePayFlag:this.forcePayFlag
        };
        return super.post('ims-app/coupon/carrierUserAdvanceCouponService', PageList<EPickUserCoupon>);
    }
}
