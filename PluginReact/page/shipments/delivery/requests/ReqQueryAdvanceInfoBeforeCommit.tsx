import {BaseRequest} from '../../../http/BaseRequest';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';

/***
 * 发货之前选择预付时提示信息
 */
export class ReqQueryAdvanceInfoBeforeCommit extends BaseRequest {
    //发货吨位
    public weightDetails?: string;
    //运输id
    public orderId?: string;
    //	预付方式
    public advanceType?: string;
    //	油品比例	只有预付方式是油品+现金时才传值
    public advanceOilRatio?: string;

    public async request(): Promise<BaseResponse<EAdvanceInfoBeforeCommit>> {
        this.params = {
            orderId: this.orderId,
            weightDetails: this.weightDetails,
            advanceType: this.advanceType,
            advanceOilRatio: this.advanceOilRatio,
        };
        return super.post('oms-app/order/deliver/queryAdvanceInfoBeforeCommit', EAdvanceInfoBeforeCommit);
    }
}

export class EAdvanceInfoBeforeCommit extends ResultData {
    //     最新预付比例
    public advanceRatio?: string;
    //是否提示标识;1:提醒;0:不提醒
    public tipFlag?: string;
    //    总提示信息
    public totalMsg?: string;
    //      富文本文本信息
    public attributeMsg?: string;
    //    预付比例是否变更 1:变更 0:未变更
    public changRatioFlag?: string;
}
