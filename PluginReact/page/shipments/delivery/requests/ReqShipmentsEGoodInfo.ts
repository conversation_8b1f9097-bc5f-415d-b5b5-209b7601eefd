import {BaseRequest} from '../../../http/BaseRequest';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import {BaseResponse} from '../../../http/BaseResponse';

/**
 * 货物信息
 */
export class ReqShipmentsEGoodInfo extends BaseRequest {
    public orderId: string;
    public detailId: string;
    //http://wiki.zczy56.com/pages/viewpage.action?pageId=31034763
    //FBIS-2414【业务】人车货合影功能优化	1：在途申请预付;其余为预付来源
    public querySource?: string;

    public async request(): Promise<BaseResponse<ShipmentsEGoodInfoRsp>> {
        this.params = {
            orderId: this.orderId,
            detailId: this.detailId,
            querySource: this.querySource,
        };
        return super.post('oms-app/order/deliver/beforeDeliverCargoQuery', ShipmentsEGoodInfoRsp);
    }
}
