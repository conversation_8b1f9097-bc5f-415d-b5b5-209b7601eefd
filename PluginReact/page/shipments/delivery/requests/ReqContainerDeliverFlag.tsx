import {EContainer} from '../models/ShipmentsEGoodInfoRsp';
import {BaseResponse} from '../../../http/BaseResponse';
import {ResultData} from '../../../http/ResultData';
import {BaseRequest} from '../../../http/BaseRequest';

/***
 *集装箱确认收货之前提示
 */
export class ReqContainerDeliverFlag extends BaseRequest {
    public orderId: string;
    public containerNoJsonArray: EContainer[];

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
            containerNoJsonArray: this.containerNoJsonArray,
        };
        return super.post('oms-app/order/deliver/queryContainerDeliverFlagByContainerListId', ResultData);
    }
}
