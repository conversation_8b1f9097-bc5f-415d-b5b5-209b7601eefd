import {View} from 'react-native';
import React, {useImperativeHandle} from 'react';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import GoodsInfoView from './GoodsInfoView';
import {ShipmentUI} from '../models/ShipmentUI';
import TextUtils from '../../../util/TextUtils';
import {Method} from '../../../util/NativeModulesTools';
interface Props {
    //发货单信息
    shipmentsEGoodInfo?: ShipmentsEGoodInfoRsp;
}

/**
 * 注释:  发货修改-商品明细
 * 时间: 2023/7/10 0010 19:17
 * <AUTHOR>
 * @param props
 * @constructor
 */
function ShipmentsEditGoodsView(props: Props, ref: React.Ref<ShipmentsEditGoodsViewRef>) {
    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        let goodInfos = props.shipmentsEGoodInfo?.rootArray;
        let size = goodInfos?.length ?? 0;
        if (goodInfos != null) {
            for (let i = 0; i < size; i++) {
                let beforeDeliverCargoWeight = goodInfos[i].beforeDeliverCargoWeight?? '';
                if (TextUtils.isEmpty(beforeDeliverCargoWeight)) {
                    Method.showToast('请输入[' + goodInfos[i].cargoName + ']货物重量');
                    return false;
                }
                if (parseFloat(beforeDeliverCargoWeight) <= 0.0) {
                    Method.showToast('[' + goodInfos[i].cargoName + ']货物,货物计量不能小于等于0，请重新确认！');
                    return false;
                }

                if (TextUtils.equals('1', goodInfos[i].unit) && TextUtils.isNoEmpty(goodInfos[i].weightOrientationLimit)) {
                    //重货
                    if (parseFloat(beforeDeliverCargoWeight) > parseFloat(goodInfos[i].weightOrientationLimit)) {
                        //zczy-13915_发货吨位提示错误
                        Method.showToast(`${goodInfos[i].cargoName}货物，货物计量不能大于${goodInfos[i].weightOrientationLimit}，请重新确认`);
                        return false;
                    }
                } else if (TextUtils.equals('2', goodInfos[i].unit) && TextUtils.isNoEmpty(goodInfos[i].shippingOrientationMargin)) {
                    //泡货
                    if (parseFloat(beforeDeliverCargoWeight) > parseFloat(goodInfos[i].shippingOrientationMargin)) {
                        //zczy-13915_发货吨位提示错误
                        Method.showToast(`${goodInfos[i].cargoName}货物，货物计量不能大于${goodInfos[i].shippingOrientationMargin}，请重新确认`);
                        return false;
                    }
                }
            }
            //货物明细
            shipmentUI.goodInfos = goodInfos!;
        }
        return true;
    };
    useImperativeHandle(ref, () => ({check}));

    return (
        <View>
            {props.shipmentsEGoodInfo?.rootArray?.map((value) => {
                // gLog('货物明细=' + JSON.stringify(value));
                return <GoodsInfoView key={value.cargoId} detail={value} editInput={true} grossAndTare={false} />;
            })}
        </View>
    );
}

export interface ShipmentsEditGoodsViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

export default React.forwardRef<ShipmentsEditGoodsViewRef, Props>(ShipmentsEditGoodsView);
