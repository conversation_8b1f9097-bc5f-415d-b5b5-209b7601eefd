import {Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {DP, SP} from '../../../util/scaled-style';
import React, {useEffect, useImperativeHandle, useState} from 'react';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import NumUtil from '../../../util/NumUtil';
import {ReqQueryCarrierAdvanceCouponList} from '../requests/ReqQueryCarrierAdvanceCouponList';
import {CouponInfo} from '../models/CouponInfo';
import UIImage from '../../../widget/UIImage';
import TextUtils from '../../../util/TextUtils';
import LanguageType from '../../../util/language/LanguageType';
import {Method} from '../../../util/NativeModulesTools';
/***
 * 优惠券
 * @param userCoupon
 * @returns {JSX.Element}
 * @constructor
 */

interface Props {
    //订单信息
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    //预付信息
    advanceInfo: AdvanceInfoRsp;
    //当前预付方式
    advanceWay?: EAdvanceType;
    //预付服务费
    serverMoney: string;
    //优惠卷切换回调
    callBack?: Function;
}

function ShowUserCouponView(props: Props, ref: React.Ref<ShowUserCouponViewRef>) {
    const [userCouponId, setUserCouponId] = useState<string>();
    //优惠券金额
    const [couponMoney, setCouponMoney] = useState<string>();
    //可以用数量
    const [userSize, setUserSize] = useState(0);

    const cleanStatus = (serverMoney) => {
        //清空选择优惠券
        setUserCouponId('');
        setCouponMoney('');
        querySize(serverMoney);
    };

    //对外引用
    useImperativeHandle(ref, () => ({cleanStatus}));

    //属性变化监听
    useEffect(() => {
        let couponInfo;
        if (TextUtils.isNoEmpty(userCouponId)) {
            couponInfo = new CouponInfo();
            couponInfo.userCouponId = userCouponId;
            couponInfo.couponMoney = couponMoney;
            couponInfo.userSize = userSize;
            couponInfo.serverMoney = props.serverMoney;
        }
        props.callBack && props.callBack(couponInfo);
    }, [userCouponId, couponMoney, userSize]);

    const querySize = (serverMoney: string) => {
        //查询优惠券可以数量
        let req = new ReqQueryCarrierAdvanceCouponList();
        req.type = '0';
        req.money = serverMoney;
        req.pbCarrierMoney = props.shipmentsEGoodInfo.pbCarrierMoney;
        req.forcePayFlag = props.advanceInfo.forceAdvanceFlag;
        req.request().then((response) => {
            if (response.isSuccess() && response.data) {
                setUserSize(response.data.totalSize ?? 0);
            }
        });
    };
    /**
     * 注释: 打开优惠券选择
     * 时间: 2023/6/27 0027 10:38
     * <AUTHOR>
     */
    const openShipmentsCouponActivity = () => {
        // 打开选择优惠券 String orderId,String pbCarrierMoney,String serverMoney,String userCouponIds,Callback callback
        Method.openShipmentsCouponActivity(props.shipmentsEGoodInfo.orderId, props.shipmentsEGoodInfo.pbCarrierMoney, props.serverMoney, userCouponId, props.advanceInfo.forceAdvanceFlag, (code, json) => {
            if (code == 200) {
                let data = JSON.parse(json);
                if (TextUtils.isEmpty(data.userCouponIds)) {
                    //清空选择优惠券
                    setUserCouponId('');
                    setCouponMoney('');
                    querySize(props.serverMoney);
                } else {
                    //重新优惠券
                    setUserCouponId(data.userCouponIds);
                    setCouponMoney(data.couponMoney);
                }
            }
        });
    };

    if (props.advanceWay && !TextUtils.equals('2', props.advanceWay.type)) {
        //优惠金额
        let discountAmount = 0;
        if (userCouponId) {
            //选了优惠卷优先使用选中优惠计算
            discountAmount = Math.min(parseFloat(couponMoney ?? '0'), parseFloat(props.serverMoney));
        } else if (TextUtils.isNoEmpty(props.advanceInfo?.userCouponId) && !TextUtils.equals('3', props.advanceWay.type)) {
            //有默认优惠券 && 不是油品+现金预付方式
            discountAmount = Math.min(parseFloat(props.serverMoney), props.advanceInfo?.getDiscountMoney(parseFloat(props.serverMoney)));
        }
        //1:预付现金;3:预付油品+现金
        let couponTxt = discountAmount > 0 ? `-¥${discountAmount}元` : userSize > 0 ? `${userSize}张可用` : '无可用红包';
        //优惠后服务费
        let money = NumUtil.sub(parseFloat(props.serverMoney), discountAmount);
        money = money <= 0 ? 0.0 : money;
        return (
            <View style={[gStyle.view_padding, {minHeight: 45, justifyContent: 'center'}]}>
                {/*优惠券选择*/}
                <View style={gStyle.view_row}>
                    <Text style={gStyle.txt_333333_34}>
                        {LanguageType.getTxt('优惠券')}
                        <UIImage source={'icon_coupon_question'} style={{width: DP(32), height: DP(32), paddingLeft: 5}} />
                    </Text>
                    <Text style={gStyle.text_flex_right} onPress={openShipmentsCouponActivity}>
                        {couponTxt}
                    </Text>
                    <UIImage source={'base_right_arrow_gray'} style={{width: DP(15), height: DP(27), marginLeft: 5}} />
                </View>
                {/*优惠卷抵扣预估*/}
                {discountAmount > 0 && (
                    <View style={gStyle.view_row}>
                        <Text style={gStyle.txt_333333_28}>抵扣后预估金额</Text>
                        <View
                            style={{
                                flexDirection: 'row',
                                flex: 1,
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                                height: 43,
                            }}>
                            <Text
                                style={{
                                    color: '#999999',
                                    fontSize: SP(24),
                                    textDecorationLine: 'line-through',
                                }}>
                                {props.serverMoney}
                            </Text>
                            <Text style={{color: '#e95d4f', fontSize: SP(24)}}>{money.toFixed(2)}</Text>
                        </View>
                    </View>
                )}
            </View>
        );
    }
    return null;
}

export interface ShowUserCouponViewRef {
    cleanStatus: (serverMoney: string) => void;
}

export default React.forwardRef<ShowUserCouponViewRef, Props>(ShowUserCouponView);
