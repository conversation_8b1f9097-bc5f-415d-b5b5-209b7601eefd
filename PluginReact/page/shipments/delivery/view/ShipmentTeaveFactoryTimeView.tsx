import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {format} from 'date-fns';
import {SP} from '../../../util/scaled-style';
import EventBus from '../../../util/EventBus';
import {Constant} from '../../../base/Constant';
import {ShipmentUI} from '../models/ShipmentUI';
import UIDatePicker from '../../../widget/UIDatePicker';
import {Method} from '../../../util/NativeModulesTools';
/***货物出厂时间 ZCZY-7729 冀东定制化需求*/
interface Props {}

function ShipmentTeaveFactoryTimeView(props: Props, ref) {
    const [timeShow, setTimeShow] = useState(false);
    const [leaveFactoryTime, setLeaveFactoryTime] = useState(null);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        if (leaveFactoryTime == null) {
            Method.showToast('请选择发货磅单出场时间!');
            return false;
        }
        //出场时间
        shipmentUI.leaveFactoryTime = format(leaveFactoryTime, 'yyyy-MM-dd HH:mm') + ':00';
        return true;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    const onSelectTime = (date) => {
        // 时间选择工具
        setTimeShow(false);
        setLeaveFactoryTime(date);
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {
            key: 'leaveFactoryTime',
            value: format(date, 'yyyy-MM-dd HH:mm'),
        });
    };

    return (
        <View style={[gStyle.view_row, styles.view_padding]}>
            <View>
                <Text style={gStyle.txt_333333_34}>发货磅单出场时间</Text>
                <Text style={gStyle.txt_999999_24}>按照发货单上的出厂时间填写</Text>
            </View>
            <Text style={gStyle.text_flex_right} onPress={() => setTimeShow(true)}>
                {leaveFactoryTime ? format(leaveFactoryTime, 'yyyy-MM-dd HH:mm') : '请选择时间 >'}
            </Text>
            {/*时间选择器*/}
            {timeShow && (
                <UIDatePicker
                    title={'请选择出场时间'}
                    onHideEvent={() => {
                        setTimeShow(false);
                    }}
                    onSelectEvent={(date) => {
                        onSelectTime(date);
                    }}
                />
            )}
        </View>
    );
}

export interface ShipmentTeaveFactoryTimeViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

export default forwardRef<ShipmentTeaveFactoryTimeViewRef>(ShipmentTeaveFactoryTimeView);

const styles = StyleSheet.create({
    view_padding: {
        backgroundColor: '#fff',
        paddingLeft: 10,
        paddingRight: 10,
        paddingTop: 5,
        paddingBottom: 5,
        marginTop: 5,
    },
    good_info_input: {
        padding: 2,
        flex: 1,
        fontSize: SP(32),
        textAlign: 'right',
        marginLeft: 5,
        marginRight: 5,
    },
});
