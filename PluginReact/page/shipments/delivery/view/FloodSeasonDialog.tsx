import React from 'react';
import {View, StyleSheet, Text, ViewProps} from 'react-native';
import UIImage from '../../../widget/UIImage';
import UIImageBackground from '../../../widget/UIImageBackground';

interface Props extends ViewProps {
    onNext?: () => void;
}
/***
 * 25677	WLHY-6150	【安卓】【加急】【汽运】汛期安全运输提醒
 */
export default function FloodSeasonDialog(props: Props) {
    return (
        <View style={{backgroundColor: '#fff', borderRadius: 8, width: 300, alignContent: 'center', alignSelf: 'center'}}>
            <UIImageBackground source={'http://img.zczy56.com/202407291457061948014.png'} style={{position: 'absolute', width: 300, height: 400}} />
            <View style={{justifyContent: 'center', alignItems: 'center', borderTopLeftRadius: 8, borderTopRightRadius: 8}}>
                <Text style={{textAlign: 'center', fontSize: 19, fontWeight: 'bold', color: '#333333', marginTop: 18}}>汛期安全运输提醒</Text>
                <View style={{flexDirection: 'row', marginTop: 6, alignItems: 'center'}}>
                    <UIImage source={'audit_nopass_tip'} style={{width: 13, height: 13}} />
                    <Text style={{textAlign: 'center', fontSize: 13, color: '#FF3434', alignItems: 'center', marginLeft: 5}}>高货值货物</Text>
                </View>
            </View>
            <Text style={{fontSize: 14, color: '#333333', marginLeft: 13, marginTop: 14, marginRight: 13}}>
                汛期是货物运输造成货损的高发季节,承运方起到对货物的监装义务，<Text style={{fontSize: 14, color: '#FF8417'}}>请您按照以下防护要求作业:</Text>
            </Text>

            <View style={{display: 'flex', marginLeft: 13, marginTop: 16, marginRight: 13}}>
                <Text style={{fontSize: 12, color: '#666666', marginTop: 7}}>
                    <UIImage source={'certification_carowner_oval'} style={{backgroundColor: '#5C89FF', width: 8, height: 8, borderRadius: 20, marginRight: 5}} /> 雨季天气多变，尽可能
                    <Text style={{fontSize: 12, color: '#FF8417'}}>在库内或雨棚下完成装卸货作业。</Text>
                </Text>
                <Text style={{fontSize: 12, color: '#666666', marginTop: 7}}>
                    <UIImage source={'certification_carowner_oval'} style={{backgroundColor: '#5C89FF', width: 8, height: 8, borderRadius: 20, marginRight: 5}} /> 装货前应
                    <Text style={{fontSize: 12, color: '#FF8417'}}>检查雨布、车底板有无积水和破损。</Text>
                </Text>
                <Text style={{fontSize: 12, color: '#666666', marginTop: 7}}>
                    <UIImage source={'certification_carowner_oval'} style={{backgroundColor: '#5C89FF', width: 8, height: 8, borderRadius: 20, marginRight: 5}} /> 无论天气好坏，都要做好防雨防潮工作，
                    <Text style={{fontSize: 12, color: '#FF8417'}}>货物顶部、车底板加封防水布。</Text>
                </Text>
                <Text style={{fontSize: 12, color: '#666666', marginTop: 7}}>
                    <UIImage source={'certification_carowner_oval'} style={{backgroundColor: '#5C89FF', width: 8, height: 8, borderRadius: 20, marginRight: 5}} /> 卸货时，需抵达卸货现场后并确认开始卸货时才可以揭开雨布，
                    <Text style={{fontSize: 12, color: '#FF8417'}}>揭篷布注意积水不要酒落在货物上。</Text>
                </Text>
            </View>
            <Text
                style={{color: '#fff', marginTop: 30, backgroundColor: '#5086fc', width: 270, height: 40, alignSelf: 'center', textAlign: 'center', textAlignVertical: 'center', borderRadius: 5}}
                onPress={() => {
                    console.log('====================================');
                    props.onNext && props.onNext();
                }}>
                我知道了
            </Text>
        </View>
    );
}
