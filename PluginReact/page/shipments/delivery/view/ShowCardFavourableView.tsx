import TextUtils from '../../../util/TextUtils';
import NumUtil from '../../../util/NumUtil';
import {Image, Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {DP} from '../../../util/scaled-style';
import React from 'react';
import UIImage from '../../../widget/UIImage';

/**
 * 使用权益卡优惠信息
 * @param {*} serverMoney 预付服务费
 * @param {*} cardType 权益卡类型
 * @param {*} discountRatio 权益卡优惠比例
 * @returns
 */
interface Props {
    serverMoney: string; //预付服务费
    cardType: string; //权益卡类型
    discountRatio: number; //权益卡折扣类型
}

export default function ShowCardFavourableView(props: Props) {
    let money = TextUtils.isEmpty(props.serverMoney) ? 0.0 : parseFloat(props.serverMoney);
    //折扣金额
    let discountMoney = NumUtil.halfup(NumUtil.mul(money, 1 - props.discountRatio));

    return (
        <View style={[gStyle.view_padding, {marginTop: 5}]}>
            <View style={gStyle.view_row}>
                <UIImage style={{width: DP(25), height: DP(25), marginRight: 5}} source={'equity_bfh_icon'} />
                <Text style={gStyle.txt_333333_32}>智运卡折扣</Text>
                <UIImage style={{width: DP(47.5), height: DP(24), marginLeft: 10}} source={`${props.cardType.includes('月') ? 'equity_yk_icon2' : 'equity_nk_icon2'}`} />
                <Text style={[gStyle.text_flex_right, {color: '#f76060'}]}>{`-¥${discountMoney}`}</Text>
            </View>
            <Text style={[gStyle.txt_999999_24, {marginTop: 10}]}>
                成为智运卡会员，本单正在享受会员服务费
                <Text style={{color: '#FF7844'}}>{`${props.discountRatio * 10}折`}</Text>权益，预计将为您
                <Text style={{color: '#FF7844'}}>{`节省${discountMoney}元`}</Text>
            </Text>
        </View>
    );
}
