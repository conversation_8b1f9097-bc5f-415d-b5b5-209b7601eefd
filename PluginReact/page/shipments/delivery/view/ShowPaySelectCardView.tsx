import React, {useEffect, useState} from 'react';
import {StyleProp, Text, View, ViewStyle} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {http} from '../../../const.global';
import {DP, SP} from '../../../util/scaled-style';
import {DiscountCardArray, DiscountCardInfo} from '../models/ShipmentsEGoodInfoRsp';
import UIImageBackground from '../../../widget/UIImageBackground';
import UIImage from '../../../widget/UIImage';
import EventBus from '../../../util/EventBus';
import {Constant} from '../../../base/Constant';
import TextUtils from '../../../util/TextUtils';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';
import {Method} from '../../../util/NativeModulesTools';
/**
 * 权益卡选择View
 * @param {*} param0
 * @returns
 */
interface Props {
    shipCard?: DiscountCardInfo; //权益卡
    onSelectCardCallback?: Function; //选择回调
    style?: StyleProp<ViewStyle>;
}

export default function ShowPaySelectCardView(props: Props) {
    //1 月卡选中 2 年卡选中
    const [selectType, setSelectType] = useState(0);
    //智运卡信息
    const [discountCardInfo, setDiscountCardInfo] = useState<DiscountCardInfo>();
    const [showCard, setShowCard] = useState(undefined);

    const onSelect = (type, value) => {
        let newType = selectType != type ? type : 0;
        setSelectType(newType);
        //选择权益卡回调
        //选择权益卡回调
        props.onSelectCardCallback && props.onSelectCardCallback(newType, value);
        EventBus.getInstance().fireEvent(Constant.event_pick_card_vip_select, {
            key: 'selectType',
            value: newType == 0 ? undefined : value,
        });
    };

    useEffect(() => {
        const listener = ({key, value}) => {
            switch (key) {
                case 'discountCardInfo':
                    setDiscountCardInfo(value);
                    //有智运卡信息并且没有买过则展示
                    let discountCardFlag = TextUtils.equals('1', value?.discountCardFlag);
                    let discountCardUseFlag = TextUtils.equals('1', value?.discountCardUseFlag);
                    setShowCard(value && discountCardFlag && !discountCardUseFlag);
                    break;
            }
        };
        EventBus.getInstance().addListener(Constant.event_pick_card_vip, listener);
        return () => {
            EventBus.getInstance().removeListener(listener);
        };
    }, []);

    /**
     * 注释: 绘制月卡
     * 时间: 2023/6/25 0025 10:37
     * <AUTHOR>
     * @param value
     * @returns {JSX.Element}
     */
    function renderMonthlyCard(value: DiscountCardArray) {
        return (
            <UIImageBackground source={`${selectType == 1 ? 'equity_yk_select_yes' : 'equity_yk_select_no'}`} style={{flex: 1, paddingBottom: 7}} key={`${value.cardNum}`} resizeMode="stretch">
                <UITouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => {
                        onSelect(1, value);
                    }}
                    style={{flex: 1}}>
                    <UIImage style={{height: DP(30), width: DP(55)}} source={'equity_yk_icon'} />
                    <View style={[gStyle.view_row, {paddingLeft: 9, paddingRight: 9, paddingTop: 3}]}>
                        <Text
                            style={{
                                color: '#894629',
                                fontSize: 14,
                                flex: 1,
                                fontWeight: 'bold',
                            }}>
                            {value.discountTitle}
                        </Text>
                        <Text style={{color: '#894629', fontSize: 13, fontWeight: 'bold'}}>{`￥${value.salePrice}`}</Text>
                    </View>
                    <View style={[gStyle.view_row, {paddingLeft: 9, paddingRight: 9}]}>
                        <Text style={{color: '#a77b68', fontSize: SP(20), flex: 1}}>{value.subTips}</Text>
                        <Text
                            style={{
                                color: '#999999',
                                fontSize: SP(20),
                                textDecorationLine: 'line-through',
                            }}>{`￥${value.orgPrice}`}</Text>
                    </View>
                </UITouchableOpacity>
            </UIImageBackground>
        );
    }

    /**
     * 注释: 绘制年卡
     * 时间: 2023/6/25 0025 10:38
     * <AUTHOR>
     * @param value
     * @returns {JSX.Element}
     */
    function renderAnnualPass(value: DiscountCardArray) {
        return (
            <UIImageBackground source={`${selectType == 2 ? 'equity_nk_select_tes' : 'equity_nk_select_no'}`} style={{flex: 1, paddingBottom: 7, marginLeft: 4, height: 65}} key={`${value.cardNum}`} resizeMode="stretch">
                <UITouchableOpacity
                    activeOpacity={0.9}
                    onPress={() => {
                        onSelect(2, value);
                    }}
                    style={{flex: 1}}>
                    <UIImage style={{height: DP(30), width: DP(55)}} source={'equity_nk_icon'} />
                    <View style={[gStyle.view_row, {paddingLeft: 9, paddingRight: 9, paddingTop: 3}]}>
                        <Text
                            style={{
                                color: '#222222',
                                fontSize: 14,
                                flex: 1,
                                fontWeight: 'bold',
                            }}>
                            {value.discountTitle}
                        </Text>
                        <Text style={{color: '#222222', fontSize: 13, fontWeight: 'bold'}}>{`￥${value.salePrice}`}</Text>
                    </View>
                    <View style={[gStyle.view_row, {paddingLeft: 9, paddingRight: 9}]}>
                        <Text style={{color: '#9a9a9a', fontSize: SP(20), flex: 1}}>{value.subTips}</Text>
                        <Text
                            style={{
                                color: '#999999',
                                fontSize: SP(20),
                                textDecorationLine: 'line-through',
                            }}>{`￥${value.orgPrice}`}</Text>
                    </View>
                </UITouchableOpacity>
            </UIImageBackground>
        );
    }

    return showCard ? (
        <UIImageBackground
            source={'equity_select_crad_bg'}
            style={[
                {
                    marginHorizontal: 8,
                    marginVertical: 6,
                    paddingLeft: 10,
                    paddingRight: 10,
                    paddingTop: 7,
                    paddingBottom: 5,
                },
                props.style,
            ]}
            resizeMode="stretch">
            <View style={gStyle.view_row}>
                <Text style={[gStyle.txt_fff_32, {flex: 1}]}>智运折扣卡</Text>
                <UITouchableOpacity
                    style={{position: 'absolute', top: -7, right: -10}}
                    onPress={() => {
                        Method.openWebNoTitle(http.url() + '/form_h5/h5_inner/index.html?_t=' + new Date().getTime() + '#/zyActivityRule?activityId=' + props.shipCard ? props.shipCard?.activityId : discountCardInfo?.activityId);
                    }}>
                    <UIImage source={'equity_hdgz_icon'} style={{width: 64, height: 24}} />
                </UITouchableOpacity>
            </View>
            <Text style={gStyle.txt_fff_22}>有效期内货物保障和预付服务费立享折扣，无次数限制</Text>
            <View style={[gStyle.view_row, {marginTop: 7, marginBottom: 10}]}>
                {
                    //没有购买智运卡=>展示智运折扣卡,办卡信息
                    props.shipCard
                        ? props.shipCard.discountCardArray?.map((value) => {
                              if (value.cardType?.includes('月')) {
                                  //月卡
                                  return renderMonthlyCard(value);
                              } else {
                                  //年卡
                                  return renderAnnualPass(value);
                              }
                          })
                        : discountCardInfo?.discountCardArray?.map((value) => {
                              if (value.cardType?.includes('月')) {
                                  //月卡
                                  return renderMonthlyCard(value);
                              } else {
                                  //年卡
                                  return renderAnnualPass(value);
                              }
                          })
                }
            </View>
        </UIImageBackground>
    ) : (
        <View />
    );
}
