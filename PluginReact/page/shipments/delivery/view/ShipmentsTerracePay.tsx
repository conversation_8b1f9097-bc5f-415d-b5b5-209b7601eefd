import React, {useEffect, useImperativeHandle, useRef, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import TextUtils from '../../../util/TextUtils';
import UIRadioButton from '../../../widget/UIRadioButton';
import {DP, SP} from '../../../util/scaled-style';
import ShowPaySelectCardView from './ShowPaySelectCardView';
import ShowPlayOilMoneyRadio from './ShowPlayOilMoneyRadio';
import ShipmentsEGoodInfoRsp, {DiscountCardArray} from '../models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import ShowOilLineView from './ShowOilLineView';
import {AdvanceServiceMoney} from '../models/AdvanceServiceMoney';
import {CouponInfo} from '../models/CouponInfo';
import {ReqAdvanceCashOilServiceMoney} from '../requests/ReqAdvanceCashOilServiceMoney';
import {ReqNewAdvanceServiceMoney} from '../requests/ReqNewAdvanceServiceMoney';
import ShowCardFavourableView from './ShowCardFavourableView';
import ShipmentsSelectAdvanceWayView from './ShipmentsSelectAdvanceWayView';
import {Constant} from '../../../base/Constant';
import EventBus from '../../../util/EventBus';
import {ReqQueryCarrierDiscounts} from '../requests/ReqQueryCarrierDiscounts';
import ShowUserCouponView, {ShowUserCouponViewRef} from './ShowUserCouponView';
import {ArrayUtils} from '../../../util/ArrayUtils';
import NumUtil from '../../../util/NumUtil';
import {ShipmentUI} from '../models/ShipmentUI';
import UIImage from '../../../widget/UIImage';
import ELogin from '../../../user/personalsafety/models/ELogin';
import {plainToInstance} from 'class-transformer';
import LanguageType from '../../../util/language/LanguageType';
import {onEvent} from '../../../base/native/UITrackingAction';
import {RouterUtils} from '../../../util/RouterUtils';
import {RouterUrl} from '../../../base/RouterUrl';
import {UserType} from '../../../user/models/UserType';
import {Method} from '../../../util/NativeModulesTools';
import {ReqAdvanceApplyConsult} from '../requests/ReqAdvanceApplyConsult';

interface Props {
    //订单信息
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    //预付信息
    advanceInfo: AdvanceInfoRsp;
    //场景 0:发货预付场景 1 : 重新预付 ,2：发货后预付申请
    sceneAdvance: number;
    //预付状态
    advanceState: string;
    //是否需要预付
    isAdvanceButtonOn?: boolean;
}

//预付 平台预付
function ShipmentsTerracePay(props: Props, ref: React.Ref<ShipmentsTerracePayRef>) {
    //显示挽留弹窗
    // const [showRetentionDialog, setShowRetentionDialog] = useState(false);
    //默认选择
    const [isAdvanceButtonOn, setIsAdvanceButtonOn] = useState(props.isAdvanceButtonOn ?? true);
    //预付金额提示信息
    const [prepayRatioTxt, setPrepayRatioTxt] = useState('选择预付，平台审核后将提前收到一部分现金');
    //不能预付原因
    const [noPrepayOn, setNoPrepayOn] = useState(false);
    //优惠信息
    const [advanceMoneyConTxt, setAdvanceMoneyConTxt] = useState(
        <Text
            style={{
                textAlign: 'center',
                fontSize: SP(20),
                color: Constant.color_66666,
                marginTop: 8,
            }}>
            {`服务费预估金额¥${NumUtil.roundStr(props.advanceInfo?.newAdvanceServiceMoney)}元`}
        </Text>,
    );
    const [advanceMoneyOilConTxt, setAdvanceMoneyOilConTxt] = useState(
        <Text
            style={{
                textAlign: 'center',
                fontSize: SP(20),
                color: Constant.color_66666,
                marginTop: 8,
            }}>
            {`服务费预估金额¥0~¥${NumUtil.roundStr(props.advanceInfo?.newAdvanceServiceMoney)}元`}
        </Text>,
    );
    const [favourableTxt, setFavourableTxt] = useState<string>();
    const [smartTransportCardStyle, setSmartTransportCardStyle] = useState<boolean>(false);
    //预付方式
    const [advanceWay, setAdvanceWay] = useState<EAdvanceType>();
    //预付服务费
    const [serverMoney, setServerMoney] = useState(props.advanceInfo.newAdvanceServiceMoney ?? '0');
    //预付奖励
    const [awardTips, setAwardTips] = useState<AdvanceServiceMoney>();
    //优惠券 优化之后预付服务费，可以使用数量
    const [userCoupon, setUserCoupon] = useState<CouponInfo>();
    //智运卡
    const [selectShipCard, setSelectShipCard] = useState<DiscountCardArray>();
    const [showShipCard, setShowShipCard] = useState<boolean>(false);
    const [discountResult, setDiscountResult] = useState<boolean>(false);
    //选择的油气比例
    const [oilRatio, setOilRatio] = useState(parseInt(props.advanceInfo.oilDefaultRatio ?? '0'));
    let shipCard = props.shipmentsEGoodInfo.discountCardInfo;
    //运营商折扣信息
    const [carrierDiscountsMsg, setCarrierDiscountsMsg] = useState<string>();
    //用户信息
    const [userInfo, setUserInfo] = useState<ELogin>();
    //计算好的立减优惠金额
    const [cutAfterAdvanceServiceMoney, setCutAfterAdvanceServiceMoney] = useState<string>();
    //赠送活动优惠券金额
    const [giveMoney, setGiveMoney] = useState<string>('');
    //最高抵用优惠券金额
    const [maxMoney, setMaxMoney] = useState<string>('');
    //抵用券截止日期
    const [endTime, setEndTime] = useState<string>('');
    //抵用券门槛金额
    const [discountMoneyTop, setDiscountMoneyTop] = useState<string>('');
    //是否显示自投保活动
    const [showActivity, setShowActivity] = useState<boolean>(false);
    //是否显示次单优惠券信息
    const [showCouponActivityTipsView, setShowCouponActivityTipsView] = useState<boolean>(true);
    //强配时展示的预付选择框 true:显示需要，false:显示不需要
    const [options, setOptions] = useState<string[]>(['1', '2']);
    //对外抛出的Ref方法
    const refSetOilRatio = (oilRatio: number) => {
        setOilRatio(oilRatio);
        //预付方式包含油品，则查一遍优惠信息
        if (advanceWay && !TextUtils.equals('1', advanceWay?.type)) {
            queryServiceMoney(oilRatio, advanceWay);
        }
    };
    //对外抛出的Ref方法
    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        shipmentUI.clearTerracePay();
        if (TextUtils.equals('1', props.advanceInfo.isAdvance) && (TextUtils.equals('2', props.advanceInfo.advanceType) || TextUtils.equals('4', props.advanceInfo.advanceType))) {
            //可预付 && (2.个体司机非指定订单 ||  4.个体司机关联车老板模式)  => 走平台预付
            //正常预付场景
            if (isAdvanceButtonOn) {
                let showSelectTerracePay = TextUtils.equals('2', props.advanceInfo.advanceType);
                //选择预付
                if (showSelectTerracePay && advanceWay == undefined) {
                    Method.showToast('请选择预付方式!');
                    return false;
                }

                shipmentUI.advanceWay = advanceWay?.type;
                shipmentUI.isAdvanceButtonOn = '1';
                shipmentUI.oilRatio = oilRatio <= 0 ? '' : oilRatio + '';

                //智运卡卡号
                if (selectShipCard) {
                    shipmentUI.discountCardNum = selectShipCard.cardNum;
                    //买卡
                    shipmentUI.discountCardBuyFlag = showShipCard ? '1' : '0';
                }

                if (TextUtils.equals('2', shipmentUI.advanceWay)) {
                    //油品预付(不使用任何优惠)【优惠券】【权益卡】【立减】等
                    //优惠券
                    shipmentUI.userCouponIds = '';
                    shipmentUI.couponMoney = '';
                    //立减优惠
                    shipmentUI.couponAmountType = '';
                    shipmentUI.cutAdvanceCouponId = '';
                    //智运卡卡
                    shipmentUI.discountCardUseFlag = '';
                    if (TextUtils.isNoEmpty(props.advanceInfo.oilOnPrePolicyId)) {
                        shipmentUI.oilOnPrePolicyId = props.advanceInfo.oilOnPrePolicyId;
                    }
                } else {
                    //优惠使用顺序：自选优惠卷=>智运卡=>立减活动=>默认优惠卷=>无优惠
                    if (userCoupon) {
                        //优惠券
                        shipmentUI.userCouponIds = userCoupon.userCouponId;
                    } else if (selectShipCard) {
                        //使用卡
                        shipmentUI.discountCardUseFlag = '1';
                    } else if (TextUtils.isNoEmpty(props.advanceInfo.couponAmountType)) {
                        //立减优惠
                        shipmentUI.couponAmountType = props.advanceInfo.couponAmountType;
                        shipmentUI.cutAdvanceCouponId = props.advanceInfo.cutAdvanceCouponId;
                        shipmentUI.couponMoney = props.advanceInfo.couponMoney;
                    } else if (TextUtils.isNoEmpty(props.advanceInfo.userCouponId) && TextUtils.equals('1', shipmentUI.advanceWay)) {
                        //默认优惠券
                        shipmentUI.userCouponIds = props.advanceInfo.userCouponId;
                    }
                    if (TextUtils.equals('1', props.advanceInfo.activityShowFlag)) {
                        ///趣味转盘活动 1展示
                        shipmentUI.activityId = props.advanceInfo.activityInfo?.activityId;
                    }
                }
            }
        }
        return true;
    };

    useImperativeHandle(ref, () => ({setOilRatio: refSetOilRatio, check, notAdvanceOn}));

    //页面初始化逻辑
    useEffect(() => {
        if (shipCard && ArrayUtils.isNoEmpty(shipCard.discountCardArray)) {
            if (TextUtils.equals('1', shipCard.discountCardFlag)) {
                //没有购买智运卡，显示智运卡购买页面
                setShowShipCard(true);
            } else if (TextUtils.equals('1', shipCard.discountCardBuyFlag)) {
                //已经购买了智运卡，无需显示智运卡购买页面
                setShowShipCard(false);
                setSelectShipCard(shipCard.discountCardArray![0]);
                setDiscountResult(!TextUtils.equals('2', advanceWay?.type));
            }
        }

        if (props.advanceInfo.advanceWay) {
            let defaultAdvanceWay;
            if (props.advanceInfo.advanceWay.length == 1) {
                defaultAdvanceWay = props.advanceInfo.advanceWay[0];
            } else {
                defaultAdvanceWay = props.advanceInfo.advanceWay?.filter((item) => {
                    return TextUtils.equals('1', item.defaultAdvanceWayFlag);
                })[0];
            }
            if (defaultAdvanceWay) {
                onSelectAdvanceWay(defaultAdvanceWay);
            }
        }
        //获取用户信息
        let userInfo = Method.getLogin();
        setUserInfo(plainToInstance(ELogin, userInfo));
        setNoPrepayOn(TextUtils.isNoEmpty(props.advanceInfo.noAdvanceReason) && !TextUtils.equals('1149', props.advanceInfo.noAdvanceCode));

        if ((props.sceneAdvance == 0 && TextUtils.equals('1', props.advanceInfo.forceAdvanceFlag)) || props.sceneAdvance == 2) {
            //强制选择 or 2：发货后预付申请
            setOptions(['1']);
            onSelectPrepay('1');
        } else {
            //非强制预付时 FBIS-15594 🏷 预付选项默认值配置
            if (TextUtils.equals('2', props.advanceInfo.disableDisappointPrepaySwitch)) {
                //默认选择不需要
                onSelectPrepay('2');
            }
        }
        // 埋点
        onEvent({
            pageId: 'ShipmentsTerracePay',
            tableId: 'view',
            event: 'click',
            requestParam: {
                orderid: props.shipmentsEGoodInfo?.orderId,
                proActId: props.advanceInfo?.activityInfo?.activityId,
            },
        });
    }, []);
    //选择智运卡监听
    useEffect(() => {
        //更新UI
        if (advanceWay) {
            setSelectAdvanceType(serverMoney, advanceWay, userCoupon);
        }
    }, [selectShipCard]);
    //监听props属性变化更新值
    useEffect(() => {
        setIsAdvanceButtonOn(props.isAdvanceButtonOn ?? false);
    }, [props]);
    //监听服务费变化
    useEffect(() => {
        queryCarrierDiscounts(props.shipmentsEGoodInfo.orderId, serverMoney, advanceWay?.type ?? '1');
    }, [serverMoney]);
    //用户选择优惠卷变化
    useEffect(() => {
        //更新UI
        if (advanceWay) {
            setSelectAdvanceType(serverMoney, advanceWay, userCoupon, cutAfterAdvanceServiceMoney);
        }
    }, [userCoupon]);

    //优惠视图Ref
    const couponViewRef = useRef<ShowUserCouponViewRef>(null);

    /**
     * 注释: 不满足预付，取消预付选项
     * 时间: 2024/8/20 17:05
     * <AUTHOR>
     */
    function notAdvanceOn() {
        if (props.sceneAdvance == 0) {
            setOptions(['2']);
            onSelectPrepay('2');
        }
    }

    /**
     * 注释: 请求优惠信息
     * 时间: 2023/6/26 0026 11:53
     * <AUTHOR>
     */
    function queryCarrierDiscounts(orderId: string, advanceServiceMoney: string, advanceType: string) {
        let req = new ReqQueryCarrierDiscounts();
        req.orderId = orderId;
        req.advanceServiceMoney = advanceServiceMoney;
        req.advanceType = advanceType;
        req.request().then((response) => {
            if (response.isSuccess() && TextUtils.equals('1', response.data?.showFlag)) {
                setCarrierDiscountsMsg(response.data?.showMsg);
            }
        });
    }

    /**
     * 预付轨迹校验提示优化并增加回访
     */
    function queryAdvanceApplyConsult() {
        let req = new ReqAdvanceApplyConsult();
        req.orderId = props.shipmentsEGoodInfo.orderId;
        req.request().then((response) => {
            Method.showDialogToast(response.getMsg());
        });
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2023/6/20 0020 11:43
     * <AUTHOR>
     */
    function renderMainView() {
        if (TextUtils.equals('1', props.advanceInfo?.isAdvance)) {
            return renderAdvanceView();
        } else {
            return renderNoSupportAdvanceView();
        }
    }

    /**
     * 注释: “次单优惠券”活动/单单打卡活动
     * 时间: 2024/4/11 15:23
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderCouponActivityTipsView() {
        if (UserType.isCarrier() && showActivity && (props.sceneAdvance == 0 || props.sceneAdvance == 2) && isAdvanceButtonOn) {
            return TextUtils.isNoEmpty(props.advanceInfo.orderRecordCouponJson) ? (
                <View style={styles.couponActivityTips2}>
                    <UIImage source={'http://img.zczy56.com/202407231049420666420.png'} style={{width: 19, height: 14}} />
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#333',
                        }}>{`连续使用${props.advanceInfo.orderRecordCouponJson?.remainRecordCount}次赠`}</Text>
                    {discountMoneyTop ? <Text style={{fontSize: 13, color: '#333'}}>{`满${discountMoneyTop}元减`}</Text> : <></>}
                    <Text style={{fontSize: 13, color: '#333'}}>
                        <Text style={{color: '#F87124'}}>{giveMoney}</Text>抵用券
                    </Text>
                    {maxMoney ? (
                        <Text style={{fontSize: 13, color: '#333'}}>
                            ，最高<Text style={{color: '#F87124'}}>{maxMoney}</Text>
                        </Text>
                    ) : (
                        <></>
                    )}
                </View>
            ) : (
                <View style={styles.couponActivityTips}>
                    <UIImage source={'http://img.zczy56.com/202404111453493344746.png'} style={{width: 38.5, height: 24}} />
                    <View style={styles.gap} />
                    <Text style={{fontSize: 9, color: '#666', flexWrap: 'wrap', flex: 1}}>
                        此单购买预付即赠
                        {discountMoneyTop && (
                            <Text style={{fontSize: 12, color: '#333'}}>
                                满<Text style={{fontSize: 12, color: '#FF1414'}}>{discountMoneyTop}</Text>减
                            </Text>
                        )}
                        <Text style={{fontSize: 12, color: '#FF1414'}}>{giveMoney}</Text>
                        <Text style={{fontSize: 12, color: '#333'}}>抵用券1张</Text>
                        <Text style={{fontSize: 9, color: '#666'}}>(下单可用）</Text>
                    </Text>
                    {maxMoney ? (
                        <View style={{backgroundColor: '#ff6a4f', borderRadius: 10, alignSelf: 'flex-start', marginTop: 5}}>
                            <Text style={{fontSize: 10, color: '#fff'}}>{` 最高${maxMoney}元 `}</Text>
                        </View>
                    ) : (
                        <></>
                    )}
                    <View style={{width: 68, alignItems: 'flex-end'}}>
                        <Text style={{fontSize: 9, color: '#666'}}>券有效期截至</Text>
                        <Text style={{fontSize: 11, color: '#666'}}>{endTime ?? ''}</Text>
                    </View>
                </View>
            );
        }
    }

    /**
     * 注释: 绘制预付视图
     * 时间: 2023/6/20 0020 11:45
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderAdvanceView() {
        return (
            <View style={{backgroundColor: '#fff'}}>
                {/*绘制智运卡选择*/}
                {renderSmartTransportCard()}
                {/*预付选择提示*/}
                {renderAdvanceSelectTip()}
                {/*绘制是否预付选择*/}
                {renderAdvanceSelectView()}
                {/*绘制核桃信用分提示*/}
                {renderCreditTip()}
                {/*绘制证件提示*/}
                {renderRiskAuditTip()}
                {/*绘制预付套餐选择*/}
                {renderAdvanceChangeView()}
                {/*“次单优惠券”活动*/}
                {showCouponActivityTipsView && renderCouponActivityTipsView()}
                {/*油气比例视图*/}
                {renderOilGasRatioView()}
                {/*路径规划视图*/}
                {renderShowOilLineView()}
                {/*绘制预付奖励提示*/}
                {renderAdvanceRewardView()}
                {/*绘制优惠券*/}
                {renderCouponView()}
                {/*绘制优惠计算结果*/}
                {renderDiscountResultView()}
                {/*绘制预付挽留弹窗*/}
                {/*{showRetentionDialog && renderRetentionDialog()}*/}
            </View>
        );
    }

    //
    // /**
    //  * 注释: 绘制预付挽留弹窗
    //  * 时间: 2023/6/30 0030 8:55
    //  * <AUTHOR>
    //  * @returns {JSX.Element}
    //  */
    // function renderRetentionDialog() {
    //     return (
    //         <AdvanceRetentionDialog
    //             preferentialMoney={carrierDiscountsMsg}
    //             advanceWay={advanceWay}
    //             advanceInfo={props.advanceInfo}
    //             onBuyCallBack={() => {
    //                 onSelectPrepay('1');
    //                 setShowRetentionDialog(false);
    //             }}
    //             onNoBuyCallBack={() => {
    //                 onSelectPrepay('2');
    //                 setShowRetentionDialog(false);
    //             }}
    //         />
    //     );
    // }

    /**
     * 注释: 25590    FBIS-6039    【安卓】司机开通信用分提示语-预付
     * @returns {JSX.Element}
     */
    function renderCreditTip() {
        return (
            isAdvanceButtonOn &&
            !TextUtils.isEmpty(props.advanceInfo.walnutCreditRatio) && (
                <Text
                    style={{
                        color: Constant.color_66666,
                        fontSize: 13,
                        marginLeft: 14,
                        marginRight: 14,
                        marginTop: 6,
                        marginBottom: 6,
                    }}>
                    开通核桃信用分此单预计可节省约<Text style={{color: '#F87124', fontSize: 13}}>{`${props.advanceInfo?.walnutCreditRatio}%`}</Text>预付服务费
                </Text>
            )
        );
    }

    /**
     * 注释: 绘制风险审核状态提示
     * 时间: 2023/8/8 0008 14:49
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderRiskAuditTip() {
        if (props.advanceInfo.documentPromptObj?.documentPromptFlag == '1') {
            return (
                <Text
                    style={{
                        color: Constant.color_66666,
                        fontSize: 13,
                        marginLeft: 14,
                        marginRight: 5,
                        marginTop: 8,
                        marginBottom: 13,
                    }}>
                    <UIImage source={'icon_renzhen'} style={{width: 17, height: 14}} />
                    <Text>{props.advanceInfo.documentPromptObj?.documentPromptTips}</Text>
                </Text>
            );
        }
    }

    /**
     * 注释: 绘制智运卡视图
     * 时间: 2023/6/20 0020 13:43
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderSmartTransportCard() {
        //个体司机非指定订单
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType);
        if (individualDriver && showShipCard) {
            return <ShowPaySelectCardView shipCard={shipCard!} onSelectCardCallback={onSelectCardCallback} />;
        }
    }

    /**
     * 注释: 预付选择提示
     * 时间: 2023/6/20 0020 13:56
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderAdvanceSelectTip() {
        //个体司机非指定订单 or 个体司机关联车老板模式
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType) || TextUtils.equals('4', props.advanceInfo.advanceType);
        //是否是0担货
        let isZeroOrder = TextUtils.equals(props.shipmentsEGoodInfo.goodsSource, '2');
        if (individualDriver && !isZeroOrder && isAdvanceButtonOn) {
            return (
                <View
                    style={{
                        flex: 1,
                        paddingLeft: 10,
                        paddingRight: 10,
                        paddingTop: 5,
                        paddingBottom: 5,
                        minHeight: DP(80),
                        backgroundColor: '#F7F7FB',
                        marginHorizontal: 10,
                        borderRadius: 4,
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center', paddingLeft: 10, paddingRight: 10}}>
                        <UIImage source={'http://img.zczy56.com/202406181651231891536.png'} style={{width: 12, height: 12, marginRight: 5}} />
                        <Text style={{fontSize: 13, color: '#333', fontWeight: '900'}}>服务须知</Text>
                    </View>
                    <Text
                        style={{
                            paddingLeft: 10,
                            paddingRight: 10,
                            paddingTop: 5,
                            paddingBottom: 5,
                            color: '#666',
                            fontSize: SP(26),
                        }}>
                        {prepayRatioTxt};依据法律规定，各类骗取预付款的行为涉嫌诈骗，需承担相应的法律责任
                    </Text>
                </View>
            );
        }
    }

    /**
     * 注释: 绘制预付选择视图
     * 时间: 2023/6/20 0020 13:53
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderAdvanceSelectView() {
        //是否是0担货
        let isZeroOrder = TextUtils.equals(props.shipmentsEGoodInfo.goodsSource, '2');
        if (!isZeroOrder && props.sceneAdvance !== 1 && props.sceneAdvance !== 2) {
            return (
                <View style={[gStyle.view_row, gStyle.view_padding]}>
                    <Text style={[gStyle.txt_333333_34, {flex: 1}]}>预付服务</Text>
                    <UIRadioButton
                        options={options}
                        selectedOption={isAdvanceButtonOn ? '1' : '2'}
                        onSelect={(item) => {
                            // if (TextUtils.equals('2', item)) {
                            //     setShowRetentionDialog(true);
                            // } else {
                            onSelectPrepay(item);
                            // }
                        }}
                        showTxt={(item) => {
                            return item === '1' ? LanguageType.getTxt('需要') : LanguageType.getTxt('不需要');
                        }}
                    />
                </View>
            );
        }
    }

    /**
     * 注释: 绘制预付套餐选择视图
     * 时间: 2023/6/20 0020 14:02
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderAdvanceChangeView() {
        //个体司机非指定订单
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType);
        if (individualDriver && isAdvanceButtonOn) {
            return (
                <ShipmentsSelectAdvanceWayView
                    sceneAdvance={props.sceneAdvance}
                    shipmentsEGoodInfo={props.shipmentsEGoodInfo}
                    advanceInfo={props.advanceInfo}
                    favourableTxt={favourableTxt}
                    advanceMoneyConTxt={advanceMoneyConTxt}
                    advanceMoneyOilConTxt={advanceMoneyOilConTxt}
                    smartTransportCardStyle={smartTransportCardStyle}
                    onSelectAdvanceWay={onSelectAdvanceWay}
                />
            );
        }
    }

    /**
     * 注释: 油气比例视图
     * 时间: 2023/6/20 0020 14:16
     * <AUTHOR>
     */
    function renderOilGasRatioView() {
        //个体司机非指定订单
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType);
        if (advanceWay && individualDriver && isAdvanceButtonOn && TextUtils.equals('1', props.advanceInfo.oilSelectBySelf)) {
            return <ShowPlayOilMoneyRadio oilRatio={oilRatio} advanceInfo={props.advanceInfo} advanceWay={advanceWay} onSelectRatio={onSelectRatio} />;
        }
    }

    /**
     * 注释: 绘制路径规划
     * 时间: 2023/6/20 0020 14:21
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderShowOilLineView() {
        //个体司机非指定订单
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType);
        if (advanceWay && individualDriver && isAdvanceButtonOn && (TextUtils.equals('2', advanceWay.type) || TextUtils.equals('3', advanceWay.type))) {
            return <ShowOilLineView orderId={`${props.shipmentsEGoodInfo.orderId}`} />;
        }
    }

    /**
     * 注释: 绘制预付奖励提示
     * 时间: 2023/6/20 0020 14:24
     * <AUTHOR>
     */
    function renderAdvanceRewardView() {
        if (awardTips && isAdvanceButtonOn) {
            return (
                <View style={{paddingHorizontal: 10, paddingBottom: 5}}>
                    {/*油气比例奖励*/}
                    {TextUtils.equals('1', awardTips.oilRewardRatioFlag) && (
                        <Text
                            style={[
                                gStyle.view_padding,
                                {
                                    backgroundColor: '#F3F7FE',
                                    fontSize: SP(22),
                                    justifyContent: 'center',
                                },
                            ]}>
                            <UIImage source={'icon_award_tips_left'} style={{width: DP(26.5), height: DP(31.5)}} />
                            预计可获得
                            <Text
                                style={{
                                    color: '#FF602E',
                                    fontSize: SP(22),
                                }}>{`${awardTips.rewardOilRatio}%(柴油)奖励`}</Text>
                            （以实际到账奖励为准）
                        </Text>
                    )}
                    {/*现金奖励*/}
                    {TextUtils.equals('1', awardTips.rewardOilCashMoneyFlag) && (
                        <Text
                            style={{
                                backgroundColor: '#F3F7FE',
                                color: '#999999',
                                fontSize: SP(22),
                                paddingHorizontal: 10,
                                paddingVertical: 8,
                            }}>
                            最高可奖励
                            <Text style={{color: '#FB6B40', fontSize: SP(22)}}>{awardTips.rewardOilCashMoney}</Text>
                            元，最终依据结算运费结算您的油品现金奖励金额，并和运费一起支付到您的智运账本，请注意查收
                        </Text>
                    )}
                    {(TextUtils.equals('1', awardTips.oilRewardRatioFlag) || TextUtils.equals('1', awardTips.rewardOilCashMoneyFlag)) && (
                        <Text
                            style={{
                                backgroundColor: '#F3F7FE',
                                color: '#999999',
                                fontSize: SP(22),
                                paddingHorizontal: 10,
                                marginTop: 5,
                                paddingVertical: 8,
                            }}>
                            运单结算时，若油品预付金额超出最大可配油比例，按以下规则处理： 若油品未全部消费，超出比例的部分将自动转为现金。若油品已全部消费，超出比例的部分不再兑现奖励。
                        </Text>
                    )}
                </View>
            );
        } else if (!TextUtils.equals('1', advanceWay?.type)) {
            return (
                <Text
                    style={{
                        backgroundColor: '#F3F7FE',
                        color: '#999999',
                        fontSize: SP(22),
                        paddingHorizontal: 10,
                        marginTop: 5,
                        paddingVertical: 8,
                    }}>
                    运单结算时，若油品预付金额超出最大可配油比例，按以下规则处理： 若油品未全部消费，超出比例的部分将自动转为现金。
                </Text>
            );
        }
    }

    /**
     * 注释: 绘制优惠券视图
     * 时间: 2023/6/20 0020 14:29
     * <AUTHOR>
     */
    function renderCouponView() {
        //个体司机非指定订单
        let individualDriver = TextUtils.equals('2', props.advanceInfo.advanceType);
        if (individualDriver && isAdvanceButtonOn) {
            return <ShowUserCouponView ref={couponViewRef} shipmentsEGoodInfo={props.shipmentsEGoodInfo} advanceInfo={props.advanceInfo} advanceWay={advanceWay} serverMoney={serverMoney} callBack={(couponInfo) => setUserCoupon(couponInfo)} />;
        }
    }

    /**
     * 注释: 绘制优惠结果
     * 时间: 2023/6/20 0020 14:38
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderDiscountResultView() {
        if (selectShipCard && discountResult && isAdvanceButtonOn && TextUtils.equals('2', props.advanceInfo.advanceType)) {
            return <ShowCardFavourableView serverMoney={serverMoney} cardType={selectShipCard.cardType ?? ''} discountRatio={selectShipCard.discountRatio ?? 0} />;
        }
    }

    /**
     * 注释: 绘制不支持预付视图
     * 时间: 2023/6/20 0020 11:41
     * <AUTHOR>
     */
    function renderNoSupportAdvanceView() {
        if (TextUtils.isNoEmpty(props.advanceInfo.noAdvanceReason) && !TextUtils.equals('1149', props.advanceInfo.noAdvanceCode)) {
            return (
                <View style={{backgroundColor: '#fff', marginTop: 5}}>
                    <View style={[gStyle.view_row, gStyle.view_padding, {alignItems: 'center', justifyContent: 'center'}]}>
                        <Text style={[gStyle.txt_333333_34, {flex: 1}]}>预付服务</Text>
                        <Text style={{color: '#666666', fontSize: 14}} onPress={() => setNoPrepayOn(!noPrepayOn)}>
                            您暂时无法使用预付
                            <UIImage style={{width: 15, height: 15}} source={'base_blue_question'} />
                        </Text>
                    </View>
                    {noPrepayOn && (
                        <View
                            style={{
                                backgroundColor: '#F7F7FB',
                                padding: 7,
                                marginLeft: 7,
                                marginRight: 7,
                                marginBottom: 7,
                                borderRadius: 4,
                            }}>
                            <Text style={{color: '#666666', fontSize: 14}}>{props.advanceInfo.noAdvanceReason}</Text>
                            {TextUtils.equals('21161', props.advanceInfo.noAdvanceCode) && (
                                <Text
                                    style={styles.payToGo}
                                    onPress={() => {
                                        //对账支付
                                        RouterUtils.skipRouter(RouterUrl.WisdomReconciliationRouter);
                                    }}>
                                    {'去还款＞'}
                                </Text>
                            )}
                            {TextUtils.equals('21383', props.advanceInfo.noAdvanceCode) && (
                                <Text
                                    style={styles.payToGo}
                                    onPress={() => {
                                        //FBIS-9865 预付轨迹校验提示优化并增加回访
                                        queryAdvanceApplyConsult();
                                    }}>
                                    {'预付申请咨询 >'}
                                </Text>
                            )}
                        </View>
                    )}
                </View>
            );
        } else {
            return (
                <View style={[gStyle.view_row, gStyle.view_padding, {marginTop: 5}]}>
                    <Text style={[gStyle.txt_333333_34, {flex: 1}]}>预付服务</Text>
                    <UIRadioButton
                        edit={false}
                        options={['2', '1']}
                        selectedOption={'2'}
                        showTxt={(item) => {
                            return item === '1' ? '需要' : '不需要';
                        }}
                    />
                </View>
            );
        }
    }

    const onSelectPrepay = (item) => {
        /**** 司机是否选择了非指定预付款 1 选择 2 未选择*/
        setIsAdvanceButtonOn(TextUtils.equals('1', item));
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {
            key: 'isAdvanceButtonOn',
            value: TextUtils.equals('1', item),
        });
        // 埋点
        onEvent({
            pageId: 'ShipmentsTerracePay',
            tableId: 'onSelectPrepay_' + item,
            event: 'click',
            requestParam: {
                orderid: props.shipmentsEGoodInfo?.orderId,
                proActId: props.advanceInfo?.activityInfo?.activityId,
            },
        });
    };

    /**
     * 注释: 查询优惠券可用数量
     * 时间: 2023/6/27 0027 19:01
     * <AUTHOR>
     */
    function queryAvailableCouponsNumber(serverMoney: string, advanceWay: EAdvanceType, cutAfterAdvanceServiceMoney?: string) {
        //更新UI
        setSelectAdvanceType(serverMoney, advanceWay, undefined, cutAfterAdvanceServiceMoney);
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'hiddenWaitDialog'});
        couponViewRef.current?.cleanStatus(serverMoney);
    }

    /**
     * 注释: 预付套餐切换
     * 时间: 2023/6/26 0026 14:43
     * <AUTHOR>
     * @param advanceWay
     */
    const onSelectAdvanceWay = (advanceWay: EAdvanceType) => {
        //切换预付方式
        setAdvanceWay(advanceWay);
        //通知父级页面预付方式更新
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'setAdvanceWay', value: advanceWay});
        switch (advanceWay.type) {
            case '1': {
                //现金预付
                setPrepayRatioTxt('选择预付，平台审核后将提前收到一部分现金');
                //现金预防不显示油品奖励
                setAwardTips(undefined);
                //显示智运卡优惠，如果选中
                setDiscountResult(true);
                //显示次单优惠券信息
                setShowCouponActivityTipsView(true);
                //重置服务费
                setServerMoney(NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney ?? '0'));
                setCutAfterAdvanceServiceMoney(undefined);
                EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'showWaitDialog'});
                //请求优惠卷可用数量
                queryAvailableCouponsNumber(props.advanceInfo.newAdvanceServiceMoney ?? '0', advanceWay);

                // 埋点
                onEvent({
                    pageId: 'ShipmentsTerracePay',
                    tableId: '#tv_moneny_terracepay',
                    event: 'click',
                    requestParam: {orderid: props.shipmentsEGoodInfo?.orderId},
                });

                break;
            }
            case '2': {
                //油品预付
                setPrepayRatioTxt('选择预付，装货后将提前收到一部分油费');
                //不显示智运卡优惠
                setDiscountResult(false);
                //不显示次单优惠券信息
                setShowCouponActivityTipsView(false);
                //如果有选中优惠劵，则取消
                if (userCoupon) {
                    setUserCoupon(undefined);
                }
                queryServiceMoney(oilRatio, advanceWay);
                //重置服务费
                setServerMoney(NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney ?? '0'));
                setCutAfterAdvanceServiceMoney(undefined);
                //更新UI
                setSelectAdvanceType(props.advanceInfo.newAdvanceServiceMoney ?? '0', advanceWay);
                // 埋点
                onEvent({
                    pageId: 'ShipmentsTerracePay',
                    tableId: '#tv_oil_terracepay',
                    event: 'click',
                    requestParam: {orderid: props.shipmentsEGoodInfo?.orderId},
                });

                break;
            }
            case '3': {
                //油品+现金预付
                setPrepayRatioTxt('选择预付，装货后将提前收到一部分现金和油费');
                //显示智运卡优惠，如果选中
                setDiscountResult(true);
                //显示次单优惠券信息
                setShowCouponActivityTipsView(true);
                //查询预付服务费
                queryServiceMoney(oilRatio, advanceWay);
                //发通知油品比例,通知一下协议
                EventBus.getInstance().fireEvent(Constant.event_shipments_page, {
                    key: 'selectOilRatio',
                    value: oilRatio,
                });
                // 埋点
                onEvent({
                    pageId: 'ShipmentsTerracePay',
                    tableId: '#tv_oil_moneny_terracepay',
                    event: 'click',
                    requestParam: {orderid: props.shipmentsEGoodInfo?.orderId},
                });
                break;
            }
        }
    };

    /**
     *  注释: 切换油品比例
     * 时间: 2023/6/27 0027 19:30
     * <AUTHOR>
     * @param ratio
     */
    const onSelectRatio = (ratio) => {
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'selectOilRatio', value: ratio});
        setOilRatio(ratio);
        if (advanceWay && !TextUtils.equals('1', advanceWay?.type)) {
            queryServiceMoney(ratio, advanceWay);
        }
    };

    /**
     * 注释: 查询预付费用
     * 时间: 2023/6/27 0027 18:40
     * <AUTHOR>
     */
    function queryServiceMoney(oilRatio: number, advanceWay: EAdvanceType) {
        // 查询一下预付费用
        if (TextUtils.equals('1', props.advanceInfo.oilSelectBySelf) && advanceWay) {
            EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'showWaitDialog'});
            // 新模式，有油气比例选择
            let request = new ReqAdvanceCashOilServiceMoney();
            request.orderId = props.shipmentsEGoodInfo.orderId;
            if (oilRatio != -1) {
                request.dlOilCardRatio = `${oilRatio}`;
            }
            request.advanceWay = advanceWay?.type;
            request.request().then((response) => {
                if (response.isSuccess() && response.data) {
                    setAwardTips(response.data);
                    setServerMoney(NumUtil.roundStr(response.data?.realAdvanceServiceMoney ?? '0'));
                    //如果有选中优惠劵，则取消
                    if (userCoupon) {
                        setUserCoupon(undefined);
                    }
                    let money = NumUtil.sub(parseFloat(response.data.realAdvanceServiceMoney ?? '0'), parseFloat(props.advanceInfo.couponMoney ?? '0'));
                    setCutAfterAdvanceServiceMoney(TextUtils.equals('3', props.advanceInfo.couponAmountType) ? (money > 0 ? NumUtil.roundStr(money) : '0') : response.data.cutAfterAdvanceServiceMoney);
                    //请求优惠卷可用数量
                    queryAvailableCouponsNumber(
                        response.data?.realAdvanceServiceMoney ?? '0',
                        advanceWay,
                        TextUtils.equals('3', props.advanceInfo.couponAmountType) ? (money > 0 ? NumUtil.roundStr(money) : '0') : response.data.cutAfterAdvanceServiceMoney,
                    );
                }
            });
        } else if (!TextUtils.equals('2', advanceWay.type.toString())) {
            EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'showWaitDialog'});
            //老模式 重新查询一下，预付服务费  油品预付不走
            let request = new ReqNewAdvanceServiceMoney();
            request.orderId = props.shipmentsEGoodInfo.orderId;
            request.detailId = props.shipmentsEGoodInfo.detailId;
            request.advanceWay = advanceWay.type;
            request.request().then((response) => {
                if (response.isSuccess()) {
                    setServerMoney(NumUtil.roundStr(response.data?.realAdvanceServiceMoney ?? '0'));
                    //如果有选中优惠劵，则取消
                    if (userCoupon) {
                        setUserCoupon(undefined);
                    }
                    let money = NumUtil.sub(parseFloat(response.data?.realAdvanceServiceMoney ?? '0'), parseFloat(props.advanceInfo.couponMoney ?? '0'));
                    setCutAfterAdvanceServiceMoney(TextUtils.equals('3', props.advanceInfo.couponAmountType) ? (money > 0 ? NumUtil.roundStr(money) : '0') : response.data?.cutAfterAdvanceServiceMoney);
                    //请求优惠卷可用数量
                    queryAvailableCouponsNumber(
                        response.data?.realAdvanceServiceMoney ?? '0',
                        advanceWay,
                        TextUtils.equals('3', props.advanceInfo.couponAmountType) ? (money > 0 ? NumUtil.roundStr(money) : '0') : response.data?.cutAfterAdvanceServiceMoney,
                    );
                }
            });
        }
    }

    const onSelectCardCallback = (type, value) => {
        //选择切换购买智运卡  type 1 月卡选中 2 年卡选中 0 都不选择
        if (type == 0) {
            setSelectShipCard(undefined);
        } else {
            setSelectShipCard(value);
            setDiscountResult(!TextUtils.equals('2', advanceWay?.type));
        }
    };

    /**
     * 注释: 选中智运卡优惠变更逻辑
     * 时间: 2023/6/27 0027 15:09
     * <AUTHOR>
     */
    function changeShipCard(serverMoney: string) {
        //智运卡
        if (selectShipCard) {
            //现金 优惠计算
            let discount = NumUtil.halfup(NumUtil.mul(parseFloat(props.advanceInfo.newAdvanceServiceMoney ?? '0'), selectShipCard.discountRatio ?? 0));
            discount = Math.max(discount, 0);
            setAdvanceMoneyConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        color: Constant.color_66666,
                        marginTop: 8,
                    }}>
                    服务费预估金额
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥${NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney)}元`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥${discount}元`}</Text>
                </Text>,
            );
            //现金+油卡 智运卡优惠计算
            let discountOil = NumUtil.halfup(NumUtil.mul(parseFloat(serverMoney), selectShipCard.discountRatio ?? 0));
            discountOil = Math.max(discountOil, 0);
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        color: Constant.color_66666,
                        marginTop: 8,
                    }}>
                    服务费预估金额
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥${serverMoney}元`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥${discountOil}元`}</Text>
                </Text>,
            );
            setSmartTransportCardStyle(true);
            setFavourableTxt(`智运卡${(selectShipCard.discountRatio ?? 0) * 10}折`);
        }
    }

    /**
     * 注释: 立减优惠变换逻辑
     * 时间: 2023/6/27 0027 15:57
     * <AUTHOR>
     * @param advanceWay
     * @param serverMoney
     * @param cutAfterAdvanceServiceMoney
     */
    function reductionDiscount(serverMoney: string, advanceWay: EAdvanceType, cutAfterAdvanceServiceMoney?: string) {
        if (TextUtils.equals('5', props.advanceInfo.couponAmountType)) {
            //优惠券活动
            handleCouponGiveActivity();
            return;
        }
        //现金 立减优惠计算
        let money = props.advanceInfo.getCouponToServerMoney(parseFloat(props.advanceInfo.newAdvanceServiceMoney ?? '0'));
        setAdvanceMoneyConTxt(
            <Text
                style={{
                    textAlign: 'center',
                    fontSize: SP(20),
                    marginTop: 8,
                }}>
                <Text style={{color: Constant.color_ff602e}}>{props.advanceInfo.couponContext}</Text>
                <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                <Text style={{textDecorationLine: 'line-through'}}>{`¥${NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney)}`}</Text>
                <Text style={{color: Constant.color_ff602e}}>{`¥${money.toFixed(2)}`}</Text>
            </Text>,
        );
        //现金+油品 立减优惠计算
        let moneyOil = props.advanceInfo.getCouponToServerMoney(parseFloat(props.advanceInfo.newAdvanceServiceMoney ?? '0'));
        if (cutAfterAdvanceServiceMoney) {
            moneyOil = parseFloat(cutAfterAdvanceServiceMoney ?? '0');
        }
        moneyOil = Math.max(moneyOil, 0);
        if (TextUtils.equals('3', advanceWay.type)) {
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        marginTop: 8,
                    }}>
                    <Text style={{color: Constant.color_ff602e}}>{props.advanceInfo.couponContext}</Text>
                    <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥${serverMoney}`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥${moneyOil.toFixed(2)}`}</Text>
                </Text>,
            );
        } else {
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        marginTop: 8,
                    }}>
                    <Text style={{color: Constant.color_ff602e}}>{props.advanceInfo.couponContext}</Text>
                    <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥0-¥${serverMoney}`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥0-¥${moneyOil.toFixed(2)}`}</Text>
                </Text>,
            );
        }
        setSmartTransportCardStyle(false);
        setFavourableTxt('立减优惠');
        //不使用智运卡,隐藏智运卡优惠
        setDiscountResult(false);
    }

    /**
     *  注释: 处理成交保险抵用券活动
     * 时间: 2024/4/11 15:43
     * <AUTHOR>
     */
    function handleCouponGiveActivity() {
        //无优惠
        setFavourableTxt(undefined);
        setAdvanceMoneyConTxt(
            <Text
                style={{
                    textAlign: 'center',
                    fontSize: SP(20),
                    marginTop: 8,
                    color: Constant.color_66666,
                }}>{`服务费预估金额¥${NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney)}元`}</Text>,
        );
        setAdvanceMoneyOilConTxt(
            <Text
                style={{
                    textAlign: 'center',
                    fontSize: SP(20),
                    marginTop: 8,
                    color: Constant.color_66666,
                }}>{`服务费预估金额¥${serverMoney}元`}</Text>,
        );
        setShowActivity(TextUtils.isEmpty(userCoupon?.userCouponId));
        let json;
        if (props.advanceInfo.nextCouponJson != null || props.advanceInfo.nextCouponJson != undefined) {
            json = props.advanceInfo.nextCouponJson;
        } else if (props.advanceInfo.orderRecordCouponJson != null || props.advanceInfo.orderRecordCouponJson != undefined) {
            json = props.advanceInfo.orderRecordCouponJson;
        }
        if (TextUtils.isEmpty(json)) {
            return;
        }
        if (TextUtils.equals('0', json?.nextCouponDiscountType)) {
            //比例
            setGiveMoney(`${json?.nextCouponDiscountRatioStr ?? ''}折`);
            setMaxMoney(json?.nextCouponDiscountMoneyTop ?? '');
        } else {
            //金额
            setDiscountMoneyTop(json?.nextCouponUseThreshold ? `${json?.nextCouponUseThreshold}元` : '');
            setGiveMoney(`${json?.nextCouponUnitMoney ?? ''}元`);
        }
        setEndTime(`${json?.nextCouponEndTimeStr ?? ''}`);
    }

    /**
     * 注释: 切换预付方式
     * 时间: 2023/6/27 0027 8:32
     * <AUTHOR>
     */
    function setSelectAdvanceType(serverMoney: string, advanceWay: EAdvanceType, userCoupon?: CouponInfo, cutAfterAdvanceServiceMoney?: string) {
        //取优惠方式 优惠使用顺序：自选优惠卷=>智运卡=>立减活动=>默认优惠卷=>无优惠
        if (userCoupon) {
            //自选优惠卷
            setFavourableTxt('优惠券折扣');
            //不使用智运卡，不显示智运卡优惠
            setDiscountResult(false);
            //不显示次单优惠券活动
            setShowActivity(false);
            //选择优惠卷处理
            handleCouponSelect(serverMoney, userCoupon);
        } else if (selectShipCard) {
            //不显示次单优惠券活动
            setShowActivity(false);
            //智运卡优惠
            changeShipCard(serverMoney);
        } else if (props.advanceInfo.isCouponShow()) {
            //立减活动
            reductionDiscount(serverMoney, advanceWay, cutAfterAdvanceServiceMoney);
        } else if (TextUtils.isNoEmpty(props.advanceInfo?.userCouponId) && !TextUtils.equals('3', advanceWay.type)) {
            //默认优惠卷
            setFavourableTxt('优惠券折扣');
            //不使用智运卡,不显示智运卡优惠
            setDiscountResult(false);
            //不显示次单优惠券活动
            setShowActivity(false);
        } else {
            //无优惠
            setFavourableTxt(undefined);
            //不显示次单优惠券活动
            setShowActivity(false);
            setAdvanceMoneyConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        marginTop: 8,
                        color: Constant.color_66666,
                    }}>{`服务费预估金额¥${NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney)}元`}</Text>,
            );
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        marginTop: 8,
                        color: Constant.color_66666,
                    }}>
                    {!TextUtils.equals('3', advanceWay.type) ? `服务费预估金额¥0~¥${serverMoney}元` : `服务费预估金额¥${serverMoney}元`}
                </Text>,
            );
        }
    }

    /**
     * 注释: 处理用户优惠卷选择
     * 时间: 2023/12/22 0022 22:37
     * <AUTHOR>
     */
    function handleCouponSelect(serverMoney: string, userCoupon: CouponInfo) {
        //设置现金优惠信息
        let money = parseFloat(props.advanceInfo.newAdvanceServiceMoney ?? '0') - parseFloat(userCoupon.couponMoney ?? '0');
        money = Math.max(money, 0);
        setAdvanceMoneyConTxt(
            <Text
                style={{
                    textAlign: 'center',
                    fontSize: SP(20),
                    color: Constant.color_66666,
                    marginTop: 8,
                }}>
                <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                <Text style={{textDecorationLine: 'line-through'}}>{`¥${NumUtil.roundStr(props.advanceInfo.newAdvanceServiceMoney)}`}</Text>
                <Text style={{color: Constant.color_ff602e}}>{`¥${money.toFixed(2)}`}</Text>
            </Text>,
        );
        //设置现金+油品优惠信息
        let moneyOil = parseFloat(serverMoney ?? '0') - parseFloat(userCoupon.couponMoney ?? '0');
        moneyOil = Math.max(moneyOil, 0);
        if (TextUtils.equals('3', advanceWay?.type)) {
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        color: Constant.color_66666,
                        marginTop: 8,
                    }}>
                    <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥${serverMoney}`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥${moneyOil.toFixed(2)}`}</Text>
                </Text>,
            );
        } else {
            setAdvanceMoneyOilConTxt(
                <Text
                    style={{
                        textAlign: 'center',
                        fontSize: SP(20),
                        marginTop: 8,
                    }}>
                    <Text style={{color: Constant.color_66666}}> {'\n服务费预估金额\n'}</Text>
                    <Text style={{textDecorationLine: 'line-through'}}>{`¥0-¥${serverMoney}`}</Text>
                    <Text style={{color: Constant.color_ff602e}}>{`¥0-¥${moneyOil.toFixed(2)}`}</Text>
                </Text>,
            );
        }
    }

    return renderMainView();
}

/**
 * 注释: 预付Ref定义
 * 时间: 2023/6/28 0028 17:51
 * <AUTHOR>
 */
export interface ShipmentsTerracePayRef {
    //设置油气比例
    setOilRatio: (oilRatio) => void;
    check: (shipmentUI: ShipmentUI) => boolean;
    notAdvanceOn: () => void;
}

export default React.forwardRef<ShipmentsTerracePayRef, Props>(ShipmentsTerracePay);

const styles = StyleSheet.create({
    payToGo: {
        fontSize: 13,
        color: '#5086fc',
        alignSelf: 'flex-end',
    },
    payToPayText: {
        color: '#333',
        fontSize: 13,
        justifyContent: 'flex-start',
        marginLeft: 7,
    },
    couponActivityTips: {
        width: 330,
        height: 45,
        alignSelf: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        paddingHorizontal: 5,
        backgroundColor: '#FFEEED',
        borderRadius: 3,
        marginTop: 10,
    },
    couponActivityTips2: {
        backgroundColor: '#FFF4E7',
        padding: 8,
        margin: 15,
        flexDirection: 'row',
        alignItems: 'center',
    },
    gap: {
        width: 0.5,
        height: 20,
        backgroundColor: '#b7b7b7',
        marginHorizontal: 5,
    },
});
