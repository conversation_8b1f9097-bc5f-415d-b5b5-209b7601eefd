import {Text, TouchableOpacity} from 'react-native';
import React, {forwardRef, useImperativeHandle} from 'react';
import UIImage from '../../../widget/UIImage';
import ShipmentBillVideoDialog from '../../views/ShipmentBillVideoDialog';
import {PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';
import {ShipmentUI} from '../models/ShipmentUI';
import TextUtils from '../../../util/TextUtils';
import { Method } from '../../../util/NativeModulesTools';

interface Props {
    deliverVideoArray?: PeopleVehicleVideoArr[]; //视频地址
    remark?: string;
    max: number; //最大
    edit: boolean;
}

/**
 * 注释:	WLHY-12652 🏷 【汽运】承运方支持上传视频
 * @constructor
 */
function ShipmentVideoRemarkView(props: Props, ref) {
    const [videoUrl, setVideoUrl] = React.useState(props.deliverVideoArray ?? []);
    const [remark, setRemark] = React.useState(props.remark);
    const [shoWVideoDialog, setShoWVideoDialog] = React.useState(false);

    const check = (shipmentUI: ShipmentUI) => {
        if (TextUtils.isNoEmpty(remark) && TextUtils.containsEmoji(remark)) {
            Method.showToast('备注信息不能输入表情等特殊符号！');
            return false;
        }
        shipmentUI.remark = remark;
        shipmentUI.deliverVideoArray = videoUrl;
        return true;
    };
   
    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    return (
        <TouchableOpacity
            style={{flex: 1, paddingLeft: 14, paddingRight: 14, paddingTop: 10, marginTop: 8,marginBottom:8, paddingBottom: 10, justifyContent: 'center', alignItems: 'center', flexDirection: 'row', backgroundColor: '#fff'}}
            onPress={() => setShoWVideoDialog(true)}>
            <Text style={{color: '#333', fontSize: 17}}>备注 (选填)</Text>
            <Text style={{color: '#999', fontSize: 17, flex: 1, textAlign: 'right'}}>文字或视频佐证说明</Text>
            <UIImage source="base_right_arrow_gray" style={{width: 6, height: 12, marginLeft: 5}} />
            {shoWVideoDialog && (
                <ShipmentBillVideoDialog
                    data={videoUrl}
                    remark={remark}
                    max={props.max}
                    edit={props.edit}
                    onClose={setShoWVideoDialog}
                    onCallback={(txt: string, data: PeopleVehicleVideoArr[]) => {
                        setShoWVideoDialog(false);
                        setRemark(txt);
                        setVideoUrl(data);
                    }}
                />
            )}
        </TouchableOpacity>
    );
}

export interface ShipmentVideoRemarkViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

export default forwardRef<ShipmentVideoRemarkViewRef, Props>(ShipmentVideoRemarkView);
