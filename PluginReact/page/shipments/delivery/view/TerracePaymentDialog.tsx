import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width, SP} from '../../../util/scaled-style';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import {AdvanceServiceMoney} from '../models/AdvanceServiceMoney';
import AdvanceInfoRsp from '../models/AdvanceInfoRsp';

interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp; //货物信息
    advanceInfo: AdvanceInfoRsp; //场景对象
    serviceMoney?: AdvanceServiceMoney; //预付服务费
    userPaymentMoney: string; //是否使用立减1 使用
    onClose: Function; //关闭方法
}

/**
 * 预付协议对话框
 * @param props
 * @constructor
 */
export default function TerracePaymentDialog(props: Props) {
    /**
     * 注释: 绘制预付比例
     * 时间: 2023/6/21 0021 15:41
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderAdvanceRatio() {
        if (props.shipmentsEGoodInfo.freightType === '1') {
            //1单价
            let pbcarriermoney = 0.0;
            if (props.shipmentsEGoodInfo.pbCarrierUnitMoney) {
                pbcarriermoney = parseFloat(props.shipmentsEGoodInfo.pbCarrierUnitMoney);
            }
            return <Text style={styles.freightAmountTxt}>{`运费金额：约${(pbcarriermoney * 100 * 0.01).toFixed(2)}元  单价(不含税)`}</Text>;
        } else if (props.shipmentsEGoodInfo.freightType === '0') {
            //包车价
            let pbcarriermoney = 0.0;
            if (props.shipmentsEGoodInfo.pbCarrierMoney) {
                pbcarriermoney = parseFloat(props.shipmentsEGoodInfo.pbCarrierMoney);
            }
            return <Text style={styles.freightAmountTxt}>{`运费金额：约${(pbcarriermoney * 100 * 0.01).toFixed(2)}元  包车价(不含税)`}</Text>;
        }
    }

    /**
     * 注释: 绘制预付服务费
     * 时间: 2023/6/21 0021 15:44
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderPrepaidServiceFee() {
        if (props.userPaymentMoney && (props.advanceInfo.couponAmountType === '1' || props.advanceInfo.couponAmountType === '2' || props.advanceInfo.couponAmountType === '3')) {
            try {
                //现金+油品预付时预估服务费
                let server = TextUtils.isEmpty(props.serviceMoney?.realAdvanceServiceMoney) ? 0.0 : parseFloat(props.serviceMoney?.realAdvanceServiceMoney ?? '0');
                if (server <= 0 || TextUtils.isEmpty(props.advanceInfo.couponMoney)) {
                    return <Text style={styles.freightAmountTxt}>{`运费预付服务费：约${props.serviceMoney?.realAdvanceServiceMoney}元（以实际运距计算为准）`}</Text>;
                } else {
                    let money = parseFloat(props.advanceInfo.couponMoney ?? '0');

                    if (money <= 0.0) {
                        return <Text style={styles.freightAmountTxt}>{`运费预付服务费：约${props.serviceMoney?.realAdvanceServiceMoney}元（以实际运距计算为准）`}</Text>;
                    } else {
                        //1:固定额度;2:固定金额折扣;3:随机减
                        //1:固定额度;2:固定金额折扣;3:随机减
                        if (TextUtils.equals('2', props.advanceInfo.couponAmountType)) {
                            if (money == 10.0 || money == 100.0) {
                                //全额抵扣
                                money = 0.0;
                            }
                            money = (money / 10.0) * server;
                        } else {
                            //保留2位 四舍五入
                            money = server - money;
                        }
                        money = money < 0.0 ? 0.0 : money;
                        return (
                            <Text style={styles.freightAmountTxt}>
                                {'运费预付服务费：￥'}
                                <Text
                                    style={{
                                        color: '#FB5959',
                                        fontSize: SP(25),
                                    }}>
                                    {` ${props.serviceMoney?.realAdvanceServiceMoney}￥${money.toFixed(2)}`}
                                </Text>
                            </Text>
                        );
                    }
                }
            } catch (e) {
                return <Text style={styles.freightAmountTxt}>{`运费预付服务费：约${props.serviceMoney?.realAdvanceServiceMoney}元（以实际运距计算为准）`}</Text>;
            }
        } else {
            return <Text style={styles.freightAmountTxt}>{`运费预付服务费：约${props.serviceMoney?.realAdvanceServiceMoney}元（以实际运距计算为准）`}</Text>;
        }
    }

    /**
     * 注释: 绘制积分提示
     * 时间: 2023/6/21 0021 15:45
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderPointsReminder() {
        if (TextUtils.equals('1', props.serviceMoney?.showFlag)) {
            //积分提示
            return <Text style={[styles.freightAmountTxt, {color: '#FB6B40'}]}>{props.serviceMoney?.showMsg}</Text>;
        }
    }

    /**
     * 注释:绘制标题
     * 时间: 2023/6/29 0029 17:12
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderTitleView() {
        return (
            <View style={styles.dialogTitleView}>
                <Text style={styles.dialogTitle}> 此单可享受预付款服务哦！</Text>
                <Text style={styles.dialogTitle2}>
                    确认发货后将给您预付<Text style={styles.dialogTitle3}>{`${props.advanceInfo.advanceRatio}`}</Text>
                    %运费！
                </Text>
            </View>
        );
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2023/6/29 0029 17:12
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderMainView() {
        return (
            <View style={styles.dialogContentView}>
                <Text style={styles.dialogContentTxt}>1.预付现金会收取服务费，服务费金额根据运单情况各有不同。</Text>
                <Text style={styles.dialogContentTxt}>2.申请取消运单时，如预付款已对账，服务费正常收取。</Text>
                <Text style={styles.dialogContentTxt}>3.扣除服务费后，预付款打款至对应账户。</Text>
                <Text style={styles.dialogContentTxt}>4.须绑定本人银行卡才可享受预付款服务。</Text>
                <Text style={styles.dialogContentTxt}>5.如遇特殊情况，预付服务将会暂停。</Text>
                <Text style={styles.dialogContentTxt}>6.如运单预付10天后未结算、轨迹异常、发起取消运单等情况会进行预付款项冻结；如冻结时您的预付款已对账，将会同时冻结服务费，后期如有资金转入会优先抵扣冻结款项。</Text>
                <Text style={styles.dialogContentTxt}>7.核桃信用分低于450分无法使用预付服务，请保持良好的承运习惯。</Text>
                <Text style={[styles.dialogContentTxt, {marginBottom: 17}]}>8.申请预付时系统同步校验车辆轨迹，若未能获取到车辆轨迹则无法申请使用预付服务。</Text>
                {renderAdvanceRatio()}
                {renderPrepaidServiceFee()}
                {/*{renderPointsReminder()}*/}
            </View>
        );
    }

    /**
     * 注释: 绘制按钮
     * 时间: 2023/6/29 0029 17:12
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderButton() {
        return (
            <Text
                style={styles.dialogBtnView}
                onPress={() => {
                    props.onClose && props.onClose();
                }}>
                我知道了
            </Text>
        );
    }

    return (
        <Modal
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}
            onBackButtonPress={() => {
                props.onClose && props.onClose();
            }} // 响应返回键
            onBackdropPress={() => {
                props.onClose && props.onClose();
            }} // 点击背景遮罩层
        >
            <View style={styles.paymentDialogBg}>
                <View style={styles.dialogView}>
                    {/*绘制标题*/}
                    {renderTitleView()}
                    {/*绘制主视图*/}
                    {renderMainView()}
                    {/*绘制按钮*/}
                    {renderButton()}
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    paymentDialogBg: {
        //全屏显示 半透明 可以看到之前的控件但是不能操作了
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },

    dialogView: {
        width: gScreen_width * 0.8,
        minHeight: gScreen_height * 0.24,
        backgroundColor: 'white',
        borderRadius: 8,
        flexDirection: 'column',
    },

    dialogTitleView: {
        width: gScreen_width * 0.8,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#ecf5ff',
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
        paddingTop: 10,
        paddingBottom: 10,
    },
    dialogTitle: {
        textAlign: 'center',
        fontSize: 17,
        color: '#5086fc',
    },
    dialogTitle2: {
        textAlign: 'center',
        fontSize: 14,
        color: '#999999',
        marginTop: 3,
    },
    dialogTitle3: {
        fontSize: 17,
        color: '#fb9c27',
    },
    dialogContentView: {
        padding: 17,
        display: 'flex',
        flexDirection: 'column',
        width: gScreen_width * 0.8,
        minHeight: gScreen_height * 0.08,
        alignItems: 'flex-start',
        justifyContent: 'center',
    },

    dialogContentTxt: {
        fontSize: 12,
        color: '#666666',
    },

    freightAmountTxt: {
        fontSize: 13,
        color: '#333333',
    },

    dialogBtnView: {
        color: '#fff',
        backgroundColor: '#5086fc',
        width: gScreen_width * 0.7,
        height: 38,
        alignSelf: 'center',
        textAlign: 'center',
        textAlignVertical: 'center',
        marginBottom: 17,
        borderRadius: 5,
    },
});
