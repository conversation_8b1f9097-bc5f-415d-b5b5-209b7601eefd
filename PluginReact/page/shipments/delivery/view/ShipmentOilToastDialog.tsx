import {Text, View} from 'react-native';
import React from 'react';
import UIImage from '../../../widget/UIImage';
import TextUtils from '../../../util/TextUtils';

interface Props {
    onClose?: (ok: boolean) => void;
    attributeMsg?: string;
}

/**
 * 注释:
 * 时间: 2024/9/26 14:36
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function ShipmentOilToastDialog(props: Props) {
    return (
        <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            {/*绘制头部视图*/}
            <View style={{backgroundColor: '#fff', borderRadius: 10, padding: 19, flexDirection: 'column'}}>
                {TextUtils.isNoEmpty(props.attributeMsg) && <Text style={{color: '#333', fontSize: 22, fontWeight: 'bold'}}>提交预付款审核</Text>}
                {TextUtils.isNoEmpty(props.attributeMsg) && <Text style={{color: '#333', fontSize: 12, marginTop: 10, marginBottom: 10}}>{props.attributeMsg}</Text>}
                <Text style={{color: '#333', fontSize: 18, fontWeight: 'bold'}}>本单油气品规则</Text>
                <Text style={{color: '#0047E1', fontSize: 15, marginTop: 20}}>一、消费规则</Text>
                <Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>
                    您收到油/气品金额后，{' '}
                    <Text
                        style={{
                            color: '#F44100',
                            fontSize: 12,
                        }}>
                        请尽快点击"优惠加油"消费，需在次年3月1日前完成消费；逾期未消费，平台将扣除未消费油/气品对应的经营成本(油：最高油品金额的35.5%/气：最高气品金额的33.6%)，剩余部分转入您的智运账本中。
                    </Text>
                </Text>
                <Text style={{color: '#0047E1', fontSize: 15, marginTop: 10}}>二、退油规则</Text>
                <Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>
                    1、运单结算后3天内申请退油，平台将扣除对应的<Text style={{color: '#F44100', fontSize: 12}}>经营成本(油：最高油品金额的5.5%/气：最高气品金额的3.6%)，</Text>余额转入您的智运账本中。
                </Text>
                <Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>
                    2、若运单结算完成时间超过3天提出退油申请，平台将扣除对应运单的<Text style={{color: '#F44100', fontSize: 12}}>经营成本(油：最高油品金额的35.5%/气：最高气品金额的33.6%)，</Text>余额转入您的智运账本中。
                </Text>

                <Text
                    style={{
                        color: '#fff',
                        fontSize: 14,
                        marginTop: 28,
                        textAlign: 'center',
                        height: 40,
                        lineHeight: 40,
                        backgroundColor: '#5086FC',
                        borderRadius: 20,
                    }}
                    onPress={() => {
                        props.onClose && props.onClose(true);
                    }}>
                    我已知悉并同意，继续确认预付
                </Text>
                <Text
                    style={{
                        color: '#5086FC',
                        fontSize: 14,
                        marginTop: 10,
                        textAlign: 'center',
                        height: 40,
                        lineHeight: 40,
                    }}
                    onPress={() => {
                        props.onClose && props.onClose(false);
                    }}>
                    不同意
                </Text>
            </View>
            <UIImage source={'http://img.zczy56.com/202409260823474555371.png'} style={{width: 100, height: 100, position: 'absolute', right: 0, top: 80}} />
        </View>
    );
}
