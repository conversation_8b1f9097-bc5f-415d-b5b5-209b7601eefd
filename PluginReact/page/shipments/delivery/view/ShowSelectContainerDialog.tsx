import React, {useEffect, useState} from 'react';
import {Method} from '../../../util/NativeModulesTools';
import Modal from 'react-native-modal';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {EContainer, EGoodInfo} from '../models/ShipmentsEGoodInfoRsp';
import {ReqQueryContainerDetailListByOrderId} from '../requests/ReqQueryContainerDetailListByOrderId';
import UIImage from '../../../widget/UIImage';
import UIButton from '../../../widget/UIButton';
import TextUtils from '../../../util/TextUtils';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';
/**
 * 集装箱选择对话框
 * @param {*} item  item 点击列表
 * @param {*} pageList  集装箱集合
 * @param {*} onClickClose  对话框关闭回调
 *
 * @returns
 */
interface Props {
    orderId: string;
    goodInfo: EGoodInfo;
    onClickClose: Function;
    onClickOk: Function;
}

export default function ShowSelectContainerDialog(props: Props) {
    //当前选择集装箱
    const [selectData, setSelectData] = useState<EContainer[]>([]);

    //集装箱列表{pageList:[]}
    const [pageList, setPageList] = useState<EContainer[]>([]);

    const onClick = () => {
        //点击确定选择
        props.goodInfo.tempContainerSize = selectData;

        props.onClickOk && props.onClickOk(props.goodInfo);
    };

    const onItemSelect = (item) => {
        let find = selectData.find((value) => TextUtils.equals(value.containerNo, item.containerNo));
        if (find) {
            //已经存在，移除
            let list = selectData.filter((value) => !TextUtils.equals(value.containerNo, item.containerNo));
            setSelectData(list);
        } else {
            if (selectData.length >= parseInt(props.goodInfo.weight)) {
                Method.showToast('最多只能选择' + props.goodInfo.weight + '箱');
                return;
            }
            let list = [...selectData, item];
            setSelectData(list);
        }
    };

    useEffect(() => {
        //查询
        let query = async () => {
            let response = await new ReqQueryContainerDetailListByOrderId(props.orderId).request();
            if (response.isSuccess() && response.data) {
                setPageList(response.data.rootArray);
            } else {
                Method.showToast(response.msg);
            }
        };
        query();
    }, []);

    return (
        <Modal
            style={{justifyContent: 'flex-end', padding: 0, margin: 0}}
            animationIn={'slideInUp'}
            backdropOpacity={0.3}
            isVisible={pageList != null}
            onBackButtonPress={() => props.onClickClose && props.onClickClose()} // 响应返回键
            onBackdropPress={() => props.onClickClose && props.onClickClose()} // 点击背景遮罩层
        >
            <View style={{backgroundColor: '#fff', margin: 0, maxHeight: 300}}>
                <View style={gStyle.view_row}>
                    <Text style={gStyle.txt_333333_34}>选择集装箱号</Text>
                </View>
                <View style={gStyle.view_row}>
                    <UIImage style={{height: 40, width: 40, marginLeft: 10}} source={'order_container_icon'} />
                    <View style={{flex: 1, alignItems: 'flex-start', paddingLeft: 10}}>
                        <Text style={gStyle.txt_333333_34}>{props.goodInfo.cargoName}</Text>
                        <Text style={gStyle.txt_666666_24}>{`${props.goodInfo.money}元/箱`}</Text>
                    </View>
                </View>
                <GridView>
                    {pageList.map((value) => {
                        let check = selectData.find((item) => TextUtils.equals(item.containerNo, value.containerNo));
                        return (
                            <UITouchableOpacity onPress={() => onItemSelect(value)} style={{flex: 1, marginRight: 4}} key={value.containerNo}>
                                <View
                                    style={{
                                        backgroundColor: check ? '#f0f6ff' : '#f9f9fa',
                                        borderRadius: 2,
                                        padding: 2,
                                        marginBottom: 4,
                                        borderColor: check ? '#1b42fd' : '#f9f9fa',
                                        borderWidth: 1,
                                    }}>
                                    <Text style={gStyle.txt_666666_24}>{value.containerNo}</Text>
                                    <Text style={gStyle.txt_999999_22}>{value.containerUnitWeight + props.goodInfo.getCargoCategory()}</Text>
                                </View>
                            </UITouchableOpacity>
                        );
                    })}
                </GridView>
                <UIButton
                    text={'确定'}
                    fontSize={16}
                    height={43}
                    borderRadius={43}
                    style={{marginLeft: 20, marginRight: 20, marginBottom: 7}}
                    onPress={() => {
                        onClick();
                    }}
                />
            </View>
        </Modal>
    );
}

export const GridView = ({children}) => {
    let numColumns = 2;
    const columnCount = Math.ceil(children.length / numColumns);
    return (
        <ScrollView>
            <View style={styles.grid}>
                {[...Array(columnCount)].map((_, i) => (
                    <View key={i} style={styles.column}>
                        {children.slice(i * numColumns, (i + 1) * numColumns)}
                    </View>
                ))}
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    grid: {
        padding: 10,
        flexDirection: 'column',
        justifyContent: 'flex-start',
    },
    column: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
});
