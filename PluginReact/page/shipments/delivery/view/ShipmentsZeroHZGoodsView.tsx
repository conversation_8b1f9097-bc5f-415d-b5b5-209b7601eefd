//确认发货，货物明细+实际发货量
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import {ShipmentUI} from '../models/ShipmentUI';
import {gStyle} from '../../../util/comm-style';
import UIImage from '../../../widget/UIImage';
import ZeroGoodsInfoView from './ZeroGoodsInfoView';
import ShipmentsEZeroGoodInfoRsp, {BacthZeroGoodInfo} from '../models/ShipmentsEZeroGoodInfoRsp';
import {gScreen_width} from '../../../util/scaled-style';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';
import {Method} from '../../../util/NativeModulesTools';
export interface ShipmentsZeroHZGoodsViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

interface Props {
    shipmentsEGoodInfo: ShipmentsEZeroGoodInfoRsp; //货物信息
    editInput: boolean;
    style?: StyleProp<ViewStyle>;
}

function ShipmentsZeroHZGoodsView(props: Props, ref) {
    //货物明细
    const [goodInfos, setGoodInfos] = useState(props.shipmentsEGoodInfo?.ltlCargoArray ?? []);
    const [showInfoView, setShowInfoView] = useState(true);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        let newGoodInfos = new Array<BacthZeroGoodInfo>();
        let size = goodInfos.length;
        for (let i = 0; i < size; i++) {
            let childItemList = goodInfos[i].cargoArray;
            //过滤出未输入
            let emptyList = childItemList.filter((value, index) => TextUtils.isEmpty(value.beforeDeliverCargoWeight)) ?? [];

            if (emptyList.length > 0) {
                //当前这个运单存在没有输入吨位,当前项不上传
            } else {
                for (let j = 0; j < childItemList.length; j++) {
                    let beforeDeliverCargoWeight = childItemList[j].beforeDeliverCargoWeight;

                    if (parseFloat(`${beforeDeliverCargoWeight}`) <= 0.0) {
                        Method.showToast('[' + childItemList[j].cargoName + ']货物,货物计量不能小于等于0，请重新确认！');
                        return false;
                    }

                    if (TextUtils.equals('1', childItemList[j].unit) && TextUtils.isNoEmpty(childItemList[j].weightOrientationLimit)) {
                        //重货
                        if (parseFloat(`${beforeDeliverCargoWeight}`) > parseFloat(childItemList[j].weightOrientationLimit)) {
                            //zczy-13915_发货吨位提示错误
                            Method.showToast(`${childItemList[j].cargoName}货物，货物计量不能大于${childItemList[j].weightOrientationLimit}，请重新确认`);
                            return false;
                        }
                    } else if (TextUtils.equals('2', childItemList[j].unit) && TextUtils.isNoEmpty(childItemList[j].shippingOrientationMargin)) {
                        //泡货
                        if (parseFloat(`${beforeDeliverCargoWeight}`) > parseFloat(childItemList[j].shippingOrientationMargin)) {
                            //zczy-13915_发货吨位提示错误
                            Method.showToast(`${childItemList[j].cargoName}货物，货物计量不能大于${childItemList[j].shippingOrientationMargin}，请重新确认`);
                            return false;
                        }
                    }
                }
                newGoodInfos.push(goodInfos[i]);
            }
        }
        //货物明细
        shipmentUI.zeroHZGoodInfos = newGoodInfos;
        return true;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    const renderItemView = () => {
        return goodInfos.map((value, index) => (
            <View style={styles.item_body} key={value.orderId}>
                <View style={gStyle.view_row}>
                    <Text style={{color: '#fff', fontSize: 9, padding: 2, paddingLeft: 6, paddingRight: 6, backgroundColor: '#74C690'}}>{index + 1}</Text>
                    <Text style={{color: '#999', fontSize: 13, marginLeft: 4, flex: 1}}>{value.orderId}</Text>
                    <UIImage source={'icon_lindan'} style={{width: 31, height: 17}} />
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#E3E3E3', marginTop: 8, marginBottom: 8}} />
                <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 10}}>
                    <Text style={styles.address} ellipsizeMode={'tail'} numberOfLines={1}>
                        {value.getStartAddress()}
                    </Text>
                    <UIImage source={'order_list_comm_item_arrow'} style={{width: 20, height: 15, marginHorizontal: 5}} />
                    <Text style={styles.address} ellipsizeMode={'tail'} numberOfLines={1}>
                        {value.getEndAddress()}
                    </Text>
                </View>
                {value.cargoArray?.map((value) => (
                    <ZeroGoodsInfoView key={value.cargoId} detail={value} editInput={props.editInput} />
                ))}
            </View>
        ));
    };

    return (
        <View style={[styles.container, props.style]}>
            <Text style={styles.title}>-- 同货主、同发货地的其他拼车单 --</Text>
            <View style={styles.body}>
                <View style={gStyle.view_row}>
                    <Text style={styles.txt_14_33_1}>
                        请在
                        <Text style={styles.txt_14_FF602E}>同时过磅</Text>
                        的运单内填写发货吨位
                    </Text>
                    <UITouchableOpacity
                        onPress={() => {
                            setShowInfoView(!showInfoView);
                        }}>
                        <Text style={styles.txt_13_33}>{showInfoView ? '收起明细' : '展开明细'}</Text>
                    </UITouchableOpacity>
                    <UIImage source={showInfoView ? 'icon_fold' : 'icon_open'} style={{width: 11, height: 11, marginLeft: 2}} />
                </View>
                <View style={{height: 1, flex: 1, backgroundColor: '#E3E3E3', marginTop: 8, marginBottom: 8}} />
                {showInfoView && renderItemView()}
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    title: {
        justifyContent: 'center',
        color: '#FF602E',
        fontSize: 14,
        flex: 1,
        textAlign: 'center',
        paddingTop: 10,
        paddingBottom: 10,
    },
    body: {backgroundColor: '#fff', paddingLeft: 10, paddingRight: 10, paddingTop: 12, paddingBottom: 12},
    txt_13_33: {
        color: '#333',
        fontSize: 13,
    },
    txt_14_FF602E: {
        color: '#FF602E',
        fontSize: 14,
    },
    txt_14_33_1: {
        color: '#333',
        fontSize: 14,
        flex: 1,
    },
    item_body: {
        padding: 10,
        backgroundColor: '#F6F8FC',
        borderRadius: 4,
    },
    address: {
        fontSize: 17,
        color: '#333',
        maxWidth: gScreen_width / 3 + 17,
    },
    _top_orderId: {
        backgroundColor: '#fff',
        fontSize: 14,
        color: '#666666',
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
    },
});

export default forwardRef<ShipmentsZeroHZGoodsViewRef, Props>(ShipmentsZeroHZGoodsView);
