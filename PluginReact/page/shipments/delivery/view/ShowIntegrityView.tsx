import React, {useEffect, useImperativeHandle, useState} from 'react';
import {Text, View} from 'react-native';
import UICheckBox from '../../../widget/UICheckBox';
import {http} from '../../../const.global';
import {ContentIds} from '../models/Integrity';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import TerracePaymentDialog from './TerracePaymentDialog';
import {ReqQueryPlateFormTreatyList4App} from '../requests/ReqQueryPlateFormTreatyList4App';
import {ReqNewAdvanceServiceMoney} from '../requests/ReqNewAdvanceServiceMoney';
import TextUtils from '../../../util/TextUtils';
import {gStyle} from '../../../util/comm-style';
import {AdvanceServiceMoney} from '../models/AdvanceServiceMoney';
import EventBus from '../../../util/EventBus';
import {Constant} from '../../../base/Constant';
import {ArrayUtils} from '../../../util/ArrayUtils';
import {Method} from '../../../util/NativeModulesTools';
/**
 *  协议
 * @param {*} keyIntegrity  ['700018','700017'] 预付+确认发货,['700018','700017','-1']预付+本地预付+确认发货,  ['700017']  发货
 * @returns
 */

interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    isAdvanceButtonOn: boolean;
    advanceInfo: AdvanceInfoRsp;
    advanceWay?: EAdvanceType; //预付方式
    dlOilCardRatio: string; //油品比例
    userPaymentMoney: string; //是否使用立减
    onCheck: Function;
    isHuoWu?: boolean; //是否显示货物保障协议
}

function ShowIntegrityView(props: Props, ref: React.Ref<ShowIntegrityViewRef>) {
    const [readCheck, setReadCheck] = useState(false);
    //协议
    const [agreements, setAgreements] = useState<ContentIds[]>([]);

    const [serviceMoney, setServiceMoney] = useState<AdvanceServiceMoney>();

    const hiddenKey = (key: string) => {
        //协议可能多个
        agreements.map((item) => {
            if (TextUtils.equals(key, item.nodename)) {
                item.hookShow = false;
            }
        });

        setAgreements(ArrayUtils.deepCopy(agreements, ContentIds));
    };

    /**
     * 注释: 显示对应Key值的协议
     * 时间: 2024/3/27 9:59
     * <AUTHOR>
     * @param key
     */
    const showKey = (key: string) => {
        //协议可能多个
        agreements.map((item) => {
            if (TextUtils.equals(key, item.nodename)) {
                item.hookShow = true;
            }
        });
        setAgreements(ArrayUtils.deepCopy(agreements, ContentIds));
    };

    useImperativeHandle(ref, () => ({
        hiddenKey,
        showKey,
    }));

    const openWeb = async (item: ContentIds) => {
        if (item.contentId === '-0000') {
            //预付服务说明 预付服务费提示
            EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'showWaitDialog'});
            let req = new ReqNewAdvanceServiceMoney();
            req.orderId = props.shipmentsEGoodInfo.orderId;
            req.detailId = props.shipmentsEGoodInfo.detailId;
            req.advanceWay = props.advanceWay?.type;
            req.dlOilCardRatio = props.dlOilCardRatio;

            let response = await req.request();
            EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'hiddenWaitDialog'});
            if (response.isSuccess() && response.data) {
                setServiceMoney(response.data);
            } else {
                Method.showToast(response.msg);
            }
        } else {
            Method.openWeb(item.contentDescAlias, item.url);
        }
    };

    /**
     * 注释: 查询协议
     * 时间: 2023/6/26 0026 16:17
     * <AUTHOR>
     */
    const query = () => {
        let keyIntegrity = [700017, 700019];

        if (TextUtils.equals('1', props.advanceInfo.isAdvance)) {
            //可以预付
            if (TextUtils.equals('0', props.shipmentsEGoodInfo.goodsSource)) {
                if (props.isAdvanceButtonOn && (TextUtils.equals('2', props.advanceInfo.advanceType) || TextUtils.equals('4', props.advanceInfo.advanceType))) {
                    //2.个体司机非指定订单 => 走平台预付
                    // 4.个体司机关联车老板模式 => 走平台预付
                    keyIntegrity = [700018, 700017, 700019, -1];
                } else if (TextUtils.equals('3', props.advanceInfo.advanceType)) {
                    // 车老板自己摘牌订单 => 车老板预付
                    keyIntegrity = [700018, 700017, 700019];
                }
            }
        }
        let request = new ReqQueryPlateFormTreatyList4App();
        request.request().then((response) => {
            if (TextUtils.equals('200', response.code)) {
                let agreements: ContentIds[] = [];
                response.dataArray?.map((value) => {
                    if (value.contentIds && value.userType == 700007) {
                        //角色类型符合
                        // SHIP("700017", "确认发货"),
                        // INTEGRITY("700018", "预付"),
                        if (keyIntegrity.includes(value.nodename ?? 0)) {
                            //产品符合
                            value.contentIds.map((item) => {
                                //货保服务
                                if (value.nodename == 700019 && !props.isHuoWu) {
                                    //没有货保不显示
                                    item.hookShow = false;
                                } else {
                                    item.hookShow = true;
                                }
                                item.nodename = `${value.nodename}`;
                                item.url = http.url() + value.url + '?contentId=' + item.contentId;
                                agreements.push(item);
                            });
                        }
                    }
                });
                if (keyIntegrity.includes(-1)) {
                    //本地固定协议=>[预付协议]
                    agreements.push({contentDescAlias: '《预付服务说明》', contentId: '-0000', hookShow: true});
                }
                setAgreements(agreements);
            } else {
                Method.showToast(response.msg);
            }
        });
    };

    useEffect(() => {
        query();
    }, [props.isAdvanceButtonOn]);

    return (
        <View
            style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                paddingLeft: 14,
                paddingRight: 14,
                paddingBottom: 14,
                alignItems: 'center',
                marginTop: 7,
            }}>
            <UICheckBox
                label="我已阅读"
                checked={readCheck}
                onChange={(check) => {
                    setReadCheck(check);
                    props.onCheck(check);
                }}
            />
            {agreements?.map((value, index) => {
                if (value.hookShow) {
                    return (
                        <Text key={`${value.contentId}_` + index} onPress={() => openWeb(value)} style={[gStyle.txt_333333_28, {color: '#5086FC'}]}>
                            {value.contentDescAlias}
                        </Text>
                    );
                }
            })}
            {/* 预付协议对话框 */}
            {serviceMoney && (
                <TerracePaymentDialog
                    shipmentsEGoodInfo={props.shipmentsEGoodInfo}
                    serviceMoney={serviceMoney}
                    advanceInfo={props.advanceInfo}
                    userPaymentMoney={props.userPaymentMoney}
                    onClose={() => {
                        setServiceMoney(undefined);
                    }}
                />
            )}
        </View>
    );
}

export interface ShowIntegrityViewRef {
    hiddenKey: (key: string) => void;
    showKey: (key: string) => void;
}

export default React.forwardRef<ShowIntegrityViewRef, Props>(ShowIntegrityView);
