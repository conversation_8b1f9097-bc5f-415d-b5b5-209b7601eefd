import {StyleSheet, Text, TextInput, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import React, {useEffect, useState} from 'react';
import {EGoodInfo} from '../models/ShipmentsEGoodInfoRsp';
import TextUtils from '../../../util/TextUtils';
import LanguageType from '../../../util/language/LanguageType';

interface Props {
    detail: EGoodInfo;
    editInput?: boolean;
    grossAndTare?: boolean;
    onTextChangedListener?: Function;
}

/**
 * 注释: 货物明细
 * 时间: 2023/7/10 0010 19:33
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function GoodsInfoView(props: Props) {
    let unitText = props.detail.getUnitTxt();

    const [beforeDeliverCargoWeight, setBeforeDeliverCargoWeight] = useState<string>(props.detail.beforeDeliverCargoWeight);
    const [beforeDeliverGrossWeight, setBeforeDeliverGrossWeight] = useState<string>(props.detail.beforeDeliverGrossWeight);
    const [beforeDeliverTareWeight, setBeforeDeliverTareWeight] = useState<string>(props.detail.beforeDeliverTareWeight);

    const onChangeText = (text) => {
        // 10位整数, 4位小数
        let reg = /^(0|[1-9]\d{0,9})$|^(0|[1-9]\d{0,9})\.(\d{0,4})$/;
        let isMatch = TextUtils.isEmpty(text) || reg.test(text);
        if (isMatch) {
            setBeforeDeliverCargoWeight(text);
            //输入框改变刷新界面
            props.detail.beforeDeliverCargoWeight = text;
            props.onTextChangedListener && props.onTextChangedListener(props.detail);
        } else {
            setBeforeDeliverCargoWeight(beforeDeliverCargoWeight);
        }
    };

    const onChangeTextGrossWeight = (text) => {
        // 10位整数, 4位小数
        let reg = /^(0|[1-9]\d{0,9})$|^(0|[1-9]\d{0,9})\.(\d{0,4})$/;
        let isMatch = TextUtils.isEmpty(text) || reg.test(text);
        if (isMatch) {
            setBeforeDeliverGrossWeight(text);
            //毛重吨位
            props.detail.beforeDeliverGrossWeight = text;
        } else {
            setBeforeDeliverGrossWeight(beforeDeliverGrossWeight);
        }
    };

    const onChangeTextTareWeight = (text) => {
        // 10位整数, 4位小数
        let reg = /^(0|[1-9]\d{0,9})$|^(0|[1-9]\d{0,9})\.(\d{0,4})$/;
        let isMatch = TextUtils.isEmpty(text) || reg.test(text);
        if (isMatch) {
            setBeforeDeliverTareWeight(text);
            //皮重吨位
            props.detail.beforeDeliverTareWeight = text;
        } else {
            setBeforeDeliverTareWeight(beforeDeliverTareWeight);
        }
    };

    useEffect(() => {
        //如果有值则计算预付运费
        props.onTextChangedListener && props.onTextChangedListener(props.detail);
    }, []);

    return (
        <View style={[gStyle.view_padding, {marginTop: 1}]} key={props.detail.cargoId}>
            <View style={gStyle.view_row}>
                <Text style={gStyle.txt_333333_34}>{LanguageType.getTxt('货物明细')}</Text>
                <Text style={[gStyle.text_flex_right, gStyle.txt_666666_34]}>{props.detail.cargoName}</Text>
            </View>
            {/*4988	FBIS-487	【安卓】【客服进线】自动发货预付界面不展示吨位输入*/}
            {(props.editInput || TextUtils.isNoEmpty(beforeDeliverCargoWeight)) && (
                <>
                    <View style={gStyle.view_row}>
                        <Text style={gStyle.txt_333333_34}>{LanguageType.getTxt('实际发货吨位')}</Text>
                        <TextInput style={styles.good_info_input} clearButtonMode="while-editing" keyboardType="numeric" value={beforeDeliverCargoWeight} editable={props.editInput ?? true} placeholder="请输入" onChangeText={onChangeText} />
                        <Text style={gStyle.txt_333333_34}>{unitText}</Text>
                    </View>
                    <Text style={{color: '#fb6b40'}}>录入吨位须与发货磅单一致。</Text>
                </>
            )}
            {props.grossAndTare && (
                <View style={gStyle.view_row}>
                    <Text style={gStyle.txt_333333_34}>毛重吨位</Text>
                    <TextInput style={styles.good_info_input} clearButtonMode="while-editing" keyboardType="numeric" value={beforeDeliverGrossWeight} placeholder="请输入" onChangeText={onChangeTextGrossWeight} />
                    <Text style={gStyle.txt_333333_34}>{unitText}</Text>
                </View>
            )}
            {props.grossAndTare && (
                <View style={gStyle.view_row}>
                    <Text style={gStyle.txt_333333_34}>皮重吨位</Text>
                    <TextInput style={styles.good_info_input} clearButtonMode="while-editing" keyboardType="numeric" value={beforeDeliverTareWeight} placeholder="请输入" onChangeText={onChangeTextTareWeight} />
                    <Text style={gStyle.txt_333333_34}>{unitText}</Text>
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    good_info_input: {
        padding: 2,
        flex: 1,
        fontSize: 17,
        textAlign: 'right',
        marginLeft: 5,
        marginRight: 5,
    },
});
