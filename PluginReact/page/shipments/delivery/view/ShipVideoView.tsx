import {StyleProp, Text, View, ViewStyle} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {http} from '../../../const.global';
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {PeopleVehicleVideoArr} from '../models/ShipmentsEGoodInfoRsp';
import {ShipmentUI} from '../models/ShipmentUI';
import TextUtils from '../../../util/TextUtils';
import UIVideoThumPostItem from '../../../widget/UIVideoThumPostItem';
import {Method} from '../../../util/NativeModulesTools';

/**
 * 视频人车上传
 */
interface Props {
    data?: PeopleVehicleVideoArr[]; //视频地址
    title: string; //标题
    toast: string; //提示
    max: number; //最大
    edit: boolean;
    showReadToastIcon: boolean; //* 必传标识
    style?: StyleProp<ViewStyle>;
    //上传视频回调
    addChanged?: Function;
}

class Video {}

function ShipVideoView(props: Props, ref) {
    //视频预览地址
    const [videoUrl, setVideoUrl] = useState(props.data ?? []);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        if (props.showReadToastIcon) {
            //比传
            if (videoUrl.length <= 0) {
                Method.showToast(`请选择上传${props.title}!`);
                return false;
            }
        }
        return true;
    };

    const getVideoUrl = () => {
        return videoUrl;
    };
    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check, getVideoUrl}));

    const delPic = (index, item, imageUrl) => {
        //删除
        let newList = videoUrl.filter((value) => !TextUtils.equals(value.videoUrl, item.videoUrl));
        setVideoUrl(newList);
    };

    const lookVideoPage = (item, imageUr) => {
        //查看播放视频
        Method.transferNativeMethod('CommonReactNativeExtension', 'videoPlay', {url: http.imagUrl(item.videoUrl)});
    };

    const chooseVideo = async () => {
        //拍摄视频
        let result = await Method.startVideoRecordAction('', '60', '0');
        if (TextUtils.equals('200', result.code)) {
            Method.showWaitDialog();
            let upDataJson = await Method.upFileNew(result.data);
            let fileResult = JSON.parse(upDataJson);
            if (TextUtils.equals('200', fileResult.code)) {
                //视频返回
                let newData = new PeopleVehicleVideoArr();
                newData.videoUrl = fileResult.url;
                setVideoUrl(videoUrl.concat(newData));
                props.addChanged && props.addChanged();
            } else {
                Method.showToast(fileResult.msg);
            }
            Method.hideWaitDialog();
        } else if (TextUtils.equals('-1', result.code)) {
            Method.showToast(result.msg);
        }
    };

    return (
        <View style={[gStyle.view_padding, {paddingBottom: 7}, props.style]} key={'video'}>
            {/*标题*/}
            <Text style={gStyle.txt_333333_32}>{props.title}</Text>
            {/*提示文案*/}
            <Text style={{color: '#FB6B40', fontSize: 11, paddingBottom: 5}}>{props.toast}</Text>
            {/*展示内容*/}
            <UIVideoThumPostItem items={videoUrl} edit={true} max={props.max} rowSize={4} delPic={delPic} lookImagePage={lookVideoPage} choosePic={chooseVideo} transformUrl={(item) => http.imagUrl(item.videoUrl)} />
        </View>
    );
}

export interface ShipVideoViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
    getVideoUrl: () => PeopleVehicleVideoArr[];
}

export default forwardRef<ShipVideoViewRef, Props>(ShipVideoView);
