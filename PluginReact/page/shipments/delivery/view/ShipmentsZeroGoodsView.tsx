//确认发货，货物明细+实际发货量
import React, {forwardRef, useImperativeHandle, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import {ShipmentUI} from '../models/ShipmentUI';
import UIImage from '../../../widget/UIImage';
import ZeroGoodsInfoView from './ZeroGoodsInfoView';
import ShipmentsEZeroGoodInfoRsp from '../models/ShipmentsEZeroGoodInfoRsp';
import {gScreen_width} from '../../../util/scaled-style';
import {Method} from '../../../util/NativeModulesTools';
export interface ShipmentsZeroGoodsViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

interface Props {
    shipmentsEGoodInfo: ShipmentsEZeroGoodInfoRsp; //货物信息
    editInput: boolean;
}

function ShipmentsZeroGoodsView(props: Props, ref) {
    //货物明细
    const [goodInfos, setGoodInfos] = useState(props.shipmentsEGoodInfo.topOrderArray ?? []);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        let size = goodInfos.length;
        for (let i = 0; i < size; i++) {
            let childItemList = goodInfos[i].cargoArray;

            for (let j = 0; j < childItemList.length; j++) {
                let beforeDeliverCargoWeight = childItemList[j].beforeDeliverCargoWeight?? '';

                if (TextUtils.isEmpty(beforeDeliverCargoWeight)) {
                    Method.showToast('请输入[' + childItemList[j].cargoName + ']货物重量');
                    return false;
                }

                if (parseFloat(beforeDeliverCargoWeight) <= 0.0) {
                    Method.showToast('[' + childItemList[j].cargoName + ']货物,货物计量不能小于等于0，请重新确认！');
                    return false;
                }

                if (TextUtils.equals('1', childItemList[j].unit) && TextUtils.isNoEmpty(childItemList[j].weightOrientationLimit)) {
                    //重货
                    if (parseFloat(beforeDeliverCargoWeight) > parseFloat(childItemList[j].weightOrientationLimit)) {
                        //zczy-13915_发货吨位提示错误
                        Method.showToast(`${childItemList[j].cargoName}货物，货物计量不能大于${childItemList[j].weightOrientationLimit}，请重新确认`);
                        return false;
                    }
                } else if (TextUtils.equals('2', childItemList[j].unit) && TextUtils.isNoEmpty(childItemList[j].shippingOrientationMargin)) {
                    //泡货
                    if (parseFloat(beforeDeliverCargoWeight) > parseFloat(childItemList[j].shippingOrientationMargin)) {
                        //zczy-13915_发货吨位提示错误
                        Method.showToast(`${childItemList[j].cargoName}货物，货物计量不能大于${childItemList[j].shippingOrientationMargin}，请重新确认`);
                        return false;
                    }
                }
            }
        }
        //货物明细
        shipmentUI.zeroGoodInfos = goodInfos;
        return true;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    return (
        <View style={styles.bg}>
            <UIImage source={'img_zero_order_bg_right'} style={{width: gScreen_width, height: 114, position: 'absolute', zIndex: -1}} resizeMode={'stretch'} />
            {goodInfos.map((value, index) => (
                <View key={'Top_view_' + value.orderId}>
                    <Text style={styles._top_orderId}>运单编号 {value.orderId}</Text>
                    <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                        <UIImage source={'icon_start_new'} style={styles.img_size} />
                        <Text style={styles.address} ellipsizeMode={'tail'} numberOfLines={1}>
                            {value.getStartAddress()}
                        </Text>
                        <UIImage source={'order_list_comm_item_arrow'} style={{width: 20, height: 15, marginHorizontal: 5}} />
                        <UIImage source={'icon_end_new'} style={styles.img_size} />
                        <Text style={styles.address} ellipsizeMode={'tail'} numberOfLines={1}>
                            {value.getEndAddress()}
                        </Text>
                    </View>
                    <UIImage source={'icon_lindan'} style={{width: 31, height: 17, marginTop: 8, marginBottom: 8}} />
                    {value.cargoArray?.map((value) => (
                        <ZeroGoodsInfoView key={value.cargoId} detail={value} editInput={props.editInput} />
                    ))}
                </View>
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    bg: {
        backgroundColor: '#fff',
        position: 'relative',
        paddingLeft: 14,
        paddingRight: 14,
        paddingTop: 12,
        paddingBottom: 12,
    },
    address: {
        fontSize: 17,
        color: '#333',
        marginLeft: 5,
        maxWidth: gScreen_width / 3 + 17,
    },
    _top_orderId: {
        fontSize: 14,
        color: '#666666',
    },
    img_size: {width: 14, height: 14},
});

export default forwardRef<ShipmentsZeroGoodsViewRef, Props>(ShipmentsZeroGoodsView);
