import React, {useEffect, useImperativeHandle, useState} from 'react';
import {Text, View} from 'react-native';
import UICheckBox from '../../../widget/UICheckBox';
import { http} from '../../../const.global';
import {ContentIds} from '../models/Integrity';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import {ReqQueryPlateFormTreatyList4App} from '../requests/ReqQueryPlateFormTreatyList4App';
import TextUtils from '../../../util/TextUtils';
import {gStyle} from '../../../util/comm-style';
import {ArrayUtils} from '../../../util/ArrayUtils';
import {Method} from '../../../util/NativeModulesTools';
/**
 *  协议
 * @param {*} keyIntegrity   ['700017']  发货
 * @returns
 */

interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    onCheck: Function;
}

function ShowTrnIntegrityView(props: Props, ref: React.Ref<ShowTrnIntegrityViewRef>) {
    const [readCheck, setReadCheck] = useState(false);
    //协议
    const [agreements, setAgreements] = useState<ContentIds[]>([]);

    const hiddenKey = (key: string) => {
        //协议可能多个
        agreements.map((item) => {
            if (TextUtils.equals(key, item.nodename)) {
                item.hookShow = false;
            }
        });

        setAgreements(ArrayUtils.deepCopy(agreements, ContentIds));
    };

    /**
     * 注释: 显示对应Key值的协议
     * @param key
     */
    const showKey = (key: string) => {
        //协议可能多个
        agreements.map((item) => {
            if (TextUtils.equals(key, item.nodename)) {
                item.hookShow = true;
            }
        });
        setAgreements(ArrayUtils.deepCopy(agreements, ContentIds));
    };

    useImperativeHandle(ref, () => ({
        hiddenKey,
        showKey,
    }));

    const openWeb = async (item: ContentIds) => {
        if (item.contentId === '-0000') {
           
        } else {
            Method.openWeb(item.contentDescAlias, item.url);
        }
    };

    /**
     * 注释: 查询协议
     * 时间: 2023/6/26 0026 16:17
     */
    const query = () => {
        let keyIntegrity = [700017];
        let request = new ReqQueryPlateFormTreatyList4App();
        request.request().then((response) => {
            if (TextUtils.equals('200', response.code)) {
                let agreements: ContentIds[] = [];
                response.dataArray?.map((value) => {
                    if (value.contentIds && value.userType == 700007) {
                        //角色类型符合
                        if (keyIntegrity.includes(value.nodename ?? 0)) {
                            //产品符合
                            value.contentIds.map((item) => {
                                item.nodename = `${value.nodename}`;
                                item.url = http.url() + value.url + '?contentId=' + item.contentId;
                                agreements.push(item);
                            });
                        }
                    }
                });
                setAgreements(agreements);
            } else {
                Method.showToast(response.msg);
            }
        });
    };

    useEffect(() => {
        query();
    }, []);

    return (
        <View
            style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                paddingLeft: 14,
                paddingRight: 14,
                paddingBottom: 14,
                alignItems: 'center',
                marginTop: 7,
            }}>
            <UICheckBox
                label="我已阅读"
                checked={readCheck}
                onChange={(check) => {
                    setReadCheck(check);
                    props.onCheck(check);
                }}
            />
            {agreements &&
                agreements.map((value) => {
                    if (value.hookShow) {
                        return (
                            <Text key={value.contentId} onPress={() => openWeb(value)} style={[gStyle.txt_333333_28, {color: '#5086FC'}]}>
                                {value.contentDescAlias}
                            </Text>
                        );
                    }
                })}
        </View>
    );
}

export interface ShowTrnIntegrityViewRef {
    hiddenKey: (key: string) => void;
    showKey: (key: string) => void;
}

export default React.forwardRef<ShowTrnIntegrityViewRef, Props>(ShowTrnIntegrityView);
