import React, {useEffect, useState} from 'react';
import {Text, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import {SP} from '../../../util/scaled-style';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import SelectAdvanceWayItem from './SelectAdvanceWayItem';
import {Constant} from '../../../base/Constant';

interface Props {
    //货物明细
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    //预付场景
    advanceInfo: AdvanceInfoRsp;
    //是否是智运卡优惠
    smartTransportCardStyle?: boolean;
    //左上角优惠信息
    favourableTxt?: string;
    //进入场景 0 正常发货 1重新预付 2:发货后预付申请
    sceneAdvance?: number;
    //现金预付优惠信息
    advanceMoneyConTxt?: JSX.Element;
    //油品+现金预付优惠信息
    advanceMoneyOilConTxt?: JSX.Element;
    //预付方式回调
    onSelectAdvanceWay: Function;
}

export default function ShipmentsSelectAdvanceWayView(props: Props) {
    //活动
    let activityShow = props.advanceInfo.activityInfo && TextUtils.equals('1', props.advanceInfo.activityShowFlag);

    //预付方式集合过滤
    let advanceWayList: EAdvanceType[] = [];
    advanceWayList = props.advanceInfo.advanceWay ?? [];

    //预付方式
    let defaultAdvanceWay, moneyAdvanceWay, oilAdvanceWay, oilMoneyAdvanceWay;
    advanceWayList.map((item) => {
        if (TextUtils.equals('1', item.type)) {
            moneyAdvanceWay = item;
        } else if (TextUtils.equals('2', item.type)) {
            oilAdvanceWay = item;
        } else if (TextUtils.equals('3', item.type)) {
            oilMoneyAdvanceWay = item;
        }
        //默认预付方式
        if (TextUtils.equals('1', item.defaultAdvanceWayFlag) || advanceWayList?.length == 1) {
            defaultAdvanceWay = item;
        }
    });

    //线上出现无默认预付问题，防止出现无默认预付方式问题，
    if (defaultAdvanceWay == null) {
        //没有默认
        if (advanceWayList && advanceWayList.length > 1) {
            //有预付方式，选择集合第一个
            defaultAdvanceWay = advanceWayList[0];
        } else {
            //集合也无值，默认新对象
            defaultAdvanceWay = new EAdvanceType();
        }
    }

    const [selectType, setSelectType] = useState(defaultAdvanceWay.type);

    const onSelectType = (advanceWay) => {
        //选择预付方式
        setSelectType(advanceWay.type);
        props.onSelectAdvanceWay && props.onSelectAdvanceWay(advanceWay);
    };

    useEffect(() => {
        //默认时回调一次
        props.onSelectAdvanceWay && props.onSelectAdvanceWay(defaultAdvanceWay);
    }, []);

    // 当可选预付方式大于等于2项时，若选项中包含“油品预付”则“油品预付”选项上增加特惠标签&&若不包“含油品预付”，则在“油品+现金预付”选项上增加特惠标签&&仅有1种可选预付方式时，无需展示特惠标签
    let showOilPreferential = advanceWayList.length > 2;
    let showOilCashPreferential = advanceWayList.length > 2 && !oilAdvanceWay && oilMoneyAdvanceWay;

    return (
        <View
            style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                backgroundColor: '#FFF',
                paddingHorizontal: 5,
                marginTop: props.sceneAdvance == 2 ? 5 : 0,
            }}>
            {moneyAdvanceWay && (
                <SelectAdvanceWayItem
                    data={moneyAdvanceWay}
                    onPress={onSelectType}
                    selected={TextUtils.equals('1', selectType)}
                    title={'现金预付'}
                    content={props.advanceMoneyConTxt}
                    showFavourable={TextUtils.isNoEmpty(props.favourableTxt)}
                    smartTransportCardStyle={props.smartTransportCardStyle}
                    favourableTxt={props.favourableTxt}
                    activityIconShow={activityShow ?? false}
                    activityName={props.advanceInfo.activityInfo?.activityName ?? ''}
                />
            )}
            {oilAdvanceWay && (
                <SelectAdvanceWayItem
                    data={oilAdvanceWay}
                    onPress={onSelectType}
                    selected={TextUtils.equals('2', selectType)}
                    title={'油品预付'}
                    content={
                        <Text
                            style={{
                                textAlign: 'center',
                                fontSize: SP(20),
                                color: Constant.color_fb6b40,
                                marginTop: 8,
                            }}>
                            免服务费
                        </Text>
                    }
                    showFavourable={showOilPreferential || TextUtils.isNoEmpty(props.advanceInfo.oilOnPrePolicyId)}
                    smartTransportCardStyle={false}
                    favourableTxt={'优惠'}
                    activityIconShow={false}
                    showOilOnPrePolicy={TextUtils.isNoEmpty(props.advanceInfo.oilOnPrePolicyId)}
                />
            )}
            {oilMoneyAdvanceWay && (
                <SelectAdvanceWayItem
                    data={oilMoneyAdvanceWay}
                    onPress={onSelectType}
                    selected={TextUtils.equals('3', selectType)}
                    title={'油品+现金预付'}
                    content={props.advanceMoneyOilConTxt}
                    showFavourable={TextUtils.isNoEmpty(props.favourableTxt) || showOilCashPreferential}
                    smartTransportCardStyle={props.smartTransportCardStyle ?? false}
                    favourableTxt={props.favourableTxt ?? '优惠'}
                    activityIconShow={activityShow ?? false}
                    activityName={props.advanceInfo.activityInfo?.activityName ?? ''}
                />
            )}
        </View>
    );
}
