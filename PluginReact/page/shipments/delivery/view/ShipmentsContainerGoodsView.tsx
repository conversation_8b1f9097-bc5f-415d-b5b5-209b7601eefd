//(集装箱)确认发货，货物明细+实际发货量
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Text, TextInput, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import TextUtils from '../../../util/TextUtils';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import AdvanceInfoRsp from '../models/AdvanceInfoRsp';
import ShowSelectContainerDialog from './ShowSelectContainerDialog';
import EventBus from '../../../util/EventBus';
import {Constant} from '../../../base/Constant';
import {ShipmentUI} from '../models/ShipmentUI';
import {ShipmentsGoodsViewRef} from './ShipmentsGoodsView';
import UIImage from '../../../widget/UIImage';
import NumUtil from '../../../util/NumUtil';
import {ArrayUtils} from '../../../util/ArrayUtils';
import {Method} from '../../../util/NativeModulesTools';
interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp; //货物明细
    advanceInfo: AdvanceInfoRsp; //预付信息
    sceneAdvance: number; //场景 0:发货预付场景 1 : 重新预付 ,2：发货后预付申请
}

function ShipmentsContainerGoodsView(props: Props, ref) {
    //货物明细
    const [goodInfos, setGoodInfos] = useState(props.shipmentsEGoodInfo.rootArray);

    const [selectGoodInfo, setSelectGoodInfo] = useState();

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        let size = goodInfos.length;
        for (let i = 0; i < size; i++) {
            //1集装箱货源
            if (ArrayUtils.isEmpty(goodInfos[i].tempContainerSize)) {
                Method.showToast('请选择集装箱箱号！');
                return false;
            }
            if (TextUtils.isEmpty(goodInfos[i].beforeDeliverCargoWeight)) {
                Method.showToast('请输入[' + goodInfos[i].cargoName + ']货物重量');
                return false;
            }
        }
        //货物明细
        shipmentUI.goodInfos = goodInfos;
        return true;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    let freight_type = ''; //价格模式
    let money = ''; //价格

    if (TextUtils.equals('3', props.advanceInfo.advanceType) || TextUtils.equals('4', props.advanceInfo.advanceType)) {
        //ZCZY-9422 对司机端隐藏运价信息
        freight_type = '';
        money = '';
    } else {
        //0：包车价 1：单价
        if (TextUtils.equals('1', props.shipmentsEGoodInfo.freightType)) {
            money = `${NumUtil.halfup(parseFloat(props.shipmentsEGoodInfo.pbCarrierUnitMoney))}`;
            freight_type = '单箱价';
        } else if (TextUtils.equals('0', props.shipmentsEGoodInfo.freightType)) {
            money = `${NumUtil.halfup(parseFloat(props.shipmentsEGoodInfo.pbCarrierMoney))}`;
            freight_type = '包箱价';
        }
    }
    useEffect(() => {
        //初始化 or 货物信息方式修改
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'goodInfos', value: goodInfos});
    }, goodInfos);

    return (
        <View>
            {goodInfos.map((value) => {
                value.money = money;
                value.freight_type = freight_type;
                return <ContainerGoodsInfoView key={value.cargoId} goodInfo={value} onShowSelectDialog={(item) => setSelectGoodInfo(item)} />;
            })}

            {/* 集装箱选择框 */}
            {selectGoodInfo && (
                <ShowSelectContainerDialog
                    orderId={props.shipmentsEGoodInfo.orderId}
                    goodInfo={selectGoodInfo}
                    onClickClose={() => setSelectGoodInfo(undefined)}
                    onClickOk={(goodInfo) => {
                        setGoodInfos([...goodInfos]);
                        setSelectGoodInfo(undefined);
                    }}
                />
            )}
        </View>
    );
}

export default forwardRef<ShipmentsGoodsViewRef, Props>(ShipmentsContainerGoodsView);
/**
 * 集装箱ItemView
 * @param {*} item
 * @param {*} onClickItemLister 点击事件（集装箱选择）
 * @param {*} selectList 选择集装箱数据
 * @returns
 */
export const ContainerGoodsInfoView = ({goodInfo, onShowSelectDialog}) => {
    const [value, setValue] = useState(goodInfo.beforeDeliverCargoWeight);

    const onChangeText = (txt) => {
        let reg = /^[0-9.]+$/g;
        let isMatch = TextUtils.isEmpty(txt) || reg.test(txt);
        if (isMatch) {
            setValue(txt);
            //输入吨位事件
            goodInfo.beforeDeliverCargoWeight = txt;
        } else {
            setValue(value);
        }
    };

    const onClickItem = () => {
        //点击事件
        onShowSelectDialog && onShowSelectDialog(goodInfo);
    };

    let selectAllWeight = '请选择';
    if (goodInfo.tempContainerSize) {
        //计算总吨位
        let allWeight = 0.0;
        goodInfo.tempContainerSize.map((value) => {
            allWeight += parseFloat(value.containerUnitWeight);
        });
        selectAllWeight = '共' + goodInfo.tempContainerSize.length + '箱/' + allWeight + goodInfo.getCargoCategory();
    }

    return (
        <View style={{marginTop: 3}} key={goodInfo.cargoId}>
            <View style={[gStyle.view_padding, gStyle.view_row]}>
                <UIImage style={{height: 50, width: 50}} source={'order_container_icon'} />
                <View style={{marginLeft: 7}}>
                    <Text style={gStyle.txt_333333_34}>{goodInfo.cargoName}</Text>
                    <Text style={gStyle.txt_666666_24}>{`${goodInfo.cargoName}*${goodInfo.weight}箱`}</Text>
                </View>
                <View style={{flex: 1, alignItems: 'flex-end'}}>
                    <Text style={{color: '#ff602e', fontSize: 16}}>{`${goodInfo.money}元`}</Text>
                    <Text style={gStyle.txt_999999_22}>{goodInfo.freight_type}</Text>
                </View>
            </View>

            <View style={{marginTop: 4, paddingTop: 7, paddingBottom: 5, backgroundColor: '#fff'}}>
                <View style={[gStyle.view_row, {paddingLeft: 10, paddingRight: 10}]}>
                    <Text style={gStyle.txt_333333_34}>集装箱箱号</Text>
                    <Text style={[gStyle.txt_666666_34, gStyle.text_flex_right]} onPress={onClickItem}>
                        {selectAllWeight}
                    </Text>
                    <UIImage source={'base_right_arrow_gray'} style={{width: 10, height: 15, marginLeft: 5}} />
                </View>
                <View style={{backgroundColor: '#e3e3e3', height: 0.5, marginTop: 9}} />
                <View style={[gStyle.view_row, {paddingLeft: 10, paddingRight: 10, marginTop: 9}]}>
                    <Text style={[gStyle.txt_333333_34, {flex: 1}]}>实际发货吨数</Text>
                    <TextInput
                        style={{minWidth: 90, backgroundColor: '#F6F6F6', borderRadius: 2, paddingTop: 3, paddingBottom: 3, paddingRight: 9, paddingLeft: 9, color: '#333', fontSize: 15}}
                        placeholderTextColor={'#999999'}
                        clearButtonMode="while-editing"
                        keyboardType="numeric"
                        value={goodInfo.beforeDeliverCargoWeight}
                        placeholder="请输入"
                        onChangeText={onChangeText}
                    />
                    <Text style={gStyle.txt_333333_34}>{goodInfo.getCargoCategory()}</Text>
                </View>
            </View>
        </View>
    );
};
