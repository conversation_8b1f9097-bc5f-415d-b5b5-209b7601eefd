import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width} from '../../../util/scaled-style';
import UIImage from '../../../widget/UIImage';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';

interface Props {
    //1 单据 2 人货车
    waybillImage: string;
    onClose: Function;
}

/**
 * 单据与人车货示例对话框
 * @param props
 * @constructor
 */
export default function DemoShipmentImageDemoDialog(props: Props) {
    /**
     * 注释: 单据示例弹窗
     * 时间: 2023/12/6 0006 9:46
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderOrderExampleDialog() {
        return (
            <View style={styles.paymentDialogBg}>
                <View style={styles.dialogView}>
                    <View
                        style={{
                            backgroundColor: '#F2F8FF',
                            borderTopLeftRadius: 8,
                            borderTopRightRadius: 8,
                            alignItems: 'center',
                            paddingTop: 7,
                            paddingBottom: 7,
                        }}>
                        <Text style={{fontSize: 16, color: '#5086fc'}}>使用照片须知</Text>
                        <Text
                            style={{
                                paddingLeft: 5,
                                paddingRight: 5,
                                fontSize: 12,
                                color: '#999',
                            }}>
                            {TextUtils.equals('1', props.waybillImage) ? '证件清晰、完整、原件拍摄' : '照片清晰且已装载货物，体现承运人本人、车牌号、货物信息'}
                        </Text>
                        <UITouchableOpacity
                            onPress={() => props.onClose && props.onClose()}
                            style={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                marginRight: 7,
                                marginTop: 7,
                            }}>
                            <UIImage source={'order_main_select_city_clear_ic'} style={{width: 15, height: 15}} />
                        </UITouchableOpacity>
                    </View>
                    <View style={{marginTop: 5}}>
                        <UIImage source={`${TextUtils.equals('1', props.waybillImage) ? 'icon_order_demo_dj' : 'icon_order_demo_rch'}`} style={{width: gScreen_width * 0.8, height: gScreen_width * 0.3}} />
                        <UIImage source={'icon_order_demo_fb'} style={{width: 20, height: 20, position: 'absolute', top: 0, right: 10}} />
                    </View>
                    <View style={{flexDirection: 'row', marginTop: 9, paddingBottom: 10, paddingLeft: 10}}>
                        <Text style={{color: '#F85A00', fontSize: 11}}>*</Text>
                        <Text style={{color: '#666', fontSize: 11}}>拍照前请关闭闪光灯，并调整合适角度，避免反光</Text>
                    </View>
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制人车货示例弹窗
     * 时间: 2023/12/6 0006 9:47
     * <AUTHOR>
     */
    function renderPeopleCarExampleDialog() {
        return (
            <View
                style={{
                    backgroundColor: '#fff',
                    borderRadius: 6,
                    padding: 16,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                {/*标题*/}
                <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                    <Text style={{fontSize: 14, color: '#333', fontWeight: 'bold'}}>示例模版</Text>
                    <View style={{flex: 1}} />
                    <UITouchableOpacity
                        onPress={() => {
                            props.onClose && props.onClose();
                        }}>
                        <UIImage source={'base_icon_close'} style={{width: 15, height: 15}} />
                    </UITouchableOpacity>
                </View>
                {/*提示*/}
                <Text style={{fontSize: 12, color: '#333', marginTop: 16}}>
                    请至少上传<Text style={{color: '#FF602E'}}>1张</Text>同时包含<Text style={{color: '#FF602E'}}>人脸、车头车牌、货物</Text>要素的照片
                </Text>
                {/*示例图片*/}
                <UIImage source={'img_peoplecar_demo_dialog'} style={{width: 249, height: 172.5, marginTop: 8, marginBottom: 10}} />
            </View>
        );
    }

    return (
        <Modal
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}
            onBackButtonPress={() => {
                // 响应返回键
                props.onClose && props.onClose();
            }}
            onBackdropPress={() => {
                // 点击背景遮罩层
                props.onClose && props.onClose();
            }}>
            {/*单据示例弹窗*/}
            {TextUtils.equals('1', props.waybillImage) && renderOrderExampleDialog()}
            {/*单据示例弹窗*/}
            {TextUtils.equals('2', props.waybillImage) && renderPeopleCarExampleDialog()}
        </Modal>
    );
}

const styles = StyleSheet.create({
    paymentDialogBg: {
        //全屏显示 半透明 可以看到之前的控件但是不能操作了
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },

    dialogView: {
        width: gScreen_width * 0.8,
        minHeight: gScreen_height * 0.24,
        backgroundColor: 'white',
        borderRadius: 8,
        flexDirection: 'column',
    },
});
