import {Text, View, ViewProps} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {gScreen_height, gScreen_width} from '../../../util/scaled-style';
import UIImage from '../../../widget/UIImage';

interface Props extends ViewProps {
    onClose?: () => void;
}

/**
 * 注释:   34662    FBIS-7960    【安卓】智运油卡留存管控-油转现、奖励、预付
 * <AUTHOR>
 * @constructor
 */
export default function OilToastDialog(props: Props) {
    return (
        <Modal
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            isVisible={true}
            hideModalContentWhileAnimating={true}
            deviceWidth={gScreen_width}
            deviceHeight={gScreen_height}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            onBackdropPress={() => {
                props.onClose && props.onClose();
            }}
            onBackButtonPress={() => {
                props.onClose && props.onClose();
            }}>
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                {/*绘制头部视图*/}
                <View style={{backgroundColor: '#fff', borderRadius: 10, padding: 19, flexDirection: 'column'}}>
                    <Text style={{color: '#333', fontSize: 18, fontWeight: 'bold'}}>提示</Text>
                    {/*<Text style={{color: '#0047E1', fontSize: 15, marginTop: 20}}>一、消费规则</Text>*/}
                    <Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>
                        此运单部分运费将根据您选择的额度作为油品/气品金额结算，请关注您的智运油卡账户余额，点击APP首页“优惠加油”模块选择油气站进行加油/气消费。
                        {/*您收到油/气品金额后，{' '}*/}
                        {/*<Text style={{color: '#F44100', fontSize: 12}}>*/}
                        {/*    请尽快点击“优惠加油”消费，需在次年3月1日前完成消费；逾期未消费，平台将扣除未消费油/气品对应的经营成本（油：最高油品金额的35.5%/气：最高气品金额的33.6%），余额转入您的智运账本中。*/}
                        {/*</Text>*/}
                    </Text>
                    {/*<Text style={{color: '#0047E1', fontSize: 15, marginTop: 10}}>二、退油规则</Text>*/}
                    {/*<Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>*/}
                    {/*    1、运单结算后3天内申请退油，平台将扣除对应的<Text style={{color: '#F44100', fontSize: 12}}>经营成本（油：最高油品金额的5.5%/气：最高气品金额的3.6%），</Text>余额转入您的智运账本中。*/}
                    {/*</Text>*/}
                    {/*<Text style={{color: '#333333', fontSize: 12, marginTop: 5}}>*/}
                    {/*    2、若运单结算完成时间超过3天提出退油申请，平台将扣除对应运单的<Text style={{color: '#F44100', fontSize: 12}}>经营成本（油：最高油品金额的35.5%/气：最高气品金额的33.6%），</Text>剩余部分转入您的智运账本中。*/}
                    {/*</Text>*/}

                    <Text
                        style={{color: '#fff', fontSize: 14, marginTop: 28, textAlign: 'center', height: 40, lineHeight: 40, backgroundColor: '#5086FC', borderRadius: 20}}
                        onPress={() => {
                            props.onClose && props.onClose();
                        }}>
                        我知道了
                    </Text>
                </View>
                <UIImage source={'http://img.zczy56.com/202409260823474555371.png'} style={{width: 100, height: 100, position: 'absolute', right: 0, top: 100}} />
            </View>
        </Modal>
    );
}
