import React, {useEffect, useState} from 'react';
import Picker from 'react-native-picker';
import {Text, TouchableOpacity, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import {gStyle} from '../../../util/comm-style';
import {DP} from '../../../util/scaled-style';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import UIImage from '../../../widget/UIImage';
import OilToastDialog from './OilToastDialog';

/**
 * 油气比例与现金比例
 * @param {*} param0
 * @returns
 */
interface Props {
    //预付信息
    advanceInfo: AdvanceInfoRsp;
    //当前选择的预付方式
    advanceWay: EAdvanceType;
    //选中油气比例回调
    onSelectRatio: Function;
    //默认的油气比例
    oilRatio?: number;
}

export default function ShowPlayOilMoneyRadio(props: Props) {
    //默认油气比例
    let oilDefaultRatio = parseFloat(props.advanceInfo.oilDefaultRatio ?? '0');
    //默认现金比例
    let advanceRatio = parseFloat(props.advanceInfo.advanceRatio ?? '0');
    //可选最大油气比例
    let maxRadio = parseFloat(props.advanceInfo.maxOilCardRatio ?? '0');
    //油气比例
    const [ratio, setRatio] = useState({
        oilRatio: oilDefaultRatio,
        moneyRatio: advanceRatio - oilDefaultRatio,
    });
    // 34662	FBIS-7960	【安卓】智运油卡留存管控-油转现、奖励、预付
    const [showOilDialog, setShowOilDialog] = useState(false);

    /**
     * 注释: 设置油气比例
     * 时间: 2023/6/28 0028 10:16
     * <AUTHOR>
     */
    function setOilRatio() {
        if (TextUtils.equals('1', props.advanceWay.type)) {
            //如果选中的现金预付，则油气比例设置为0
            oilDefaultRatio = 0;
        } else {
            //如果不是现金预防，则优先取上次设置的油气比例，没有则取默认值
            oilDefaultRatio = props.oilRatio ?? oilDefaultRatio;
        }
        setRatio({
            oilRatio: oilDefaultRatio,
            moneyRatio: advanceRatio - oilDefaultRatio,
        });
    }

    //页面初始化
    useEffect(() => {
        setOilRatio();
    }, []);

    //props属性监听
    useEffect(() => {
        setOilRatio();
    }, [props.oilRatio, props.advanceWay]);

    /**
     * 注释: 选择油气比例
     * 时间: 2023/6/27 0027 19:26
     * <AUTHOR>
     */
    const onSelectOilRatio = () => {
        const myArray = props.advanceInfo.advanceOilRatioList;
        Picker.init({
            pickerData: myArray,
            pickerTitleText: '请选择比例',
            pickerConfirmBtnText: '确定',
            pickerCancelBtnText: '取消',
            onPickerConfirm: (data) => {
                Picker.hide();
                setRatio({oilRatio: data[0], moneyRatio: advanceRatio - data[0]});
                props.onSelectRatio && props.onSelectRatio(data[0]);
            },
        });
        Picker.show();
    };

    return (
        <View style={{paddingBottom: 5, paddingHorizontal: 3}}>
            {/*油气比例*/}
            {(TextUtils.equals('2', props.advanceWay.type) || TextUtils.equals('3', props.advanceWay.type)) && (
                <View style={[gStyle.view_padding, gStyle.view_row, {minHeight: 40, justifyContent: 'center'}]}>
                    <TouchableOpacity
                        style={gStyle.view_row}
                        onPress={() => {
                            setShowOilDialog(true);
                        }}>
                        <UIImage style={{width: 20, height: 20, marginRight: 8}} source={'icon_pre'} />
                        <Text style={gStyle.txt_333333_24}>申请油/气比例 （预计）</Text>
                        <UIImage style={{width: 15, height: 15, marginRight: 8}} source={'base_blue_question'} />
                    </TouchableOpacity>
                    <Text style={[gStyle.text_flex_right, gStyle.txt_333333_24, {fontWeight: 'bold'}]} onPress={onSelectOilRatio}>
                        {ratio.oilRatio + '%'}
                    </Text>
                    <UIImage source={'base_right_arrow_gray'} style={{width: 10, height: 15, marginLeft: 5}} />
                </View>
            )}
            {showOilDialog && <OilToastDialog onClose={() => setShowOilDialog(false)} />}
            {/*现金比例*/}
            {(TextUtils.equals('1', props.advanceWay.type) || TextUtils.equals('3', props.advanceWay.type)) && !TextUtils.equals('1', props.advanceInfo.fixedAmountFlag) && (
                <View style={[gStyle.view_padding, gStyle.view_row, {minHeight: 40, justifyContent: 'center'}]}>
                    <UIImage style={{width: 20, height: 20, marginRight: 8}} source={'icon_pre'} />
                    <Text style={gStyle.txt_333333_24}> 现金比例 （预计）</Text>
                    <Text style={[gStyle.text_flex_right, gStyle.txt_333333_24, {fontWeight: 'bold'}]}>{ratio.moneyRatio + '%'}</Text>
                </View>
            )}
        </View>
    );
}
