import {Text, TextInput, View} from 'react-native';
import React, {useImperativeHandle, useState} from 'react';
import {gStyle} from '../../../util/comm-style';
import {ShipmentUI} from '../models/ShipmentUI';
import {Constant} from '../../../base/Constant';
import TextUtils from '../../../util/TextUtils';
import LanguageType from '../../../util/language/LanguageType';
import {Method} from '../../../util/NativeModulesTools';
interface Props {
    maxLength?: number;
    value?: string;
}

/**
 * 注释:
 * 时间: 2023/7/31 0031 19:00
 * <AUTHOR>
 * @param props
 * @constructor
 * @param ref
 * @return {JSX.Element}
 */
function ShipmentsRemarksView(props: Props, ref: React.Ref<ShipmentsRemarksViewRef>) {
    const [remark, setRemark] = useState<string>(props.value ?? '');
    const check = (shipmentUI: ShipmentUI) => {
        if (TextUtils.isNoEmpty(remark) && TextUtils.containsEmoji(remark)) {
            Method.showToast('备注信息不能输入表情等特殊符号！');
            return false;
        }
        shipmentUI.remark = remark;
        return true;
    };

    //对外抛出方法
    useImperativeHandle(ref, () => ({check}));

    return (
        <View style={[{flex: 1, backgroundColor: '#fff', marginVertical: 7}, gStyle.view_padding]}>
            <Text style={gStyle.txt_333333_32}>备注</Text>
            <View style={{backgroundColor: Constant.color_f6f6f6, padding: 5, marginVertical: 5}}>
                <TextInput
                    textAlignVertical={'top'}
                    placeholder={LanguageType.getTxt('请输入备注内容')}
                    value={remark}
                    maxLength={props.maxLength ?? 50}
                    multiline={true}
                    numberOfLines={4}
                    onChangeText={(text) => {
                        setRemark(text);
                    }}
                />
                {/*角标*/}
                <Text
                    style={{
                        position: 'absolute',
                        right: 10,
                        bottom: 10,
                    }}>{`(${remark?.length ?? 0}/${props.maxLength ?? 50})`}</Text>
            </View>
        </View>
    );
}

export interface ShipmentsRemarksViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

export default React.forwardRef<ShipmentsRemarksViewRef, Props>(ShipmentsRemarksView);
