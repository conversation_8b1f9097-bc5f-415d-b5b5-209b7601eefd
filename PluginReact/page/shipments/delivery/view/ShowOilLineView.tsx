import React from 'react';
import { Text, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import {DP, SP} from '../../../util/scaled-style';
import {ReqOrderCoordinate} from '../requests/ReqOrderCoordinate';
import UIImage from '../../../widget/UIImage';
import {Method} from '../../../util/NativeModulesTools';

/***
 * 线路规划
 * @param orderId
 * @returns {JSX.Element}
 * @constructor
 */
interface Props {
    orderId: string;
}

export default function ShowOilLineView(props: Props) {
    const openOilMap = async () => {
        let respone = await new ReqOrderCoordinate(props.orderId).request();
        if (respone.isSuccess()) {
            Method.openOilMapDetailActivity(JSON.stringify(respone.data));
        } else {
            Method.showToast(respone.getMsg());
        }
    };

    return (
        <View style={{paddingHorizontal: 10, paddingBottom: 5}}>
            <View style={[gStyle.view_row, {backgroundColor: '#FFFAF7', height: 60}]}>
                <UIImage source={'oil_orange_bg'} style={{width: DP(84), height: DP(84), marginLeft: 5}} resizeMode={'contain'} />
                <Text style={[gStyle.txt_333333_24, {flex: 1, height: 60}]}>
                    本单平台为您规划了<Text style={{color: '#ff602e', fontSize: SP(24)}}>3</Text>
                    条线路，经过若干个合作油站。
                </Text>
                <Text style={{color: '#5086FC', fontSize: SP(24), position: 'absolute', right: 8, bottom: 0}} onPress={openOilMap}>
                    {'点击查看 >'}
                </Text>
            </View>
        </View>
    );
}
