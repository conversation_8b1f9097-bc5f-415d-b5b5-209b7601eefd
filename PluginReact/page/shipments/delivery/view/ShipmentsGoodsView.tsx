//确认发货，货物明细+实际发货量
import React, {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {Text, View} from 'react-native';
import TextUtils from '../../../util/TextUtils';
import AdvanceInfoRsp from '../models/AdvanceInfoRsp';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import EventBus from '../../../util/EventBus';
import {Constant} from '../../../base/Constant';
import {ShipmentUI} from '../models/ShipmentUI';
import GoodsInfoView from './GoodsInfoView';
import {gStyle} from '../../../util/comm-style';
import LanguageType from '../../../util/language/LanguageType';
import {Method} from '../../../util/NativeModulesTools';
interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp; //货物信息
    advanceInfo?: AdvanceInfoRsp; //场景对象
    sceneAdvance: number; //场景 0:发货预付场景 1 : 重新预付 ,2：发货后预付申请
}

function ShipmentsGoodsView(props: Props, ref) {
    //货物明细
    const [goodInfos, setGoodInfos] = useState(props.shipmentsEGoodInfo.rootArray);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        let size = goodInfos.length;

        //可输入状态：发货之后预付申请 or （龙腾特钢 过磅后的重量就传 1）吨位不能修改
        let edit = !(props.sceneAdvance == 2 || TextUtils.equals('1', props.shipmentsEGoodInfo.LTCantUpdate));

        for (let i = 0; i < size; i++) {
            let beforeDeliverCargoWeight = goodInfos[i].beforeDeliverCargoWeight?? '';
            if (edit && TextUtils.isEmpty(beforeDeliverCargoWeight)) {
                Method.showToast('请输入[' + goodInfos[i].cargoName + ']货物重量');
                return false;
            }

            if (parseFloat(beforeDeliverCargoWeight) <= 0.0) {
                Method.showToast('[' + goodInfos[i].cargoName + ']货物,货物计量不能小于等于0，请重新确认！');
                return false;
            }

            if (TextUtils.equals('1', goodInfos[i].unit) && TextUtils.isNoEmpty(goodInfos[i].weightOrientationLimit)) {
                //重货
                if (parseFloat(beforeDeliverCargoWeight) > parseFloat(goodInfos[i].weightOrientationLimit)) {
                    //zczy-13915_发货吨位提示错误
                    Method.showToast(`${goodInfos[i].cargoName}货物，货物计量不能大于${goodInfos[i].weightOrientationLimit}，请重新确认`);
                    return false;
                }
            } else if (TextUtils.equals('2', goodInfos[i].unit) && TextUtils.isNoEmpty(goodInfos[i].shippingOrientationMargin)) {
                //泡货
                if (parseFloat(beforeDeliverCargoWeight) > parseFloat(goodInfos[i].shippingOrientationMargin)) {
                    //zczy-13915_发货吨位提示错误
                    Method.showToast(`${goodInfos[i].cargoName}货物，货物计量不能大于${goodInfos[i].shippingOrientationMargin}，请重新确认`);
                    return false;
                }
            }
        }
        //货物明细
        shipmentUI.goodInfos = goodInfos;
        //是否需要上传皮毛重(非必填)
        shipmentUI.uploadGrossAndTare = TextUtils.equals('1', props.shipmentsEGoodInfo.uploadGrossAndTareWeightFlag);
        return true;
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check}));

    const onTextChangedListeners = (value) => {
        if (props.advanceInfo && TextUtils.equals('1', props.advanceInfo.advanceType) && TextUtils.equals('1', props.advanceInfo.isAdvance)) {
            // //个体司机指定订单 && 走货主预付款=> 计算货主预付费用
            let hzPayMoney = calculationAdvanceMoney(props.shipmentsEGoodInfo, props.advanceInfo, goodInfos);
            EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'hzPayMoney', value: hzPayMoney});
        }
    };
    // gLog('货物明细 列表=' + goodInfos.length);

    useEffect(() => {
        //初始化 or 货物信息方式修改
        EventBus.getInstance().fireEvent(Constant.event_shipments_page, {key: 'goodInfos', value: goodInfos});
    }, goodInfos);

    //可输入状态：发货之后预付申请 or （龙腾特钢 过磅后的重量就传 1）吨位不能修改
    let edit = !(props.sceneAdvance == 2 || TextUtils.equals('1', props.shipmentsEGoodInfo.LTCantUpdate));

    //第一个货物明细对象
    let goodInfo = goodInfos?.find((value, index) => index == 0);

    return (
        <View>
            {goodInfos.map((value) => {
                // gLog('货物明细=' + JSON.stringify(value));
                // 是否需要上传皮毛重
                let grossAndTare = TextUtils.equals('1', props.shipmentsEGoodInfo.uploadGrossAndTareWeightFlag);
                return <GoodsInfoView key={value.cargoId} detail={value} editInput={edit} grossAndTare={grossAndTare} onTextChangedListener={onTextChangedListeners} />;
            })}
            {/*4988	FBIS-487	【安卓】【客服进线】自动发货预付界面不展示吨位输入*/}
            {edit == false && TextUtils.isEmpty(goodInfo?.beforeDeliverCargoWeight) && (
                <View style={[gStyle.view_row, gStyle.view_padding]}>
                    <Text style={gStyle.txt_333333_34}>{LanguageType.getTxt('实际发货总吨位')}</Text>
                    <Text style={[gStyle.text_flex_right, gStyle.txt_666666_34]}>{`${props.shipmentsEGoodInfo.deliverWeight} ${goodInfo?.getUnitTxt() ?? '吨'}`}</Text>
                </View>
            )}
        </View>
    );
}

export interface ShipmentsGoodsViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
}

export default forwardRef<ShipmentsGoodsViewRef, Props>(ShipmentsGoodsView);

/**
 * 计算发货预付款金额[货主预付]
 *
 * @return
 */
function calculationAdvanceMoney(shipmentsEGoodInfo, advanceInfo, goodsList): string {
    // 预付比例
    let advanceRatio = 0.0;
    if (!TextUtils.isEmpty(advanceInfo.advanceRatio)) {
        advanceRatio = parseFloat(advanceInfo.advanceRatio);
    }

    // 服务费
    let expectMoney = 0.0;
    if (TextUtils.isEmpty(shipmentsEGoodInfo.expectMoney)) {
        expectMoney = 0.0;
    } else {
        expectMoney = parseFloat(shipmentsEGoodInfo.expectMoney);
    }

    if (TextUtils.equals('1', shipmentsEGoodInfo.freightType)) {
        //单价   预付运费 = (运费(单价 * 数量)- 服务费) * 预付比例
        let tmpWeight = 0.0;
        goodsList.map((value) => {
            tmpWeight += parseFloat(isNumber(value.beforeDeliverCargoWeight));
        });

        let tempMoney = tmpWeight * parseFloat(isNumber(shipmentsEGoodInfo.pbCarrierUnitMoney)) - expectMoney;
        // 防止未输入吨位减去服务费出现负值
        tempMoney = tempMoney <= 0.0 ? 0.0 : tempMoney;
        let totalMoney = totalMoneyNew(tempMoney);
        let unitAdvanceMoney = (parseFloat(totalMoney) * advanceRatio) / 100.0;

        return totalMoneyNew(unitAdvanceMoney);
    } else {
        //包车价 预付运费 = （包车价 - 服务费） * 预付比例
        let tempMoney = parseFloat(isNumber(shipmentsEGoodInfo.pbCarrierMoney)) - expectMoney;
        let totalMoney = totalMoneyNew(tempMoney);
        let advanceMoney = (parseFloat(totalMoney) * advanceRatio) / 100.0;
        return totalMoneyNew(advanceMoney);
    }
}

/**
 * 四舍五入保留2位小数
 *
 * @param money
 * @return
 */
function totalMoneyNew(money): string {
    return (Math.round(money * 100) * 0.01).toFixed(2);
}

/**
 * 判断传入的字符串是不是数字
 *
 * @param number
 * @return
 */
function isNumber(number) {
    if (TextUtils.isEmpty(number)) {
        return '0';
    } else {
        return number;
    }
}
