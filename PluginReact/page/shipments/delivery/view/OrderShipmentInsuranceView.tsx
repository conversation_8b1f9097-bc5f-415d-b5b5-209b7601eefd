import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React, {useEffect, useImperativeHandle, useState} from 'react';
import {Constant} from '../../../base/Constant';
import EventBus from '../../../util/EventBus';
import ShipmentsEGoodInfoRsp from '../models/ShipmentsEGoodInfoRsp';
import UIImage from '../../../widget/UIImage';
import TextUtils from '../../../util/TextUtils';
import UIImageBackground from '../../../widget/UIImageBackground';
import NumUtil from '../../../util/NumUtil';
import {ShipmentUI} from '../models/ShipmentUI';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';
import {Method} from '../../../util/NativeModulesTools';
interface Props {
    shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    orderId: string;
    setId?: string;
    style?: StyleProp<ViewStyle>;
    //选择回调
    onSelect?: Function;
}

/**
 * 注释: 货物保障服务
 * 时间: 2024/4/7 9:52
 * <AUTHOR>
 * @param props
 * @param ref
 * @returns {JSX.Element}
 * @constructor
 */
function OrderShipmentInsuranceView(props: Props, ref: React.Ref<OrderShipmentInsuranceViewRef>) {
    //是否购买
    const [needService, setNeedService] = useState<boolean>();
    //服务费
    const [serverMoney, setServerMoney] = useState<number>(parseFloat(props.shipmentsEGoodInfo.deliverPolicyInfo?.guaranteeFee ?? '0'));
    //购买优惠标记
    const [tag, setTag] = useState<string>('');

    useEffect(() => {
        //选中回调
        if (props.onSelect) {
            props.onSelect(needService);
        }
    }, [needService]);

    useEffect(() => {
        //处理立减
        handleReduction();
    }, []);

    /**
     * 注释: 数据检测
     * 时间: 2023/11/22 0022 10:12
     * <AUTHOR>
     * @param uiPickData
     * @returns {boolean}
     */
    function check(shipmentUI: ShipmentUI) {
        if (needService == true) {
            shipmentUI.buyPolicyFlag = '1';
            shipmentUI.buyPolicyCouponId = props.shipmentsEGoodInfo.deliverPolicyInfo?.buyPolicyCouponId;
        } else if (needService == false) {
            shipmentUI.buyPolicyFlag = '0';
        } else {
            Method.showToast('请选是否购货物保障服务!');
            return false;
        }
        return true;
    }

    useImperativeHandle(ref, () => ({check}));

    /**
     * 注释: 处理立减活动
     * 时间: 2023/11/9 0009 8:47
     * <AUTHOR>
     * @param ePolicyCoupon
     */
    function handleReduction() {
        if (TextUtils.isNoEmpty(props.shipmentsEGoodInfo.deliverPolicyInfo?.reduceGuaranteeFee)) {
            let tag = '限时优惠';
            tag = '限时立减';
            setTag(tag);
            let money = parseFloat(props.shipmentsEGoodInfo.deliverPolicyInfo?.leftGuaranteeFee ?? '0');
            setServerMoney(NumUtil.halfup(money > 0 ? money : 0));
        }
    }

    /**
     * 注释: 绘制超载提示
     * 时间: 2023/11/6 0006 19:11
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderOverloadTip() {
        if (needService) {
            return (
                <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 7}}>
                    <Text style={{fontSize: 12, color: '#FF5B5B'}}>因超载引起的事故存在无法赔付风险</Text>
                </View>
            );
        }
    }

    /**
     * 注释: 客服咨询
     * 时间: 2023/11/6 0006 19:12
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderCustomerService() {
        let cargoMoney = parseFloat(props.shipmentsEGoodInfo.deliverPolicyInfo?.cargoMoney ?? '0');
        let tip = `仅需${serverMoney}元即可保障${(cargoMoney / 10000).toFixed(2)}万元货值`;

        return (
            <UIImageBackground
                source={'http://img.zczy56.com/202401191635345904884.png'}
                resizeMode={'stretch'}
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    position: 'relative',
                    top: 0,
                    zIndex: 1,
                    left: 7,
                }}>
                <Text style={{fontSize: 9, color: '#F64F4F', margin: 4}}>{tip}</Text>
            </UIImageBackground>
        );
    }

    /**
     * 注释: 绘制选择视图
     * 时间: 2023/11/6 0006 19:13
     * <AUTHOR>
     */
    function renderSelectView() {
        let cargoMoney = parseFloat(props.shipmentsEGoodInfo.deliverPolicyInfo?.cargoMoney ?? '0');
        let originalPrice = parseFloat(props.shipmentsEGoodInfo.deliverPolicyInfo?.guaranteeFee ?? '0');
        let showFee = TextUtils.isNoEmpty(props.shipmentsEGoodInfo.deliverPolicyInfo?.reduceGuaranteeFee);
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginVertical: 8,
                }}>
                <UITouchableOpacity
                    activeOpacity={1}
                    onPress={() => {
                        setNeedService(false);
                        EventBus.getInstance().fireEvent(Constant.event_pick_insurance_coupon, {
                            key: 'needService',
                            value: false,
                        });
                    }}
                    style={{
                        width: 102.5,
                        height: 50,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderColor: needService ?? true ? '#999' : '#5086FC',
                        borderRadius: 8,
                    }}>
                    <Text style={{fontSize: 14, color: '#666'}}>不购买</Text>
                    <UIImage source={needService ?? true ? 'base_choose_car_item_unselect' : 'base_choose_car_item_select'} style={styles.selectTag} />
                </UITouchableOpacity>
                {/*购买*/}
                <UITouchableOpacity
                    activeOpacity={1}
                    onPress={() => {
                        setNeedService(true);
                        EventBus.getInstance().fireEvent(Constant.event_pick_insurance_coupon, {
                            key: 'needService',
                            value: true,
                        });
                    }}
                    style={{
                        width: 220,
                        height: 50,
                        marginLeft: 5,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderColor: needService ? '#5086FC' : '#999',
                        borderRadius: 8,
                    }}>
                    <View style={{alignItems: 'center'}}>
                        <View style={{flexDirection: 'row', marginLeft: 14}}>
                            <Text style={{fontSize: 14, color: '#5086FC', fontWeight: 'bold'}}>购买</Text>
                            <View style={{flex: 1}} />
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    marginRight: 18,
                                }}>
                                {/*原价*/}
                                {originalPrice != serverMoney && <Text style={styles.originalPrice}>{`${originalPrice.toFixed(2)}`}</Text>}
                                {/*优惠价*/}
                                <Text style={styles.discountedPrice}>
                                    <Text style={{fontSize: 11}}>¥</Text>
                                    {`${serverMoney.toFixed(2)}`}
                                </Text>
                            </View>
                        </View>
                        <View style={{flexDirection: 'row'}}>
                            <Text style={styles.feeStyle}>{`预计保障货值${cargoMoney.toFixed(2)}元`}</Text>
                            <View style={{flex: 1}} />
                            <Text
                                style={{
                                    fontSize: 9,
                                    color: '#999',
                                    textAlign: 'center',
                                    marginRight: 10,
                                }}>
                                此单预计服务费
                            </Text>
                        </View>
                    </View>
                    <UIImage source={needService ? 'base_choose_car_item_select' : 'base_choose_car_item_unselect'} style={styles.selectTag} />
                    {/*优惠标记*/}
                    {showFee ? renderTag() : <View />}
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释: 绘制Tag
     * 时间: 2023/11/8 0008 16:30
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderTag() {
        if (TextUtils.isNoEmpty(tag)) {
            return (
                <UIImageBackground source={'http://img.zczy56.com/202401221918455354352.png'} resizeMode={'stretch'} style={styles.discountStyle}>
                    <Text style={{fontSize: 9, color: '#fff', margin: 4}}>{tag}</Text>
                </UIImageBackground>
            );
        }
        return <View />;
    }

    //需要购买货物保障服务
    return (
        <View style={[{backgroundColor: '#fff', borderRadius: 6}, props.style]}>
            <View style={{paddingHorizontal: 7}}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text style={{fontSize: 15, color: '#333', marginTop: 7}}>货物安全保障</Text>
                    {/*货物保障提示*/}
                    {renderCustomerService()}
                </View>
                {/*超载提示*/}
                {renderOverloadTip()}
                {/*选择购买视图*/}
                {renderSelectView()}
                {TextUtils.isNoEmpty(props.shipmentsEGoodInfo.deliverPolicyInfo?.deductibleDesc) && (
                    <Text
                        style={{
                            marginVertical: 7,
                            fontSize: 14,
                            color: '#666',
                        }}>{`免赔额说明：${props.shipmentsEGoodInfo.deliverPolicyInfo?.deductibleDesc ?? ''}`}</Text>
                )}
            </View>
        </View>
    );
}

export interface OrderShipmentInsuranceViewRef {
    check: (uiPickData) => boolean;
}

export default React.forwardRef<OrderShipmentInsuranceViewRef, Props>(OrderShipmentInsuranceView);

const styles = StyleSheet.create({
    activityInfo: {
        width: 107,
        height: 21.6,
        justifyContent: 'flex-start',
        alignItems: 'flex-end',
        paddingRight: 5,
        paddingTop: 1,
    },
    discountedPrice: {
        fontSize: 15,
        color: '#555',
        fontWeight: 'bold',
        marginLeft: 5,
    },
    originalPrice: {
        fontSize: 11,
        color: '#555',
        textDecorationLine: 'line-through',
    },
    discountTag: {
        position: 'absolute',
        top: 0,
        left: 0,
        backgroundColor: '#fff',
        borderWidth: 1,
        padding: 2,
        borderColor: '#f54545',
        borderTopLeftRadius: 4,
        textAlign: 'center',
        borderBottomRightRadius: 4,
        fontSize: 9,
        color: '#F54545',
    },
    discountSelectTag: {
        position: 'absolute',
        top: 0,
        left: 0,
        backgroundColor: '#f54545',
        borderWidth: 1,
        padding: 2,
        borderColor: '#f54545',
        borderTopLeftRadius: 4,
        textAlign: 'center',
        borderBottomRightRadius: 4,
        fontSize: 9,
        color: '#FFF',
    },
    smartTag: {
        position: 'absolute',
        top: 0,
        left: 0,
        backgroundColor: '#C2962F',
        padding: 2,
        borderTopLeftRadius: 4,
        textAlign: 'center',
        borderBottomRightRadius: 4,
        fontSize: 9,
        color: '#FFFFFF',
    },
    selectCoupon: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 7,
        justifyContent: 'space-between',
        height: 42,
    },
    itemView: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 40,
    },
    feeStyle: {
        fontSize: 9,
        color: '#999',
        marginLeft: 14,
    },
    selectTag: {
        width: 33,
        height: 33,
        position: 'absolute',
        top: -1,
        right: -1,
    },
    discountStyle: {
        position: 'absolute',
        top: -12,
        right: 14,
        zIndex: 1,
        alignItems: 'center',
    },
    insuranceTip: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 7,
        backgroundColor: '#fff9ed',
        borderTopLeftRadius: 6,
        borderTopRightRadius: 6,
    },
});
