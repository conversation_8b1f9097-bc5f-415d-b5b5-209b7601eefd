import {EAdvanceType} from '../models/AdvanceInfoRsp';
import {Image, Text, View, ViewProps} from 'react-native';
import {DP, SP} from '../../../util/scaled-style';
import React from 'react';
import {Constant} from '../../../base/Constant';
import UIImage from '../../../widget/UIImage';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';

interface Props extends ViewProps {
    data: EAdvanceType;
    onPress: Function;
    selected: boolean;
    title: string;
    //预付服务费优惠信息
    content?: JSX.Element;
    //是否显示优惠信息
    showFavourable?: boolean;
    //是否是智运卡优惠
    smartTransportCardStyle?: boolean;
    //优惠文案
    favourableTxt?: string;
    //显示活动
    activityIconShow?: boolean;
    //显示油品活动
    showOilOnPrePolicy?: boolean;
    //活动名称
    activityName?: string;
}

export default function SelectAdvanceWayItem(props: Props) {
    return (
        <UITouchableOpacity
            activeOpacity={0.9}
            onPress={() => {
                props.onPress(props.data);
            }}
            style={{flex: 1, marginHorizontal: 5}}>
            <View
                style={{
                    flex: 1,
                    alignItems: 'center',
                    padding: 5,
                    backgroundColor: Constant.color_f6f6f6,
                    borderRadius: 4,
                    borderWidth: 1,
                    borderColor: props.selected ? Constant.color_5086fc : Constant.color_transparent,
                }}>
                <Text
                    style={{
                        paddingTop: 7,
                        color: props.selected ? Constant.color_5086fc : Constant.color_333333,
                        fontWeight: '500',
                        fontSize: SP(24),
                    }}>
                    {props.title}
                </Text>
                {/*预付服务费优惠信息*/}
                {props.content}
                {/*活动标记*/}
                {props.activityIconShow && (
                    <Text
                        style={{
                            color: '#fff',
                            backgroundColor: '#FF451C',
                            borderRadius: 4,
                            fontSize: SP(20),
                        }}>
                        {props.activityName}
                    </Text>
                )}
                {/*左上角优惠信息*/}
                {props.showFavourable && (
                    <Text
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            color: '#fff',
                            backgroundColor: props.smartTransportCardStyle ? Constant.color_c2962f : Constant.color_ff451c,
                            borderTopLeftRadius: 3,
                            borderBottomRightRadius: 3,
                            fontSize: 10,
                            paddingLeft: 7,
                            paddingRight: 7,
                        }}>
                        {props.showOilOnPrePolicy ? '赠油气券' : props.favourableTxt}
                    </Text>
                )}
                {/*选中图片*/}
                {props.selected && <UIImage style={{position: 'absolute', top: 0, right: 0, width: DP(52), height: DP(46.5)}} source={'icon_select'} />}
            </View>
        </UITouchableOpacity>
    );
}
