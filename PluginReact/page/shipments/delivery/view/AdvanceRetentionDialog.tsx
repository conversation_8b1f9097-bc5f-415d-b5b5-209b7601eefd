import {StyleSheet, Text, View, ViewProps} from 'react-native';
import React, {useRef} from 'react';
import Modal from 'react-native-modal';
import AdvanceInfoRsp, {EAdvanceType} from '../models/AdvanceInfoRsp';
import {DP, gScreen_height, gScreen_width, SP} from '../../../util/scaled-style';
import {Constant} from '../../../base/Constant';
import TextUtils from '../../../util/TextUtils';
import UIImage from '../../../widget/UIImage';
import UIImageBackground from '../../../widget/UIImageBackground';
import UITouchableOpacity from '../../../widget/UITouchableOpacity';

interface Props extends ViewProps {
    //预付信息
    advanceInfo: AdvanceInfoRsp;
    //暂不购买
    onNoBuyCallBack?: Function;
    //选择购买
    onBuyCallBack?: Function;
    //优惠金额
    preferentialMoney?: string;
    //预付方式
    advanceWay?: EAdvanceType;
}

/**
 * 注释:   预付挽留弹窗
 * 时间: 2023/6/25 0025 19:16
 * <AUTHOR>
 * @param props
 * @param ref
 * @constructor
 */
export default function AdvanceRetentionDialog(props: Props) {
    let dialog = useRef<Modal>(null);

    /**
     * 注释: 绘制头部
     * 时间: 2023/6/25 0025 19:29
     * <AUTHOR>
     */
    function renderHeaderView() {
        return (
            <View>
                <UIImage source={'kind_tips'} style={styles.headView} />
            </View>
        );
    }

    /**
     * 注释: 绘制提示视图
     * 时间: 2023/6/25 0025 19:29
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderTipView() {
        return (
            <View
                style={{
                    backgroundColor: '#fff',
                    paddingTop: 35,
                    paddingHorizontal: 30,
                    width: gScreen_width - 45,
                    borderTopLeftRadius: 8,
                    borderTopRightRadius: 8,
                }}>
                <Text
                    style={{
                        fontSize: SP(32),
                        fontWeight: 'bold',
                        color: Constant.color_333333,
                    }}>{`已有${props.advanceInfo.advanceOrderDriverCount}位司机申请预付，是否确定不申请？`}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制按钮视图
     * 时间: 2023/6/25 0025 19:34
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderButtonView() {
        return (
            <View
                style={{
                    backgroundColor: '#FFF',
                    flexDirection: 'row',
                    width: gScreen_width - 45,
                    borderBottomLeftRadius: 8,
                    borderBottomRightRadius: 8,
                }}>
                {/*不购买*/}
                <UITouchableOpacity
                    style={styles.buttonStyle}
                    onPress={() => {
                        props.onNoBuyCallBack && props.onNoBuyCallBack();
                    }}>
                    <Text style={{color: Constant.color_5086fc}}>不申请</Text>
                </UITouchableOpacity>
                {/*购买*/}
                <UITouchableOpacity
                    style={[styles.buttonStyle, {backgroundColor: Constant.color_5086fc}]}
                    onPress={() => {
                        props.onBuyCallBack && props.onBuyCallBack();
                    }}>
                    <Text style={{color: Constant.color_white}}>申请</Text>
                </UITouchableOpacity>
                {/*不是油品预付并且优惠金额不为空，展示优惠标记*/}
                {TextUtils.isNoEmpty(props.preferentialMoney) && !TextUtils.equals('2', props.advanceWay?.type) && (
                    <UIImageBackground source={'icon_order_pick_insu'} style={styles.tagStyle}>
                        <Text
                            style={{
                                fontSize: SP(20),
                                color: Constant.color_white,
                            }}>
                            {'仅限此单\n'}
                            <Text
                                style={{
                                    color: Constant.color_ed5400,
                                    fontWeight: 'bold',
                                }}>{`服务费优惠${props.preferentialMoney}`}</Text>
                        </Text>
                    </UIImageBackground>
                )}
            </View>
        );
    }

    return (
        <Modal
            ref={dialog}
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            isVisible={true}
            hideModalContentWhileAnimating={true}
            deviceWidth={gScreen_width}
            deviceHeight={gScreen_height}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            onBackdropPress={() => {
                props.onNoBuyCallBack && props.onNoBuyCallBack();
            }}
            onBackButtonPress={() => {
                props.onNoBuyCallBack && props.onNoBuyCallBack();
            }}>
            <View style={styles.mainView}>
                {/*绘制头部视图*/}
                {renderHeaderView()}
                {/*绘制提示文案*/}
                {renderTipView()}
                {/*绘制按钮视图*/}
                {renderButtonView()}
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headView: {
        width: gScreen_width,
        height: (gScreen_width / 1125) * 327,
    },
    buttonStyle: {
        marginVertical: 25,
        marginHorizontal: 15,
        flex: 1,
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 8,
        borderColor: Constant.color_5086fc,
    },
    tagStyle: {
        width: DP(250),
        height: DP((250 / 303) * 92),
        position: 'absolute',
        right: 18,
        justifyContent: 'center',
        paddingLeft: DP(18),
    },
});
