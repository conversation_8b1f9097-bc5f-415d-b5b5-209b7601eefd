import {StyleSheet, Text, TextInput, View} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import React, {useEffect, useState} from 'react';
import {SP} from '../../../util/scaled-style';
import {EGoodInfo} from '../models/ShipmentsEGoodInfoRsp';
import TextUtils from '../../../util/TextUtils';
import LanguageType from '../../../util/language/LanguageType';

interface Props {
    detail: EGoodInfo;
    editInput?: boolean;
    hasReadIcon?: boolean; //红色* 是否显示
    onTextChangedListener?: Function;
}

/**
 * 注释: 货物明细[零担]
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function ZeroGoodsInfoView(props: Props) {
    let unitText = props.detail.getUnitTxt();

    const [beforeDeliverCargoWeight, setBeforeDeliverCargoWeight] = useState<string>(props.detail.beforeDeliverCargoWeight);

    const onChangeText = (text) => {
        // 10位整数, 4位小数
        let reg = /^(0|[1-9]\d{0,9})$|^(0|[1-9]\d{0,9})\.(\d{0,4})$/;
        let isMatch = TextUtils.isEmpty(text) || reg.test(text);
        if (isMatch) {
            // 保留4位小数并更新状态
            setBeforeDeliverCargoWeight(text);
            //输入框改变刷新界面
            props.detail.beforeDeliverCargoWeight = text;
            props.onTextChangedListener && props.onTextChangedListener(props.detail);
        } else {
            setBeforeDeliverCargoWeight(beforeDeliverCargoWeight);
        }
    };

    return (
        <View key={props.detail.cargoId}>
            <View style={gStyle.view_row}>
                <Text style={gStyle.txt_333333_34}>货物明细</Text>
                <Text style={[gStyle.text_flex_right, gStyle.txt_666666_34]}>{props.detail.cargoName + '【' + props.detail.weight + unitText + '】'}</Text>
            </View>
            <View style={[gStyle.view_row, {marginTop: 10}]}>
                <Text style={gStyle.txt_333333_34}>实际发货吨位</Text>
                {(props.hasReadIcon ?? true) && <Text style={{color: '#F55648'}}>*</Text>}
                <TextInput style={styles.good_info_input} clearButtonMode="while-editing" keyboardType="numeric" value={beforeDeliverCargoWeight} editable={props.editInput ?? true} placeholder="请输入" onChangeText={onChangeText} />
                <Text style={gStyle.txt_333333_34}>{unitText}</Text>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    good_info_input: {
        padding: 2,
        flex: 1,
        fontSize: 17,
        textAlign: 'right',
        marginLeft: 5,
        marginRight: 5,
    },
});
