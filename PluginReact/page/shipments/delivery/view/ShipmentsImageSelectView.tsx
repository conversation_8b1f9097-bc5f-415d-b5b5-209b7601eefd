import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {StyleProp, Text, View, ViewStyle} from 'react-native';
import {gStyle} from '../../../util/comm-style';
import TextUtils from '../../../util/TextUtils';
import {http} from '../../../const.global';
import {EWater} from '../models/ShipmentsEGoodInfoRsp';
import {ShipmentUI} from '../models/ShipmentUI';
import UIImagePostItem from '../../../widget/UIImagePostItem';
import TerracePaymentDialog from './DemoShipmentImageDemoDialog';
import {Method} from '../../../util/NativeModulesTools';
import UIImage from '../../../widget/UIImage';
/**
 * 图片上传
 */

interface Props {
    orderId: string; //运单ID
    imgs?: EWater[]; //图片集合
    onChange?: Function; //图片改变回调
    title: string; //标题
    toast: string; //提示
    warning?: string; //警示信息
    showReadToastIcon: boolean; //* 必传标识
    waterMarkFlag?: string; // 1 加水印标识
    takePhoto: boolean; //只拍照
    edit: boolean; //是否可编辑
    max: number; //最大上传数量
    type: string; ////1 单据 2 人货车
    style?: StyleProp<ViewStyle>;
    //上传照片回调
    addChanged?: Function;
}

function ShipmentsImageSelectView(props: Props, ref) {
    const [imgsList, setImageList] = useState(props.imgs ?? []);

    const [showDemoDailog, setShowDemoDailog] = useState(false);

    const check = (shipmentUI: ShipmentUI) => {
        //参数检查
        if (props.showReadToastIcon) {
            //比传
            if (imgsList.length <= 0) {
                Method.showToast(`未上传${props.title}，请上传后再提交`);
                return false;
            }
        }
        return true;
    };

    //当前运行状态
    const running = useRef<boolean>(true);
    const imgsListRef = useRef<EWater[]>(imgsList);

    useEffect(() => {
        return () => {
            running.current = false;
        };
    }, []);

    const getImages = () => {
        //获取数据
        return imgsList;
    };

    const clear = () => {
        //清空数据
        setImageList([]);
    };
    //对外部公开调用方法
    useImperativeHandle(ref, () => ({check, clear, getImages}));

    const lookImagePage = (item, imageUrl) => {
        //查看大图
        Method.onLookImageClick2(imageUrl);
    };

    const delPic = (index, item, imageUrl) => {
        //删除
        let newList = imgsList.filter((value) => !TextUtils.equals(value.picUrl, item.picUrl));
        setImageList(newList);
        Method.showToast('已删除图片');
        imgsListRef.current = newList;
        props.onChange && props.onChange(newList);
    };

    /***上传文件*/
    const upFile = async (imageWater, imageOld, compateCallback = () => {}) => {
        console.log('上传图片==开始====' + imageWater);
        let json = await Method.upFileNew(imageWater);
        let result = JSON.parse(json);

        if (TextUtils.equals('200', result.code)) {
            let water = new EWater();
            water.picUrl = result.url;

            imgsListRef.current = [...imgsListRef.current, water];
            //更新显示
            let list = imgsListRef.current;
            setImageList(imgsListRef.current);

            props.onChange && props.onChange(list);
            //上传照片回调
            props.addChanged && props.addChanged();

            if (TextUtils.isNoEmpty(imageOld)) {
                //上传旧图
                let oldJson = await Method.upFileNew(imageOld);
                let oldResult = JSON.parse(oldJson);
                if (TextUtils.equals('200', oldResult.code)) {
                    water.picOrgUrl = oldResult.url;
                }
            }
        }

        compateCallback();
        console.log('上传图片==结束====' + imageWater);
    };

    /***接收返回拍照或选择图片(加水印)*/
    const activityResultFile = (code, json) => {
        try {
            console.log('上传图片======' + json);
            let result = JSON.parse(json);
            if (code == 200) {
                if (imgsList.length >= props.max) {
                    Method.showToast('图片数量已达上限！');
                    return;
                }

                //上传图片 显示loading
                Method.showWaitDialog();
                let count = 0;
                let compateCallback = () => {
                    count++;
                    if (count >= result.length) {
                        //隐藏loading
                        Method.hideWaitDialog();
                    }
                };
                result.map(async (item) => {
                    await upFile(item.imageWater, item.imageOld, compateCallback);
                });
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            console.log('上传图片======' + e);
            Method.hideWaitDialog();
            Method.showToast('请重新选择上传文件(R1001');
        }
    };

    /***接收返回拍照或选择图片(无水印)*/
    const activityNoWaterResultFile = (code, json) => {
        try {
            let result = JSON.parse(json);
            if (code == 200) {
                if (imgsList.length >= props.max) {
                    Method.showToast('图片数量已达上限！');
                    return;
                }
                //上传图片文件是集合  显示loading
                Method.showWaitDialog();
                let count = 0;
                let compateCallback = () => {
                    count++;
                    if (count >= result.length) {
                        //隐藏loading
                        Method.hideWaitDialog();
                    }
                };
                result.map(async (item) => {
                    await upFile(item, '', compateCallback);
                });
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            Method.hideWaitDialog();
            Method.showToast('请重新选择上传文件(R1001');
        }
    };

    /**
     * 选择图片
     */
    const choosePic = () => {
        let count = props.max - imgsList.length;
        if (TextUtils.equals('1', props.waterMarkFlag)) {
            //加水印
            Method.openCameraAndLibWater(props.takePhoto, props.orderId, count > 0 ? count : 1, activityResultFile);
        } else {
            //不加水印
            Method.openCameraAndLibNew(props.takePhoto, count > 0 ? count : 1, activityNoWaterResultFile);
        }
    };

    return (
        <View style={[gStyle.view_padding, {paddingBottom: 7}, props.style]} key={'imgs'}>
            {/*标题*/}
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                {props.showReadToastIcon && <Text style={{color: '#fb6b40'}}>*</Text>}
                <Text style={gStyle.txt_333333_32}>{props.title}</Text>
                <Text style={[gStyle.txt_999999_24, {flex: 1}]}>{props.toast}</Text>
                {/*{props.edit && (*/}
                {/*    <Text style={{color: '#5086fc', fontSize: 12}} onPress={() => setShowDemoDailog(true)}>*/}
                {/*        查看示例*/}
                {/*    </Text>*/}
                {/*)}*/}
            </View>
            {/*提示文案*/}
            {TextUtils.isNoEmpty(props.warning) && <Text style={{color: '#FB6B40', fontSize: 11, paddingBottom: 5}}>{props.warning}</Text>}
            {props.edit && <UIImage source={'http://img.zczy56.com/202406201026508424812.png'} style={{width: 277, height: 133, alignSelf: 'center'}} />}
            {/*/!*人车货合影示例*!/*/}
            {/*{TextUtils.equals('人车货合影', props.title) && <UIImage source={'img_peoplecar_demo'} style={{width: 345, height: 131, alignSelf: 'center'}} />}*/}
            {/*展示内容*/}
            <UIImagePostItem item={imgsList} edit={props.edit} max={props.max} rowSize={4} delPic={delPic} lookImagePage={lookImagePage} choosePic={choosePic} transformUrl={(item) => http.imagUrl(item.picUrl)} />
            {/*示例弹窗*/}
            {showDemoDailog && props.edit && <TerracePaymentDialog waybillImage={props.type} onClose={() => setShowDemoDailog(false)} />}
        </View>
    );
}

export interface ShipmentsImageSelectViewRef {
    check: (shipmentUI: ShipmentUI) => boolean;
    clear: () => void; //清空数据
    getImages: () => EWater[];
}

export default forwardRef<ShipmentsImageSelectViewRef, Props>(ShipmentsImageSelectView);
