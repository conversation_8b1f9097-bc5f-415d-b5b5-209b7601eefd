import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../../widget/UITitleView';
import ShipmentsImageSelectView, {ShipmentsImageSelectViewRef} from './view/ShipmentsImageSelectView';
import TextUtils from '../../util/TextUtils';
import {ShipmentUI} from './models/ShipmentUI';
import ShipmentsRemarksView, {ShipmentsRemarksViewRef} from './view/ShipmentsRemarksView';
import ShipmentsZeroGoodsView, {ShipmentsZeroGoodsViewRef} from './view/ShipmentsZeroGoodsView';
import ShipmentsZeroHZGoodsView, {ShipmentsZeroHZGoodsViewRef} from './view/ShipmentsZeroHZGoodsView';
import ShipmentsEZeroGoodInfoRsp from './models/ShipmentsEZeroGoodInfoRsp';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import {PermissionType, PermissionUtil} from '../../util/PermissionUtil';
import AntiDraudDialog from '../../pick/views/AntiDraudDialog';
import AgreementView, {AgreementViewRef, EAgreementType} from '../../pick/views/AgreementView';
import {ShipmentsZeroDto} from './models/ShipmentsZeroDto';
import {Method} from '../../util/NativeModulesTools';
import ShipmentVideoRemarkView, {ShipmentVideoRemarkViewRef} from './view/ShipmentVideoRemarkView';
interface State extends BaseState {
    init: boolean;
}

/**
 * 注释: 批量确认发货【零担】
 * <AUTHOR>
 */
export default class ShipmentsZeroBatchPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    //原生端传过来的值
    waybill: any;
    orderId: string;
    detailId: string;

    //发货单信息
    mShipmentsEGoodInfo: ShipmentsEZeroGoodInfoRsp;
    private shipmentsZeroDto = new ShipmentsZeroDto(this);
    /***  货物明细 控件对象*/
    refGoodInfoView = React.createRef<ShipmentsZeroGoodsViewRef>();
    /***  货物明细 控件对象*/
    refHZGoodsViewRef = React.createRef<ShipmentsZeroHZGoodsViewRef>();
    /***  运单图片 控件对象*/
    refImagesWaybillView = React.createRef<ShipmentsImageSelectViewRef>();
    /***  人车货照片 控件对象*/
    refIImagesCarPersonView = React.createRef<ShipmentsImageSelectViewRef>();

    //备注视图索引
    private refShipmentsRemarksView = React.createRef<ShipmentVideoRemarkViewRef>();
    //协议Ref
    refAgreementView = React.createRef<AgreementViewRef>();

    //人脸识别标记
    private faceIdentifyFlag?: string;

    constructor(props) {
        super(props);
        this.waybill = this.getParams();
        this.orderId = this.waybill.orderId;
        this.detailId = this.waybill.detailId;
        this.faceIdentifyFlag = '';
        this.state = {
            init: false,
        };
    }

    componentDidMount() {
        super.componentDidMount();

        //请求货物信息和预付信息
        this.shipmentsZeroDto.openSceneMerge(this.waybill, (data) => {
            this.mShipmentsEGoodInfo = data ?? new ShipmentsEZeroGoodInfoRsp();
            this.setState({
                init: true,
            });
        });
    }

    postShipments = () => {
        if (!Method.isOpenGPS()) {
            //打开GPS 设置
            Method.openGPS();
            return;
        }
        PermissionUtil.applyPermission(
            '中储智运需申请您的地址位置权限,以便为您确认实际到达收发货地点服务。拒绝或取消授权不影响使用其他服务',
            (code, msg) => {
                if (code == 200) {
                    this.checkAndPost();
                } else {
                    this._showMsgDialog(msg);
                }
            },
            PermissionType.LOCATION,
        );
    };
    checkAndPost = async () => {
        if (TextUtils.equals('1', this.mShipmentsEGoodInfo.faceIdentifyFlag) && Method.getHostVersion() >= 1900) {
            //大于等于804版本，需要进行人脸识别
            if (TextUtils.isEmpty(this.faceIdentifyFlag)) {
                //当前没有进行人脸识别
                let response = await RouterUtils.skipRouter(RouterUrl.FaceRecognitionPage);
                if (response != null) {
                    this.faceIdentifyFlag = response.code;
                }
            }
        }
        //确认发货
        let shipmentUI = new ShipmentUI();
        shipmentUI.faceIdentifyFlag = this.faceIdentifyFlag;
        shipmentUI.zeroShipmentsEGoodInfo = this.mShipmentsEGoodInfo;
        //SDK是否可打开
        shipmentUI.haveOpenSdk = TextUtils.equals('1', this.mShipmentsEGoodInfo.haveOpenSdk);

        let ok = true;

        if (ok && this.refGoodInfoView.current) {
            //货物明细
            ok = this.refGoodInfoView.current.check(shipmentUI);
        }
        if (ok && this.refHZGoodsViewRef.current) {
            //同货主，同发货地
            ok = this.refHZGoodsViewRef.current?.check(shipmentUI);
        }

        if (ok && this.refImagesWaybillView.current) {
            //运单图片
            ok = this.refImagesWaybillView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesWaybill = this.refImagesWaybillView.current.getImages();
            }
        }
        if (ok && this.refIImagesCarPersonView.current) {
            //人车货图片
            ok = this.refIImagesCarPersonView.current.check(shipmentUI);
            if (ok) {
                shipmentUI.imagesCarPerson = this.refIImagesCarPersonView.current.getImages();
            }
        }
        if (ok && this.refShipmentsRemarksView.current) {
            ok = this.refShipmentsRemarksView.current.check(shipmentUI);
        }
        //协议校验
        if (!this.refAgreementView.current?.check()) {
            return;
        }
        if (!ok) {
            return;
        }
        // 确认发货
        await this.shipmentsZeroDto.confrimMergeOrderDeliver(shipmentUI);
    };

    renderOrderImg = () => {
        //4.单据照片
        let orderImgObj = this.mShipmentsEGoodInfo.orderImgObj;
        return (
            orderImgObj &&
            TextUtils.equals('1', orderImgObj.showUploadFlag) && (
                <ShipmentsImageSelectView
                    ref={this.refImagesWaybillView}
                    key={'imagesWaybill'}
                    style={{marginTop: 5}}
                    orderId={this.orderId}
                    imgs={this.mShipmentsEGoodInfo?.imageJsonObjArr}
                    title={orderImgObj?.title ?? ''}
                    toast={`最多上传${orderImgObj?.limitCount}张`}
                    warning={orderImgObj?.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', orderImgObj?.uploadFlag)}
                    waterMarkFlag={orderImgObj?.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', orderImgObj?.takePhotoFlag)}
                    edit={true}
                    type={'1'}
                    max={orderImgObj?.limitCount ?? 8}
                />
            )
        );
    };

    renderPeopleVehicleImg = () => {
        //人车货合影
        let peopleVehicleImgObj = this.mShipmentsEGoodInfo?.peopleVehicleImgObj;
        return (
            peopleVehicleImgObj &&
            TextUtils.equals('1', peopleVehicleImgObj.showUploadFlag) && (
                <ShipmentsImageSelectView
                    ref={this.refIImagesCarPersonView}
                    key={'imagesCarPerson'}
                    orderId={this.orderId}
                    imgs={this.mShipmentsEGoodInfo?.imageJsonObjArr2}
                    title={peopleVehicleImgObj.title ?? ''}
                    toast={`最多上传${peopleVehicleImgObj.limitCount}张`}
                    warning={peopleVehicleImgObj.warningMsg ?? ''}
                    showReadToastIcon={TextUtils.equals('1', peopleVehicleImgObj.uploadFlag)}
                    waterMarkFlag={peopleVehicleImgObj.waterMarkFlag ?? ''}
                    takePhoto={TextUtils.equals('1', peopleVehicleImgObj.takePhotoFlag)}
                    edit={true}
                    type={'2'}
                    max={peopleVehicleImgObj.limitCount ?? 8}
                />
            )
        );
    };

    render() {
        //头部
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'批量确认发货'} />
                <ScrollView style={{flex: 1}}>
                    {/*1. 货物明细*/}
                    {this.state.init && <ShipmentsZeroGoodsView shipmentsEGoodInfo={this.mShipmentsEGoodInfo} editInput={true} ref={this.refGoodInfoView} />}
                    {/*2. 同货主*/}
                    {this.state.init && <ShipmentsZeroHZGoodsView shipmentsEGoodInfo={this.mShipmentsEGoodInfo} editInput={true} ref={this.refHZGoodsViewRef} />}
                    {/*4.单据照片*/}
                    {this.state.init && this.renderOrderImg()}
                    {/*5.人车货合影*/}
                    {this.state.init && this.renderPeopleVehicleImg()}
                    {/*备注视图*/}
                    {this.state.init && <ShipmentVideoRemarkView ref={this.refShipmentsRemarksView} max={3} edit={true} deliverVideoArray={this.mShipmentsEGoodInfo.deliverVideoJsonArr} remark={this.mShipmentsEGoodInfo.carrierDeliverRemark} />}
                    {/*6.必需显示内容 协议*/}
                    {this.state.init && <AgreementView ref={this.refAgreementView} style={{marginHorizontal: 17, marginVertical: 8, marginBottom: 10}} keys={[EAgreementType.SHIP, EAgreementType.LOCAL]} />}
                    {/*7.防诈骗对话框*/}
                    {this.state.init && <AntiDraudDialog orderId={this.orderId} selectNode={'5'} />}
                </ScrollView>
                {/* 底部按钮 */}
                <Text style={styles.bottom_view_right} onPress={this.postShipments}>
                    确认发货
                </Text>
                {/*基础组件初始化*/}
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    bottom_view_right: {
        textAlign: 'center',
        textAlignVertical: 'center',
        backgroundColor: '#5086FC',
        height: 50,
        fontSize: 18,
        color: '#fff',
    },
    _top_orderId: {
        backgroundColor: '#fff',
        fontSize: 14,
        color: '#666666',
        paddingLeft: 15,
        paddingTop: 13,
        paddingBottom: 13,
    },
    _top_hz: {
        fontSize: 14,
        color: '#FF602E',
        padding: 13,
    },
});
