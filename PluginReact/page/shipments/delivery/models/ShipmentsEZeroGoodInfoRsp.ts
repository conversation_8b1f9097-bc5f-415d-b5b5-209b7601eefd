import 'reflect-metadata';
import {Type} from 'class-transformer';
import {ShipmentsImgObj} from './ShipmentsImgObj';
import TextUtils from '../../../util/TextUtils';
import {ResultData} from '../../../http/ResultData';
import ShippingNoteInfo from '../../../bill/models/ShippingNoteInfo';
import {AddressInfoObj, DiscountCardInfo, EGoodInfo, ESDKInfoObj, EWater, PeopleVehicleVideoArr} from './ShipmentsEGoodInfoRsp';

export default class ShipmentsEZeroGoodInfoRsp extends ResultData {
    public orderCarpoolingId: string;
    public detailId: string;
    /*** 承运方总价*/
    public pbCarrierMoney: string;

    /*** 承运方单价*/
    public pbCarrierUnitMoney: string;

    /*** 货物总量*/
    public weight: string;

    /*** 费用类型 0：包车价 1：单价*/
    public freightType: string;

    /*** 货物类别 1：重货,2 泡货*/
    public cargoCategory: string;

    /*** 是否高级承运人 0：不是 1：是*/
    public isSeniorCarrier: string;

    //权限卡信息
    @Type(() => DiscountCardInfo)
    public discountCardInfo?: DiscountCardInfo;

    /*** 是否可以开启非指定预付款 0：不是 1：是*/
    public isNoSpecialAdvance: string;

    /*** 服务费*/
    public expectMoney: string;

    /****  是否含税: 0：不是 1：是*/
    public haveInvoice: string;

    /*** 是否弹框  1:不弹框  2：弹去绑卡  3：弹我要预付  4:预付打回，之前选择了预付的，且当前不符合条件*/
    public advanceFlagState: string;

    /*** 上传发货单配置项 1必填 0 非必填（不展示上传模块） 2非必填（展示上传回单模块 司机可以不上传）*/
    public uploadInvoiceConfig: string;

    /*** 打回原因*/
    public advanceReason: string;

    /***货主userId*/
    public consignorUserId: string;

    /***结算依据 1:确认发货吨位结算  2：确认收货吨位结算  3: 按收发货榜单较小值结算*/
    public settleBasisType: string;

    /***车老板预付字段：0-关 1-开*/
    public bossAdvance: string;

    /***1:预付现金;2:预付油品;3:预付油品+现金*/
    public advanceWay: string;

    /*** 是否需要上传皮毛重 “0”:"否"，“1”：“是”*/
    public uploadGrossAndTareWeightFlag: string;

    /**
     * 发货起点区域代码
     */
    public despatchDisCode: string;
    /**
     * 发货终点区域代码
     */
    public deliverDisCode: string;

    /***SDK是否可打开 1 可打开 0 不可打开*/
    public haveOpenSdk: string;

    //保险提示  	1:提示;其余不提示
    public orderPolicyFlag: string;
    //保险金额
    public orderPolicyMoney: string;

    //是否需要人脸识别 1：需要;0:不需要
    public faceIdentifyFlag: string;

    @Type(() => EWater)
    public imageJsonObjArr: EWater[];
    @Type(() => EWater)
    public imageJsonObjArr2: EWater[];
    @Type(() => EWater)
    public pageImageJsonObjArr: EWater[];
    @Type(() => EWater)
    public deliverImageJsonObjArr: EWater[];

    //业务平台0 中储智运平台 1 融通平台
    public plateType: string;
    // 是否提示兰鑫货主运单	0 否 1 是
    public haveAuthorize: string;
    @Type(() => ESDKInfoObj)
    public sdkInfoObj: ESDKInfoObj;
    /*判断是否是宁夏的运单*/
    public deliverTrackPersonImgFlag: string;

    //龙腾特钢一期需求 总重量
    public LTTotalMoney: string;
    //总重量是否为过磅后重量  已经是过磅后的重量就传 1，如果传了，app端吨位不能修改
    public LTCantUpdate: string;
    // 是否必传出场时间 1:是0否	ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public deliverOutStageFlag: string;
    //	出场时间 ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public outStageTime: string;
    //货源类型 CZY-7768	新增字段0正常货源 1集装箱货源 2:零担货
    public goodsSource: string;
    //加水印 0 不必填 1必填（不限制） 2 必填（只拍照）
    public deliverPicConfig: string;

    //平台货物的装卸货要求
    public platFormCargoPrompt: string;
    //   货主发单时的装卸货要求
    public consignorPrompt: string;
    //   装卸货要求是否展示	 1:展示0：不展示
    public promptFlag: string;

    //非预付情况下运单照片展示逻辑
    @Type(() => ShipmentsImgObj)
    public orderImgObj: ShipmentsImgObj;
    //非预付情况下人车货照片展示逻辑
    @Type(() => ShipmentsImgObj)
    public peopleVehicleImgObj: ShipmentsImgObj;

    /*** 订单ID*/
    public orderId: string;
    public advanceState: string;

    @Type(() => AddressInfoObj)
    public addressInfoObj: AddressInfoObj;

    //FBIS-975 人车货视频拍摄功能
    @Type(() => PeopleVehicleVideoArr)
    public peopleVehicleVideoArr: PeopleVehicleVideoArr[];

    //实际发货总吨位
    public deliverWeight: string;

    @Type(() => BacthZeroGoodInfo)
    public topOrderArray: BacthZeroGoodInfo[];

    @Type(() => BacthZeroGoodInfo)
    public ltlCargoArray: BacthZeroGoodInfo[];

       //备注
       public carrierDeliverRemark?: string;
       //上一次视频
       @Type(() => PeopleVehicleVideoArr)
       public deliverVideoJsonArr: PeopleVehicleVideoArr[];
}

export class BacthZeroGoodInfo {
    public orderId: string; //512403151018209563,
    public despatchPro: string; // "江苏省",
    public despatchCity: string; // "南京市",
    public despatchDis: string; // "栖霞区",
    public despatchPlace: string; // "南京大学",
    public deliverPro: string; // "浙江省",
    public deliverCity: string; // "杭州市",
    public deliverDis: string; // "西湖区",
    public deliverPlace: string; // "浙江大学",
    //货物明细
    @Type(() => EGoodInfo)
    public cargoArray: EGoodInfo[];

    public getStartAddress(): string {
        return this.despatchCity + (this.despatchDis ? this.despatchDis : this.despatchPlace);
    }
    public getEndAddress(): string {
        return this.deliverCity + (this.deliverDis ? this.deliverDis : this.deliverPlace);
    }
}
