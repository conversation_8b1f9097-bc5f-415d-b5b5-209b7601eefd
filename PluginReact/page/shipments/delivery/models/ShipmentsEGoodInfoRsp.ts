import 'reflect-metadata';
import {Type} from 'class-transformer';
import {ShipmentsImgObj} from './ShipmentsImgObj';
import TextUtils from '../../../util/TextUtils';
import {ResultData} from '../../../http/ResultData';
import ShippingNoteInfo from '../../../bill/models/ShippingNoteInfo';

export default class ShipmentsEGoodInfoRsp extends ResultData {
    //零担专用
    public orderCarpoolingId?: string;
    public detailId: string;
    /*** 承运方总价*/
    public pbCarrierMoney: string;

    /*** 承运方单价*/
    public pbCarrierUnitMoney: string;

    /*** 货物总量*/
    public weight: string;

    /*** 费用类型 0：包车价 1：单价*/
    public freightType: string;

    /*** 货物类别 1：重货,2 泡货*/
    public cargoCategory: string;

    /*** 是否高级承运人 0：不是 1：是*/
    public isSeniorCarrier: string;

    //权限卡信息
    @Type(() => DiscountCardInfo)
    public discountCardInfo?: DiscountCardInfo;

    /*** 是否可以开启非指定预付款 0：不是 1：是*/
    public isNoSpecialAdvance: string;

    /*** 服务费*/
    public expectMoney: string;

    /****  是否含税: 0：不是 1：是*/
    public haveInvoice: string;

    /*** 是否弹框  1:不弹框  2：弹去绑卡  3：弹我要预付  4:预付打回，之前选择了预付的，且当前不符合条件*/
    public advanceFlagState: string;

    /*** 上传发货单配置项 1必填 0 非必填（不展示上传模块） 2非必填（展示上传回单模块 司机可以不上传）*/
    public uploadInvoiceConfig: string;

    /*** 打回原因*/
    public advanceReason: string;

    /***货主userId*/
    public consignorUserId: string;

    /***结算依据 1:确认发货吨位结算  2：确认收货吨位结算  3: 按收发货榜单较小值结算*/
    public settleBasisType: string;

    /***车老板预付字段：0-关 1-开*/
    public bossAdvance: string;

    /***1:预付现金;2:预付油品;3:预付油品+现金*/
    public advanceWay: string;

    /*** 是否需要上传皮毛重 “0”:"否"，“1”：“是”*/
    public uploadGrossAndTareWeightFlag: string;

    /**
     * 发货起点区域代码
     */
    public despatchDisCode: string;
    /**
     * 发货终点区域代码
     */
    public deliverDisCode: string;

    /***SDK是否可打开 1 可打开 0 不可打开*/
    public haveOpenSdk: string;

    //保险提示  	1:提示;其余不提示
    public orderPolicyFlag: string;
    //保险金额
    public orderPolicyMoney: string;
    //距离
    public receiveFenceDistance: string;

    //是否需要人脸识别 1：需要;0:不需要
    public faceIdentifyFlag: string;

    @Type(() => EWater)
    public imageJsonObjArr: EWater[];
    @Type(() => EWater)
    public imageJsonObjArr2: EWater[];
    @Type(() => EWater)
    public pageImageJsonObjArr: EWater[];
    @Type(() => EWater)
    public deliverImageJsonObjArr: EWater[];
    @Type(() => EWater)
    public trailerImageJsonObjArr: EWater[];

    //业务平台0 中储智运平台 1 融通平台
    public plateType: string;
    // 是否提示兰鑫货主运单	0 否 1 是
    public haveAuthorize: string;
    @Type(() => ESDKInfoObj)
    public sdkInfoObj: ESDKInfoObj;
    /*判断是否是宁夏的运单*/
    public deliverTrackPersonImgFlag: string;

    //龙腾特钢一期需求 总重量
    public LTTotalMoney: string;
    //总重量是否为过磅后重量  已经是过磅后的重量就传 1，如果传了，app端吨位不能修改
    public LTCantUpdate: string;
    // 是否必传出场时间 1:是0否	ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public deliverOutStageFlag: string;
    //	出场时间 ZCZY-7729 如果是冀东的单子需要必传出厂时间
    public outStageTime: string;
    //货源类型 CZY-7768	新增字段0正常货源 1集装箱货源 2:零担货
    public goodsSource: string;
    //加水印 0 不必填 1必填（不限制） 2 必填（只拍照）
    public deliverPicConfig: string;

    //平台货物的装卸货要求
    public platFormCargoPrompt: string;
    //   货主发单时的装卸货要求
    public consignorPrompt: string;
    //   装卸货要求是否展示	 1:展示0：不展示
    public promptFlag: string;

    //非预付情况下运单照片展示逻辑
    @Type(() => ShipmentsImgObj)
    public orderImgObj: ShipmentsImgObj;
    //非预付情况下人车货照片展示逻辑
    @Type(() => ShipmentsImgObj)
    public peopleVehicleImgObj: ShipmentsImgObj;

    //货物明细
    @Type(() => EGoodInfo)
    public rootArray: EGoodInfo[];

    /*** 订单ID*/
    public orderId: string;
    public advanceState: string;

    @Type(() => AddressInfoObj)
    public addressInfoObj: AddressInfoObj;

    //FBIS-975 人车货视频拍摄功能
    @Type(() => PeopleVehicleVideoArr)
    public peopleVehicleVideoArr: PeopleVehicleVideoArr[];

    //实际发货总吨位
    public deliverWeight: string;
    //预付类型 1:现金；2：油品；3：油品+现金
    public advanceType?: string;
    //是否预付 1:是
    public advanceFlag?: string;
    //发货购买保险
    public deliverPolicyInfo?: DeliverPolicyInfo;
    //	防汛提示 1：需要;0:不需要 WLHY-6150【加急】【汽运】汛期安全运输提醒
    public floodSafeTipFlag?: string;
    //1 是 0 否 是否大件运输
    public bulkCargoFlag?: string;
    //WLHY-10243【汽运】承运方端大件货物提醒效果优化
    public bulkCargoTipFlag?: string;

    //备注
    public carrierDeliverRemark?: string;
    //上一次视频
    @Type(() => PeopleVehicleVideoArr)
    public deliverVideoJsonArr: PeopleVehicleVideoArr[];
    //照片最小上传数量
    public minLimitCount?: number;

    //非预付情况下挂车车牌合影图片 展示逻辑 WLHY-15998 发货挂车车牌合影照片
    @Type(() => ShipmentsImgObj)
    public trailerImageObj: ShipmentsImgObj;
    //是否自动打开内置导航 1 打开 0 不打开
    public autoGuidFlag?: string;
    public checkDeliverImage?: string; //1 检查单据 0不检查
    public checkPeopleVehicle?: string; //1检查人车货 0不检查
}

export class PeopleVehicleVideoArr {
    public videoUrl?: string;
}

export class AddressInfoObj {
    public endAddress?: string;
}

export class DiscountCardInfo {
    public discountCardBuyFlag?: number;
    //是否可使用智运折扣卡; 1:是;0否
    public discountCardUseFlag?: number;
    public activityId?: number;
    //是否展示智运折扣卡 1:是;0否
    public discountCardFlag?: number;
    // 返回的是购买卡的信息
    @Type(() => DiscountCardArray)
    public discountCardArray?: DiscountCardArray[];
}

export class DiscountCardArray {
    public cardNum?: string;
    public discountTitle?: string;
    public salePrice?: number;
    public subTips?: string;
    public cardType?: string;
    public orgPrice?: number;
    public discountRatio?: number;
}

export class EWater {
    public picUrl: string; //图片url	String	是
    public picOrgUrl: string; //原始图片上传url
}

export class ESDKInfoObj {
    public orderId: string;
    public startLocationText: string;
    public startLongitude: string;
    public startLatitude: string;
    public despatchProCode: string;
    public despatchCityCode: string;
    public despatchDisCode: string;

    public endLocationText: string;
    public endLongitude: string;
    public endLatitude: string;
    public deliverProCode: string;
    public deliverCityCode: string;
    public deliverDisCode: string;
    public vehicleNumber: string;
    public driverName: string;
    public consignorSubsidiaryId: string;

    public orderState: string; //7已收货(非APP操作收货)  8已终止
    public changeMacFlag: string; //    -- 是否变更设备【APP启动使用】
    public changeFlag: string; //   -- 是否变更地址【收货使用】

    public interval: number;

    public getShippingNoteInfo(): ShippingNoteInfo {
        let info = new ShippingNoteInfo();
        //运单号
        info.shippingNoteNumber = this.orderId;
        //分单号
        info.serialNumber = '0000';
        //起点地址文字描述
        info.startLocationText = this.startLocationText;
        //起点位置行政区划代码
        info.startCountrySubdivisionCode = this.despatchDisCode;
        //起点位置经度
        info.startLongitude = this.startLongitude;
        //起点位置纬度
        info.startLatitude = this.startLatitude;
        //到达地址文字描述
        info.endLocationText = this.endLocationText;
        //到达位置行政区划代码
        info.endCountrySubdivisionCode = this.deliverDisCode;
        //到达位置经度
        info.endLongitude = this.endLongitude;
        //到达位置纬度
        info.endLatitude = this.endLatitude;
        //车牌号
        info.vehicleNumber = this.vehicleNumber;
        //司机姓名
        info.driverName = this.driverName;
        return info;
    }
}

export class EGoodInfo {
    /**
     * 货物品类
     */
    public cargoType: string;

    /**
     * 货物类别：1：重货，2：泡货
     */
    public cargoCategory: string;

    /**
     * 货物ID
     */
    public cargoId: string;

    /**
     * 货物单位：1：吨，2：m3,
     */
    public unit: string;

    /**
     * 货物名称
     */
    public cargoName: string;

    /**
     * 运单号
     */
    public orderId: string;

    /**
     * 重量/体积
     */
    public weight: string;

    /***
     * 货主配置项发货方控制(泡)
     */
    public shippingOrientationMargin: string;

    /***
     * 货主配置项发货方控制(重)
     */
    public weightOrientationLimit: string;

    /**
     * 回单打回时之前输入数量 or 当前输入内容
     */
    public beforeDeliverCargoWeight?: string;

    /**
     * 回单打回时之前输入数量 or 当前输入内容(旧值用于对比数据是否发生修改,本地字段)
     */
    public oldBeforeDeliverCargoWeight: string;

    /*** 之前发货的毛重 3.5.5-4716需求*/
    public beforeDeliverGrossWeight: string;

    /*** 之前发货的皮重 3.5.5-4716需求*/
    public beforeDeliverTareWeight: string;

    //[本地数据]当前选择的集装箱箱号
    @Type(() => EContainer)
    public tempContainerSize?: EContainer[];
    public money?: string;
    public freight_type?: string;

    public getUnitTxt(): string {
        // 货物单位：1：吨，2：m3,
        return TextUtils.equals('1', this.unit) ? '吨' : TextUtils.equals('2', this.unit) ? '方' : TextUtils.equals('3', this.unit) ? '箱' : '';
    }
    public getCargoCategory(): string {
        // 货物单位：1：吨，2：m3,
        return TextUtils.equals('1', this.unit) ? '吨' : TextUtils.equals('2', this.unit) ? '方' : '';
    }

    public static getCargoCategoryUI(cargoCategory: string): string {
        // 货物单位：1：吨，2：m3,
        return TextUtils.equals('1', cargoCategory) ? '吨' : TextUtils.equals('2', cargoCategory) ? '方' : '';
    }
}

export class EContainer {
    //平台基础信息集装箱id【需传给后台】
    public id: string;
    public containerId: string;
    //箱号Id
    public containerListId: string;
    //箱号
    public containerNo: string;
    //	吨位
    public containerUnitWeight: string;
}

export class DeliverPolicyInfo {
    //活动id
    public buyPolicyCouponId?: string;
    //是否能够购买保险	 0 不能购买保险 1 可以购买保险
    public canBuyPolicy?: string;
    //保险服务费
    public guaranteeFee?: string;
    //优惠立减钱
    public reduceGuaranteeFee?: string;
    //货值
    public cargoMoney?: string;
    //免赔额说明
    public deductibleDesc?: string;
    //优惠完之后的钱，有优惠才会返回
    public leftGuaranteeFee?: string;
}
