import 'reflect-metadata';
import {Type} from 'class-transformer';

export default class Integrity {
    public nodename?: number;
    public userType?: number;
    @Type(() => ContentIds)
    public contentIds: ContentIds[];
    public url?: string;
}

export class ContentIds {
    public contentDescAlias?: string;
    public contentId?: string;
    public contentDesc?: string;
    public url?: string;
    public hookShow?: boolean = true;
    public nodename?: string; //本地赋值用于判断显示隐藏协议
}
