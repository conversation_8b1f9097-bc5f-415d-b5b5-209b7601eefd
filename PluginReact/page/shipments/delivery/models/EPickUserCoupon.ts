import TextUtils from '../../../util/TextUtils';

export class EPickUserCoupon {
    /**
     * 优惠券类型 1增值券 2抵用券 7 收货付券
     */
    couponTypeId?: string;

    /**
     * 抵扣比例
     */
    discountRatio?: string;
    discountRatioStr?: string;

    /**
     * 抵扣类型
     */
    discountType?: string;
    /**
     * 主键ID
     */
    userCouponId: string;

    /**
     * 查询类型
     */
    queryType?: string;
    /**
     * 有效期
     */
    validityTime?: string;
    /**
     * 优惠券名称
     */
    couponTypeName?: string;

    /**
     * 满多少可用
     */
    minMoney?: string;
    /**
     * 面额
     */
    couponUnitMoney: string;
    /**
     * 优惠券批次号编码
     */
    couponCode?: string;
    /**
     * 是否支持叠加使用0,1
     */
    haveAllowRepeat?: string;
    couponCodeComto?: string;
    /**
     * 未使用
     */
    unusedCoupon?: string;
    /**
     * 已使用
     */
    usedCoupon?: string;
    /**
     * 已过期
     */
    expiredCoupon?: string;
    /**
     * 可用
     */
    applicable?: string;
    /**
     * 不可用
     */
    notApplicable?: string;

    /**
     * 列表类型
     *
     * @return
     */
    couponType?: string;
    /**
     * 运单最多可叠加张数
     */
    maxCount: number;

    /**
     * 優惠金額[发货->预付打回专用字段]
     */
    discountAmount?: string;

    /**
     * 折扣金额上限
     */
    discountMoneyTop?: string;

    /**
     * 使用门槛
     */
    useThreshold?: string;

    /**后台计算后直接显示优惠金额即可*/
    saleMoney?: string;
    createdBy?: string; // 默认200 是报价有奖

    select?: boolean; //添加的选中属性

    /**
     * 注释:[摘单-保障服务优惠]计算金额
     * 时间: 2023/11/6 0006 15:33
     * <AUTHOR>
     * @param moneyDou
     */
    public pickMoney(moneyDou: number) {
        let plusMoney = 0.0;
        if (TextUtils.equals('2', this.couponTypeId)) {
            if (TextUtils.equals('1', this.discountType)) {
                plusMoney = parseFloat(this.couponUnitMoney);
            } else if (TextUtils.equals('0', this.discountType)) {
                let discountRatio = TextUtils.equals('10.0', this.discountRatio) ? '0' : this.discountRatio;
                plusMoney = moneyDou * (1 - parseFloat(discountRatio ?? '0') / 10);
                if (TextUtils.isNoEmpty(this.discountMoneyTop)) {
                    plusMoney = Math.min(plusMoney, parseFloat(this.discountMoneyTop ?? '0'));
                }
            }
        }
        return plusMoney;
    }
}
