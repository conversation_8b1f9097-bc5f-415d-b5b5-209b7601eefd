import {ResultData} from '../../../http/ResultData';
import {Type} from 'class-transformer';

export class EShipmentsSuccess extends ResultData {
    public discountPrice: string;

    /***真实服务费*/
    public realAdvanceServiceMoney: string;

    //失败ID
    public reqNo: string;

    //是否需要异步上传定位 1:是，其余否
    public synUploadLocationFlag: string;

    //失败的集合[零担]
    @Type(() => ShipZeroInfo)
    public failList?: ShipZeroInfo[];

    //成功的集合[零担]
    @Type(() => ShipZeroInfo)
    public successList?: ShipZeroInfo[];

    //成功总吨位[零担]
    public totalWeightOfUnit1?: string;
    //成功总方数[零担]
    public totalWeightOfUnit2?: string;
    //是否预付错误提示	 1:是，其余否
    public advanceErrorFlag?: string;
}

export class ShipZeroInfo {
    public orderId?: string;
    public despatchPro?: string;
    public despatchCity?: string;
    public despatchDis?: string;
    public despatchPlace?: string;
    public deliverPro?: string;
    public deliverCity?: string;
    public deliverDis?: string;
    public deliverPlace?: string;
    public message?: string;
}
