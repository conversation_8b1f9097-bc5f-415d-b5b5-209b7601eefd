import {DialogBuilder} from '../../../base/BaseCommPage';
import ShipmentsEGoodInfoRsp, {ESDKInfoObj} from './ShipmentsEGoodInfoRsp';
import {ShipmentUI} from './ShipmentUI';
import {ReqBeforeDeliverLTLCargoQuery} from '../requests/ReqBeforeDeliverLTLCargoQuery';
import {BaseResponse} from '../../../http/BaseResponse';
import {EShipmentsSuccess} from './EShipmentsSuccess';
import BaseServerDto from '../../../base/BaseServerDto';
import TextUtils from '../../../util/TextUtils';
import {ReqDoConfirmLTLDeliver} from '../requests/ReqDoConfirmLTLDeliver';
import {ReqDoConfirmLTLListDeliver} from '../requests/ReqDoConfirmLTLListDeliver ';
import {ReqBeforeDeliverLTLCargoListQuery} from '../requests/ReqBeforeDeliverLTLCargoListQuery ';
import ShipmentsEZeroGoodInfoRsp from './ShipmentsEZeroGoodInfoRsp';
import {RouterUtils} from '../../../util/RouterUtils';
import {RouterUrl} from '../../../base/RouterUrl';
import {Method} from '../../../util/NativeModulesTools';
export class ShipmentsZeroDto extends BaseServerDto {
    public mLongitude: string;
    public mLatitude: string;

    public location() {
        //异步定位一次,清除上次定位数据
        Method.removeStringExtra('shipmen_bill_location');

        Method.locationAndonLooper(
            async (code, json) => {
                if (code == 200) {
                    //定位成功
                    let location = JSON.parse(json);
                    this.mLongitude = location.longitude;
                    this.mLatitude = location.latitude;
                    Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                }
            },
            (code, json) => {
                if (code == 200) {
                    let location = JSON.parse(json);
                    this.mLongitude = location.longitude;
                    this.mLatitude = location.latitude;
                    Method.putStringExtra('shipmen_bill_location', `${location.latitude}_${location.longitude}`);
                }
            },
        );
    }

    /**
     * 请求发货信息
     * @param {*} waybill
     * @param page
     * @param callback
     */
    public async openScene(waybill, callback = (data?: ShipmentsEGoodInfoRsp) => {}) {
        this.showWaitDialog();

        this.location();

        const orderId = waybill.orderId;
        const detailId = waybill.detailId;

        //接口使用新网络框架重写
        let request = new ReqBeforeDeliverLTLCargoQuery();
        request.orderId = orderId;
        request.detailId = detailId;
        let shipmentsEGoodInfoRsp = await request.request();

        if (shipmentsEGoodInfoRsp.isSuccess()) {
            let shipmentsEGoodInfo = shipmentsEGoodInfoRsp.data;

            if (shipmentsEGoodInfo) {
                shipmentsEGoodInfo.orderId = orderId;
                shipmentsEGoodInfo.detailId = detailId;
                shipmentsEGoodInfo.advanceState = waybill.advanceState;
            }

            this.hideWaitDialog();
            callback && callback(shipmentsEGoodInfo);
        } else {
            this.showDialogToast(shipmentsEGoodInfoRsp.getMsg());
        }
    }

    /***
     * 确认发货
     */
    public async confrimOrderDeliver(shipmentUI: ShipmentUI) {
        this.showWaitDialog();

        let req = new ReqDoConfirmLTLDeliver();
        req.orderId = shipmentUI.orderId;
        req.detailId = shipmentUI.detailId;
        req.remark = shipmentUI.remark;
        req.deliverVideoArray = shipmentUI.deliverVideoArray;
        req.faceIdentifyFlag = shipmentUI.faceIdentifyFlag;

        //拼装字符串 货物ID:重量,货物Id:重量
        req.weightDetails = shipmentUI.getCargoIdWeightData();
        //确认收货图片名称连接字符串
        req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
        //确认人车图片名称连接字符串()
        req.picUrls2JsonArrayObj = shipmentUI.imagesCarPerson;
        //网络制式
        req.signalType = Method.getNetworkType();
        req.longitude = this.mLongitude;
        req.latitude = this.mLatitude;

        let result = await req.request();

        this.hideWaitDialog();

        this.onSuccess(shipmentUI.orderId, shipmentUI.orderId, shipmentUI.shipmentsEGoodInfo.deliverPicConfig, shipmentUI.shipmentsEGoodInfo.addressInfoObj?.endAddress ?? '', shipmentUI.haveOpenSdk, shipmentUI.shipmentsEGoodInfo.sdkInfoObj, result);
    }

    /***
     * 查询发货信息（合并）
     * @param waybill
     * @param callback
     */
    public async openSceneMerge(waybill, callback = (data?: ShipmentsEZeroGoodInfoRsp) => {}) {
        this.showWaitDialog();

        this.location();

        const orderId = waybill.orderId;
        const detailId = waybill.detailId;

        //接口使用新网络框架重写
        let request = new ReqBeforeDeliverLTLCargoListQuery();
        request.orderId = orderId;
        request.detailId = detailId;
        let shipmentsEGoodInfoRsp = await request.request();

        if (shipmentsEGoodInfoRsp.isSuccess()) {
            let shipmentsEGoodInfo = shipmentsEGoodInfoRsp.data;
            if (shipmentsEGoodInfo) {
                shipmentsEGoodInfo.orderId = orderId;
                shipmentsEGoodInfo.detailId = detailId;
                shipmentsEGoodInfo.advanceState = waybill.advanceState;
            }

            this.hideWaitDialog();
            callback && callback(shipmentsEGoodInfo);
        } else {
            this.showDialogToast(shipmentsEGoodInfoRsp.getMsg());
        }
    }

    /***
     * 确认发货(合并)
     */
    public async confrimMergeOrderDeliver(shipmentUI: ShipmentUI) {
        this.showWaitDialog();

        let req = new ReqDoConfirmLTLListDeliver();
        req.orderId = shipmentUI.zeroShipmentsEGoodInfo.orderId;
        req.detailId = shipmentUI.zeroShipmentsEGoodInfo.detailId;
        req.remark = shipmentUI.remark;
        req.deliverVideoArray = shipmentUI.deliverVideoArray;

        //确认收货图片名称连接字符串
        req.picUrlsJsonArrayObj = shipmentUI.imagesWaybill;
        //确认人车图片名称连接字符串()
        req.picUrls2JsonArrayObj = shipmentUI.imagesCarPerson;
        //网络制式
        req.signalType = Method.getNetworkType();
        req.longitude = this.mLongitude;
        req.latitude = this.mLatitude;

        req.topOrderArray = shipmentUI.zeroGoodInfos;
        req.ltlCargoArray = shipmentUI.zeroHZGoodInfos;
        req.orderCarpoolingId = shipmentUI.zeroShipmentsEGoodInfo.orderCarpoolingId;
        req.faceIdentifyFlag = shipmentUI.faceIdentifyFlag;

        let result = await req.request();

        this.hideWaitDialog();

        this.onSuccess(
            shipmentUI.orderId,
            shipmentUI.orderId,
            shipmentUI.zeroShipmentsEGoodInfo.deliverPicConfig,
            shipmentUI.zeroShipmentsEGoodInfo.addressInfoObj?.endAddress ?? '',
            shipmentUI.haveOpenSdk,
            shipmentUI.zeroShipmentsEGoodInfo.sdkInfoObj,
            result,
        );
    }

    private onSuccess(orderId: string, detailId: string, deliverPicConfig: string, endAddress: string, haveOpenSdk: boolean, sdkInfoObj: ESDKInfoObj, result: BaseResponse<EShipmentsSuccess>) {
        if (result.isSuccess()) {
            //是否上传定位
            if (TextUtils.equals('1', result.data?.synUploadLocationFlag)) {
                let json = {
                    detailId: detailId,
                    reqNo: '',
                    type: '1',
                    coordinateFlag: deliverPicConfig,
                    count: 2,
                };
                Method.onBillShipmentsServerNew(json);
            }

            if (haveOpenSdk && sdkInfoObj ) {
                //是否上报省部
                let json = {
                    orderId: orderId,
                    haveOpenSdkType: '1', //1 发货 2 收货
                    haveOpenSdk: '1',
                    sdkInfoObj: sdkInfoObj,
                };
                Method.onPostESDKInfoObj(json);
            }

            //收发货成功通知
            Method.onEventShipmentsBillSuccess(0);

            //打开发货成功页
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ShipZeroSuccessPage',
                action: RouterUtils.ACTION_REPLACE,
                data: {
                    shipmentsSuccess: result.data,
                    orderId: orderId,
                    detailId: detailId,
                    endAddress: endAddress,
                },
            });
        } else {
            if (TextUtils.equals('2021', result.getCode())) {
                let dialog = new DialogBuilder();
                dialog.title = '确认发货失败';
                dialog.model = 2;
                dialog.msg = result.getMsg();
                dialog.cancelTxt = '联系客服';
                dialog.onCancelEvent = () => {
                    Method.openLineServer();
                };
                this.showDialogBuilder(dialog);
            } else {
                this.showDialogToast(result.getMsg());
            }
            //错误信息上报
            if (TextUtils.isNoEmpty(result.data?.reqNo ?? '')) {
                let error = {
                    reqNo: result.data?.reqNo,
                    detailId: detailId,
                    type: '1', //发货
                };
                Method.onBillShipmentsServer(JSON.stringify(error));
            }
        }
    }
}
