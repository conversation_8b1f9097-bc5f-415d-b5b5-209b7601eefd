import 'reflect-metadata';
import {Type} from 'class-transformer';

export default class OffLineShipmentsEGoodInfo {
    public unit?: number;
    public orderId?: number;
    public resultCode?: string;
    @Type(() => CargoList)
    public cargoList?: CargoList[];
    public resultMsg?: string;
    //司机不能修改重量（龙腾特钢运单）不能修改返回 1
    public LTCantUpdate: string;
    //龙腾特钢货物总重量
    public LTTotalMoney: string;
    receiveFenceDistance?: string; //返回的距离
}

export class CargoList {
    public cargoId?: number;
    public orderId?: number;
    public cargoName?: string;
    public cargoCategory?: number;
    public weight?: number;
    public unit?: number;
    public cargoType?: any;
    public cargoLength?: any;
    public cargoWidth?: any;
    public cargoHeight?: any;
    public pack?: string;
    public warehouseName?: any;
    public warehouseLocation?: any;
    public warehouseAddr?: any;
    public baseId?: number;
    public layer1Id?: number;
    public layer1Name?: string;
    public layer2Id?: number;
    public layer2Name?: string;
    public layer3Id?: any;
    public layer3Name?: any;
    public cargoVersion?: any;
    public auditFlag?: number;
    public addressId?: any;
    public createdBy?: number;
    public createdByName?: string;
    public createdTime?: string;
    public lastUptBy?: number;
    public lastUptByName?: string;
    public lastUptTime?: string;
    public deleteFlag?: number;
}
