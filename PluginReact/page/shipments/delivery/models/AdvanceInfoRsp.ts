import 'reflect-metadata';
import {ShipmentsImgObj} from './ShipmentsImgObj';
import TextUtils from '../../../util/TextUtils';
import NumUtil from '../../../util/NumUtil';
import {ResultData} from '../../../http/ResultData';

export default class AdvanceInfoRsp extends ResultData {
    /***预付场景：1：个体司机指定订单；2.个体司机非指定订单；3.车老板自己摘牌订单；4.个体司机关联车老板模式；5.承运商指派订单*/
    public advanceType?: string;

    /***个体司机信用分*/
    public creditPoint?: string;
    /***是否支持预付 isAdvance-->0：不支持，1：支持*/
    public isAdvance?: string;
    /***不支持预付原因*/
    public noAdvanceReason?: string;

    /*** 预计预付服务费 服务费新计算公式得出*/
    public newAdvanceServiceMoney?: string;

    /***预付业务立减类型 1:固定额度;2:固定金额折扣;3:随机减, 5:次单优惠券活动*/
    public couponAmountType?: string;

    /***立减内容 如：立减5元*/
    public couponContext?: string;

    /***优惠金额*/
    public couponMoney?: string;

    //立减后预付款优惠id
    public cutAdvanceCouponId?: string;

    /**
     * 默认可以使用的抵扣券
     */
    public userCouponId?: string;
    /**
     * 抵扣券类型
     * 0 抵扣比例 1 抵扣金额
     */
    public discountType?: string;
    /**
     * 固定金额
     * 如果，抵用券是抵扣金额，则取这个值
     */
    public couponUnitMoney?: string;
    /**
     * 抵扣比例
     * 如果，抵用券是抵扣比例，则取这个值
     */
    public discountRatio?: string;

    /***预付方式*/
    public advanceWay?: EAdvanceType[];

    //不能预付原因code  返回 21161为存在欠款
    public noAdvanceCode?: string;
    /**
     * 折扣金额上限
     */
    public discountMoneyTop?: string;

    //是否需要展示油卡选项
    public oilSelectBySelf?: string;

    //油品 比例 （用于第一次带出默认或者重新预付带出）
    public oilDefaultRatio?: string;

    /**
     * ZCZY_9452 司机端预付页面进行优化
     *
     * @return
     */
    public advanceOrderDriverCount?: string;
    //趣味转盘活动展不展示，1展示
    public activityShowFlag?: string;
    public activityInfo?: EActivityInfo;

    //折扣计算金额
    public cutAfterAdvanceServiceMoney?: string;

    /*** 预付比例*/
    public advanceRatio?: string;
    //油品预付比例上限 61003 zczy-16737
    public maxOilCardRatio?: string;

    //预付情况下运单照片展示逻辑
    public orderImgObj?: ShipmentsImgObj;
    //预付情况下人车货照片展示逻辑
    public peopleVehicleImgObj?: ShipmentsImgObj;
    //证件不全提示
    public documentPromptObj: DocumentPromptObj;

    //FBIS-975 人车货视频拍摄功能
    public peopleVehicleVideoObj?: ShipmentsImgObj;
    public advanceOilRatioList?: string[];
    //次单优惠券活动
    public nextCouponJson?: NextCouponJson;
    //FBIS-2777预付审核管理增加司机操作提示
    public rejectOperTips?: string;
    //单单打卡活动
    public orderRecordCouponJson?: OrderRecordCouponJson;
    //FBIS-6039 核桃信用比例
    public walnutCreditRatio?: string;
    //线路预付固额
    public fixedAmountFlag?: string;
    //是否强制预付 1 是 0 否
    public forceAdvanceFlag?: string;
    //照片最小上传数量
    public minLimitCount?: number;
    // FBIS-15594预付选项默认值配置 是否禁用全平台运力池交易预付	   0否1是2否（按钮默认关闭）
    public disableDisappointPrepaySwitch?: string;
    //油品在途预付活动id
    public oilOnPrePolicyId?: string;
    
    //WLHY-15998 挂车车牌合影图片 上传要求
    public trailerImageObj?: ShipmentsImgObj;

    /**
     * 注释: 获取默认优惠券金额
     * 时间: 2023/6/27 0027 10:23
     * <AUTHOR>
     */
    getDiscountMoney(serverMoney: number): number {
        let discountMoney = 0.0;
        if (TextUtils.equals('0', this.discountType)) {
            //抵扣比例
            let discountRatio = parseFloat(this.discountRatio ?? '0.0');
            if (discountRatio == 10.0 || discountRatio == 100.0) {
                //全额抵扣
                discountRatio = 0.0;
            }
            discountMoney = NumUtil.halfup(NumUtil.mul(serverMoney, 1 - NumUtil.div(discountRatio, 10.0, 2)));
            if (TextUtils.isNoEmpty(this.discountMoneyTop)) {
                let top = parseFloat(this.discountMoneyTop ?? '0.0');
                discountMoney = Math.min(discountMoney, top);
            }
        } else if (TextUtils.equals('1', this.discountType)) {
            discountMoney = parseFloat(this.couponUnitMoney ?? '0.0');
        }
        return discountMoney;
    }

    /**
     * 注释: 是否是立减活动
     * 时间: 2023/6/27 0027 15:12
     * <AUTHOR>
     */
    isCouponShow(): boolean {
        return TextUtils.equals('1', this.couponAmountType) || TextUtils.equals('2', this.couponAmountType) || TextUtils.equals('3', this.couponAmountType) || TextUtils.equals('5', this.couponAmountType);
    }

    /**
     * 注释: 获取立减之后预付金额
     * 时间: 2023/6/27 0027 15:15
     * <AUTHOR>
     */
    getCouponToServerMoney(serverMoney: number): number {
        let money = serverMoney;
        if (money > 0) {
            let couponMoney = parseFloat(this.couponMoney ?? '0');
            if (couponMoney > 0) {
                //1:固定额度;2:固定金额折扣;3:随机减
                if (TextUtils.equals('2', this.couponAmountType)) {
                    money = parseFloat(this.cutAfterAdvanceServiceMoney ?? '0');
                } else {
                    //保留2位 四舍五入
                    money = NumUtil.halfup(money - couponMoney);
                }
            }
        }
        return Math.max(money, 0.0);
    }
}

export class EAdvanceType {
    //1:预付现金;2:预付油品;3:预付油品+现金
    public type: string;
    public desc?: string;
    //是否默认选择 1是 0 否
    public defaultAdvanceWayFlag?: string;
}

export class EActivityInfo {
    //活动id
    public activityId?: string;
    //5链接
    public advertUrl?: string;
    //活动名字
    public activityName?: string;
}

export class DocumentPromptObj {
    public documentPromptFlag?: string;
    public documentPromptTips?: string;
}

export class NextCouponJson {
    //优惠券截止时间
    public nextCouponEndTimeStr?: string;
    //抵用券折扣比例，这里8.8代表8.8折
    public nextCouponDiscountRatioStr?: string;
    //抵扣金额
    public nextCouponUnitMoney?: string;
    //抵用金额上限
    public nextCouponDiscountMoneyTop?: string;
    //使用门槛
    public nextCouponUseThreshold?: string;
    //抵用券折扣类型：0 抵扣比例 1 抵扣金额
    public nextCouponDiscountType?: string;
}

export class OrderRecordCouponJson {
    //优惠券截止时间
    public nextCouponEndTimeStr?: string;
    //抵用券折扣比例，这里8.8代表8.8折
    public nextCouponDiscountRatioStr?: string;
    //抵扣金额
    public nextCouponUnitMoney?: string;
    //抵用金额上限
    public nextCouponDiscountMoneyTop?: string;
    //使用门槛
    public nextCouponUseThreshold?: string;
    //抵用券折扣类型：0 抵扣比例 1 抵扣金额
    public nextCouponDiscountType?: string;
    //继续使用X次保障
    public remainRecordCount?: string;
}
