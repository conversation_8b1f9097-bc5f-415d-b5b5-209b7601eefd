import ShipmentsEGoodInfoRsp, {EContainer, EGoodInfo, EWater, PeopleVehicleVideoArr} from './ShipmentsEGoodInfoRsp';
import TextUtils from '../../../util/TextUtils';
import AdvanceInfoRsp from './AdvanceInfoRsp';
import ShipmentsEZeroGoodInfoRsp, {BacthZeroGoodInfo} from './ShipmentsEZeroGoodInfoRsp';

export class ShipmentUI {
    public shipmentsEGoodInfo: ShipmentsEGoodInfoRsp;
    public advanceInfo: AdvanceInfoRsp;
    //TMS货源ID，走TMS货主APP发单渠道才会有值
    public sourceId: string;

    /*** 货物明细（EGoodInfo,包含输入发货吨位）*/
    public goodInfos: EGoodInfo[];
    /***货物出厂时间 ZCZY-7729 冀东定制化需求*/
    public leaveFactoryTime?: string;
    /***是否需要上传皮毛重  */
    public uploadGrossAndTare: boolean;

    //订单Id
    public orderId: string;

    //订单详情Id
    public detailId: string;

    /*** 运单图片*/
    public imagesWaybill?: EWater[];

    /***人车货图片*/
    public imagesCarPerson?: EWater[];

    /***挂车车牌合影图片*/
    public imagesTrailer?: EWater[];

    /**** 司机是否选择了非指定预付款 1 选择 2 未选择*/
    public isAdvanceButtonOn: string;

    /**** 预付方式*/
    public advanceWay?: string;

    /**
     * 油气比例值
     */
    public oilRatio?: string;

    /**
     * 人脸识别标记
     * @type {string}
     */
    public faceIdentifyFlag?: string;

    /*** 优惠券ID*/
    public userCouponIds?: string;
    /***    预付业务立减金额 如果用户使用了预付业务则传，金额的传金额，折扣的传折扣数*/
    public couponMoney?: string;

    /***预付业务立减 1:固定额度;2:固定金额折扣;3:随机减*/
    public couponAmountType?: string;
    //立减后预付款优惠id
    public cutAdvanceCouponId?: string;

    /***申请预付标识 null普通发货，1预付申请*/
    public applyAdvance?: string;

    /***SDK是否可打开 1 可打开 0 不可打开*/
    public haveOpenSdk: boolean;

    //是否购买智运折扣卡;1:是;0否
    public discountCardBuyFlag?: string;
    //是否使用智运折扣卡;1:是;0否:
    public discountCardUseFlag?: string;
    //智运折卡号
    public discountCardNum?: string;
    //ZCZY-12277 【加急】2023惊蛰（3.6）活动趣味转盘
    //活动id
    public activityId?: string;

    //备注
    public remark?: string;
    //WLHY-12652 🏷 【汽运】承运方支持上传视频
    public deliverVideoArray?: PeopleVehicleVideoArr[];

    //FBIS-975 人车货视频拍摄功能
    public peopleVehicleVideoArrayObj?: PeopleVehicleVideoArr[];

    //零担【专用】
    public zeroShipmentsEGoodInfo: ShipmentsEZeroGoodInfoRsp;
    /***零担【专用】货物明细（EGoodInfo,包含输入发货吨位）*/
    public zeroGoodInfos: BacthZeroGoodInfo[];
    /***零担【专用】 合并发货的货物明细（EGoodInfo,包含输入发货吨位）*/
    public zeroHZGoodInfos: BacthZeroGoodInfo[];
    //货物保障 1： 购买
    public buyPolicyFlag?: string;
    //货物保险活动id
    public buyPolicyCouponId?: string;
    //	防汛提示 1：需要;0:不需要 WLHY-6150【加急】【汽运】汛期安全运输提醒
    public floodSafeTipFlag?: string;
    //自维护发货场景 0：确认发货，1：申请预付，2：重新申请预付
    public scene?: string;
    //油品在途预付活动id
    public oilOnPrePolicyId?: string;
    //是否自动打开内置导航 1 打开 0 不打开
    public autoGuidFlag?: string;

    /***
     * 清空预付值
     */
    public clearTerracePay() {
        //没有选择预付
        this.advanceWay = '';
        this.isAdvanceButtonOn = '2';
        this.oilRatio = '';
        this.discountCardUseFlag = '';
        this.activityId = '';
        this.oilRatio = '';
        this.userCouponIds = '';
        this.couponMoney = '';
        this.couponAmountType = '';
        this.cutAdvanceCouponId = '';
        this.discountCardNum = '';
    }

    /***
     * 货物明细
     * @return
     */
    public getCargoIdWeightData(): string {
        //ZCZY-15815 正大预付需求提示优化
        return this.goodInfos.map((item) => item.cargoId + ':' + (TextUtils.isEmpty(item.beforeDeliverCargoWeight) ? '' : item.beforeDeliverCargoWeight)).join(',');
    }

    /***
     * 毛重
     *  拼装字符串 货物ID:毛重,货物Id:毛重
     * @return
     */
    public getGrossWeightDetails(): string {
        if (this.uploadGrossAndTare) {
            return this.goodInfos
                .map((item) => {
                    if (TextUtils.isNoEmpty(item.beforeDeliverGrossWeight)) {
                        return item.cargoId + ':' + item.beforeDeliverGrossWeight;
                    } else {
                        return item.cargoId;
                    }
                })
                .join(',');
        }
        return '';
    }

    /***
     * 皮重
     * 拼装字符串 货物ID:皮重
     * ,货物Id:皮重
     * @return
     */
    public getTareWeightDetails(): string {
        if (this.uploadGrossAndTare) {
            return this.goodInfos
                .map((item) => {
                    if (TextUtils.isNoEmpty(item.beforeDeliverTareWeight)) {
                        return item.cargoId + ':' + item.beforeDeliverTareWeight;
                    } else {
                        return item.cargoId;
                    }
                })
                .join(',');
        }
        return '';
    }

    public getEContainer(): EContainer[] {
        let list = [];
        this.goodInfos.map((item) => {
            // @ts-ignore
            list = list.concat(item.tempContainerSize);
        });

        return list;
    }

    /***
     * 发生发货吨位修改数据
     * @return
     */
    public getChangeSize(): EGoodInfo[] {
        return this.goodInfos.filter((item) => !TextUtils.equals(item.oldBeforeDeliverCargoWeight, item.beforeDeliverCargoWeight));
    }
}
