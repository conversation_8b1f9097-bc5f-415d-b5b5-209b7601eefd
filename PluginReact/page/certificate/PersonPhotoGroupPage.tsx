import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import TextUtils from '../util/TextUtils';
import UIButton from '../widget/UIButton';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {RouterUtils} from '../util/RouterUtils';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import {ReqAddPersonCar} from './requests/ReqAddPersonCar';
import {RouterUrl} from '../base/RouterUrl';
import {http} from '../const.global';
import LanguageType from '../util/language/LanguageType';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {ReqCheckExpireChangeVehicleTrailer} from './requests/ReqCheckExpireChangeVehicleTrailer';
import CertificateReactExtension from './CertificateReactExtension';
import {ReqQueryMemberVehicleLicense} from './requests/ReqQueryMemberVehicleLicense';

interface State extends BaseState {
    rejectReason?: string;
    personCarUrl1?: string; //人车合影照片（人脸+车牌号照片）
    personCarUrl2?: string; //人车合影照片（车头车牌+车身照片）
    showExpireChange?: boolean; //已更换挂车，点这里去更新挂车证件
    backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    vehicleFlag?: string; //车辆类型（车辆类别） 1、普通货车 2、挂车  初次认证前的修改必传，初次提交认证必传，重新提交传担不能改
    plateNumber?: string; //车身展示用车牌号
    plateNumber_1?: string; //人脸展示用车牌号
}

/**
 * 注释: 人车合影 上传 更新 过期 3:证件缺失
 * 时间: 2025/2/24 20:10
 * <AUTHOR>
 */
export default class PersonPhotoGroupPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 0:上传 1:更新 2:过期 3:证件缺失
    mode: number;
    //是否添加车辆 true 添加车辆 false 认证车辆
    isAdd: boolean;
    //成功回调
    callBack: Function = this.getCallBack();
    //车辆id
    vehicleId: string;
    backup1?: string; //临时/超限车辆 0、否 1、
    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        this.vehicleId = this.pageParams?.vehicleId ?? '';
        this.isAdd = this.pageParams?.isAdd;
        this.backup1 = this.pageParams?.backup1;
    }

    componentDidMount() {
        super.componentDidMount();
        switch (this.mode) {
            case 0: {
                if (this.isAdd) {
                    this.queryVehicleDetail();
                } else {
                    this.queryLicenseDetail();
                }
                break;
            }
            case 3: {
                //3:证件缺失
                this.queryVehicleDetail();
                break;
            }
            default: {
                //2:过期
                this.queryMemberVehicleLicense();
                break;
            }
        }
    }

    /**
     * 注释：获取认证详情
     * 时间：2025/2/28 17:18
     * @author：宋双朋
     */
    queryLicenseDetail() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((response) => {
            if (response.isSuccess() && response.data?.vehicle != null) {
                let item = response?.data?.vehicle;
                this.setState({
                    personCarUrl1: item?.personCarUrl1,
                    personCarUrl2: item?.personCarUrl2,
                    backup1: 0 == this.mode ? this.backup1 : `${item?.backup1}`,
                    vehicleFlag: `${item?.vehicleFlag}`,
                });
            }
        });
    }

    /**
     * 注释：请求车辆详情
     * 时间：2025/2/25 10:23
     * @author：宋双朋
     */
    queryVehicleDetail() {
        //车辆id不为空 请求车辆详情
        let req = new ReqVehicleDetails();
        req.vehicleId = this.vehicleId;
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                this.setState({
                    personCarUrl1: item?.personCarUrl1,
                    personCarUrl2: item?.personCarUrl2,
                    showExpireChange: TextUtils.equals('2', item?.vehicleFlag) && this.mode == 3,
                    backup1: 0 == this.mode ? this.backup1 : `${item?.backup1}`,
                    vehicleFlag: `${item?.vehicleFlag}`,
                    plateNumber: TextUtils.equals('1', `${item?.backup1}`) ? item?.plateNumber : TextUtils.equals('2', `${item?.vehicleFlag}`) ? item?.trailerPlateNumber : item?.plateNumber,
                    plateNumber_1: item?.plateNumber,
                });
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    queryMemberVehicleLicense() {
        let req = new ReqQueryMemberVehicleLicense();
        req.vehicleId = this.vehicleId;
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                this.setState({
                    backup1: `${item?.backup1}`,
                    vehicleFlag: `${item?.vehicleFlag}`,
                });
            }
        });
    }

    /**
     * 注释：提交上传
     * 时间：2025/2/25 13:53
     * @author：宋双朋
     */
    licenseInputAccuracyCheck = () => {
        let personCarUrl1 = this.state.personCarUrl1;
        if (TextUtils.isEmpty(personCarUrl1)) {
            this._showMsgDialog('人车合影照片（人脸+车牌号照片）不能为空');
            return;
        }
        let personCarUrl2 = this.state.personCarUrl2;
        if (TextUtils.isEmpty(personCarUrl2)) {
            this._showMsgDialog(TextUtils.equals('1', this.state.backup1) ? '人车合影照片（车头车牌+车身照片）不能为空' : '人车合影照片（车尾车牌+车身照片）不能为空');
            return;
        }
        if (this.mode == 3) {
            this.addPersonCar();
        } else {
            this._showWaitDialog();
            let req = new ReqLicenseInputAccuracyCheck();
            req.type = '11';
            req.isNeedSave = this.mode == 0 ? '1' : '0';
            req.vehicleId = this.vehicleId;
            req.personCarUrl1 = personCarUrl1;
            req.personCarUrl2 = personCarUrl2;
            req.request().then((res) => {
                this._dismissWait();
                if (res.isSuccess()) {
                    switch (this.mode) {
                        case 0:
                            this._showToast(res.getMsg());
                            this.callBack && this.callBack();
                            EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                            RouterUtils.skipPop();
                            break;
                        case 1:
                            this.addPersonCar();
                            break;
                    }
                } else {
                    this._showMsgDialog(res.getMsg());
                }
            });
        }
    };
    addPersonCar = () => {
        this._showWaitDialog();
        let req = new ReqAddPersonCar();
        req.vehicleId = this.vehicleId;
        req.personCarUrl1 = this.state.personCarUrl1;
        req.personCarUrl2 = this.state.personCarUrl2;
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    /**
     * 注释: 点击过期上传页、人车合影补录上传页面内更换挂车申请按钮的前置校验
     * 时间: 2025/4/17 19:11
     * <AUTHOR>
     */
    checkExpireChangeVehicleTrailer = () => {
        let request = new ReqCheckExpireChangeVehicleTrailer();
        request.vehicleId = this.vehicleId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                CertificateReactExtension.gotoTrailerVehicleActivity(res.data?.vehicleId ?? '', res.data?.trailerVehicleId ?? '', res.data?.plateNumber ?? '');
                this._goBack();
                this._goBack();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    //过期页
    renderExpiredView() {
        return (
            <View style={{flexDirection: 'column'}}>
                <View style={{backgroundColor: 'white', marginTop: 5, alignItems: 'center'}}>
                    <View style={{backgroundColor: '#FFF3EF', padding: 5}}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: 'red',
                                marginRight: 5,
                            }}>
                            证件即将 / 已经过期，为保障您的业务不受影响，请及时更新。
                        </Text>
                    </View>
                </View>
                <View
                    style={{
                        backgroundColor: '#fff',
                        paddingLeft: 12,
                        height: 58,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        marginBottom: 8,
                    }}>
                    <Text
                        style={{
                            fontSize: 16,
                            color: 'black',
                            marginRight: 5,
                        }}>
                        {this.getParams().plateNumber ?? ''}
                    </Text>
                </View>
            </View>
        );
    }

    render() {
        let title = '车辆认证';
        if (this.mode == 1 || this.mode == 3) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={title} />
                <ScrollView>
                    {this.mode == 2 && this.renderExpiredView()}
                    <ComCertificationUploadImgView
                        ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                        style={{
                            backgroundColor: '#FFFFFF',
                            paddingLeft: 15,
                            paddingRight: 15,
                            paddingBottom: 7,
                        }}
                        showTemplateImg={true}
                        onClickImgTemplate={() => {
                            let url = http.webUrl() + `/form_h5/h5_mobile/index.html?_t=${new Date().getTime()}#/sampleTemplate` + (TextUtils.isEmpty(LanguageType.getWebUl()) ? '?' : '&') + 'backup1=' + this.state.backup1;
                            RouterUtils.skipRouter(RouterUrl.WebViewPage, {uri: url});
                        }}
                        tvTitle1View={
                            <View>
                                <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                                    人脸+车牌号照片<Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                                </Text>
                                <Text style={{fontSize: 12, color: '#999', marginTop: 8, marginBottom: 5}}>
                                    请上传本人与认证车辆
                                    <Text
                                        style={{
                                            fontSize: 12,
                                            color: '#FF602E',
                                        }}>
                                        {this.mode == 3 ? `${this.state.plateNumber_1}` : ''}
                                    </Text>
                                    的合影，需要包含<Text style={{fontSize: 12, color: '#FF602E'}}>人脸，车头车牌</Text>
                                </Text>
                            </View>
                        }
                        layout={1}
                        leftImg={{
                            url: this.state.personCarUrl1 ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_23',
                            onDelPic: () => {
                                this.setState({personCarUrl1: undefined});
                            },
                            onPostSuccess: (url) => {
                                this.setState({personCarUrl1: url});
                            },
                        }}
                    />
                    <ComCertificationUploadImgView
                        ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                        style={{
                            backgroundColor: '#FFFFFF',
                            paddingLeft: 15,
                            paddingRight: 15,
                            marginTop: 7,
                            paddingBottom: 7,
                        }}
                        tvTitle1View={
                            <View>
                                <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                                    {TextUtils.equals('1', this.state.backup1) ? '车头车牌' : '车尾车牌'}+车身照片<Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                                </Text>
                                <Text style={{fontSize: 12, color: '#999', marginTop: 5, marginBottom: 5}}>
                                    请上传认证车辆
                                    <Text
                                        style={{
                                            fontSize: 12,
                                            color: '#FF602E',
                                        }}>
                                        {this.mode == 3 ? `${this.state.plateNumber}` : ''}
                                    </Text>
                                    的车辆照片，需要包含
                                    <Text
                                        style={{
                                            fontSize: 12,
                                            color: '#FF602E',
                                        }}>
                                        {TextUtils.equals('1', this.state.backup1) ? '车头车牌' : '车尾车牌'}，车身
                                    </Text>
                                </Text>
                            </View>
                        }
                        layout={1}
                        leftImg={{
                            url: this.state.personCarUrl2 ?? '',
                            defaultValue: TextUtils.equals('1', this.state.backup1) ? 'http://img.zczy56.com/202504291335164082334.png' : 'certification_driver_license_template_diagram_24',
                            onDelPic: () => {
                                this.setState({personCarUrl2: undefined});
                            },
                            onPostSuccess: (url) => {
                                this.setState({personCarUrl2: url});
                            },
                        }}
                    />
                    {/*更换挂车*/}
                    {this.state.showExpireChange && (
                        <UITouchableOpacity style={{alignItems: 'center', marginTop: 20, marginBottom: 100}} onPress={this.checkExpireChangeVehicleTrailer}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: '#5086FC',
                                }}>{`已更换挂车，点这里去更新挂车证件 >`}</Text>
                        </UITouchableOpacity>
                    )}
                </ScrollView>
                <UIButton text={'提交上传'} onPress={this.licenseInputAccuracyCheck} style={{borderRadius: 0}} fontWeight={true} />
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
