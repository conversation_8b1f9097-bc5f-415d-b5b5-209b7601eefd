import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {UserType} from '../user/models/UserType';
import {LicenseList} from './models/ResQueryMemberVehicleLicense';

export default class CertificateReactExtension {
    /**
     * 注释: 跳转会员证件更新
     * 时间: 2024/8/30 0030 8:37
     * <AUTHOR>
     * @param item
     */
    static skipMemberCertificateUpdate(item) {
        // Method.transferNativeMethod('CertificateReactNativeExtension', 'skipMemberCertificateUpdate', item);
        // licenseStatus  1证件缺失（无证件）,2 证件过期, 3证件即将过期, 4 证件缺失（风险通过）, 0不展示, 5完善驳回, 6自主更新,7审核中, -1已报废
        if (TextUtils.equals('7', item.licenseStatus)) {
            //审核中证件跳转资料补充 DriverAdditionalInformationActivity
            RouterUtils.skipRouter(RouterUrl.DriverAdditionalInformationActivity);
        } else {
            if (UserType.isCarrier()) {
                if (TextUtils.equals('101', item.licenseCode)) {
                    //101身份证证件
                    let mode = TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'CertificationAuthenticationNoPassPage',
                        data: {
                            rspQueryUserExpireLicenseB: item,
                            mode: mode,
                        },
                    });
                } else if (TextUtils.equals('102', item.licenseCode)) {
                    //驾驶证证件
                    // 1:上传 2:更新 3:过期
                    let mode =
                        TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus)
                            ? 1
                            : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus)
                            ? 2
                            : 0;
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'DriverLicenseAuthPage',
                        data: {
                            mode: mode,
                        },
                    });
                } else if (TextUtils.equals('103', item.licenseCode)) {
                    //103司机从业资格证   0:上传 1:更新 2:过期
                    let mode =
                        TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus)
                            ? 1
                            : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus)
                            ? 2
                            : TextUtils.equals('1', item.licenseStatus) || TextUtils.equals('4', item.licenseStatus)
                            ? 3
                            : 0;
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'QualificationCertificatePage',
                        data: {
                            mode: mode,
                            licenseType: item.licenseType,
                            vehicleId: item.targetId,
                            rspQueryUserExpireLicenseB: item,
                        },
                    });
                } else {
                    let data = {
                        licenseName: item.licenseName,
                        licenseType: item.licenseType,
                        licenseState: item.licenseStatus,
                        licenseCode: item.licenseCode,
                        vehicleId: item.targetId,
                        plateNumber: item.licensePlateNumber,
                    };
                    RouterUtils.skipRouter(RouterUrl.CyrExpiredCertificateUpdateActivity, {rspQueryUserExpireLicenseB: data});
                }
            } else if (UserType.isBoss()) {
                // 1:上传 2:更新 3:过期
                let mode = TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
                if (TextUtils.equals('101', item.licenseCode)) {
                    //101身份证证件
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'CertificationAuthenticationNoPassPage',
                        data: {
                            mode: mode,
                            rspQueryUserExpireLicenseB: item,
                        },
                    });
                } else {
                    RouterUtils.skipRouter(RouterUrl.BossExpiredCertificateUpdateActivity);
                }
            } else {
                //承运商-物流企业  证件过期上传 CysExpiredCertificateUpdateActivity
                if (TextUtils.equals('101', item.licenseCode)) {
                    //101身份证证件
                    let mode = TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'CertificationAuthenticationNoPassPage',
                        data: {
                            mode: mode,
                            rspQueryUserExpireLicenseB: item,
                        },
                    });
                } else {
                    RouterUtils.skipRouter(RouterUrl.CysExpiredCertificateUpdateActivity);
                }
            }
        }
    }

    /**
     * 注释: 跳转车辆证件更新
     * 时间: 2024/8/30 0030 8:55
     * <AUTHOR>
     * @param item
     */
    static skipCarCertificateUpdate(item: LicenseList) {
        // Method.transferNativeMethod('CertificateReactNativeExtension', 'skipCarCertificateUpdate', item);
        // licenseStatus  1证件缺失（无证件）,2 证件过期, 3证件即将过期, 4 证件缺失（风险通过）, 0不展示, 5完善驳回, 6自主更新,7审核中, -1已报废
        //审核中证件跳转资料补充
        if (TextUtils.equals('7', item.licenseStatus)) {
            // CarrierAdditionalInformationActivity.start(context, vehicleId);
            RouterUtils.skipRouter(RouterUrl.CarrierAdditionalInformationActivity, {vehicleId: item.targetId});

        }else if (TextUtils.equals('105', item.licenseCode) || TextUtils.equals('205', item.licenseCode)) {
            //105 道路运输经营许可证 物流企业,205道路运输经营许可证司机、车老板
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;

            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ExpiredTransportBusinessUpdatePage',
                data: {
                    vehicleId: item.targetId,
                    licenseType: item.licenseType,
                    mode: mode,
                },
            });
        } else if (TextUtils.equals('4', item.licenseStatus)) {
            //风险完善资料页 二期优化不涉及车辆完善驳回
            if (UserType.isCarrier()) {
                //   CarrierCarRiskActivityV1.jumpPage(context, vehicleId);
                RouterUtils.skipRouter(RouterUrl.CarrierCarRiskV1Router, {
                    vehicleId: item.targetId,
                });
            } else if (UserType.isBoss()) {
                //   BossVehicleRistActivity.start(context, vehicleId);
                RouterUtils.skipRouter(RouterUrl.BossVehicleRistActivity, {
                    vehicleId: item.targetId,
                });
            } else {
                //  CysVehicleCarRiskActivity.start(context, vehicleId);
                RouterUtils.skipRouter(RouterUrl.CysVehicleCarRiskActivity, {
                    vehicleId: item.targetId,
                });
            }
        } else if (TextUtils.equals('5', item.licenseStatus)) {
            //车辆完善驳回 二期优化不涉及车辆完善驳回
            // VehiclePerfectActivity.Companion.startContentUI(context, vehicleId);
            RouterUtils.skipRouter(RouterUrl.VehiclePerfectActivity, {
                vehicleId: item.targetId,
            });
        } else if (TextUtils.equals('201', item.licenseCode) || TextUtils.equals('203', item.licenseCode)) {
            //201（牵引车）行驶证主副页,203牵引车行驶证年检页
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'DrivingHeadLicensePage',
                data: {
                    mode: mode,
                    plateNumber: item.licensePlateNumber,
                    vehicleId: item.targetId,
                },
            });
        } else if (TextUtils.equals('202', item.licenseCode)) {
            //202行驶证年检页
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'DrivingLicensePage',
                data: {
                    mode: mode,
                    plateNumber: item.licensePlateNumber,
                    vehicleId: item.targetId,
                },
            });
        } else if (TextUtils.equals('204', item.licenseCode)) {
            //204 道路运输证
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ExpiredTransportUpdatePage',
                data: {
                    rspQueryUserExpireLicenseB: item,
                    mode: mode,
                    plateNumber: item.licensePlateNumber,
                    vehicleId: item.targetId,
                },
            });
        } else if (TextUtils.equals('206', item.licenseCode) || TextUtils.equals('207', item.licenseCode)) {
            //206 挂车行驶证主副页,207 挂车行驶证年检页
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'DrivingGuaLicensePage',
                data: {
                    mode: mode,
                    plateNumber: item.licensePlateNumber,
                    vehicleId: item.targetId,
                },
            });
        } else if (TextUtils.equals('208', item.licenseCode)) {
            //208 超限临牌 (驳回，过期打开原生)
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ProCardPage',
                data: {
                    mode: mode,
                    licenseType: item.licenseType,
                    vehicleId: item.targetId,
                    plateNumber: item.licensePlateNumber,
                    rspQueryUserExpireLicenseB: item,
                },
            });
        } else if (TextUtils.equals('209', item.licenseCode)) {
            //209 人车合影(驳回打开原生)
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus)
                    ? 1
                    : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus)
                    ? 2
                    : TextUtils.equals('1', item.licenseStatus)
                    ? 3
                    : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'PersonPhotoGroupPage',
                data: {
                    mode: mode,
                    vehicleId: item.targetId,
                },
            });
        } else if (TextUtils.equals('210', item.licenseCode)) {
            //证明材料（驳回）
            let mode =
                TextUtils.equals('5', item.licenseStatus) || TextUtils.equals('6', item.licenseStatus) || TextUtils.equals('0', item.licenseStatus) ? 1 : TextUtils.equals('2', item.licenseStatus) || TextUtils.equals('3', item.licenseStatus) ? 2 : 0;
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ProofMaterialsPage',
                data: {
                    mode: mode,
                    vehicleId: item.targetId,
                },
            });
        } else {
            Method.transferNativeMethod('CertificateReactNativeExtension', 'skipCarCertificateUpdate', item);
        }
    }

    /**
     * 注释: 跳转到首页
     * 时间: 2024/12/18 星期三 17:18
     * <AUTHOR>
     */
    static jumpToHome() {
        Method.transferNativeMethod('CertificateReactNativeExtension', 'jumpToHome', {});
    }

    /**
     * 注释: 拨打400电话
     * 时间: 2024/12/18 星期三 17:18
     * <AUTHOR>
     */
    static callPhone() {
        Method.transferNativeMethod('CertificateReactNativeExtension', 'callPhone', {});
    }

    /**
     * 注释: 跳转登录页
     * 时间: 2024/12/19 星期四 10:17
     * <AUTHOR>
     */
    static openLogin() {
        Method.transferNativeMethod('CertificateReactNativeExtension', 'openLogin', {});
    }

    /**
     * 注释:MMS-14255 年检页过期、更换挂车、人车合影补录入口提示优化
     * 时间: 2025/4/17 19:05
     * <AUTHOR>
     */
    static gotoTrailerVehicleActivity(vehicleId: string, trailerVehicleId: string, plateNumber: string) {
        Method.transferNativeMethod('CertificateReactNativeExtension', 'gotoTrailerVehicleActivity', {
            vehicleId,
            trailerVehicleId,
            plateNumber,
        });
    }
}
