import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../../base/BaseCommPage';
import UITitleView from '../../widget/UITitleView';
import {Method} from '../../util/NativeModulesTools';
import UIImage from '../../widget/UIImage';
import UIButton from '../../widget/UIButton';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {http} from '../../const.global';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import {ReqUpdateMemberInfo} from '../requests/ReqUpdateMemberInfo';
import {onEvent} from '../../base/native/UITrackingAction';
import {UserType} from '../../user/models/UserType';

interface State extends BaseState {
    showTip: boolean;
}

/**
 * 注释:认证人脸识别失败
 * 时间: 2024/12/10 10:47
 * <AUTHOR>
 */
export default class FaceRecognitionFailurePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    idcardFrontUrl: string;
    idcardReverseUrl: string;
    callback: Function; //重新人脸识别 回调
    constructor(props) {
        super(props);
        this.state = {
            showTip: true,
        };
        this.idcardFrontUrl = this.getParams().idcardFrontUrl ?? '';
        this.idcardReverseUrl = this.getParams().idcardReverseUrl ?? '';
        this.callback = this.getParams().callback;
    }

    /**
     * 修改会员信息
     */
    updateSeniorMemberInfo() {
        let req = new ReqUpdateMemberInfo();
        req.frontIdCardUrl = this.idcardFrontUrl;
        req.negativeIdCardUrl = this.idcardReverseUrl;
        this._showWaitDialog();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'CarOwnerRiskCertificationSubmitSuccessPage'});
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={'身份信息认证'}
                    rightText={'客服'}
                    clickRight={() => {
                        Method.openServerPopWindow();
                    }}
                    clickBack={() => {
                        RouterUtils.skipPop();
                        onEvent({pageId: `CarOwnerRiskFaceRecognitionFailurePage`, tableId: 'auth_2&back'});
                    }}
                />
                {this.state.showTip && (
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingHorizontal: 14,
                            paddingVertical: 8,
                            backgroundColor: '#FEF6D9',
                        }}>
                        <UIImage source={'certification_carowner_tip'} style={{width: 18, height: 18}} />
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#666',
                            }}>
                            请按提示做人脸识别，动作轻缓。
                        </Text>
                        <View style={{flex: 1}} />
                        <UITouchableOpacity
                            onPress={() => {
                                this.setState({showTip: false});
                            }}>
                            <UIImage source={'certification_carowner_tip_hide'} style={{width: 15, height: 15}} />
                        </UITouchableOpacity>
                    </View>
                )}
                <ScrollView style={{backgroundColor: '#fff', flex: 1}}>
                    <View style={{flex: 1, alignItems: 'center'}}>
                        <UIImage source={'certification_carowner_failure_faild'} style={{width: 180, height: 180, marginTop: 40}} />
                        <Text
                            style={{
                                fontSize: 18,
                                color: '#333',
                                fontWeight: 'bold',
                                marginTop: 13,
                            }}>
                            人脸识别未成功
                        </Text>
                        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 5}}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: '#999',
                                }}>
                                {'·请在光线充足的环境下进行操作 \n·采集过程中保持正对着手机 \n·做动作时不要太快'}
                            </Text>
                        </View>
                        <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 35}}>
                            <UIButton
                                text={'重新做人脸识别'}
                                textColor={'#5086fc'}
                                width={UserType.isBoss() ? 140 : 200}
                                height={45}
                                borderRadius={25}
                                backgroundColor={'#fff'}
                                fontSize={17}
                                onPress={() => {
                                    this.callback && this.callback();
                                    RouterUtils.skipPop();
                                    onEvent({
                                        pageId: `CarOwnerRiskFaceRecognitionFailurePage`,
                                        tableId: 'auth_2&Doagain',
                                    });
                                }}
                            />
                            {UserType.isBoss() && (
                                <UIButton
                                    text={'申请视频申述'}
                                    style={{marginLeft: 14}}
                                    width={140}
                                    height={45}
                                    borderRadius={25}
                                    fontSize={17}
                                    onPress={() => {
                                        RouterUtils.skipRouter(RouterUrl.CommVidioApplyActivity, {
                                            faceReason: '1',
                                            FromFrag: '6',
                                            isLogin: '2',
                                            idCardNo: '',
                                        });
                                        onEvent({pageId: `CarOwnerRiskFaceRecognitionFailurePage`, tableId: 'auth_2&next'});
                                    }}
                                />
                            )}
                        </View>
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                alignSelf: 'center',
                                marginBottom: 25,
                                marginTop: 75,
                            }}>
                            <UITouchableOpacity
                                activeOpacity={1}
                                style={{flexDirection: 'row', alignItems: 'center'}}
                                onPress={() => {
                                    Method.openServerPopWindow();
                                }}>
                                <UIImage source={'driver_certification_regiest_phone'} style={{width: 12, height: 12, marginRight: 7}} />
                                <Text style={{fontSize: 13, color: '#FF602E'}}>联系客服</Text>
                            </UITouchableOpacity>
                            <Text style={{fontSize: 13, color: '#FF602E', paddingHorizontal: 14}}> | </Text>
                            <UITouchableOpacity
                                activeOpacity={1}
                                style={{flexDirection: 'row', alignItems: 'center'}}
                                onPress={() => {
                                    Method.openWebView(`${http.url()}/form_h5/order/index.html?_t=${new Date().getTime()}#/idAuthentication?type=2`, '认证帮助');
                                }}>
                                <UIImage source={'driver_certificaiton_regiest_wenhao'} style={{width: 12, height: 12, marginRight: 7}} />
                                <Text style={{fontSize: 13, color: '#FF602E'}}>认证帮助</Text>
                            </UITouchableOpacity>
                        </View>
                    </View>
                </ScrollView>
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
