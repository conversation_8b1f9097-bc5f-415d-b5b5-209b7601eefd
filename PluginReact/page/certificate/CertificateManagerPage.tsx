import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {Constant} from '../base/Constant';
import UIImageBackground from '../widget/UIImageBackground';
import {gScreen_width} from '../util/scaled-style';
import UIImage from '../widget/UIImage';
import {SceneMap, TabView} from 'react-native-tab-view';
import MembershipIDCardView from './views/MembershipIDCardView';
import VehicleIdCardView from './views/VehicleIdCardView';
import UIBubble from '../widget/UIBubble';
import {ReqQueryLicensePendingNum} from './requests/ReqQueryLicensePendingNum';
import EventBus from '../util/EventBus';
import UITouchableOpacity from '../widget/UITouchableOpacity';

interface State extends BaseState {
    tabIndex: number;
    //会员证件待处理数
    memberLicensePendingNum: number;
    //车辆证件待处理数
    vehicleLicensePendingNum: number;
}

/**
 * 注释:  证件管理页
 * 时间: 2024/8/27 0027 9:40
 * <AUTHOR>
 */
export default class CertificateManagerPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    eventListener: Function;

    constructor(props) {
        super(props);
        this.pageParams = this.getParams();
        this.state = {
            tabIndex: this.pageParams?.tabIndex ?? 0,
            memberLicensePendingNum: 0,
            vehicleLicensePendingNum: 0,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        //查询证件管理角标数
        this.queryLicensePendingNum();
        //监听证件刷新角标事件
        this.eventListener = () => {
            this.queryLicensePendingNum();
        };
        EventBus.getInstance().addListener(Constant.event_certification_refresh_subscript, this.eventListener);
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        EventBus.getInstance().removeListener(this.eventListener);
    }

    /**
     * 注释: 查询证件管理角标数
     * 时间: 2024/8/27 0027 15:40
     * <AUTHOR>
     */
    public async queryLicensePendingNum() {
        let request = new ReqQueryLicensePendingNum();
        let response = await request.request();
        if (response.isSuccess()) {
            this.setState({
                memberLicensePendingNum: response.data?.memberLicensePendingNum ?? 0,
                vehicleLicensePendingNum: response.data?.vehicleLicensePendingNum ?? 0,
            });
        }
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UIImage
                    source={'img_certificate_bg'}
                    style={{
                        width: gScreen_width,
                        height: (gScreen_width / 375) * 180,
                        position: 'absolute',
                        top: 0,
                    }}
                    resizeMode={'cover'}
                />
                <UITitleView title={'证件管理'} style={{backgroundColor: Constant.color_transparent, marginBottom: 18}} showLine={false} textStyle={{color: '#fff'}} backIcon={'base_back_white'} />
                {this.renderMainView()}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2024/8/27 0027 11:09
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderMainView() {
        //界面title
        let routes: {key: string; title: string}[];
        //tabs页面
        let renderScene: any;
        routes = [
            {key: 'first', title: '会员证件'},
            {key: 'second', title: '车辆证件'},
        ];
        renderScene = SceneMap({first: MembershipIDCardView, second: VehicleIdCardView});
        return (
            <TabView
                navigationState={{index: this.state.tabIndex, routes}}
                renderScene={renderScene}
                onIndexChange={(tabIndex) => {
                    this.setState({tabIndex: tabIndex});
                }}
                renderTabBar={(props) => this.renderTabBar(props, this.state.tabIndex)}
                initialLayout={{width: Dimensions.get('window').width}}
            />
        );
    }

    /**
     * 注释: Tab标签样式修改
     * 时间: 2024/8/27 0027 15:47
     * <AUTHOR>
     * @param props
     * @param tabIndex
     * @returns {JSX.Element}
     */
    renderTabBar(props, tabIndex: number) {
        return (
            <UIImageBackground source={tabIndex == 0 ? 'ic_tab_left' : 'ic_tab_right'} style={{width: gScreen_width, height: (gScreen_width / 375) * 61}}>
                <View style={styles.tabView}>
                    {/*会员证件Tab*/}
                    <UITouchableOpacity style={[styles.tabView, {paddingBottom: 10}]} onPress={() => props.jumpTo('first')}>
                        <Text style={tabIndex == 0 ? styles.selectStyle : styles.unSelectStyle}>会员证件</Text>
                        {this.state.memberLicensePendingNum > 0 && <UIBubble text={`${this.state.memberLicensePendingNum}`} style={{marginLeft: 5}} />}
                    </UITouchableOpacity>
                    {/*车辆证件Tab*/}
                    <UITouchableOpacity style={[styles.tabView, {paddingBottom: 10}]} onPress={() => props.jumpTo('second')}>
                        <Text style={tabIndex == 1 ? styles.selectStyle : styles.unSelectStyle}>车辆证件</Text>
                        {this.state.vehicleLicensePendingNum > 0 && <UIBubble text={`${this.state.vehicleLicensePendingNum}`} style={{marginLeft: 5}} />}
                    </UITouchableOpacity>
                </View>
            </UIImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    tabView: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectStyle: {
        fontSize: 16,
        color: '#333',
    },
    unSelectStyle: {
        fontSize: 16,
        color: '#fff',
    },
});
