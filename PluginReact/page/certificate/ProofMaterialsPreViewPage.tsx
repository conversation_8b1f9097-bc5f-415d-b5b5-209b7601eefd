import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {FlatGrid} from 'react-native-super-grid';
import UIImage from '../widget/UIImage';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UITouchableOpacity from '../widget/UITouchableOpacity';

interface State extends BaseState {}

/**
 * 注释:  证明材料预览页
 * 时间: 2024/8/29 0029 14:19
 * <AUTHOR>
 */
export default class ProofMaterialsPreViewPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    label: string;
    urlList: Array<string>;
    licensePlateNumber: string;
    carPurchaseProofPictureList: Array<string>;
    paymentProofPictureList: Array<string>;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.label = this.pageParams.label;
        this.urlList = this.pageParams?.urlList ?? [];
        this.licensePlateNumber = this.pageParams?.licensePlateNumber;
        this.carPurchaseProofPictureList = this.pageParams?.carPurchaseProofPictureList ?? [];
        this.paymentProofPictureList = this.pageParams?.paymentProofPictureList ?? [];
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'证明材料'} />
                <View style={styles.licensePlateNumberStyle}>
                    <Text style={{fontSize: 16, color: '#333'}}>车牌号</Text>
                    <Text style={{fontSize: 16, color: '#333'}}>{this.licensePlateNumber}</Text>
                </View>
                <View style={{backgroundColor: '#fff', marginTop: 10}}>
                    <Text style={styles.labelStyle}>{this.label}</Text>
                    <FlatGrid data={this.urlList} renderItem={({item, index}) => this.renderItem(item, index)} itemDimension={65} spacing={10} />
                </View>
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制Item布局
     * 时间: 2024/8/29 0029 15:17
     * <AUTHOR>
     * @param item
     * @param index
     * @returns {JSX.Element}
     */
    renderItem(item: string, index: number) {
        return (
            <UITouchableOpacity
                onPress={() => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'ImagePreViewPage',
                        data: {urlList: this.urlList, index: index},
                    });
                }}>
                <UIImage source={item} style={{width: 75, height: 75}} resizeMode={'cover'} />
            </UITouchableOpacity>
        );
    }
}

const styles = StyleSheet.create({
    labelStyle: {
        fontSize: 16,
        color: '#3D3D3D',
        fontWeight: 'bold',
        marginTop: 15,
        marginBottom: 10,
        marginLeft: 15,
    },
    licensePlateNumberStyle: {
        justifyContent: 'space-between',
        marginTop: 10,
        backgroundColor: '#fff',
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 15,
        paddingVertical: 10,
    },
});
