// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class ResMemberLicenseList extends ResultData {
    licenseList?: LicenseList[];
    licensePendingNum?: number;
    //	0-正常 2-冻结 3-异常
    status?: number;
    backup1?: number;
    statusText?: string;
}

export class LicenseList {
    licenseCode?: string;
    licenseName?: string;
    licenseStatus?: number;
    licenseStatusText?: string;
    licensePlateNumber?: string;
    licenseType?: string;
    targetId?: string;
    urlList?: string[];
}
