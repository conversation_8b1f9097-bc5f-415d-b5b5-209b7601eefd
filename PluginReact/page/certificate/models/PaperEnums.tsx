import React from 'react';
import {Text} from 'react-native';

export enum Flag {
    papers_triverpermit, // 普通车行驶证 or 牵引车行驶证
    papers_triverpermit1, // 普通车行驶证年检页
    papers_triverpermit2, // 牵引车行驶证年检页
    papers_roadtransportpermit, // 道路运输证
    papers_trailer, // 挂车行驶证
    papers_trailer1, // 挂车行驶证年检页
    papers_temporary, // 临牌牌照
    papers_roadtransportpermitlicense, // 道路运输许可证

    prove_carpurchaseproof, // 购车证明材料
    prove_paymentproof, // 支付证明材料
    prove_proof, // 挂靠证明材料
    prove_leaseproof, // 租赁证明材料
    prove_prove, // 证明材料
    prove_selfrealtion, // 亲属关系证明
}

// PapersEnum 转换为 TypeScript

export type PaperType = 1 | 2;

export interface Paper {
    flag: string;
    type: PaperType;
    title1: string;
    title2?: string;
    src: string;
    src2?: string;
    bigSrc?: string;
    bigSrc2?: string;
}

export const PapersEnum: {[key: string]: Paper} = {
    NULL: {
        flag: '',
        type: 1,
        title1: '',
        src: '',
    },

    TRIVERPERMIT: {
        flag: Flag[Flag.papers_triverpermit],
        type: 1,
        title1: '行驶证',
        title2: '(主页+副页)',
        src: 'certification_driver_license_template_diagram_7',
    },

    TRIVERPERMIT11: {
        flag: Flag[Flag.papers_triverpermit1],
        type: 1,
        title1: '行驶证',
        title2: '(年检页)',
        src: 'certification_driver_license_template_diagram_18',
    },

    ROADTRANSPORTPERMIT: {
        flag: Flag[Flag.papers_roadtransportpermit],
        type: 1,
        title1: '道路运输证',
        src: 'certification_driver_license_template_diagram_10',
    },

    TRIVERPERMIT2: {
        flag: Flag[Flag.papers_triverpermit],
        type: 1,
        title1: '牵引车行驶证',
        title2: '(主页+副页)',
        src: 'certification_driver_license_template_diagram_8',
    },

    TRIVERPERMIT21: {
        flag: Flag[Flag.papers_triverpermit2],
        type: 1,
        title1: '牵引车行驶证',
        title2: '(年检页)',
        src: 'certification_driver_license_template_diagram_19',
    },

    ROADTRANSPORTPERMIT2: {
        flag: Flag[Flag.papers_roadtransportpermit],
        type: 1,
        title1: '道路运输证',
        title2: '(牵引车/车头)',
        src: 'certification_driver_license_template_diagram_10',
    },

    TRAILER: {
        flag: Flag[Flag.papers_trailer],
        type: 1,
        title1: '挂车行驶证',
        title2: '(主页+副页)',
        src: 'certification_driver_license_template_diagram_6',
    },

    TRAILER1: {
        flag: Flag[Flag.papers_trailer1],
        type: 1,
        title1: '挂车行驶证',
        title2: '(年检页)',
        src: 'certification_driver_license_template_diagram_20',
    },

    TEMPORARY: {
        flag: Flag[Flag.papers_temporary],
        type: 2,
        title1: '临牌牌照',
        src: 'certification_driver_license_template_diagram_14',
        src2: 'certification_driver_license_template_diagram_11',
        bigSrc: 'certification_driver_license_template_diagram_13',
        bigSrc2: 'certification_driver_license_template_diagram_12',
    },

    ROADTRANSPORTPERMITLICENSE: {
        flag: Flag[Flag.papers_roadtransportpermitlicense],
        type: 1,
        title1: '道路运输经营许可证',
        src: 'certification_driver_license_template_diagram_9',
    },
};

export interface Prove {
    flag: string;
    maxCount: number;
    title: string;
    doubtMsg?: React.ReactNode;
}

export const ProveEnum: {[key: string]: Prove} = {
    NULL: {
        flag: '',
        maxCount: 0,
        title: '',
    },

    BOSS_CARPURCHASEPROO: {
        flag: Flag[Flag.prove_carpurchaseproof],
        maxCount: 5,
        title: '购车证明',
        doubtMsg: '车老板购车合同（如：车老板购车贷款合同、或车辆买卖合同、或融资租赁合同、或售后回租合同），必须包括日期，车辆的车架号、车牌号、发动机号，以及车老板姓名和身份证号，双方权利与义务，企业材料需要加盖公章，个体证明需签字加盖手印；',
    },

    BOSS_PAYMENTPROOF: {
        flag: Flag[Flag.prove_paymentproof],
        maxCount: 10,
        title: '支付证明',
        doubtMsg: '车老板支付的购买车辆全款转账记录、首付款和连续近3个月及以上的分期还款转账记录。\n以行驶证发证日期为准，购买不足3个月的，提供行驶证发证日期以来的首付款和分期还款转账记录。',
    },

    BOSS_PROOF: {
        flag: Flag[Flag.prove_proof],
        maxCount: 5,
        title: '挂靠证明',
        doubtMsg: '证明中必须包括挂靠日期起止，车辆的车架号、车牌号、发动机号，以及车老板姓名和身份证号，双方权利与义务，企业材料需要加盖公章，个体证明需签字加盖手印；',
    },

    BOSS_LEASEPROOF: {
        flag: Flag[Flag.prove_leaseproof],
        title: '租赁证明',
        maxCount: 5,
        doubtMsg: '证明中必须包括租赁起止日期，车辆的车架号、车牌号、发动机号，以及车老板姓名和身份证号，双方权力与义务，企业材料需要加盖公章，个体证明需签字加盖手印。',
    },

    BOSS_SELFREALTION: {
        flag: Flag[Flag.prove_selfrealtion],
        title: '亲属关系证明',
        maxCount: 5,
    },

    CYS_PROVE: {
        flag: Flag[Flag.prove_prove],
        title: '证明材料',
        maxCount: 5,
        doubtMsg:
            '所有车辆需由物流企业添加注册认证，除标准认证信息外，物流企业还需要提供相关材料，证明其与车辆之间的从属关系，材料如下:' +
            <Text style={{color: '#ff602e'}}>1.购车证明、贷款买车证明;</Text> +
            '2.以上三种里面任意一种可以证明车辆权属的材料，' +
            <Text style={{color: '#ff602e'}}>该证明中必须包括车辆的{'\n'}车架号或车牌号，以及物流企业名称</Text> +
            '。',
    },
};
