export class RspQueryUserExpireLicenseDetail {
    licenseReason?: string; // 证件驳回理由
    licenseState?: string; // 证件状态 0：待上传；1：审核中; 3：审核失败
    licenseType?: string; // 证件类型
    licenseUrl: string; // 图片url
    licenseName: string; // 图片名称
    targetName?: string; // 目标名称 会员名称或者车牌号或者船舶名称
    ownRelation: string; // 车辆所属关系 1、挂靠关系 2、租赁关系
    licenseUrl1?: string; // 营业执照法人身份证照片
    isEntrustRegister?: string; // 是否非法定代表人注册 1-是 0-否
}
