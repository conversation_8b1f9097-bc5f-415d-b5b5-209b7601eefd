// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class ResQueryMemberVehicleLicense extends ResultData {
    licenseList?: LicenseList[];
    licensePendingNum?: number;
    plateNumber?: string;
    // 0-正常 1-报废 2-冻结 3-异常
    status?: number;
    vehicleId?: string;
    statusText?: string;
    //1-普通车 2-半挂车
    vehicleFlag?: number;
    backup1?: number; //临时/超限车辆 0-否 1-是
    trailerPlateNumber?: number; //挂车车牌号
}

export interface LicenseList {
    licenseCode?: string;
    licenseName?: string;
    licensePlateNumber?: string;
    licenseStatus?: number;
    licenseStatusText?: string;
    licenseType?: null | string;
    targetId?: number;
    //材料证明
    urlList?: string[];
    //购车证明
    carPurchaseProofPictureList: string[];
    //支付证明
    paymentProofPictureList: string[];
}
