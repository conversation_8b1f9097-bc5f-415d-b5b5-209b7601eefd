// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class ResQueryLicensePendingNum extends ResultData {
    /**
     * 会员待处理证件数量
     */
    memberLicensePendingNum?: number;
    /**
     * 待处理证件总数量
     */
    totalLicensePendingNum?: number;
    /**
     * 车辆待处理证件数量
     */
    vehicleLicensePendingNum?: number;
}
