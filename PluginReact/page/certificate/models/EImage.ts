import TextUtils from '../../util/TextUtils';

export class EImage {
    path?: string; // 图片本地路径
    imageId?: string; // 服务器返回 图片ID
    netUrl?: string; // 网络url
    tempDatFlag?: boolean = false; // 临时数据 暂用在 拍照图片上
    pictureType?: string; //图片类型
    bgColor?: string; //背景颜色
    toastStr?: string; //提示信息

    constructor(path?: string, imageId?: string, netUrl?: string, tempDatFlag?: boolean, pictureType?: string, bgColor?: string, toastStr?: string) {
        this.path = path ?? '';
        this.imageId = imageId ?? '';
        this.netUrl = netUrl ?? '';
        this.tempDatFlag = tempDatFlag ?? false;
        this.pictureType = pictureType ?? '';
        this.bgColor = bgColor ?? '';
        this.toastStr = toastStr ?? '';
    }

    // 是否是空实体
    isEmpty() {
        if (this != null) {
            return TextUtils.isEmpty(this.path) && TextUtils.isEmpty(this.imageId) && TextUtils.isEmpty(this.netUrl);
        } else {
            return true;
        }
    }
}
