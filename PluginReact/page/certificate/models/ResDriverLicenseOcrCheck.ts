// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class ResDriverLicenseOcrCheck extends ResultData {
    data: DriverLicenseOcrResult;
}

export class DriverLicenseOcrResult {
    birthday: string;
    driverLiceNo: string;
    address: string;
    sex: number;
    expiryDate: string; //有效期止
    issuingAuthority: string;
    driverLiceName: string;
    nationality: string;
    archivesNo: string;
    quasiVehicleType: string;
    firstIssueDate: string;
    startDate: string;
    name: string; //从业资格证OCR 姓名
}
