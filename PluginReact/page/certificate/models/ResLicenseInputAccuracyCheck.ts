import {ResultData} from '../../http/ResultData';

export default class ResLicenseInputAccuracyCheck extends ResultData {
    public data?: LincenseTipInfo;
}

export class LincenseTipInfo {
    public transportLicensePlateNumberText?: string;
    public quasiVehicleTypeText?: string;
    public driverLicNoText?: string;
    public driverLicEffectDateText?: string;
    public qualificationLicNameText?: string; // 从业资格证姓名错误提示
    public qualificationLicEffectDateText?: string; // 从业资格证有效期错误提示
    public idCardNameText?: string; // 	身份证姓名错误提示
    public idCardNoText?: string; // 	身份证号错误提示
    public idCardEffectDateText?: string; // 	身份证有效期错误提示
    public transportLicensePlateNumber?: string; // 	道路运输证车牌号错误提示
    public plateNumberText?: string; //车牌号错误展示
    public vehicleLicenseEffectDateText?: string; //年检页截止时间错误展示
    public vehicleBodyLicensePlateNumberText?: string;
    public trailerVehicleLicenseEffectDateText?: string;
}
