// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import {ResultData} from '../../http/ResultData';

export class ResMemberVehicleLicenseList extends ResultData {
    licensePendingNum?: number;
    vehicleList?: VehicleList[];
}

export class VehicleList {
    licenseList?: LicenseList[];
    plateNumber?: string;
    //0-正常 1-报废 2-冻结 3-异常
    status?: number;
    statusText?: string;
    vehicleId?: string;
    //待处理证件数量
    vehicleLicensePendingNum?: number;
}

export class LicenseList {
    carPurchaseProofPictureList?: string[];
    licenseCode?: string;
    licenseName?: string;
    licensePlateNumber?: string;
    licenseStatus?: number;
    licenseStatusText?: string;
    licenseType?: null | string;
    paymentProofPictureList?: string[];
    targetId?: number;
    urlList?: string[];
}
