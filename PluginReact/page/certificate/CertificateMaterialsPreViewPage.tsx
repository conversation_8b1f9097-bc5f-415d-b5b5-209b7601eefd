import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import TextUtils from '../util/TextUtils';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {Constant} from '../base/Constant';

interface State extends BaseState {}

/**
 * 注释: 证件材料预览
 * 时间: 2024/9/14 0014 9:37
 * <AUTHOR>
 */
export default class CertificateMaterialsPreViewPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    label: string;
    licenseCode: string;
    licenseStatus: number;
    urlList: Array<string>;
    callBack: Function;
    licensePlateNumber: string;
    backup1: string;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.label = this.pageParams.label;
        this.licenseCode = this.pageParams.licenseCode;
        this.licenseStatus = this.pageParams.licenseStatus;
        this.licensePlateNumber = this.pageParams.licensePlateNumber;
        this.backup1 = this.pageParams.backup1;
        this.callBack = this.getCallBack();
        this.urlList = this.pageParams?.urlList ?? [];
    }

    /**
     * 注释: 绘制图片
     * 时间: 2024/9/14 0014 9:47
     * <AUTHOR>
     * @param url
     * @returns {JSX.Element}
     * @param index
     */
    renderImageView(url, index) {
        return (
            <UITouchableOpacity
                style={{paddingHorizontal: 10, marginBottom: 15}}
                onPress={() => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'ImagePreViewPage',
                        data: {urlList: this.urlList, index: index},
                    });
                }}>
                <UIImage source={url} style={{width: '100%', height: 250}} key={`${url}`} resizeMode={'contain'} />
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 人车合影
     * 时间: 2024/9/14 0014 15:45
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderPeopleCardImages() {
        return (
            <View style={{flexDirection: 'column'}}>
                <View style={{backgroundColor: '#fff', marginTop: 10}}>
                    <Text style={styles.labelStyle}>
                        {'人脸+车牌号照片'}
                        <Text style={{color: Constant.color_ff451c}}>*</Text>
                    </Text>
                    <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingVertical: 10}}>
                        <Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                            }}>
                            请上传本人与认证车辆<Text style={{color: Constant.color_ff451c}}>({this.licensePlateNumber})</Text>的合影，需要包含<Text style={{color: Constant.color_ff451c}}>人脸、车头车牌</Text>
                        </Text>
                    </View>
                    {this.renderImageView(this.urlList[0], 0)}
                </View>
                <View style={{backgroundColor: '#fff', marginTop: 10}}>
                    <Text style={styles.labelStyle}>
                        {TextUtils.equals('1', this.backup1) ? '车头车牌+车身照片' : '车尾车牌+车身照片'}
                        <Text style={{color: Constant.color_ff451c}}>*</Text>
                    </Text>
                    <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingVertical: 10}}>
                        <Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                            }}>
                            请上传本人与认证车辆<Text style={{color: Constant.color_ff451c}}>({this.licensePlateNumber})</Text>的车辆照片，需要包含<Text style={{color: Constant.color_ff451c}}>车尾车牌、车身</Text>
                        </Text>
                    </View>
                    {this.renderImageView(this.urlList[1], 1)}
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制临牌证件
     * 时间: 2024/9/14 0014 15:47
     * <AUTHOR>
     */
    renderLinpaiImages() {
        return (
            <View>
                {/*车牌号*/}
                {this.renderPlateNumber()}
                {/*证件照片*/}
                <View style={{flexDirection: 'column'}}>
                    <View style={{backgroundColor: '#fff', marginTop: 10}}>
                        <Text style={styles.labelStyle}>{'超限临牌正面照'}</Text>
                        {this.renderImageView(this.urlList[0], 0)}
                    </View>
                    <View style={{backgroundColor: '#fff', marginTop: 10}}>
                        <Text style={styles.labelStyle}>{'超限临牌反面照'}</Text>
                        {this.renderImageView(this.urlList[1], 1)}
                    </View>
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制普通证件照片
     * 时间: 2024/9/14 0014 15:46
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderNormalImages() {
        let isCanUpdate = false;
        if (this.licenseCode == '204' || this.licenseCode == '101' || this.licenseCode == '102') {
            isCanUpdate = true;
        }
        return (
            <View>
                {/*车牌号*/}
                {this.renderPlateNumber()}
                {/*证件照片*/}
                <View style={{backgroundColor: '#fff', marginTop: 10}}>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center'}}>
                        <Text style={styles.labelStyle}>{`${this.label}照片`}</Text>
                        {/*道路运输证自主更新*/}
                        {isCanUpdate && (
                            <UITouchableOpacity
                                onPress={() => {
                                    RouterUtils.skipPop();
                                    this.callBack && this.callBack();
                                }}>
                                <Text style={{color: '#5086FC', fontSize: 15, marginRight: 15}}>{'自主更新 >'}</Text>
                            </UITouchableOpacity>
                        )}
                    </View>
                    {this.urlList.map((value, index) => {
                        return this.renderImageView(value, index);
                    })}
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制车牌号
     * 时间: 2024/11/29 星期五 16:45
     * <AUTHOR>
     */
    renderPlateNumber() {
        if (TextUtils.isNoEmpty(this.licensePlateNumber)) {
            return (
                <View style={styles.vehicleStyle}>
                    <Text style={{color: '#333333', fontSize: 16}}>{'车牌号码'}</Text>
                    <Text style={{color: '#333333', fontSize: 16}}>{this.licensePlateNumber}</Text>
                </View>
            );
        }
    }

    render() {
        let children;
        if (TextUtils.equals('208', this.licenseCode)) {
            children = this.renderLinpaiImages();
        } else if (TextUtils.equals('209', this.licenseCode)) {
            children = this.renderPeopleCardImages();
        } else {
            children = this.renderNormalImages();
        }
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={`${this.label}`} />
                <ScrollView>{children}</ScrollView>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    labelStyle: {
        fontSize: 16,
        color: '#3D3D3D',
        fontWeight: 'bold',
        marginTop: 15,
        marginBottom: 10,
        marginLeft: 15,
    },
    vehicleStyle: {
        marginTop: 10,
        backgroundColor: '#fff',
        paddingHorizontal: 15,
        paddingVertical: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
});
