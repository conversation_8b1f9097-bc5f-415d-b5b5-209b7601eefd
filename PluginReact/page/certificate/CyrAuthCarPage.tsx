import React, {ReactNode} from 'react';
import {ParamListBase} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {Text, TouchableOpacity, View} from 'react-native';
import UIImage from '../widget/UIImage';
import UITitleView from '../widget/UITitleView';
import {EVehicle, ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import TextUtils from '../util/TextUtils';
import UIPopup from '../widget/UIPopup';
import {ReqUserUpgradeSaveLicense, ReqVehicle} from './requests/ReqUserUpgradeSaveLicense';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {Method} from '../util/NativeModulesTools';
import {DialogNewBuilder} from '../base/Dialog';
import {ReqIntelligentAudit2CacheClear} from './requests/ReqIntelligentAudit2CacheClear';
import CarItemCheckView from './views/CarItemCheckView';
import CarItemCertificateView from './views/CarItemCertificateView';
import CarItemSelectView from './views/CarItemSelectView';

/**
 * 司机车辆认证
 */
interface State extends BaseState {
    init?: boolean;
    vehicle?: EVehicle;
    showEmissionStandardDialog?: boolean;
    showTop?: boolean; //显示准驾车型非A2照不可添加半挂车
    quasiVehicleType?: string; // 准驾车型
    postOnClickEnable?: boolean; //提交按钮是否可点击
}

export default class CyrAuthCarPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    convertedEmissionStandardList = ['国Ⅰ(国一)', '国Ⅱ(国二)', '国Ⅲ(国三)', '国Ⅳ(国四)', '国Ⅴ(国五)', '国Ⅵ(国六)'];

    componentWillUnmount() {
        super.componentWillUnmount();
        const call: Function = this.getCallBack();
        call && call();
    }

    componentDidMount(): void {
        super.componentDidMount();
        this._showWaitDialog();
        //车辆信息
        let req = new ReqQueryUserLicense();
        req.request().then((resp) => {
            this._dismissWait();
            if (resp.isSuccess()) {
                let quasiVehicleType = resp.data?.driverLicense?.quasiVehicleType;
                let vehicleFlag = resp.data?.vehicle?.vehicleFlag;
                let backup2 = resp.data?.vehicle?.backup2;

                //2、准驾车型为A2允许添加普通货车、半挂车，其他准驾车型仅支持添加普通车辆
                if (TextUtils.isEmpty(vehicleFlag)) {
                    //没有车辆类型
                    if (TextUtils.equals('A2', quasiVehicleType)) {
                        //准驾车型为A2车辆类型默认半挂车
                        vehicleFlag = '2';
                    } else {
                        //其他准驾车型仅支持添加普通车辆
                        vehicleFlag = '1';
                        if (TextUtils.equals('C', quasiVehicleType)) {
                            //C照默认总质量小于4.5吨的普通货车
                            backup2 = '0';
                        } else {
                            //非C照、非A2照默认车辆类型为总质量大于4.5吨的普通货车，
                            backup2 = '1';
                        }
                    }
                }
                this.setState(
                    {
                        init: true,
                        quasiVehicleType: quasiVehicleType,
                        vehicle: {...resp.data?.vehicle, vehicleFlag: vehicleFlag, backup2: backup2},
                        showTop: TextUtils.isNoEmpty(quasiVehicleType) && !TextUtils.equals('A2', quasiVehicleType),
                    },
                    () => {
                        //处理按钮是否可以点击
                        this.handlerPostOnClickEnable();
                    },
                );
            } else {
                this.setState({
                    init: true,
                    vehicle: {},
                    showTop: false,
                });
            }
        });
    }

    renderTopToastView = () => {
        return (
            <View
                style={{
                    backgroundColor: '#FFF4EF',
                    paddingLeft: 14,
                    paddingRight: 14,
                    paddingTop: 5,
                    paddingBottom: 12,
                    flexDirection: 'row',
                }}>
                <UIImage source="base_warning_red" style={{width: 12, height: 12, marginTop: 3, marginRight: 5}} />
                <Text
                    style={{
                        color: '#C86966',
                        fontSize: 14,
                        flex: 1,
                    }}>
                    准驾车型非A2照不可添加半挂车。
                </Text>
            </View>
        );
    };

    renderOrdinaryViews = (vehicle?: EVehicle) => {
        //普通车 1、身份认证 2、驾驶证信息 5、从业资格证 6、行驶证 7、道路运输证 8、道路运输经营许可证 9、挂车行驶证 10、超限临牌 11、人车合影 12、证明材料
        //行驶证上传证件 or 修改证件 (必须先上传,其他证件才可上传)
        const isAddVehicleLicense = TextUtils.isEmpty(vehicle?.vehicleLicenseUrl);
        //道路运输证
        const isAddTransportLicense = TextUtils.isEmpty(vehicle?.transportLicenseUrl);
        //道路运输经营许可证
        const isAddTransportPermission = TextUtils.isEmpty(vehicle?.transportPermissionLicenseUrl);
        //从业资格证
        const isAdQualificate = TextUtils.isEmpty(vehicle?.qualificatePicUrl);
        //人车合影
        const isAddPersonCarUrl = TextUtils.isEmpty(vehicle?.personCarUrl1);
        //是否显示 已上传道路运输证 && 无道路运输证号
        const showTransportPermission = TextUtils.isNoEmpty(vehicle?.transportLicenseUrl) && TextUtils.isEmpty(vehicle?.businessLicenceNo);
        return (
            <>
                <CarItemCheckView title={'总质量是否大于4.5吨'} flag={true} edit={true} checkLeftRight={TextUtils.equals('1', vehicle?.backup2) ? 1 : 2} leftTxt={'是'} rightText={'否'} onChange={this.onSelectBackup2} />
                <CarItemCertificateView title={'行驶证'} flag={1} must={true} edit={true} add={isAddVehicleLicense} onClick={this.onClickVehicle} />
                {TextUtils.equals('1', vehicle?.backup2) && (
                    <>
                        <CarItemCertificateView title={'道路运输证'} flag={1} must={true} edit={!isAddVehicleLicense} add={isAddTransportLicense} onClick={this.onClickTransportLicense} />
                        {showTransportPermission && <CarItemCertificateView title={'道路运输经营许可证'} flag={1} must={true} edit={!isAddVehicleLicense} add={isAddTransportPermission} onClick={this.onClickTransportPermission} />}
                        <CarItemCertificateView title={'从业资格证'} flag={1} must={true} edit={!isAddVehicleLicense} add={isAdQualificate} onClick={this.onClickQualificate} />
                    </>
                )}
                <CarItemCertificateView title={'人车合影'} flag={1} must={true} edit={!isAddVehicleLicense} add={isAddPersonCarUrl} onClick={this.onClickPersonCarUrl} />
            </>
        );
    };
    renderOtherViews = (vehicle?: EVehicle) => {
        //挂车  1、身份认证 2、驾驶证信息 5、从业资格证 6、行驶证 7、道路运输证 8、道路运输经营许可证 9、挂车行驶证 10、超限临牌 11、人车合影 12、证明材料
        //行驶证+ 行驶证年检页 上传证件 or 修改证件 (必须先上传,其他证件才可上传)
        const isAddVehicleLicense = TextUtils.isEmpty(vehicle?.vehicleLicenseUrl);
        //道路运输证
        const isAddTransportLicense = TextUtils.isEmpty(vehicle?.transportLicenseUrl);
        //道路运输经营许可证
        const isAddTransportPermission = TextUtils.isEmpty(vehicle?.transportPermissionLicenseUrl);
        //从业资格证
        const isAdQualificate = TextUtils.isEmpty(vehicle?.qualificatePicUrl);
        //挂车行驶证+年检页
        const isAddVehicleBodyLicense = TextUtils.isEmpty(vehicle?.vehicleBodyLicenseUrl);
        //临时牌照
        const isAddOverloadBack = TextUtils.isEmpty(vehicle?.overloadPlateFrontUrl);
        //人车合影
        const isAddPersonCarUrl = TextUtils.isEmpty(vehicle?.personCarUrl1);
        //是否显示 已上传道路运输证 && 无道路运输证号
        const showTransportPermission = TextUtils.isNoEmpty(vehicle?.transportLicenseUrl) && TextUtils.isEmpty(vehicle?.businessLicenceNo);
        return (
            <>
                <CarItemCheckView title={'是否为临时/超牌车辆'} flag={true} edit={true} checkLeftRight={TextUtils.equals('1', vehicle?.backup1) ? 1 : 2} leftTxt={'是'} rightText={'否'} onChange={this.onSelectBackup1} />
                <CarItemCertificateView title={'牵引车/车头行驶证'} flag={2} must={true} edit={true} add={isAddVehicleLicense} onClick={this.onClickVehicle} />
                <CarItemCertificateView title={'道路运输证(牵引车/车头)'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddTransportLicense} onClick={this.onClickTransportLicense} />
                {showTransportPermission && <CarItemCertificateView title={'道路运输经营许可证'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddTransportPermission} onClick={this.onClickTransportPermission} />}
                {TextUtils.equals('1', vehicle?.backup1) ? (
                    <CarItemCertificateView title={'临时牌照'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddOverloadBack} onClick={this.onClickOverload} />
                ) : (
                    <CarItemCertificateView title={'挂车/车身行驶证'} flag={3} must={true} edit={!isAddVehicleLicense} add={isAddVehicleBodyLicense} onClick={this.onClickVehicle} />
                )}
                <CarItemCertificateView title={'从业资格证'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAdQualificate} onClick={this.onClickQualificate} />
                <CarItemCertificateView title={'人车合影'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddPersonCarUrl} onClick={this.onClickPersonCarUrl} />
            </>
        );
    };

    getEmissionStandardTxt = (emissionStandard?: string) => {
        //'国Ⅰ(国一)', '国Ⅱ(国二)', '国Ⅲ(国三)', '国Ⅳ(国四)', '国Ⅴ(国五)', '国Ⅵ(国六)'
        switch (`${emissionStandard}`) {
            case '1': {
                return '国Ⅰ(国一)';
            }
            case '2': {
                return '国Ⅱ(国二)';
            }
            case '3': {
                return '国Ⅲ(国三)';
            }
            case '4': {
                return '国Ⅳ(国四)';
            }
            case '5': {
                return '国Ⅴ(国五)';
            }
            case '6': {
                return '国Ⅵ(国六)';
            }
        }
        return '';
    };

    render(): ReactNode {
        const vehicle = this.state.vehicle;
        //2、准驾车型为A2允许添加普通货车、半挂车，其他准驾车型仅支持添加普通车辆
        const okSelect = TextUtils.isEmpty(this.state.quasiVehicleType) || TextUtils.equals('A2', this.state.quasiVehicleType);
        console.log('非A2照不可添加半挂车===' + okSelect);

        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView
                    title={'车辆认证'}
                    rightText="客服"
                    clickRight={() => {
                        //打开客服
                        Method.openServerPopWindow();
                    }}
                />
                {this.state.init && (
                    <>
                        {this.state.showTop && this.renderTopToastView()}
                        <CarItemCheckView title={'车辆类型'} flag={true} edit={okSelect} checkLeftRight={TextUtils.equals('2', vehicle?.vehicleFlag) ? 2 : 1} leftTxt={'普通货车'} rightText={'半挂车'} onChange={this.onSelectVehicleFlag} />
                        <CarItemSelectView title={'排放标准'} value={this.getEmissionStandardTxt(vehicle?.emissionStandard)} onClick={this.onSelectVehicleEmissionStandard} />
                        {TextUtils.equals('2', vehicle?.vehicleFlag) ? this.renderOtherViews(vehicle) : this.renderOrdinaryViews(vehicle)}
                    </>
                )}
                <View style={{flex: 1}} />
                <Text
                    onPress={this.onCliclSave}
                    style={{
                        backgroundColor: this.state.postOnClickEnable ? '#5086FC' : '#CCC',
                        color: '#fff',
                        fontSize: 18,
                        textAlign: 'center',
                        paddingTop: 15,
                        paddingBottom: 15,
                    }}>
                    提交上传
                </Text>
                {this.state.showEmissionStandardDialog && (
                    <UIPopup title="请选择排放标准" onClose={() => this.setState({showEmissionStandardDialog: false})}>
                        {this.convertedEmissionStandardList.flatMap((item, index) => (
                            <Text
                                onPress={() => {
                                    this.setState({
                                        showEmissionStandardDialog: false,
                                        vehicle: {
                                            ...this.state.vehicle,
                                            emissionStandard: `${index + 1}`,
                                        },
                                    });
                                }}
                                style={{
                                    color: '#333',
                                    fontSize: 14,
                                    textAlign: 'center',
                                    paddingTop: 7,
                                    paddingBottom: 7,
                                    backgroundColor: '#fff',
                                }}>
                                {item}
                            </Text>
                        ))}
                    </UIPopup>
                )}
                {this._initCommView()}
            </View>
        );
    }

    //车辆类型
    onSelectVehicleFlag = (type: number) => {
        //用户更新证件后切换了车辆类型，清空所有证件全部重新上传
        let dialog = new DialogNewBuilder();
        dialog.setTitle('提示');
        dialog.setMsg('更换车辆类型后需要重新上传所有证件，是否继续操作？');
        dialog.setRightTxt('确认更换');
        dialog.setLeftTxt('取消');
        dialog.setRightOnClick((dialog) => {
            dialog.dismiss();
            //用户更新证件后切换了车辆类型，清空所有证件全部重新上传，
            this._showWaitDialog();
            let req = new ReqIntelligentAudit2CacheClear();
            req.vehicleId = this.state.vehicle?.vehicleId;
            req.request().then((response) => {
                this._dismissWait();
                if (response.isSuccess()) {
                    this.setState({
                        vehicle: {
                            vehicleFlag: type === 1 ? '1' : '2',
                            backup2: type === 1 ? '1' : '', //普通车辆总质量是否>4.5吨
                        },
                    });
                    this.onRefresh();
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            });
        });
        dialog?.show();
    };

    //排放标准
    onSelectVehicleEmissionStandard = () => {
        this.setState({
            showEmissionStandardDialog: true,
        });
    };

    onSelectBackup1 = (type: number) => {
        //是否为临时/超牌车辆
        this.setState(
            {
                vehicle: {
                    ...this.state.vehicle,
                    backup1: type === 1 ? '1' : '0',
                },
            },
            () => {
                //处理按钮是否可以点击
                this.handlerPostOnClickEnable();
            },
        );
    };

    onSelectBackup2 = (type: number) => {
        //总质量是否>4.5吨
        this.setState(
            {
                vehicle: {
                    ...this.state.vehicle,
                    backup2: type === 1 ? '1' : '0',
                },
            },
            () => {
                //处理按钮是否可以点击
                this.handlerPostOnClickEnable();
            },
        );
    };

    onRefresh = () => {
        //刷新当前界面车辆认证数据
        let req = new ReqQueryUserLicense();
        req.type = '3';
        req.request().then((resp) => {
            if (resp.isSuccess() && resp.data?.vehicle) {
                let vehicleFlag = this.state.vehicle?.vehicleFlag;
                let emissionStandard = this.state.vehicle?.emissionStandard;
                let backup1 = this.state.vehicle?.backup1;
                let backup2 = this.state.vehicle?.backup2;

                this.setState(
                    {
                        vehicle: {
                            ...resp.data?.vehicle,
                            vehicleFlag: vehicleFlag,
                            emissionStandard: emissionStandard,
                            backup1: backup1,
                            backup2: backup2,
                        },
                    },
                    () => {
                        //处理按钮是否可以点击
                        this.handlerPostOnClickEnable();
                    },
                );
            }
        });
    };
    /**
     * 行驶证
     * @param type  1 普通车行驶证 2 车头行驶证 3 挂车行驶证
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickVehicle = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: type === 2 ? 'DrivingHeadLicensePage' : type === 3 ? 'DrivingGuaLicensePage' : 'DrivingLicensePage',
            data: {
                mode: statue == 1 ? 0 : 1, // 0:上传 1:更新 2:过期
                addOrManage: 1,
                vehicleId: this.state.vehicle?.vehicleId,
                flag: this.state.vehicle?.vehicleFlag,
                backup1: this.state.vehicle?.backup1,
                backup2: this.state.vehicle?.backup2,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 道路运输证
     * @param type  1 普通车 2 道路运输证（牵引车/车头）
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickTransportLicense = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'ExpiredTransportUpdatePage',
            data: {
                rspQueryUserExpireLicenseB: {
                    targetId: this.state.vehicle?.vehicleId,
                    licensePlateNumber: this.state.vehicle?.plateNumber,
                },
                mode: statue == 1 ? 0 : 1, // 0:上传 1:更新 2:过期
                addOrManage: 1,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 道路运输经营许可证
     * @param type  1 普通车 2 挂车
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickTransportPermission = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'ExpiredTransportBusinessUpdatePage',
            data: {
                vehicleId: this.state.vehicle?.vehicleId,
                mode: statue == 1 ? 0 : 1, // 0:上传 1:更新 2:过期
                addOrManage: 1,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 从业资格证
     * @param type  1 普通车 2 挂车
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickQualificate = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'QualificationCertificatePage',
            data: {
                vehicleId: this.state.vehicle?.vehicleId,
                mode: 0,
                isAdd: false,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 临时牌照
     * @param type 1 普通车 2 挂车
     * @param statue   1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickOverload = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'ProCardPage',
            data: {
                vehicleId: this.state.vehicle?.vehicleId,
                mode: 0, // 0:上传 1:更新 2:过期
                isAdd: false,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 人车合影
     * @param type  1 普通车 2 挂车
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickPersonCarUrl = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'PersonPhotoGroupPage',
            data: {
                vehicleId: this.state.vehicle?.vehicleId,
                isAdd: false,
                mode: 0, // 0:上传 1:更新 2:过期
                backup1: this.state.vehicle?.backup1,
            },
            callBack: this.onRefresh,
        });
    };

    handlerPostOnClickEnable = () => {
        if (this.state.vehicle) {
            if (TextUtils.equals('2', this.state.vehicle?.vehicleFlag)) {
                //2、挂车
                if (TextUtils.equals('1', this.state.vehicle?.backup1)) {
                    //临时/超限车辆
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.vehicleLicenseUrl) || // 牵引车行驶证
                        TextUtils.isEmpty(this.state.vehicle?.transportLicenseUrl) || // 道路运输证
                        (TextUtils.isEmpty(this.state.vehicle?.businessLicenceNo) && TextUtils.isEmpty(this.state.vehicle?.transportPermissionLicenseUrl)) || //道路运输经营许可证
                        TextUtils.isEmpty(this.state.vehicle?.overloadPlateFrontUrl) || // 临时牌照
                        TextUtils.isEmpty(this.state.vehicle?.qualificatePicUrl) || //从业资格证
                        TextUtils.isEmpty(this.state.vehicle?.personCarUrl1) // 人车合影
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                } else {
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.vehicleLicenseUrl) || // 牵引车行驶证
                        TextUtils.isEmpty(this.state.vehicle?.transportLicenseUrl) || //道路运输证照片
                        (TextUtils.isEmpty(this.state.vehicle?.businessLicenceNo) && TextUtils.isEmpty(this.state.vehicle?.transportPermissionLicenseUrl)) || //道路运输经营许可证
                        TextUtils.isEmpty(this.state.vehicle?.vehicleBodyLicenseUrl) || // 挂车行驶证
                        TextUtils.isEmpty(this.state.vehicle?.qualificatePicUrl) || // 从业资格证
                        TextUtils.isEmpty(this.state.vehicle?.personCarUrl1) // 人车合影
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                }
            } else {
                //1、普通货车
                if (TextUtils.equals('1', this.state.vehicle?.backup2)) {
                    //总质量是否>4.5吨
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.vehicleLicenseUrl) || //行驶证
                        TextUtils.isEmpty(this.state.vehicle?.transportLicenseUrl) || // 道路运输证照片
                        (TextUtils.isEmpty(this.state.vehicle?.businessLicenceNo) && TextUtils.isEmpty(this.state.vehicle?.transportPermissionLicenseUrl)) || // 道路运输经营许可证
                        TextUtils.isEmpty(this.state.vehicle?.qualificatePicUrl) || // 从业资格证
                        TextUtils.isEmpty(this.state.vehicle?.personCarUrl1) //'人车合影
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                } else {
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.vehicleLicenseUrl) || //请上传行驶证
                        TextUtils.isEmpty(this.state.vehicle?.personCarUrl1) // 人车合影
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                }
            }

            this.setState({postOnClickEnable: true});
        } else {
            //没有数据不能点击提交
            this.setState({postOnClickEnable: false});
        }
    };

    onCliclSave = () => {
        if (this.state.postOnClickEnable) {
            this._showWaitDialog();
            let vehicle = new ReqVehicle();

            vehicle.emissionStandard = this.state.vehicle?.emissionStandard;
            vehicle.plateColor = this.state.vehicle?.plateColor;
            vehicle.plateNumber = this.state.vehicle?.plateNumber;
            vehicle.remark = this.state.vehicle?.remark;
            vehicle.vehicleId = this.state.vehicle?.vehicleId;

            if (TextUtils.equals('2', this.state.vehicle?.vehicleFlag)) {
                //2、挂车
                vehicle.vehicleFlag = '2';
                vehicle.backup2 = '';

                if (TextUtils.equals('1', this.state.vehicle?.backup1)) {
                    vehicle.backup1 = '1';
                    vehicle.vehicleLicenseUrl = this.state.vehicle?.vehicleLicenseUrl;
                    vehicle.vehicleLicenseEffectDate = this.state.vehicle?.vehicleLicenseEffectDate;
                    vehicle.tractorVehicleLicenseUrl = this.state.vehicle?.tractorVehicleLicenseUrl;
                    vehicle.annualVehicleLicenseUrl = this.state.vehicle?.annualVehicleLicenseUrl;
                    vehicle.transportLicenseUrl = this.state.vehicle?.transportLicenseUrl;
                    vehicle.transportLicensePlateNumber = this.state.vehicle?.transportLicensePlateNumber;
                    vehicle.transportBusinessNo = this.state.vehicle?.businessLicenceNo;
                    vehicle.transportPermissionLicenseUrl = this.state.vehicle?.transportPermissionLicenseUrl;
                    vehicle.overloadPlateFrontUrl = this.state.vehicle?.overloadPlateFrontUrl;
                    vehicle.overloadPlateBackUrl = this.state.vehicle?.overloadPlateBackUrl;
                    vehicle.qualificatePicUrl = this.state.vehicle?.qualificatePicUrl;
                    vehicle.qualificationLicName = this.state.vehicle?.qualificationLicName;
                    vehicle.qualificationLicEffectDate = this.state.vehicle?.qualificationLicName;
                    vehicle.personCarUrl1 = this.state.vehicle?.personCarUrl1;
                    vehicle.personCarUrl2 = this.state.vehicle?.personCarUrl2;
                } else {
                    vehicle.backup1 = '0';
                    vehicle.vehicleLicenseUrl = this.state.vehicle?.vehicleLicenseUrl;
                    vehicle.vehicleLicenseEffectDate = this.state.vehicle?.vehicleLicenseEffectDate;
                    vehicle.tractorVehicleLicenseUrl = this.state.vehicle?.tractorVehicleLicenseUrl;
                    vehicle.annualVehicleLicenseUrl = this.state.vehicle?.annualVehicleLicenseUrl;
                    vehicle.transportLicenseUrl = this.state.vehicle?.transportLicenseUrl;
                    vehicle.transportLicensePlateNumber = this.state.vehicle?.transportLicensePlateNumber;
                    vehicle.transportBusinessNo = this.state.vehicle?.businessLicenceNo;
                    vehicle.transportPermissionLicenseUrl = this.state.vehicle?.transportPermissionLicenseUrl;
                    vehicle.vehicleBodyLicenseUrl = this.state.vehicle?.vehicleBodyLicenseUrl;
                    vehicle.vehicleBodyLicensePlateNumber = this.state.vehicle?.vehicleBodyLicensePlateNumber;
                    vehicle.trailerVehicleLicenseUrl = this.state.vehicle?.trailerVehicleLicenseUrl;
                    vehicle.trailerVehicleLicenseEffectDate = this.state.vehicle?.trailerVehicleLicenseEffectDate;
                    vehicle.qualificatePicUrl = this.state.vehicle?.qualificatePicUrl;
                    vehicle.qualificationLicName = this.state.vehicle?.qualificationLicName;
                    vehicle.qualificationLicEffectDate = this.state.vehicle?.qualificationLicName;
                    vehicle.personCarUrl1 = this.state.vehicle?.personCarUrl1;
                    vehicle.personCarUrl2 = this.state.vehicle?.personCarUrl2;
                }
            } else {
                //1、普通货车
                vehicle.vehicleFlag = '1';
                vehicle.backup1 = '';

                if (TextUtils.equals('1', this.state.vehicle?.backup2)) {
                    //总质量是否>4.5吨
                    vehicle.backup2 = '1';
                    vehicle.vehicleLicenseUrl = this.state.vehicle?.vehicleLicenseUrl;
                    vehicle.annualVehicleLicenseUrl = this.state.vehicle?.annualVehicleLicenseUrl;
                    vehicle.vehicleLicenseEffectDate = this.state.vehicle?.vehicleLicenseEffectDate;
                    vehicle.transportLicenseUrl = this.state.vehicle?.transportLicenseUrl;
                    vehicle.transportLicensePlateNumber = this.state.vehicle?.transportLicensePlateNumber;
                    vehicle.transportBusinessNo = this.state.vehicle?.businessLicenceNo;
                    vehicle.transportPermissionLicenseUrl = this.state.vehicle?.transportPermissionLicenseUrl;
                    vehicle.qualificatePicUrl = this.state.vehicle?.qualificatePicUrl;
                    vehicle.qualificationLicName = this.state.vehicle?.qualificationLicName;
                    vehicle.qualificationLicEffectDate = this.state.vehicle?.qualificationLicName;
                    vehicle.personCarUrl1 = this.state.vehicle?.personCarUrl1;
                    vehicle.personCarUrl2 = this.state.vehicle?.personCarUrl2;
                } else {
                    vehicle.backup2 = '0';
                    vehicle.vehicleLicenseUrl = this.state.vehicle?.vehicleLicenseUrl;
                    vehicle.annualVehicleLicenseUrl = this.state.vehicle?.annualVehicleLicenseUrl;
                    vehicle.vehicleLicenseEffectDate = this.state.vehicle?.vehicleLicenseEffectDate;
                    vehicle.personCarUrl1 = this.state.vehicle?.personCarUrl1;
                    vehicle.personCarUrl2 = this.state.vehicle?.personCarUrl2;
                }
            }

            let req = new ReqUserUpgradeSaveLicense();
            req.type = '3';
            req.vehicle = vehicle;
            req.request().then((resp) => {
                this._dismissWait();
                if (resp.isSuccess()) {
                    this._showToast(resp.getMsg());
                    this._setResultGoBack();
                } else {
                    this._showMsgDialog(resp.getMsg());
                }
            });
        } else {
            this._showMsgDialog('请先完善车辆认证信息');
        }
    };
}
