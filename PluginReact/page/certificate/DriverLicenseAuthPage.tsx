import {<PERSON><PERSON><PERSON><PERSON>, KeyboardAvoidingView, ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import UIButton from '../widget/UIButton';
import UIFormInputView, {UIFormInputViewRef} from './views/UIFormInputView';
import UIImageSigleFileView, {UIImageSingleFileViewRef} from '../widget/UIImageSingleFileView';
import LinearGradient from 'react-native-linear-gradient';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {ReqQueryDictConfig} from './requests/ReqQueryDictConfig';
import {DictConfigBean} from '../pick/requests/ReqDictConfig';
import {ArrayUtils} from '../util/ArrayUtils';
import Picker from 'react-native-picker';
import {Method} from '../util/NativeModulesTools';
import ELogin from '../user/personalsafety/models/ELogin';
import {plainToInstance} from 'class-transformer';
import {LincenseTipInfo} from './models/ResLicenseInputAccuracyCheck';
import {ReqDriverLicenseOcrCheck} from './requests/ReqDriverLicenseOcrCheck';
import {DriverLicenseOcrResult} from './models/ResDriverLicenseOcrCheck';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ReqUpdateMemberLicense} from './requests/ReqUpdateMemberLicense';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import OcrFailTipDialog from './views/OcrFailTipDialog';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import {ReqUserUpgradeSaveLicense} from './requests/ReqUserUpgradeSaveLicense';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import UIDatePicker from '../widget/UIDatePicker';
import {dateFormat} from '../util/DateUtil';
import { onEvent } from '../base/native/UITrackingAction';

interface State extends BaseState {
    showTip: boolean;
    //是否是电子版
    electronic: boolean;
    //显示表单
    showForm: boolean;
    //显示识别结果
    ocrResult?: DriverLicenseOcrResult;
    // 显示OCR识别失败弹窗
    showOcrFailDialog?: boolean;
    // 驳回理由
    rejectReason?: string;
    // 准驾车型配置
    configs?: DictConfigBean[];
    //证件报错提示
    licenseTipInfo?: LincenseTipInfo;
    //纸质版驾驶证数据
    driverLicUrl?: string;
    quasiVehicleType?: string;
    driverLicNo?: string;
    expiryDate?: string;
    //电子版驾驶证数据
    electronicUrl?: string;
    electronicQuasiVehicleType?: string;
    electronicDriverLicNo?: string;
    electronicExpiryDate?: string;
    //OCR失败次数
    ocrFailNum: number;
    showTimerDialog?: boolean;
}

/**
 * 注释: 驾驶证认证页面
 * 时间: 2025/2/20 星期四 16:15
 * <AUTHOR>
 */
export default class DriverLicenseAuthPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 0:上传 1:更新 2:过期
    mode: number;
    // 1认证页面
    updateType: number;
    //登录信息
    eLogin?: ELogin;
    // OCR最大识别次数
    maxOcrFailNum: number;
    //准驾车型Ref
    quasiVehicleTypeRef = React.createRef<UIFormInputViewRef>();
    //驾驶证证号Ref
    driverLicNoRef = React.createRef<UIFormInputViewRef>();
    //有效期止Ref
    driverLicEffectDateRef = React.createRef<UIFormInputViewRef>();
    //纸质版上传控件Ref
    imagePaperUploadRef = React.createRef<UIImageSingleFileViewRef>();
    //电子版上传控件Ref
    imageElectronicUploadRef = React.createRef<UIImageSingleFileViewRef>();

    //回调
    callBack: Function = this.getCallBack();

    constructor(props) {
        super(props);
        this.state = {showTip: true, electronic: false, showOcrFailDialog: false, ocrFailNum: 0, showForm: false};
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        this.updateType = this.pageParams?.updateType ?? 0;
    }

    handleBackPress = () => {
        //阻断系统返回
        Picker.hide();
        RouterUtils.skipPop();
        return true;
    };

    componentDidMount() {
        super.componentDidMount();
        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
        let login = Method.getLogin();
        this.eLogin = plainToInstance(ELogin, login);
        // 查询准驾车型
        this.queryDriverDict();
        this.queryDriverLicenseNumber();
        if (this.mode == 0 && this.updateType != 2) {
            this.queryUserLicense();
        } else {
            this.queryUserExpireLicenseDetail();
        }
        onEvent({pageId: 'jszscy', tableId:  'jszscy',event: 'view'});
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackPress);
    }

    /**
     * 注释: 查询证件详情
     * 时间: 2025/3/5 星期三 9:26
     * <AUTHOR>
     */
    queryUserLicense() {
        let request = new ReqQueryUserLicense();
        request.type = '2';
        request.request().then((res) => {
            if (res.isSuccess() && res.data?.driverLicense != undefined) {
                if (TextUtils.isNoEmpty(res.data.driverLicense?.driverLicNo) && TextUtils.isNoEmpty(res.data.driverLicense?.quasiVehicleType) && TextUtils.isNoEmpty(res.data.driverLicense?.driverLicEffectDate)) {
                    if (res.data.driverLicense?.isElectronic == '1') {
                        this.setState({
                            electronicUrl: res.data.driverLicense?.driverLicUrl,
                            electronic: true,
                            showForm: true,
                            electronicDriverLicNo: res.data.driverLicense?.driverLicNo,
                            electronicQuasiVehicleType: res.data.driverLicense?.quasiVehicleType,
                            electronicExpiryDate: res.data.driverLicense?.driverLicEffectDate,
                        });
                    } else {
                        this.setState({
                            electronic: false,
                            showForm: true,
                            driverLicUrl: res.data.driverLicense?.driverLicUrl,
                            driverLicNo: res.data.driverLicense?.driverLicNo,
                            quasiVehicleType: res.data.driverLicense?.quasiVehicleType,
                            expiryDate: res.data.driverLicense?.driverLicEffectDate,
                        });
                    }
                }
            }
        });
    }

    /**
     * 注释: 查询驾驶证过期详情
     * 时间: 2025/2/24 星期一 13:44
     * <AUTHOR>
     */
    queryUserExpireLicenseDetail() {
        let request = new ReqQueryUserExpireLicenseDetail();
        request.licenseType = '200004';
        request.request().then((res) => {
            if (res.isSuccess() && TextUtils.isNoEmpty(res.data?.licenseReason)) {
                this.setState({rejectReason: res.data?.licenseReason});
            }
        });
    }

    /**
     * 注释: 证件校验
     * 时间: 2025/2/24 星期一 11:08
     * <AUTHOR>
     */
    licenseCheck() {
        onEvent({pageId: 'jszscy', tableId:  'jszscy_bcan'});
        this._showWaitDialog();
        if (this.state.electronic) {
            if (TextUtils.isEmpty(this.state.electronicUrl)) {
                this._showToast('请上传电子驾驶证');
                return;
            }
        } else {
            if (TextUtils.isEmpty(this.state.driverLicUrl)) {
                this._showToast('请上传纸质驾驶证');
                return;
            }
        }
        let quasiVehicleType = this.quasiVehicleTypeRef.current?.getValue() ?? '';
        let driverLicNo = this.driverLicNoRef.current?.getValue() ?? '';
        let driverLicEffectDate = this.driverLicEffectDateRef?.current?.getValue() ?? '';
        if (TextUtils.isEmpty(quasiVehicleType)) {
            this._showToast('请选择准驾类型');
            return;
        }
        if (TextUtils.isEmpty(driverLicNo)) {
            this._showToast('请输入驾驶证证号');
            return;
        }
        if (TextUtils.isEmpty(driverLicEffectDate)) {
            this._showToast('请输入有效期');
            return;
        }
        let request = new ReqLicenseInputAccuracyCheck();
        request.isElectronic = this.state.electronic ? '1' : '0';
        request.type = '2';
        request.isNeedSave = TextUtils.equals('0', this.mode) ? '1' : '0';
        request.driverLicUrl = this.state.electronic ? this.state.electronicUrl : this.state.driverLicUrl;
        request.quasiVehicleType = quasiVehicleType;
        request.driverLicNo = driverLicNo;
        request.driverLicEffectDate = driverLicEffectDate;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.mode == 0) {
                    //上传
                    this.upgradeSaveLicense();
                } else if (this.mode == 1) {
                    //更新
                    this.updateDriverLicense();
                } else if (this.mode == 2) {
                    //过期
                    this.uploadExpireDateLicense();
                }
            } else {
                this._dismissWait();
                this.setState({licenseTipInfo: res.data?.data});
            }
        });
    }

    /**
     * 注释: 开始上传驾驶证
     * 时间: 2025/2/24 星期一 11:14
     * <AUTHOR>
     */
    upgradeSaveLicense() {
        let request = new ReqUserUpgradeSaveLicense();
        request.type = '2';
        request.driverLicense = {
            driverLicUrl: this.state.electronic ? this.state.electronicUrl : this.state.driverLicUrl,
            quasiVehicleType: this.quasiVehicleTypeRef?.current?.getValue(),
            isElectronic: this.state.electronic ? '1' : '0',
            driverLicNo: this.driverLicNoRef.current?.getValue() ?? '',
            driverLicEffectDate: this.driverLicEffectDateRef.current?.getValue() ?? '',
        };
        request.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this._showToast('上传驾驶证成功');
                this.callBack();
                RouterUtils.skipPop();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    /**
     * 注释: 驾驶证过期上传
     * 时间: 2025/2/24 星期一 11:34
     * <AUTHOR>
     */
    uploadExpireDateLicense() {
        let request = new ReqUploadExpireDateLicense();
        request.picUrlList = [
            {
                type: '200004',
                picUrl: this.state.electronic ? this.state.electronicUrl : this.state.driverLicUrl,
                newLicenseExpire: this.driverLicEffectDateRef?.current?.getValue(),
                quasiVehicleType: this.quasiVehicleTypeRef?.current?.getValue(),
                isElectronic: this.state.electronic ? '1' : '0',
            },
        ];
        request.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this._showToast('更新证件成功');
                RouterUtils.skipPop();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    /**
     * 注释: 更新驾驶证证件
     * 时间: 2025/2/24 星期一 11:26
     * <AUTHOR>
     */
    updateDriverLicense() {
        let request = new ReqUpdateMemberLicense();
        request.picUrlList = [
            {
                type: '200004',
                picUrl: this.state.electronic ? this.state.electronicUrl : this.state.driverLicUrl,
                newLicenseExpire: this.driverLicEffectDateRef?.current?.getValue(),
                quasiVehicleType: this.quasiVehicleTypeRef?.current?.getValue(),
                isElectronic: this.state.electronic ? '1' : '0',
            },
        ];
        request.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this._showToast('更新证件成功');
                RouterUtils.skipPop();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    /**
     * 注释: 驾驶证OCR识别
     * 时间: 2025/2/24 星期一 10:57
     * <AUTHOR>
     */
    driverLicenseOcrCheck(driverLicUrl: string) {
        this._showWaitDialog();
        let request = new ReqDriverLicenseOcrCheck();
        request.driverLicUrl = driverLicUrl;
        request.isElectronic = this.state.electronic ? '1' : '0';
        request.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                if (this.state.electronic) {
                    this.setState({
                        ocrResult: res.data?.data,
                        electronicUrl: driverLicUrl,
                        showForm: true,
                        electronicDriverLicNo: res.data?.data?.driverLiceNo,
                        electronicQuasiVehicleType: res.data?.data?.quasiVehicleType,
                        electronicExpiryDate: res.data?.data?.expiryDate,
                    });
                } else {
                    this.setState({
                        ocrResult: res.data?.data,
                        driverLicUrl: driverLicUrl,
                        showForm: true,
                        driverLicNo: res.data?.data?.driverLiceNo,
                        quasiVehicleType: res.data?.data?.quasiVehicleType,
                        expiryDate: res.data?.data?.expiryDate,
                    });
                }
            } else {
                if (this.state.electronic) {
                    this.setState({electronicUrl: driverLicUrl, showForm: true});
                } else {
                    if (this.state.ocrFailNum >= this.maxOcrFailNum) {
                        this.setState({driverLicUrl: driverLicUrl});
                    }
                    this.setState({showOcrFailDialog: true, ocrFailNum: this.state.ocrFailNum + 1});
                }
            }
        });
    }

    /**
     * 注释: 查询准驾车型配置
     * 时间: 2025/2/21 星期五 14:44
     * <AUTHOR>
     */
    queryDriverDict() {
        let request = new ReqQueryDictConfig();
        request.dictCode = 'DRIVER_TYPE';
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({configs: res.data?.records ?? []});
            }
        });
    }

    /**
     * 注释: 查询OCR最大识别次数
     * 时间: 2025/2/27 星期四 8:56
     * <AUTHOR>
     */
    queryDriverLicenseNumber() {
        let request = new ReqQueryDictConfig();
        request.dictCode = 'LICENSE_UPLOAD_VALIDATE_SETTINGS';
        request.request().then((res) => {
            if (res.isSuccess()) {
                let config = res.data?.records?.find((e) => {
                    return e.dictKey == 'DRIVER_LICENSE';
                });
                if (config) {
                    this.maxOcrFailNum = parseFloat(config.value ?? '3');
                }
            }
        });
    }

    render() {
        let title = '司机认证';
        if (this.mode == 1) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView
                    title={title}
                    clickBack={() => {
                        Picker.hide();
                        RouterUtils.skipPop();
                        onEvent({pageId: 'jszscy', tableId:  'jszscy_fh'});
                    }}
                />
                <KeyboardAvoidingView behavior={'padding'} style={{flex: 1}}>
                    <ScrollView>
                        {/*提示文案*/}
                        {this.renderTipView()}
                        {/*绘制证件上传*/}
                        {this.renderCertificatesUpload()}
                        {/*绘制表单视图*/}
                        {this.state.showForm && this.renderFormView()}
                    </ScrollView>
                </KeyboardAvoidingView>
                <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingBottom: 25, paddingTop: 10}}>
                    <UIButton
                        text={'提交上传'}
                        onPress={() => {
                            this.licenseCheck();
                        }}
                    />
                </View>
                {/*OCR识别弹窗*/}
                {this.state.showOcrFailDialog && this.renderOcrResultFailDialog()}
                {/*有效期弹窗*/}
                {this.state.showTimerDialog && this.renderTimerView()}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释:  绘制OCR识别报错弹窗
     * 时间: 2025/2/24 星期一 14:33
     * <AUTHOR>
     *
     * @returns {JSX.Element}
     */
    renderOcrResultFailDialog() {
        return (
            <OcrFailTipDialog
                image={'icon_com_driver_licenseocrtip_dialog_bg'}
                mainTip={'驾驶证模糊或不完整，请重新上传'}
                showContinue={this.state.ocrFailNum > this.maxOcrFailNum}
                onResetUpload={() => {
                    if (this.state.electronic) {
                        this.imageElectronicUploadRef.current?.clear();
                        this.setState({showOcrFailDialog: false, electronicUrl: undefined});
                        // 延迟触发上传，确保状态更新完成
                        setTimeout(() => {
                            this.imageElectronicUploadRef.current?.triggerUpload();
                        }, 100);
                    } else {
                        this.imagePaperUploadRef.current?.clear();
                        this.setState({showOcrFailDialog: false, driverLicUrl: undefined});
                        // 延迟触发上传，确保状态更新完成
                        setTimeout(() => {
                            this.imagePaperUploadRef.current?.triggerUpload();
                        }, 100);
                    }
                }}
                onContinueUpload={() => {
                    this.setState({showOcrFailDialog: false, showForm: true});
                }}
            />
        );
    }

    /**
     * 注释: 绘制提示视图
     * 时间: 2025/2/20 星期四 18:55
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderTipView() {
        if (this.state.showTip) {
            return (
                <View style={styles.tipStyle}>
                    <Text style={{fontSize: 13, color: '#3D3D3D', flex: 1}}>驾驶证姓名需与身份认证为同一人</Text>
                    <UITouchableOpacity
                        onPress={() => {
                            this.setState({showTip: false});
                        }}>
                        <UIImage source={'ic_close'} style={{width: 15, height: 15}} />
                    </UITouchableOpacity>
                </View>
            );
        }
    }

    /**
     * 注释: 绘制证件上传
     * 时间: 2025/2/20 星期四 18:54
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderCertificatesUpload() {
        return (
            <LinearGradient style={{padding: 15}} colors={TextUtils.isNoEmpty(this.state.rejectReason) ? ['#FFF3EF', '#FFFFFF'] : ['#FFFFFF', '#FFFFFF']} useAngle={true} angle={180}>
                {/*切换*/}
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    {/*纸质版*/}
                    <UITouchableOpacity
                        style={[this.state.electronic ? styles.unSelectStyle : styles.selectStyle, {marginRight: 10}]}
                        onPress={() => {
                            Picker.hide();
                            this.setState({
                                electronic: false,
                                showForm: TextUtils.isNoEmpty(this.state.driverLicUrl),
                                licenseTipInfo: undefined,
                            });
                        }}>
                        <Text
                            style={{
                                fontSize: 16,
                                color: this.state.electronic ? '#666' : '#5086FC',
                                fontWeight: 'bold',
                            }}>
                            纸质版
                        </Text>
                    </UITouchableOpacity>
                    {/*电子版*/}
                    <UITouchableOpacity
                        style={this.state.electronic ? styles.selectStyle : styles.unSelectStyle}
                        onPress={() => {
                            Picker.hide();
                            this.setState({
                                electronic: true,
                                showForm: TextUtils.isNoEmpty(this.state.electronicUrl),
                                licenseTipInfo: undefined,
                            });
                        }}>
                        <Text
                            style={{
                                fontSize: 16,
                                color: this.state.electronic ? '#5086FC' : '#666',
                                fontWeight: 'bold',
                            }}>
                            电子版
                        </Text>
                    </UITouchableOpacity>
                </View>
                {/*纸质版上传*/}
                {!this.state.electronic && this.renderPaperVersion()}
                {/*电子版上传*/}
                {this.state.electronic && this.renderElectronicVersion()}
                {/*驳回理由*/}
                {TextUtils.isNoEmpty(this.state.rejectReason) && (
                    <Text
                        style={{
                            color: '#FD5353',
                            fontSize: 13,
                            marginTop: 15,
                        }}>{`驳回理由:${this.state.rejectReason}`}</Text>
                )}
            </LinearGradient>
        );
    }

    /**
     * 注释: 绘制电子版上传
     * 时间: 2025/2/21 星期五 10:19
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderElectronicVersion() {
        return (
            <View style={{marginTop: 10}}>
                <UIImageSigleFileView
                    ref={this.imageElectronicUploadRef}
                    url={this.state.electronicUrl ?? ''}
                    defaultValue={'certification_driver_license_template_diagram_25'}
                    imgStyle={{width: 150, height: 225}}
                    onDelPic={() => {
                        this.setState({
                            ocrResult: undefined,
                            electronicUrl: undefined,
                            showForm: false,
                            electronicQuasiVehicleType: undefined,
                            electronicDriverLicNo: undefined,
                            electronicExpiryDate: undefined,
                        });
                    }}
                    onPostSuccess={(url) => {
                        this.driverLicenseOcrCheck(url);
                        onEvent({pageId: 'jszscy', tableId:  'jszscy_djscan'});
                    }}
                />
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 15, alignSelf: 'center'}}>
                    <UIImage source={'user_auto_fail'} style={{width: 12, height: 12, marginRight: 5}} />
                    <Text style={{fontSize: 12, color: '#666'}}>您可以在交管12123APP上查询驾驶证电子版正页</Text>
                </View>
                <UITouchableOpacity
                    style={{alignSelf: 'center'}}
                    onPress={() => {
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'QueryElectronicDriverPage'});
                    }}>
                    <Text style={{color: '#5086FC', fontSize: 12, marginTop: 6}}>如何查询电子版？</Text>
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释: 绘制纸质版
     * 时间: 2025/2/21 星期五 10:18
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderPaperVersion() {
        return (
            <ComCertificationUploadImgView
                ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                style={{marginTop: 15}}
                tvTitle1View={
                    <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                        驾驶证<Text style={{fontSize: 16, color: '#3D3D3D'}}> (主页+副页) </Text>
                        <Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                    </Text>
                }
                tvDescView={
                    <Text style={{fontSize: 14, color: '#666', marginTop: 6}}>
                        请上传<Text style={{fontSize: 14, color: '#66B8FC'}}>{` ${this.eLogin?.memberName ?? ''} `}</Text>本人的驾驶证
                    </Text>
                }
                leftImg={{
                    url: this.state.driverLicUrl ?? '',
                    defaultValue: 'certification_driver_license_template_diagram',
                    onDelPic: () => {
                        this.setState({
                            ocrResult: undefined,
                            driverLicUrl: undefined,
                            showForm: false,
                            quasiVehicleType: undefined,
                            driverLicNo: undefined,
                            expiryDate: undefined,
                        });
                    },
                    onPostSuccess: (url) => {
                        this.driverLicenseOcrCheck(url);
                        onEvent({pageId: 'jszscy', tableId:  'jszscy_djscan'});
                    },
                    ref: this.imagePaperUploadRef,
                }}
            />
        );
    }

    /**
     * 注释: 显示Picker
     * 时间: 2025/2/21 星期五 15:47
     * <AUTHOR>
     */
    showPicker() {
        if (ArrayUtils.isNoEmpty(this.state.configs)) {
            let listData = this.state.configs?.map((item) => item.value);
            Picker.init({
                pickerData: listData,
                pickerTitleText: '请选择证件类型',
                pickerConfirmBtnText: '确定',
                pickerCancelBtnText: '取消',
                pickerToolBarBg: [255, 255, 255, 100],
                pickerBg: [255, 255, 255, 100],
                onPickerConfirm: (item: string[]) => {
                    Picker.hide();
                    this.quasiVehicleTypeRef.current?.setValue(item[0]);
                },
            });
            if (!Picker.isPickerShow()) {
                Picker.show();
            }
        }
    }

    /**
     * 注释: 绘制表单视图
     * 时间: 2025/2/20 星期四 19:45
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderFormView() {
        return (
            <View style={{backgroundColor: '#fff', marginTop: 8}}>
                <UIFormInputView
                    label={'准驾车型'}
                    showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.quasiVehicleTypeText)}
                    error={this.state.licenseTipInfo?.quasiVehicleTypeText}
                    isRequired={true}
                    edit={false}
                    value={this.state.electronic ? this.state.electronicQuasiVehicleType : this.state.quasiVehicleType}
                    ref={this.quasiVehicleTypeRef}
                    onChangeText={() => {
                        this.setState({licenseTipInfo: undefined});
                    }}
                    isSelect={true}
                    onSelect={() => {
                        this.showPicker();
                    }}
                />
                <UIFormInputView
                    label={'驾驶证证号'}
                    showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.driverLicNoText)}
                    error={this.state.licenseTipInfo?.driverLicNoText}
                    isRequired={true}
                    value={this.state.electronic ? this.state.electronicDriverLicNo : this.state.driverLicNo}
                    ref={this.driverLicNoRef}
                    onChangeText={() => {
                        this.setState({licenseTipInfo: undefined});
                    }}
                />
                <UIFormInputView
                    label={'有效期止'}
                    edit={false}
                    showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.driverLicEffectDateText)}
                    error={this.state.licenseTipInfo?.driverLicEffectDateText}
                    isRequired={true}
                    value={this.state.electronic ? this.state.electronicExpiryDate : this.state.expiryDate}
                    ref={this.driverLicEffectDateRef}
                    onChangeText={() => {
                        this.setState({licenseTipInfo: undefined});
                    }}
                    isSelect={true}
                    onSelect={() => this.setState({showTimerDialog: true})}
                />
            </View>
        );
    }

    /**
     * 注释: 绘制有效期选择
     * 时间: 2025/3/9 星期日 13:48
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderTimerView() {
        return (
            <UIDatePicker
                title={'请选择有效期截止日期'}
                mode={'date'}
                minDate={new Date(1900, 0, 1, 0, 0, 0)}
                maxDate={new Date(2099, 0, 1, 0, 0, 0)}
                onHideEvent={() => {
                    this.setState({showTimerDialog: false});
                }}
                onSelectEvent={(date) => {
                    if (this.state.electronic) {
                        this.setState({electronicExpiryDate: dateFormat(date, 'yyyy-MM-dd')});
                    } else {
                        this.setState({expiryDate: dateFormat(date, 'yyyy-MM-dd')});
                    }
                }}
            />
        );
    }
}

const styles = StyleSheet.create({
    tipStyle: {
        backgroundColor: '#FDF6D9',
        height: 40,
        paddingHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
    },
    selectStyle: {
        backgroundColor: '#E5EEFF',
        height: 36,
        borderRadius: 2,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    unSelectStyle: {
        backgroundColor: '#F6F6F6',
        height: 36,
        borderRadius: 2,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
});
