import {StyleSheet, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {Method} from '../util/NativeModulesTools';
import UIImage from '../widget/UIImage';
import {gScreen_width} from '../util/scaled-style';

interface State extends BaseState {}

/**
 * 注释:
 * 时间: 2025/2/21 星期五 13:42
 * <AUTHOR>
 */
export default class QueryElectronicDriverPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#CDE5FD'}}>
                <UITitleView
                    title={'如何查询电子版?'}
                    rightText={'客服'}
                    clickRight={() => {
                        Method.openLineServer();
                    }}
                />
                <UIImage source={'img_query_electronic_driver'} style={{width: gScreen_width, height: (gScreen_width / 375) * 625}} />
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
