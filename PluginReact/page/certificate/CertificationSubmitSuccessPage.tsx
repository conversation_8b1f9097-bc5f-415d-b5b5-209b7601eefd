import {<PERSON><PERSON><PERSON><PERSON>, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState, DialogBuilder} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {Method} from '../util/NativeModulesTools';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {http} from '../const.global';
import {ReqQueryMemberSilentSignState} from './requests/ReqQueryMemberSilentSignState';
import ELogin from '../user/personalsafety/models/ELogin';
import TextUtils from '../util/TextUtils';
import {RouterUrl} from '../base/RouterUrl';
import {RouterUtils} from '../util/RouterUtils';
import {UserType} from '../user/models/UserType';
import { onEvent } from '../base/native/UITrackingAction';

interface State extends BaseState {
    silentSign: boolean;
}

/**
 * 注释: 认证信息提交成功
 * 时间: 2024/12/5 14:31
 * <AUTHOR>
 */
export default class CertificationSubmitSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    login: ELogin = Method.getLogin();
    update: boolean;
    riskSubmit: string;

    constructor(props) {
        super(props);
        this.state = {
            silentSign: false,
        };
        this.update = this.getParams().update ?? false;
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryMemberSilentSignState();
        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
        onEvent({pageId: 'rztjym', tableId:  'rztjym',event: 'view'});
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackPress);
    }

    handleBackPress = () => {
        //阻断系统返回
        Method.openHomeTab(5);
        return false;
    };

    /**
     * 查询电签状态
     */
    queryMemberSilentSignState() {
        let req = new ReqQueryMemberSilentSignState();
        req.userId = this.login.userId;
        req.request().then((response) => {
            if (response.isSuccess()) {
                this.riskSubmit = response.data?.riskSubmit ?? '';
                this.setState({silentSign: TextUtils.equals('0', response.data?.faceAuthState)});
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    /**
     * 继续电签认证
     */
    goToSing() {
        let dialog = new DialogBuilder();
        dialog.title = '温馨提示';
        dialog.msg = '为保障您的合法权益及在线协议的法律效力，后续运输合同需基于电子签名方式签署。需要您先完成实名认证，同时为免去后续每次签署运输合同需操作意愿认证，本次实名会同步为您完成《静默签授权书》的签署。';
        dialog.okTxt = '同意并继续';
        dialog.onOkEvent = () => {
            RouterUtils.skipRouter(RouterUrl.SilentProtocolWeActivity, {
                fromType: '3',
                faceReason: '54',
                fromFrag: '5',
                userUpdate: '1',
                riskSubmit: this.riskSubmit,
                idCardName: '',
                idCardNo: '',
                carrierOrBoss: UserType.isCarrier() ? '2' : '10',
            });
        };
        this._showDialog(dialog);
    }

    render() {
        return (
            <View style={{flex: 1, alignItems: 'center'}}>
                <UITitleView title={'身份信息认证'} showBack={false} />
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingHorizontal: 14,
                        paddingVertical: 8,
                        backgroundColor: '#FEF6D9',
                    }}>
                    <UIImage source={'certification_carowner_tip'} style={{width: 18, height: 18}} />
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#666',
                        }}>
                        温馨提示：为确保您能够顺利摘单，请在认证通过后绑定本人借记银行卡
                    </Text>
                </View>
                <UIImage source={'carowner_certification_submit_success'} style={{width: 180, height: 178, marginTop: 40}} />
                <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold', marginTop: 21}}>您的认证信息提交成功</Text>
                <Text style={{fontSize: 14, color: '#ff602e', marginTop: 6}}>请耐心等待后台人员审核…</Text>
                {this.update && (
                    <View
                        style={{
                            marginTop: 10,
                            marginHorizontal: 15,
                            backgroundColor: '#f8f8f8',
                            borderRadius: 5,
                            height: 68,
                            justifyContent: 'center',
                            paddingHorizontal: 5,
                        }}>
                        <Text style={{fontSize: 14, color: '#999'}}>
                            如遇5分钟后审核仍未完成,请在菜单栏<Text style={{fontWeight: 'bold'}}>【我的】-点击【催审核】</Text>
                        </Text>
                    </View>
                )}
                {this.state.silentSign && UserType.isBoss() && (
                    <View style={{marginTop: 21, alignItems: 'center'}}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#333',
                                fontWeight: 'bold',
                            }}>
                            您尚未完成电签认证，请您继续完成电签
                        </Text>
                        <Text style={{fontSize: 14, color: '#333', fontWeight: 'bold'}}>认证以免影响业务</Text>
                        <UITouchableOpacity
                            style={{marginTop: 15}}
                            onPress={() => {
                                this.goToSing();
                            }}>
                            <Text style={{fontSize: 17, color: '#5086fc', fontWeight: 'bold'}}>继续电签认证</Text>
                        </UITouchableOpacity>
                    </View>
                )}
                <UIButton
                    text={'我知道了'}
                    style={{marginTop: 35}}
                    width={160}
                    height={45}
                    borderRadius={25}
                    fontSize={17}
                    onPress={() => {
                        // 跳转到首页
                        Method.openHomeTab(1);
                        onEvent({pageId: 'rztjym', tableId:  'rztjym_wzdl'});
                    }}
                />
                <View style={{flex: 1}} />
                <View style={{flexDirection: 'row', alignItems: 'center', alignSelf: 'center', marginBottom: 25}}>
                    <UITouchableOpacity
                        activeOpacity={1}
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            Method.openWeb('认证帮助', `${http.url()}/form_h5/order/index.html?_t=${new Date().getTime()}#/idAuthentication?type=2`);
                        }}>
                        <UIImage source={'driver_certificaiton_regiest_wenhao'} style={{width: 12, height: 12, marginRight: 7}} />
                        <Text style={{fontSize: 13, color: '#FF602E'}}>认证帮助</Text>
                    </UITouchableOpacity>
                </View>
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
