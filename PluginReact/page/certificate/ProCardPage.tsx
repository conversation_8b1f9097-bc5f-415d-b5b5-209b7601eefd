import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import TextUtils from '../util/TextUtils';
import UIButton from '../widget/UIButton';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {RouterUtils} from '../util/RouterUtils';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {ReqCheckExpireChangeVehicleTrailer} from './requests/ReqCheckExpireChangeVehicleTrailer';
import CertificateReactExtension from './CertificateReactExtension';
import {Method} from '../util/NativeModulesTools';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';

interface State extends BaseState {
    rejectReason?: string;
    overloadPlateFrontUrl?: string; //超限临牌正面照
    overloadPlateBackUrl?: string; //超限临牌反面照
}

/**
 * 注释: 临时车牌 上传 更新 过期
 * 时间: 2025/2/24 20:10
 * <AUTHOR>
 */
export default class ProCardPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 0:上传 1:更新 2:过期
    mode: number;
    //是否添加车辆 true 添加车辆 false 认证车辆
    isAdd: boolean;
    //成功回调
    callBack: Function = this.getCallBack();
    //车辆id
    vehicleId: string;
    //车牌号
    plateNumber?: string;
    licenseType?: string;
    //1-普通车 2-半挂车
    vehicleFlag?: number;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        this.isAdd = this.pageParams?.isAdd ?? false;
        this.vehicleId = this.pageParams?.vehicleId ?? '';
        this.vehicleFlag = this.pageParams?.vehicleFlag ?? 2;
        this.licenseType = this.pageParams?.licenseType ?? '';
    }

    componentDidMount() {
        super.componentDidMount();
        if (this.isAdd) {
            this.queryVehicleDetail();
        } else {
            this.queryLicenseDetail();
        }
    }

    /**
     * 注释：提交上传
     * 时间：2025/2/25 13:53
     * @author：宋双朋
     */
    licenseInputAccuracyCheck = () => {
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '10';
        req.isNeedSave = this.mode == 0 ? '1' : '0';
        req.vehicleId = this.vehicleId;
        req.backup1 = '1';
        //临时牌照正面
        let overloadPlateFrontUrl = this.state.overloadPlateFrontUrl;
        if (TextUtils.isEmpty(overloadPlateFrontUrl)) {
            this._showMsgDialog('临时牌照正面不能为空');
            return;
        }
        req.overloadPlateFrontUrl = overloadPlateFrontUrl;
        //临时牌照反面
        let overloadPlateBackUrl = this.state.overloadPlateBackUrl;
        if (TextUtils.isEmpty(overloadPlateBackUrl)) {
            this._showMsgDialog('临时牌照反面不能为空');
            return;
        }
        req.overloadPlateBackUrl = overloadPlateBackUrl;
        req.request().then((res) => {
            if (res.isSuccess()) {
                if (this.mode == 2) {
                    let req = new ReqUploadExpireDateLicense();
                    req.picUrlList = [
                        {
                            picUrl: overloadPlateFrontUrl + ',' + overloadPlateBackUrl,
                            type: this.licenseType,
                        },
                    ];
                    req.targetId = this.vehicleId;
                    this._showWaitDialog();
                    req.request().then((res) => {
                        this._dismissWait();
                        if (res.isSuccess()) {
                            RouterUtils.skipPop();
                            EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                        } else {
                            this._showMsgDialog(res.getMsg());
                        }
                    });
                } else {
                    this._showToast(res.getMsg());
                    this.callBack && this.callBack();
                    EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                    RouterUtils.skipPop();
                }
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    /**
     * 注释：获取认证详情
     * 时间：2025/2/28 17:18
     * @author：宋双朋
     */
    queryLicenseDetail() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((response) => {
            if (response.isSuccess() && response.data?.vehicle != null) {
                let item = response?.data?.vehicle;
                this.setState({
                    overloadPlateFrontUrl: this.mode !== 2 ? item?.overloadPlateFrontUrl : '',
                    overloadPlateBackUrl: this.mode !== 2 ? item?.overloadPlateBackUrl : '',
                });
            }
        });
    }

    /**
     * 注释：请求车辆详情
     * 时间：2025/2/25 10:23
     * @author：宋双朋
     */
    queryVehicleDetail() {
        //车辆id不为空 请求车辆详情
        let req = new ReqVehicleDetails();
        req.vehicleId = this.vehicleId;
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                this.setState({
                    overloadPlateFrontUrl: item?.overloadPlateFrontUrl,
                    overloadPlateBackUrl: item?.overloadPlateBackUrl,
                });
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    //过期页
    renderExpiredView() {
        return (
            <View style={{flexDirection: 'column'}}>
                <View style={{backgroundColor: 'white', marginTop: 5, alignItems: 'center'}}>
                    <View style={{backgroundColor: '#FFF3EF', padding: 5}}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: 'red',
                                marginRight: 5,
                            }}>
                            证件即将 / 已经过期，为保障您的业务不受影响，请及时更新。
                        </Text>
                    </View>
                </View>
                <View
                    style={{
                        backgroundColor: '#fff',
                        paddingLeft: 12,
                        height: 58,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        marginBottom: 8,
                    }}>
                    <Text
                        style={{
                            fontSize: 16,
                            color: 'black',
                            marginRight: 5,
                        }}>
                        {this.getParams().plateNumber ?? ''}
                    </Text>
                </View>
            </View>
        );
    }

    /**
     * 注释: 点击过期上传页、人车合影补录上传页面内更换挂车申请按钮的前置校验
     * 时间: 2025/4/17 19:11
     * <AUTHOR>
     */
    checkExpireChangeVehicleTrailer = () => {
        let request = new ReqCheckExpireChangeVehicleTrailer();
        request.vehicleId = this.vehicleId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                CertificateReactExtension.gotoTrailerVehicleActivity(res.data?.vehicleId ?? '', res.data?.trailerVehicleId ?? '', res.data?.plateNumber ?? '');
                this._goBack();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    render() {
        let title = '车辆认证';
        if (this.mode == 1) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={title} />
                {this.mode == 2 && this.renderExpiredView()}
                <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingVertical: 15}}>
                    <ComCertificationUploadImgView
                        ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                        style={{
                            backgroundColor: '#FFFFFF',
                        }}
                        tvTitle1View={
                            <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                                超限临牌<Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                            </Text>
                        }
                        layout={1}
                        leftImg={{
                            url: this.state.overloadPlateFrontUrl ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_13',
                            onDelPic: () => {
                                this.setState({overloadPlateFrontUrl: undefined});
                            },
                            onPostSuccess: (url) => {
                                this.setState({overloadPlateFrontUrl: url});
                            },
                        }}
                        rightImg={{
                            url: this.state.overloadPlateBackUrl ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_12',
                            onDelPic: () => {
                                this.setState({overloadPlateBackUrl: undefined});
                            },
                            onPostSuccess: (url) => {
                                this.setState({overloadPlateBackUrl: url});
                            },
                        }}
                    />
                </View>
                {/*更换挂车*/}
                {this.vehicleFlag == 2 && this.mode == 2 && (
                    <UITouchableOpacity style={{alignItems: 'center', marginTop: 20}} onPress={this.checkExpireChangeVehicleTrailer}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#5086FC',
                            }}>{`已更换挂车，点这里去更新挂车证件 >`}</Text>
                    </UITouchableOpacity>
                )}
                <View style={{flex: 1}} />
                <UIButton text={'提交上传'} onPress={this.licenseInputAccuracyCheck} style={{borderRadius: 0}} fontWeight={true} />
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
