import {KeyboardAvoidingView, ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import ComCertificationUploadImgView, {IngProps} from './views/ComCertificationUploadImgView';
import TextUtils from '../util/TextUtils';
import ELogin from '../user/personalsafety/models/ELogin';
import {Method} from '../util/NativeModulesTools';
import {plainToInstance} from 'class-transformer';
import {DriverLicenseOcrResult} from './models/ResDriverLicenseOcrCheck';
import UIFormInputView, {UIFormInputViewRef} from './views/UIFormInputView';
import {LincenseTipInfo} from './models/ResLicenseInputAccuracyCheck';
import {UIImageSingleFileViewRef} from '../widget/UIImageSingleFileView';
import UIButton from '../widget/UIButton';
import {ReqQualificationLicenseOcrCheck} from './requests/ReqQualificationLicenseOcrCheck';
import OcrFailTipDialog from './views/OcrFailTipDialog';
import UIDatePicker from '../widget/UIDatePicker';
import {dateFormat} from '../util/DateUtil';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {RouterUtils} from '../util/RouterUtils';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import {ReqUpdateMemberLicense} from './requests/ReqUpdateMemberLicense';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import {LicenseList} from './models/ResMemberLicenseList';
import {ReqQueryDictConfig} from './requests/ReqQueryDictConfig';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import {RspQueryUserExpireLicenseDetail} from './models/RspQueryUserExpireLicenseDetail';
import {ReqAddQualificateLicense} from './requests/ReqAddQualificateLicense';
import {Qualification, ReqUserUpgradeSaveLicense} from './requests/ReqUserUpgradeSaveLicense';

interface State extends BaseState {
    //从业资格证上传提醒
    showTip: boolean;
    //驳回理由
    rejectReason?: string;
    //从业资格证照片
    qualificatePicUrl?: string;
    //显示识别结果
    ocrResult?: DriverLicenseOcrResult;
    //OCR失败次数
    ocrFailNum: number;
    // OCR最大识别次数
    maxOcrFailNum: number;
    //显示表单
    showForm: boolean;
    //显示OCR识别失败弹窗
    showOcrFailDialog?: boolean;
    //显示时间选择弹窗
    showTimeDialog?: boolean;
    //证件报错提示
    licenseTipInfo?: LincenseTipInfo;
    //从业资格证过期 更新 上传提醒
    showTopTip?: boolean;
    errorMsg?: string;
    showViewBg: boolean;
    canDelete: boolean;
    //登录信息
    eLogin?: ELogin;
}

const LicenseType_4 = '100011'; //道路运输证

/**
 * 注释: 从业资格证 上传 更新 过期
 * 时间: 2025/2/25 19:09
 * <AUTHOR>
 */
export default class QualificationCertificatePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 0:上传 1:更新 2:过期 3:证件缺失
    mode: number;
    //是否添加车辆 true 添加车辆 false 认证车辆
    isAdd: boolean;
    //登录信息
    eLogin?: ELogin;
    //姓名Ref
    qualificationNameRef = React.createRef<UIFormInputViewRef>();
    //有效期止Ref
    qualificationEffectDateRef = React.createRef<UIFormInputViewRef>();
    //上传控件Ref
    imagePaperUploadRef = React.createRef<UIImageSingleFileViewRef>();
    //成功回调
    callBack: Function = this.getCallBack();
    //车辆id
    vehicleId?: string;
    //证件过期
    rspQueryUserExpireLicenseB?: LicenseList = new LicenseList();
    //证件类型
    licenseType?: string;
    leftImg: IngProps;

    constructor(props) {
        super(props);
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        this.isAdd = this.pageParams?.isAdd ?? '';
        this.vehicleId = this.pageParams?.vehicleId ?? '';
        this.licenseType = this.pageParams?.licenseType ?? '';
        this.rspQueryUserExpireLicenseB = this.pageParams?.rspQueryUserExpireLicenseB ?? new LicenseList();
        this.state = {
            showTip: this.mode == 0,
            showTopTip: ['2', '3'].includes(`${this.rspQueryUserExpireLicenseB?.licenseStatus}`) ? TextUtils.equals('103', this.rspQueryUserExpireLicenseB?.licenseCode) : false,
            showForm: false,
            ocrFailNum: 0,
            showViewBg: false,
            canDelete: true,
            maxOcrFailNum: 3,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        let login = Method.getLogin();
        this.eLogin = plainToInstance(ELogin, login);
        this.queryDriverLicenseNumber();
        if (TextUtils.equals('5', this.getParams()?.newType)) {
            this.queryQualificationDetailNew();
        } else {
            switch (this.mode) {
                case 2:
                    this.queryUserExpireLicenseDetail();
                    break;
                case 3:
                    break;
                default:
                    if (this.isAdd) {
                        this.queryVehicleDetail();
                    } else {
                        this.queryLicenseDetail();
                    }
                    break;
            }
        }
    }

    /**
     * 注释: 人车分离新增逻辑
     * 时间: 2025/7/23 9:59
     * <AUTHOR>
     */
    queryQualificationDetailNew() {
        let item = this.getParams()?.qualification;
        let ocrResult = new DriverLicenseOcrResult();
        ocrResult.name = item?.qualificationLicName ?? '';
        ocrResult.expiryDate = item?.qualificationLicEffectDate ?? '';
        if (TextUtils.isNoEmpty(item?.qualificatePicUrl)) {
            this.setState({
                qualificatePicUrl: item?.qualificatePicUrl,
                ocrResult: ocrResult,
                showForm: TextUtils.isNoEmpty(ocrResult.name) || TextUtils.isNoEmpty(ocrResult.expiryDate),
            });
        }
    }

    /**
     * 证件详情
     * @param targetId
     * @param licenseType
     */
    queryUserExpireLicenseDetail() {
        let req = new ReqQueryUserExpireLicenseDetail();
        req.targetId = this.rspQueryUserExpireLicenseB?.targetId ?? '';
        req.licenseType = LicenseType_4;
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                switch (res.data?.licenseState) {
                    case '3': {
                        //审核不通过
                        this.setState({
                            showViewBg: true,
                            errorMsg: res.data?.licenseReason,
                            canDelete: true,
                        });
                        break;
                    }
                    default: {
                        // 0:待完善 || 1:待审核 || else:审核通过
                        if (TextUtils.isEmpty(res.data?.licenseUrl)) {
                            this.leftImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                        } else {
                            if (TextUtils.isEmpty(res.data?.licenseUrl)) {
                                this.setState({
                                    showViewBg: false,
                                    errorMsg: '',
                                    qualificatePicUrl: '',
                                    canDelete: true,
                                });
                            } else {
                                this.setState({
                                    showViewBg: false,
                                    errorMsg: '',
                                    qualificatePicUrl: res.data?.licenseUrl ?? '',
                                    canDelete: !TextUtils.equals('1', res.data?.licenseState),
                                });
                            }
                        }
                        break;
                    }
                }
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 证件图片
     * @returns {string}
     */
    getImg(pageDetail?: RspQueryUserExpireLicenseDetail) {
        return pageDetail && ['2', '4', '6'].includes(pageDetail?.licenseState ?? '') ? '' : pageDetail?.licenseUrl ?? '';
    }

    /**
     * 注释：获取认证详情
     * 时间：2025/2/28 17:18
     * @author：宋双朋
     */
    queryLicenseDetail() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((response) => {
            if (response.isSuccess() && response.data?.vehicle != null) {
                let item = response?.data?.vehicle;
                let ocrResult = new DriverLicenseOcrResult();
                ocrResult.name = item?.qualificationLicName ?? '';
                ocrResult.expiryDate = item?.qualificationLicEffectDate ?? '';
                if (TextUtils.isNoEmpty(item?.qualificatePicUrl)) {
                    this.setState({
                        qualificatePicUrl: item?.qualificatePicUrl,
                        ocrResult: ocrResult,
                        showForm: TextUtils.isNoEmpty(ocrResult.name) || TextUtils.isNoEmpty(ocrResult.expiryDate),
                    });
                }
            }
        });
    }

    /**
     * 注释: 查询OCR最大识别次数
     * 时间: 2025/2/27 星期四 8:56
     * <AUTHOR>
     */
    queryDriverLicenseNumber() {
        let request = new ReqQueryDictConfig();
        request.dictCode = 'LICENSE_UPLOAD_VALIDATE_SETTINGS';
        request.request().then((res) => {
            if (res.isSuccess()) {
                let config = res.data?.records?.find((e) => {
                    return e.dictKey == 'QUALIFICATION';
                });
                if (config) {
                    this.setState({maxOcrFailNum: parseFloat(config.value ?? '3')});
                }
            }
        });
    }

    /**
     * 注释：请求车辆详情
     * 时间：2025/2/25 10:23
     * @author：宋双朋
     */
    queryVehicleDetail() {
        //车辆id不为空 请求车辆详情
        let req = new ReqVehicleDetails();
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                let ocrResult = new DriverLicenseOcrResult();
                ocrResult.name = item?.qualificationLicName ?? '';
                ocrResult.expiryDate = item?.qualificationLicEffectDate ?? '';
                if (TextUtils.isNoEmpty(item?.qualificateUrl)) {
                    this.setState({
                        qualificatePicUrl: item?.qualificateUrl,
                        ocrResult: ocrResult,
                        showForm: TextUtils.isNoEmpty(ocrResult.name) || TextUtils.isNoEmpty(ocrResult.expiryDate),
                    });
                }
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 注释：更新从业资格证证件
     * 时间：2025/3/3 11:03
     * @author：宋双朋
     */
    updateDriverLicense() {
        let request = new ReqUpdateMemberLicense();
        request.picUrlList = [
            {
                type: LicenseType_4,
                picUrl: this.state.qualificatePicUrl,
                targetId: this.rspQueryUserExpireLicenseB?.targetId,
                newLicenseExpire: this.qualificationEffectDateRef?.current?.getValue(),
            },
        ];
        request.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 注释：从业资格证过期上传
     * 时间：2025/3/3 11:03
     * @author：宋双朋
     */
    uploadExpireDateLicense() {
        let request = new ReqUploadExpireDateLicense();
        request.picUrlList = [
            {
                type: LicenseType_4,
                picUrl: this.state.qualificatePicUrl,
                newLicenseExpire: this.qualificationEffectDateRef?.current?.getValue(),
            },
        ];
        request.targetId = this.rspQueryUserExpireLicenseB?.targetId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 注释：从业资格证缺失上传
     * 时间：2025/3/3 11:03
     * @author：宋双朋
     */
    addQualificateLicense() {
        let request = new ReqAddQualificateLicense();
        request.qualificateLicense = this.state.qualificatePicUrl;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 注释：证件校验
     * 时间：2025/2/26 14:40
     * @author：宋双朋
     */
    licenseCheck() {
        if (TextUtils.isEmpty(this.state.qualificatePicUrl)) {
            this._showToast('请上传从业资格证');
            return;
        }
        let qualificationLicName = this.qualificationNameRef.current?.getValue()?.trim() ?? '';
        let qualificationLicEffectDate = this.qualificationEffectDateRef.current?.getValue() ?? '';
        if (TextUtils.isEmpty(qualificationLicName)) {
            this._showToast('请输入姓名');
            return;
        }
        if (TextUtils.isEmpty(qualificationLicEffectDate)) {
            this._showToast('请选择有效期止');
            return;
        }
        this._showWaitDialog();
        if (TextUtils.equals('5', this.getParams()?.newType)) {
            console.log('上传啦啦啦');
            let reqNew = new ReqUserUpgradeSaveLicense();
            reqNew.type = '5';
            reqNew.qualification = new Qualification();
            reqNew.qualification.qualificationLicName = qualificationLicName;
            reqNew.qualification.qualificatePicUrl = this.state.qualificatePicUrl;
            reqNew.qualification.qualificationLicEffectDate = qualificationLicEffectDate;
            reqNew.request().then((res) => {});
        }
        let request = new ReqLicenseInputAccuracyCheck();
        request.vehicleId = this.vehicleId;
        request.type = '5';
        request.isNeedSave = this.mode == 0 ? '1' : '0';
        request.qualificationLicUrl = this.state.qualificatePicUrl;
        request.qualificationLicName = qualificationLicName;
        request.qualificationLicEffectDate = qualificationLicEffectDate;
        request.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                switch (this.mode) {
                    case 0:
                        //认证 添加车辆
                        this._showToast(res.getMsg());
                        this.callBack && this.callBack();
                        EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                        RouterUtils.skipPop();
                        break;
                    case 1:
                        //更新
                        this.updateDriverLicense();
                        break;
                    case 2:
                        //过期
                        this.uploadExpireDateLicense();
                        break;
                    case 3:
                        //证件缺失
                        this.addQualificateLicense();
                        break;
                    default:
                        break;
                }
            } else {
                this.setState({licenseTipInfo: res.data?.data});
            }
        });
    }

    /**
     * 注释：绘制提示视图
     * 时间：2025/2/25 19:28
     * @author：宋双朋
     * @returns {JSX.Element}
     */
    renderTipView() {
        if (this.state.showTip) {
            return (
                <View style={styles.tipStyle}>
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#3D3D3D',
                            flex: 1,
                        }}>
                        应交通部规定，请您立即上传从业资格证，以免影响摘单！
                    </Text>
                    <UITouchableOpacity
                        onPress={() => {
                            this.setState({showTip: false});
                        }}>
                        <UIImage source={'ic_close'} style={{width: 15, height: 15}} />
                    </UITouchableOpacity>
                </View>
            );
        }
    }

    /**
     * 注释：从业资格证上传
     * 时间：2025/2/25 19:41
     * @author：宋双朋
     * @returns {JSX.Element}
     */
    renderPaperVersion() {
        return (
            <ComCertificationUploadImgView
                ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                style={{
                    backgroundColor: '#FFFFFF',
                    paddingLeft: 15,
                    paddingEnd: 15,
                    paddingTop: 7,
                    paddingBottom: 7,
                }}
                showViewBg={this.state.showViewBg}
                tvTitle1View={
                    <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                        从业资格证
                        <Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                    </Text>
                }
                auditText={this.state.errorMsg}
                tvDescView={
                    <Text style={{fontSize: 14, color: '#666', marginTop: 6}}>
                        请上传<Text style={{fontSize: 14, color: '#66B8FC'}}>{this.eLogin?.memberName}</Text>本人从业资格证
                    </Text>
                }
                leftImg={{
                    url: this.state.qualificatePicUrl ?? '',
                    defaultValue: 'certification_driver_license_template_diagram_17',
                    onDelPic: () => {
                        this.setState({ocrResult: undefined, qualificatePicUrl: undefined});
                    },
                    onPostSuccess: (url) => {
                        this.driverLicenseOcrCheck(url);
                    },
                    ref: this.imagePaperUploadRef,
                    canDelete: this.state.canDelete,
                }}
            />
        );
    }

    /**
     * 注释：从业资格证OCR识别
     * 时间：2025/2/25 19:33
     * @author：宋双朋
     * @param qualificatePicUrl
     */
    driverLicenseOcrCheck(qualificatePicUrl?: string) {
        let request = new ReqQualificationLicenseOcrCheck();
        request.qualificationLicUrl = qualificatePicUrl;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({
                    ocrResult: res.data?.data,
                    qualificatePicUrl: qualificatePicUrl,
                    showForm: true,
                });
            } else {
                if (this.state.ocrFailNum >= this.state.maxOcrFailNum) {
                    this.setState({qualificatePicUrl: qualificatePicUrl});
                } else {
                    this.setState({qualificatePicUrl: ''});
                }
                this.setState({showOcrFailDialog: true, ocrFailNum: this.state.ocrFailNum + 1});
            }
        });
    }

    /**
     * 注释：绘制OCR识别报错弹窗
     * 时间：2025/2/26 10:43
     * @author：宋双朋
     * @returns {JSX.Element}
     */
    renderOcrResultFailDialog() {
        return (
            <OcrFailTipDialog
                image={'certification_driver_license_template_diagram_17'}
                mainTip={'从业资格证模糊或不完整，请重新上传'}
                showContinue={this.state.ocrFailNum > this.state.maxOcrFailNum}
                onResetUpload={() => {
                    this.imagePaperUploadRef.current?.clear();
                    this.setState({showOcrFailDialog: false, qualificatePicUrl: undefined});
                    // 延迟触发上传，确保状态更新完成
                    setTimeout(() => {
                        this.imagePaperUploadRef.current?.triggerUpload();
                    }, 100);
                }}
                onContinueUpload={() => {
                    this.setState({showOcrFailDialog: false, showForm: true});
                }}
                onCancel={() => {
                    this.setState({showOcrFailDialog: false});
                }}
            />
        );
    }

    /**
     * 注释：时间选择
     * 时间：2025/2/26 10:43
     * @author：宋双朋
     * @returns {JSX.Element}
     */
    renderSelectTimeView = () => {
        return (
            <UIDatePicker
                title={'请选择'}
                mode={'date'}
                minDate={new Date(2014, 1, 1, 0, 0, 0)}
                maxDate={new Date(2100, 12, 31, 12, 59, 59)}
                onHideEvent={() => {
                    this.setState({showTimeDialog: false});
                }}
                onSelectEvent={(date) => {
                    this.qualificationEffectDateRef.current?.setValue(dateFormat(date, 'yyyy-MM-dd'));
                    this.setState({licenseTipInfo: undefined});
                }}
            />
        );
    };

    /**
     * 注释: 绘制表单视图
     * 时间: 2025/2/20 星期四 19:45
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderFormView() {
        return (
            <View style={{backgroundColor: '#fff', marginTop: 8}}>
                <UITouchableOpacity onPress={() => {}}>
                    <UIFormInputView
                        label={'姓名'}
                        showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.qualificationLicNameText)}
                        error={this.state.licenseTipInfo?.qualificationLicNameText}
                        isRequired={true}
                        edit={true}
                        value={this.state.ocrResult?.name}
                        ref={this.qualificationNameRef}
                        onChangeText={() => {
                            this.setState({licenseTipInfo: undefined});
                        }}
                    />
                </UITouchableOpacity>
                <UIFormInputView
                    label={'有效期止'}
                    showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.qualificationLicEffectDateText)}
                    error={this.state.licenseTipInfo?.qualificationLicEffectDateText}
                    isRequired={true}
                    isSelect={true}
                    value={this.state.ocrResult?.expiryDate}
                    ref={this.qualificationEffectDateRef}
                    onSelect={() => {
                        this.setState({showTimeDialog: true});
                    }}
                />
            </View>
        );
    }

    render() {
        let title = '从业资格证上传';
        if (this.mode == 1) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={title} />
                <KeyboardAvoidingView behavior={'padding'} style={{flex: 1}}>
                    <ScrollView>
                        {this.state.showTopTip && (
                            <View style={styles.topTipContainer}>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: 'red',
                                        marginRight: 5,
                                    }}>
                                    {this.mode == 1 && TextUtils.equals('103', this.rspQueryUserExpireLicenseB?.licenseCode)
                                        ? '如果您已更换证件可上传最新证件，如果证件未过期或未更换则无需上传。'
                                        : '证件即将 / 已经过期，为保障您的业务不受影响，请及时更新。'}
                                </Text>
                                <UITouchableOpacity
                                    onPress={() => {
                                        this.setState({showTopTip: false});
                                    }}>
                                    <UIImage source={'close_red'} style={{width: 12, height: 12}} />
                                </UITouchableOpacity>
                            </View>
                        )}
                        {/*提示文案*/}
                        {/*{this.renderTipView()}*/}
                        {/*绘制证件上传*/}
                        {this.renderPaperVersion()}
                        {/*绘制表单视图*/}
                        {this.state.showForm && this.renderFormView()}
                    </ScrollView>
                </KeyboardAvoidingView>
                <UIButton
                    text={'提交上传'}
                    style={{borderRadius: 0}}
                    onPress={() => {
                        this.licenseCheck();
                    }}
                />
                {/*OCR识别弹窗*/}
                {this.state.showOcrFailDialog && this.renderOcrResultFailDialog()}
                {/*时间选择*/}
                {this.state.showTimeDialog && this.renderSelectTimeView()}
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    tipStyle: {
        backgroundColor: '#FDF6D9',
        height: 40,
        paddingHorizontal: 15,
        flexDirection: 'row',
        alignItems: 'center',
    },
    topTipContainer: {
        backgroundColor: '#FFF4EF',
        paddingHorizontal: 14,
        paddingVertical: 7,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
});
