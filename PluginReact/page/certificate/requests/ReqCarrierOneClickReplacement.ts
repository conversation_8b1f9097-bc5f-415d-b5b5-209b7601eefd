import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 功能描述: CO-5079 【网络货运】承运方APP找回账号流程简化
 *一键替换手机号接口
 *
 * 接口地址：http://wiki.zczy56.com/pages/viewpage.action?pageId=65185819
 * 接口对接：张 嘉豪
 */
export class ReqCarrierOneClickReplacement extends BaseRequest {
    occupiedMobile?: string; //已占用的手机号
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            occupiedMobile: this.occupiedMobile,
        };
        return super.post('/mms-app/platform/authentication/carrierOneClickReplacement', ResultData);
    }
}
