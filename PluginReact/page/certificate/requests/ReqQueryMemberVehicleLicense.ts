import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';
import {ResQueryMemberVehicleLicense} from '../models/ResQueryMemberVehicleLicense';

/**
 * 注释: 车辆证件查询接口
 * 时间: 2024/8/28 0028 13:49
 * <AUTHOR>
 */
export class ReqQueryMemberVehicleLicense extends BaseRequest {
    public vehicleId: string;

    async request(): Promise<BaseResponse<ResQueryMemberVehicleLicense>> {
        this.params = {
            vehicleId: this.vehicleId,
        };
        return super.post('/mms-app/vehicle/queryMemberVehicleLicense', ResQueryMemberVehicleLicense);
    }
}
