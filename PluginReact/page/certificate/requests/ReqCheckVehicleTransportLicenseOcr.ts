import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {Method} from '../../util/NativeModulesTools';

/**
 * 注释：道路运输证ocr
 * 时间：2025/2/28 15:29
 * 作者：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=65180682
 * 对接：靳志伟
 * */
export class ReqCheckVehicleTransportLicenseOcr extends BaseRequest {
    img?: string; // 道路运输证照片
    public async request(): Promise<BaseResponse<RspCheckVehicleTransportLicenseOcr>> {
        this.params = {
            url: this.img,
            memberName: Method.getLogin().memberName,
        };
        return super.post('/mms-app/whiteUrl/vehicle/checkVehicleTransportLicenseOcr', RspCheckVehicleTransportLicenseOcr);
    }
}

export class RspCheckVehicleTransportLicenseOcr extends ResultData {
    transportBusinessNo?: string; //道路运输经营许可证号
    plateNumber?: string; //车牌号
}
