import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：
 * 时间：2024/12/10 9:57
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqFaceRegoin extends BaseRequest {
    orderNo?: string;
    idCardName?: string;
    idCardNo?: string;
    faceReason?: string;
    certifyId?: string;
    userType?: string; //用户类型

    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderNo: this.orderNo,
            idCardName: this.idCardName,
            idCardNo: this.idCardNo,
            faceReason: this.faceReason,
            certifyId: this.certifyId,
            userType: this.userType,
        };
        return super.post('mms-app/platform/authentication/faceRecognitionForUpgrade', ResultData);
    }
}
