import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 从业资格证缺失
 * 时间: 2025/2/21 星期五 11:22
 * <AUTHOR>
 */
export class ReqAddQualificateLicense extends BaseRequest {
    qualificateLicense?: string; //从业者资格证照片

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            qualificateLicense: this.qualificateLicense,
        };
        return super.post('mms-app/mms/selfCenter/addQualificateLicense', ResultData);
    }
}
