import {Method} from '../../util/NativeModulesTools';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

export class ReqIsShowVehicleTypeTip extends BaseRequest {
    public async request(): Promise<BaseResponse<RspIsShowVehicleTypeTip>> {
        this.params = {
            carrierId: Method.getLogin().userId,
        };
        return super.post('mms-app/vehicle/isShowVehicleTypeTip', RspIsShowVehicleTypeTip);
    }
}

export class RspIsShowVehicleTypeTip extends ResultData {
    public data?: string;
}
