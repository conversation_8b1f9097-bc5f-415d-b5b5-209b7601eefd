import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：认证审核按钮
 * 时间：2024/12/16 9:46
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqUserPromote extends BaseRequest {
    promoteType?: string = '10';

    public async request(): Promise<BaseResponse<QuserPromote>> {
        this.params = {
            promoteType: this.promoteType,
        };
        return super.post('/mms-app/platform/authentication/userPromote', QuserPromote);
    }
}

export class QuserPromote extends ResultData {
    completeThreeImages?: string;
    threeFactorRiskState?: string; ////三要素是否有风险，0没有，1有
    riskControlRiskState?: string; //风控是否有风险，0没有，1有
    verifyState?: string; //实名认证状态，0没有，1有
    riskSubmit?: string; //是否申诉过,0否，1是
    faceReason?: string; //实名原因
    threeElementsName?: string; //常用手机号办理人姓名（手机号办理人填过的话会返回）
    threeElementsIdCardNo?: string; //常用手机号办理人身份证（手机号办理人身份证填过的话会返回）
    threeElementsCheckNums?: string; //三要素校验次数是否超限 0否 1是
    silentSignState?: string; //静默签是否需要签署，0不需要，1需要
}
