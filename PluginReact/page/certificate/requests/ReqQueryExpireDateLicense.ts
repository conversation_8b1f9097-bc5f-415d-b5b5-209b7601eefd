import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import PageList from '../../http/PageList';

/**
 * 注释: 查询过期证件
 * 时间: 2024/12/26 星期四 10:59
 * <AUTHOR>
 */
export class ReqQueryExpireDateLicense extends BaseRequest {
    type: String = '1'; //1:证件、2:车辆、3:船舶;如果为空，查询全部
    async request(): Promise<BaseResponse<PageList<ExpireDate>>> {
        this.params = {
            type: this.type,
        };
        return super.postList('mms-app/member/queryExpireDateLicense', ExpireDate);
    }
}

export class ExpireDate {
    type: String = '';
    vehicleId: String = '';
}
