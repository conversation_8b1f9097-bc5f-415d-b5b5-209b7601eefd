import { Method } from '../../util/NativeModulesTools';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

//查询从业资格证状态
export class ReqQualificateState extends BaseRequest {
   
    public async request(): Promise<BaseResponse<RspQualificateState>> {
        this.params = {
            carrierId: Method.getLogin().userId,
        };
        return super.post('mms-app/member/queryQualificateState', RspQualificateState);
    }
}

export class RspQualificateState  extends ResultData {
   public licenseState?: string  //-1：未上传 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
}
 