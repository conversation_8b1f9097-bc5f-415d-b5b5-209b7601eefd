import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';

/**
 * wiki:http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
 * 个体司机提交认证接口
 *
 */
export class ReqUserPromoteNew extends BaseRequest {
    public idCardEffectDate?: string; //	身份证有效期	String	是
    public quasiVehicleType?: string; //	准驾车型	String	是
    public driverLicEffectDate?: string; //	驾驶证有效期	String	是
    public qualificationLicEffectDate?: string; //	从业资格证有效期	String	否	普通车大于4.5吨或挂车时必填
    public vehicleLicenseEffectDate?: string; //	行驶证年检页有效期	String	是
    public trailerVehicleLicenseEffectDate?: string; //	车身行驶证年检页有效期	String	否	挂车非临牌时必填
    public isPickUpTheListBySelf?: string; //是否自主摘单为 0、否1是，注意将已有的从业资格证有效期填充（如果上传了）

    async request(): Promise<BaseResponse<EQuserPromoteNew>> {
        this.params = {
            idCardEffectDate: this.idCardEffectDate,
            quasiVehicleType: this.quasiVehicleType,
            driverLicEffectDate: this.driverLicEffectDate,
            qualificationLicEffectDate: this.qualificationLicEffectDate,
            trailerVehicleLicenseEffectDate: this.trailerVehicleLicenseEffectDate,
            isPickUpTheListBySelf: this.isPickUpTheListBySelf,
        };
        return super.post('/mms-app/platform/authentication/userPromoteNew', EQuserPromoteNew);
    }
}
export class EQuserPromoteNew extends ResultData {
    public completeThreeImages?: string;
    public threeFactorRiskState?: string; ////三要素是否有风险，0没有，1有
    public riskControlRiskState?: string; //风控是否有风险，0没有，1有
    public verifyState?: string; //实名认证状态，0没有，1有
    public riskSubmit?: string; //是否申诉过,0否，1是
    public faceReason?: string; //实名原因
    public threeElementsName?: string; //常用手机号办理人姓名（手机号办理人填过的话会返回）
    public threeElementsIdCardNo?: string; //常用手机号办理人身份证（手机号办理人身份证填过的话会返回）
    public threeElementsCheckNums?: string; //三要素校验次数是否超限 0否 1是
    public silentSignState?: string; //静默签是否需要签署，0不需要，1需要
}
