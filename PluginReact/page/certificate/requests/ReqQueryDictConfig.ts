import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {DictConfigRes} from '../../pick/requests/ReqDictConfig';

/**
 * 注释: 查询配置
 * 时间: 2025/2/21 星期五 14:40
 * <AUTHOR>
 */
export class ReqQueryDictConfig extends BaseRequest {
    dictCode: string;

    async request(): Promise<BaseResponse<DictConfigRes>> {
        this.params = {
            dictCode: this.dictCode,
        };
        return super.post('mms-app/dictConfig/queryDictConfig', DictConfigRes);
    }
}
