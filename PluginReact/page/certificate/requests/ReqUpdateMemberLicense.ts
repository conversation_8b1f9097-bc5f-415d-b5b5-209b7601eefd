import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {PicUrlBean} from '../models/PicUrlBean';

/**
 * 注释:  证件自主更新
 * 时间: 2025/2/21 星期五 11:16
 * <AUTHOR>
 */
export class ReqUpdateMemberLicense extends BaseRequest {
    picUrlList: PicUrlBean[];

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {picUrlList: this.picUrlList};
        return super.post('mms-app/license/updateMemberLicense', ResultData);
    }
}
