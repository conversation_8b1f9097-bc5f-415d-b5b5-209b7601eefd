import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {RspQueryUserExpireLicenseDetail} from '../models/RspQueryUserExpireLicenseDetail';

/**
 * 注释: 查询证件信息详情
 * 时间: 2025/2/24 星期一 11:41
 * <AUTHOR>
 */
export class ReqQueryUserExpireLicenseDetail extends BaseRequest {
    targetId: string; //人的证件可以不传，车的证件传vehicleId
    licenseType: string; //200008 牵引车行驶证年检页

    async request(): Promise<BaseResponse<RspQueryUserExpireLicenseDetail>> {
        this.params = {
            targetId: this.targetId,
            licenseType: this.licenseType,
        };
        return super.post('mms-app/member/queryUserExpireLicenseDetail', RspQueryUserExpireLicenseDetail);
    }
}
