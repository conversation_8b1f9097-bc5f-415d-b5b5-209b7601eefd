import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：车老板提交人工审核
 * 时间：2024/12/4 15:26
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqSubmitExamingForFaceFailed extends BaseRequest {
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {};
        return super.post('/mms-app/mms/upgrade/carrierBossSubmit2ExamingForFaceFailed', ResultData);
    }
}
