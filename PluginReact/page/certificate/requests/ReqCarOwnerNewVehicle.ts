import TextUtils from '../../util/TextUtils';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {VehicleDetailsBean} from './ReqVehicleDetails';

/**
 * 功能描述: 车老板新增车辆
 * wiki:http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
 */
export class ReqCarOwnerNewVehicle extends BaseRequest {
    vehicle?: ECarOwnerNewVehicle;

    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = this.vehicle ?? {};
        return super.post('mms-app/vehicle/carrierBoss/add', ResultData);
    }
}

export class ECarOwnerNewVehicle {
    vehicleId?: string; 
    carriageType?: string; //承运人类型 1-个体司机 2-承运商 3-初级会员 4-车老板
    plateNumber?: string; // 车牌号
    plateNumberColor?: string; //车牌颜色
    newEnergyType?: string; //    是否新能源：0、否（默认） 1、是
    vehicleFlag?: string; // vehicleFlag 车辆类型(1.普通 2.半挂)
    triverPermitUrl?: string; // 车头行驶证 or普通货车的行驶证主副页
    roadTransportPermitUrl?: string; // 道路运输证
    trailerNewUrl?: string; // 挂车行驶证主副页
    annualVehicleLicenseUrl?: string; // 行驶证年检页
    tractorVehicleLicenseUrl?: string; // 牵引车/车头行驶证年检页
    trailerVehicleLicenseUrl?: string; // 车身行驶证年检页
    ownFlag?: string; //   车辆所属 0非自有车辆  1自有车辆
    ownRelation?: string; //车辆所属关系 1、挂靠关系 2、租赁关系 3、购买关系
    carPurchaseProofPictureUrl?: string; //购车证明
    paymentProofPictureUrl?: string; //支付证明
    proofPictureUrl1?: string; // 挂靠证明（原证明材料）
    backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    backup2?: string; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    overloadPlateFrontUrl?: string; //	超限临牌正面照     挂车，超限车辆必传
    overloadPlateBackUrl?: string; //超限临牌反面照     挂车，超限车辆必传
    remark?: string; // 备注
    emissionStandard?: string; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    domesticRelationFlag?: string; //  是否为亲属关系 0否 1是
    domesticRelationUrl?: string; // 亲属关系证明   多张用逗号拼接
    transportPermissionLicenseUrl?: string; // 	道路运输许可证 （OCR未识别到道路运输证号时必传）
    transportLicenseNo?: string; // 道路运输证号

    transportLicensePlateNumber?: string; //道路运输证车牌号	String	是
    vehicleBodyLicensePlateNumber?: string; //车身行驶证车牌号	String	否
    vehicleLicenseEffectDate?: string; //行驶证年检页有效期	String	是
    trailerVehicleLicenseEffectDate?: string; //	车身行驶证年检页有效期	String	否
}

export function handlerCarOwnerNewVehicle(data: VehicleDetailsBean): ECarOwnerNewVehicle {
    let vehicle = new ECarOwnerNewVehicle();
    vehicle.vehicleId = data.vehicleId;
    vehicle.carriageType = '4'; //承运人类型 1-个体司机 2-承运商 3-初级会员 4-车老板
    vehicle.plateNumber = data.plateNumber; // 车牌号
    vehicle.plateNumberColor = data.plateNumberColor; //车牌颜色
    vehicle.newEnergyType = '0'; //    是否新能源：0、否（默认） 1、是
    vehicle.vehicleFlag = TextUtils.isNoEmpty( data.vehicleFlag)? data.vehicleFlag:'1'; // vehicleFlag 车辆类型(1.普通 2.半挂)
    vehicle.triverPermitUrl = data.triverPermitUrl; // 车头行驶证 or普通货车的行驶证主副页
    vehicle.roadTransportPermitUrl = data.roadTransportPermitUrl; // 道路运输证
    vehicle.trailerNewUrl = data.trailerNewUrl; // 挂车行驶证主副页
    vehicle.annualVehicleLicenseUrl = data.annualVehicleLicenseUrl; // 行驶证年检页
    vehicle.tractorVehicleLicenseUrl = data.tractorVehicleLicenseUrl; // 牵引车/车头行驶证年检页
    vehicle.trailerVehicleLicenseUrl = data.trailerVehicleLicenseUrl; // 车身行驶证年检页
    vehicle.ownFlag = TextUtils.isNoEmpty(data.ownFlag)?data.ownFlag:'0'; //   车辆所属
    vehicle.ownRelation = data.ownRelation; //车辆所属关系 1、挂靠关系 2、租赁关系 3、购买关系
    vehicle.proofPictureUrl1 = data.proofPictureUrl1; // 挂靠证明（原证明材料）
    vehicle.carPurchaseProofPictureUrl = data.carPurchaseProofPictureUrl; //购车证明
    vehicle.paymentProofPictureUrl = data.paymentProofPictureUrl; //支付证明

    vehicle.backup1 = TextUtils.isNoEmpty(data.backup1)?  data.backup1:'0'; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    vehicle.backup2 =  TextUtils.isNoEmpty(data.backup2)? data.backup2:'0'; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    vehicle.overloadPlateFrontUrl = data.overloadPlateFrontUrl; //	超限临牌正面照     挂车，超限车辆必传
    vehicle.overloadPlateBackUrl = data.overloadPlateBackUrl; //超限临牌反面照     挂车，超限车辆必传
    vehicle.remark = data.remark; // 备注
    vehicle.emissionStandard = data.emissionStandard; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    vehicle.domesticRelationFlag = data.domesticRelationFlag; //  是否为亲属关系 0否 1是
    vehicle.domesticRelationUrl = data.domesticRelationUrl; // 亲属关系证明   多张用逗号拼接
    vehicle.transportPermissionLicenseUrl = data.transportPermissionLicenseUrl; // 	道路运输许可证 （OCR未识别到道路运输证号时必传）
    vehicle.transportLicenseNo = data.businessLicenceNo; // 道路运输证经营许可证号

    vehicle.transportLicensePlateNumber = data.transportLicensePlateNumber; //道路运输证车牌号	String	是
    vehicle.vehicleBodyLicensePlateNumber = data.vehicleBodyLicensePlateNumber; //车身行驶证车牌号	String	否
    vehicle.vehicleLicenseEffectDate = data.vehicleLicenseEffectDate; //行驶证年检页有效期	String	是
    vehicle.trailerVehicleLicenseEffectDate = data.trailerVehicleLicenseEffectDate; //	车身行驶证年检页有效期	String	否
    return vehicle;
}
