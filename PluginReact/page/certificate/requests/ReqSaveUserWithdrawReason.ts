import ELogin from '../../user/personalsafety/models/ELogin';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import { Method } from '../../util/NativeModulesTools';

/**
 * 注释：MMS-15806 🏷 运力促活-司机注册挽留策略
 * wiki：https://zczy56-uac.feishu.cn/docx/AbjRdQLGYodn4Ix2FY9c3kEznsd
 * 对接：张家豪
 * */
export class ReqSaveUserWithdrawReason extends BaseRequest {
    reason?: string;
    public async request(): Promise<BaseResponse<ResultData>> {

        const login: ELogin = Method.getLogin();
        this.params = {
            reason: this.reason,
            mobile:login.mobile
        };
        return super.post('/mms-app/userWithdraw/saveUserWithdrawReason', ResultData);
    }
}

