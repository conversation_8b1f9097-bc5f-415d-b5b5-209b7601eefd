import {BaseResponse} from '../../http/BaseResponse';
import {BaseRequest} from '../../http/BaseRequest';
import {ResMemberVehicleLicenseList} from '../models/ResMemberVehicleLicenseList';
import {Method} from '../../util/NativeModulesTools';

export class ReqQueryMemberVehicleLicenseList extends BaseRequest {
    async request(): Promise<BaseResponse<ResMemberVehicleLicenseList>> {
        let login = Method.getLogin();
        this.params = {
            userId: login.userId,
        };
        return super.post('/mms-app/vehicle/queryMemberVehicleLicenseList', ResMemberVehicleLicenseList);
    }
}
