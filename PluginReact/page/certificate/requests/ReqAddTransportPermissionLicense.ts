import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：上传道路运输经营许可证
 * 时间：2024/12/24 16:55
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqAddTransportPermissionLicense extends BaseRequest {
    public memberName?: string; //用户名称
    public transportPermissionLicenseUrl?: string; //道路运输经营许可证
    public vehicleId?: string; //车辆id
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            memberName: this.memberName,
            transportPermissionLicenseUrl: this.transportPermissionLicenseUrl,
            vehicleId: this.vehicleId,
        };
        return super.post('mms-app/vehicle/addTransportPermissionLicense', ResultData);
    }
}
