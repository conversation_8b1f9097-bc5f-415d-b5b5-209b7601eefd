import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import TextUtils from '../../util/TextUtils';
import {ReactNode} from 'react';
import {EImage} from '../models/EImage';
import {Paper, PapersEnum} from '../models/PaperEnums';
import {Method} from '../../util/NativeModulesTools';

/**
 * 注释：车辆详情
 * 时间：2024/12/30 10:04
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqVehicleDetails extends BaseRequest {
    vehicleId?: string;

    public async request(): Promise<BaseResponse<VehicleDetailsBean>> {
        let loginInfo = Method.getLogin();
        this.params = {
            userId: loginInfo.userId,
            vehicleId: this.vehicleId,
        };
        return super.post('mms-app/vehicle/detail', VehicleDetailsBean);
    }
}

export class VehicleDetailsBean extends ResultData {
    vehicleExamineType?: string; //
    vehicleLength?: string; //
    vehicleWidth?: string; //
    businessPermit?: string; //
    vehicleHeight?: string; //
    createdTime?: string; //
    plantainUrl?: string; //
    vehicleId?: string; //
    vehicleLoad?: string; //
    vehicleFlag?: string; //车辆类型（车辆类别） 1、普通货车 2、挂车  初次认证前的修改必传，初次提交认证必传，重新提交传担不能改
    remark?: string; //
    plateNumber?: string; //车牌号
    trailerPlateNumber?: string; //挂车车牌号
    plateNumberColor?: string; //车牌颜色
    triverPermitUrl?: string; //（牵引车行驶证/普通货车的行驶证主副页）
    triverPermitRiskAudit?: string; //（牵引车行驶证/普通货车的行驶证主副页）风险审核标识 0、待审核 1、驳回 2、风险通过 3、异常 5、审核通过
    triverPermitRiskAuditText?: string; //风险提示文字
    triverPermitRiskHisMarkText?: string; //  牵引车行驶证驳回原因

    roadTransportPermitUrl?: string; //路运输证照片
    roadTransportPermitRiskAudit?: string; //道路运输证照片风险审核标识 0、待审核 1、驳回 2、风险通过 3、异常 5、审核通过
    roadTransportPermitRiskAuditText?: string; //道路运输证风险提示文字
    roadTransportDate?: string; //道路运输证日期
    roadTransportPermitRiskHisMarkText?: string; //  道路运输证驳回原因

    trailerNewUrl?: string; // 挂车行驶证主副页
    trailerNewRiskAudit?: string; //挂车行驶证主副页风险审核标识 0、待审核 1、驳回 2、风险通过 3、异常 5、审核通过
    trailerNewRiskAuditText?: string; //风险提示文字
    trailerNewRiskHisMarkText?: string; //  挂车行驶证驳回原因

    roadCertificateNum?: string; //
    vehicleType?: string; //
    vehicleOwner?: string; //车辆所有人
    proofPictureUrl1?: string; //车老板车辆挂靠证明 或 物流企业车辆证明材料 证明材料图片1
    proofPictureUrl2?: string; // 证明材料图片2
    proofPictureUrl3?: string; // 证明材料图片2
    carPurchaseProofPictureUrl?: string; // 证明材料图片2 购买证明
    paymentProofPictureUrl?: string; // 证明材料图片3 支付证明
    domesticRelationUrl?: string; // 证明材料图片4 亲属关系证明

    orderVerify?: string; // 摘单验证 0-关 1-开
    selectOil?: string; // 选择油品 0-关 1-开

    plantainRiskAudit?: string; ///车前清晰照片风险审核标识 1-风险审核通过 2-风险审核不通过 3-待风险审核
    plantainRiskAuditText?: string; //风险提示文字

    notPassReason?: string; //驳回原因

    examineNewType?: string; //1：复审通过 2：初审通过 3，待终审 4。终审通过 5:终审不通过
    examineType?: string; //0:待初审 1:待复审(初审通过)  2:审核不通过(初审不通过)  3:未认证 4:待车老板确认 5:车老板同意 6:车老板拒绝

    originateBy?: string; //详情是否展示证明材料三，2展示
    transportLicenseHisMark?: boolean = false; //道路运输证老数据异常标记
    vehicleLicenseHisMark?: boolean = false; //（车头）行驶证老数据异常标记
    vehicleBodyLicenseHisMark?: boolean = false; //车身行驶证老数据异常标记
    proofPictureHisMark?: boolean = false; //证明材料老数据异常标记
    freezeText?: string; //冻结原因
    freezeSource?: string; //冻结类型 0:系统冻结 1：人工冻结
    hisMarkNotPassReason?: string; //   老数据异常审核不通过原因

    proofPicture1RiskHisMarkText?: string; // 证明材料一驳回原因
    proofPicture1RiskAuditing?: string; //1：审核中(证件更新审核中)

    proofPicture1RiskAudit?: string; //材料证明1 风险审核标识 10 证明材料已过期
    proofPicture1RiskAuditText?: string; //风险提示文字
    proofPicture2RiskAudit?: string; //材料证明2 风险审核标识
    proofPicture2RiskAuditText?: string; //风险提示文字
    proofPicture3RiskAudit?: string; // 证明材料3
    proofPicture3RiskAuditText?: string; //风险提示文字
    proofPicture4RiskAudit?: string;
    proofPicture4RiskAuditText?: string;
    proofPicture5RiskAudit?: string; //证明材料风险标识（1~5）
    proofPicture5RiskAuditText?: string; //证明材料驳回理由（1~5）

    ownFlag?: string; // 车辆所属 0 非自有车辆 1 自有车辆
    ownRelation?: string; //车辆所属关系 1、挂靠关系 2、租赁关系 3、购买关系

    affiliatedProofPicture?: VehicleDetailsBeanPicture[]; //车老板车辆挂靠证明 | 物流企业证明材料 图片url、风险标识、驳回理由
    leaseProofPicture?: VehicleDetailsBeanPicture[]; //	租赁证明
    carPurchaseProofPicture?: VehicleDetailsBeanPicture[]; //购车证明
    domesticRelationPicture?: VehicleDetailsBeanPicture[]; //亲属关系证明
    paymentProofPicture?: VehicleDetailsBeanPicture[]; //支付证明

    backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    backup2?: string; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0

    overloadPlateFrontUrl?: string; //	超限临牌正面照
    overloadPlateFrontRiskAudit?: string; //	  超限临牌正面照 审核标识  0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    overloadPlateFrontAuditText?: string; //	超限临牌正面照 驳回理由

    overloadPlateBackUrl?: string; //超限临牌反面照     挂车，超限车辆必传
    overloadPlateBackRiskAudit?: string; //超限临牌反面照 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    overloadPlateBackAuditText?: string; //超限临牌反面照 驳回理由

    overloadPlateFrontHisMark: boolean = false; //超限临牌正面照，完善/老数据完善异常标记
    overloadPlateFrontHisMarkText?: string; //超限临牌正面照，完善/老数据完善，驳回理由
    overloadPlateBackHisMark: boolean = false; //超限临牌反面照完善/老数据完善异常标记
    overloadPlateBackHisMarkText?: string; //超限临牌反面照，完善/老数据完善，驳回理由

    annualVehicleLicenseUrl?: string; // 行驶证年检页
    tractorVehicleLicenseUrl?: string; // 牵引车/车头行驶证年检页
    trailerVehicleLicenseUrl?: string; // 车身行驶证年检页

    annualVehicleLicenseRiskAudit?: string; // 行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    tractorVehicleLicenseRiskAudit?: string; // 牵引车/车头行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    trailerVehicleLicenseRiskAudit?: string; // 车身行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    proofPictureRiskAudit?: string; //  审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    leaseProofPictureRiskAudit?: string; // 租赁证明 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    carPurchaseProofPictureRiskAudit?: string; // 购车证明 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    paymentProofPictureRiskAudit?: string; // 支付证明 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过

    annualVehicleLicenseAuditText?: string; // 行驶证年检页 驳回理由
    tractorVehicleLicenseAuditText?: string; // 牵引车/车头行驶证年检页 驳回理由
    trailerVehicleLicenseAuditText?: string; // 车身行驶证年检页 驳回理由

    annualVehicleLicenseHisMark?: boolean = false; // 行驶证年检页 数据完善异常标记
    tractorVehicleLicenseHisMark?: boolean = false; // 牵引车/车头行驶证年检页 数据完善异常标记
    trailerVehicleLicenseHisMark?: boolean = false; // 车身行驶证年检页 审核标识 数据完善异常标记

    annualVehicleLicenseHisMarkText?: string; // 行驶证年检页 驳回原因
    tractorVehicleLicenseHisMarkText?: string; // 牵引车/车头行驶证年检页 驳回原因
    trailerVehicleLicenseHisMarkText?: string; // 车身行驶证年检页 驳回原因

    qualificateUrl?: string; // 从业资格证
    qualificateRiskAudit?: string; //从业资格证审核状态 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    qualificateRiskAuditText?: string; // 从业资格证驳回理由

    domesticRelationFlag?: string; //    是否为亲属关系 0否 1是
    domesticRelationUrlRiskAudit?: string; //   亲属关系证明 总风险标识 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过

    transportPermissionLicenseUrl?: string; // 	道路运输许可证 （OCR未识别到道路运输证号时必传）
    transportPermissionRiskAudit?: string; //道路运输许可证审核状态  0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    transportPermissionAuditContent?: string; // 道路运输许可证驳回理由
    transportLicenseNo?: string; // 道路运输证号

    personCarUrl1?: string; // 人车合影照片（人脸+车牌号照片）
    personCarUrl1RiskAudit?: string; // 人车合影照片（人脸+车牌号照片）审核状态 -1、未上传 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    personCarUrl1AuditContent?: string; //人车合影照片（人脸+车牌号照片）驳回理由
    personCarUrl2?: string; // 人车合影照片（车头车牌+车身照片）
    personCarUrl2RiskAudit?: string; // 人车合影照片（车头车牌+车身照片）审核状态	-1、未上传 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    personCarUrl2AuditContent?: string; //人车合影照片（车头车牌+车身照片）驳回理由

    emissionStandard?: string; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    businessLicenceNo?: string; //道路运输证经营许可证号
    transportLicensePlateNumber?: string; //道路运输证车牌号
    qualificationLicEffectDate?: string; //从业资格证有效期
    vehicleLicenseEffectDate?: string; //行驶证年检页有效期
    trailerVehicleLicenseEffectDate?: string; //车身行驶证年检页有效期
    vehicleBodyLicensePlateNumber?: string; //车身行驶证车牌号
    qualificationLicName?: string; //从业资格证姓名
    isSubmitMap?: {[key: string]: string}; //阶段状态标识
}

export const toImageList = (item?: VehicleDetailsBean, imageType?: number) => {
    switch (imageType) {
        case 1: {
            //挂靠证明
            if (TextUtils.isEmpty(item?.proofPictureUrl1)) {
                return [];
            }
            return item?.proofPictureUrl1?.split(',').map((item) => {
                let image = new EImage();
                image.imageId = item;
                return image;
            });
        }
        case 2: {
            //购车证明
            if (TextUtils.isEmpty(item?.carPurchaseProofPictureUrl)) {
                return [];
            }
            return item?.carPurchaseProofPictureUrl?.split(',').map((item) => {
                let image = new EImage();
                image.imageId = item;
                return image;
            });
        }
        case 3: {
            //支付证明
            if (TextUtils.isEmpty(item?.paymentProofPictureUrl)) {
                return [];
            }
            return item?.paymentProofPictureUrl?.split(',').map((item) => {
                let image = new EImage();
                image.imageId = item;
                return image;
            });
        }
        case 4: {
            //亲属关系证明
            if (TextUtils.isEmpty(item?.domesticRelationUrl)) {
                return [];
            }
            return item?.domesticRelationUrl?.split(',').map((item) => {
                let image = new EImage();
                image.imageId = item;
                return image;
            });
        }
    }
};

export class AnnuallyItem {
    name?: string; //图片名称
    eImage: EImage = new EImage(); //图片信息
}

export const showPerfectView = (item?: VehicleDetailsBean) => {
    let list: RspVehiclePerfect[] = [];
    if (item != null) {
        switch (item.vehicleFlag) {
            case '1': //1.行驶证
                if (item.vehicleLicenseHisMark) {
                    let bean = new RspVehiclePerfect();
                    bean.title1 = '行驶证';
                    bean.title2 = '(主副页)';
                    bean.red = true;
                    bean.errorMsg = item.triverPermitRiskHisMarkText ?? '';
                    bean.resId1 = 'certification_driver_license_template_diagram_7';
                    list.push(bean);
                }
                //1.1行驶证年检页
                if (item.annualVehicleLicenseHisMark) {
                    let bean = new RspVehiclePerfect();
                    bean.title1 = '行驶证';
                    bean.title2 = '(年检页)';
                    bean.red = true;
                    bean.errorMsg = item.annualVehicleLicenseHisMarkText ?? '';
                    bean.resId1 = 'certification_driver_license_template_diagram_20';
                    list.push(bean);
                }
                switch (item.backup1) {
                    case '1':
                        //2.道路运输证
                        if (item.transportLicenseHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '道路运输证';
                            bean.title2 = '(主副页)';
                            bean.red = true;
                            bean.errorMsg = item.roadTransportPermitRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_10';
                            list.push(bean);
                        }
                        break;
                }
                break;
            default: {
                //1.牵引车行驶证
                if (item.vehicleLicenseHisMark) {
                    let bean = new RspVehiclePerfect();
                    bean.title1 = '牵引车/车头行驶证';
                    bean.title2 = '(主副页)';
                    bean.red = true;
                    bean.errorMsg = item.triverPermitRiskHisMarkText ?? '';
                    bean.resId1 = 'certification_driver_license_template_diagram_8';
                    list.push(bean);
                }
                //1.1牵引车行驶证年检页
                if (item.tractorVehicleLicenseHisMark) {
                    let bean = new RspVehiclePerfect();
                    bean.title1 = '牵引车/车头行驶证';
                    bean.title2 = '(年检页)';
                    bean.red = true;
                    bean.errorMsg = item.tractorVehicleLicenseHisMarkText ?? '';
                    bean.resId1 = 'certification_driver_license_template_diagram_18';
                    list.push(bean);
                }
                //2.道路运输证
                if (item.transportLicenseHisMark) {
                    let bean = new RspVehiclePerfect();
                    bean.title1 = '道路运输证';
                    bean.title2 = '(主副页)';
                    bean.red = true;
                    bean.errorMsg = item.roadTransportPermitRiskHisMarkText ?? '';
                    bean.resId1 = 'certification_driver_license_template_diagram_10';
                    list.push(bean);
                }
                switch (item.backup1) {
                    case '1':
                        //超限临时牌
                        if (item.vehicleBodyLicenseHisMark && item.overloadPlateBackHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '临时牌照';
                            bean.title2 = '(正反面)';
                            bean.red = true;
                            bean.tImg = true;
                            bean.errorMsg = item.trailerNewRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_14';
                            bean.resId2 = 'certification_driver_license_template_diagram_11';
                            list.push(bean);
                        } else if (item.vehicleBodyLicenseHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '临时牌照';
                            bean.title2 = '(正面)';
                            bean.red = true;
                            bean.errorMsg = item.trailerNewRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_13';
                            list.push(bean);
                        } else if (item.overloadPlateBackHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '临时牌照';
                            bean.title2 = '(反面)';
                            bean.red = true;
                            bean.errorMsg = item.trailerNewRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_12';
                            list.push(bean);
                        }
                        break;
                    default: {
                        //3.挂车行驶证
                        if (item.vehicleBodyLicenseHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '挂车/车身行驶证';
                            bean.title2 = '(主副页)';
                            bean.red = true;
                            bean.errorMsg = item.trailerNewRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_6';
                            list.push(bean);
                        }
                        //3.1挂车行驶证年检页
                        if (item.vehicleBodyLicenseHisMark) {
                            let bean = new RspVehiclePerfect();
                            bean.title1 = '挂车/车身行驶证';
                            bean.title2 = '(年检页)';
                            bean.red = true;
                            bean.errorMsg = item.trailerNewRiskHisMarkText ?? '';
                            bean.resId1 = 'certification_driver_license_template_diagram_20';
                            list.push(bean);
                        }
                        break;
                    }
                }
                break;
            }
        }
    }
    return list;
};

export class VehicleDetailsBeanPicture {
    url?: string; //图片
    //风险标识 0、待审核 1、驳回 2、风险通过 3、异常 5、审核通过
    //a：对于物流企业证明材料来说，1和2都有可能，看车辆本身的状态，
    //如果车辆是初审驳回（车辆列表showSubmitBtn为true），则只需要重新上传 riskAudit =1 的照片，
    //如果车辆是风险通过（车辆列表showRiskBtn为true），则只需要重新上传 riskAudit =2 的照片。
    //b：对于车老板证明材料来说，不会有2，驳回只可能是1，代表终审驳回（挂靠证明、租赁证明、购车证明、支付证明）
    riskAudit?: string;
    riskAuditText?: string; //驳回理由
    operatorId?: string; //非驳回照片的该字段必须原值传入
    operateName?: string; //非驳回照片的该字段必须原值传入
}

export const expired = (item?: VehicleDetailsBean) => {
    return TextUtils.equals('10', item?.proofPicture1RiskAudit);
};

export class RspVehiclePerfect {
    title1?: string;
    title2?: string;
    red: boolean = false;
    tImg: boolean = false;
    errorMsg?: string;
    imgUrl?: string;
    resId1?: string;
    resId2?: string;
}

export const showMsg = (item?: RspVehiclePerfect) => {
    return (item?.errorMsg ?? '').split(';');
};

export class PapersFileState {
    type: Paper = PapersEnum['NULL'];
    isTakePhote = false;
    mustPass = true; //必传
    redAsterisk = true;
    fileUrl?: string = '';
    fileUrl2?: string = '';
    edit = true;
    editFile: boolean = true;
    editFile2: boolean = true;
    signColor: boolean = false;
    toast?: string = '';
    fileSuccessCallback?: Function; //文件上传成功回调
    show = true; //是否可以见，默认可以见
    toast2?: ReactNode; //特殊提示内容
}
