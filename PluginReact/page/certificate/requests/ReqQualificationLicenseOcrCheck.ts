import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResDriverLicenseOcrCheck} from '../models/ResDriverLicenseOcrCheck';

/**
 * 注释: 从业资格证OCR识别
 * 时间: 2025/2/24 星期一 10:53
 * <AUTHOR>
 */
export class ReqQualificationLicenseOcrCheck extends BaseRequest {
    //从业资格证
    qualificationLicUrl?: string;

    async request(): Promise<BaseResponse<ResDriverLicenseOcrCheck>> {
        this.params = {
            qualificationLicUrl: this.qualificationLicUrl,
        };
        return super.post('/mms-app/whiteUrl/vehicle/qualificationLicenseOcrCheck', ResDriverLicenseOcrCheck);
    }
}
