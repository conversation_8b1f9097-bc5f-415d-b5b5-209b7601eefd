import {BaseResponse} from '../../http/BaseResponse';
import {BaseRequest} from '../../http/BaseRequest';
import {Method} from '../../util/NativeModulesTools';
import {ResQueryLicensePendingNum} from '../models/ResQueryLicensePendingNum';

/**
 * 注释: 证件管理角标
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=65187083
 * 时间: 2024/8/27 0027 15:28
 * <AUTHOR>
 */
export class ReqQueryLicensePendingNum extends BaseRequest {
    async request(): Promise<BaseResponse<ResQueryLicensePendingNum>> {
        let login = Method.getLogin();
        this.params = {
            userId: login.userId,
        };
        return super.post('/mms-app/member/queryLicensePendingNum', ResQueryLicensePendingNum);
    }
}
