import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：人脸识别
 * 时间：2024/12/16 9:34
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqAuthIdCard extends BaseRequest {
    haveIdCardNoFlag?: string; //是否带身份证？ 0否 1是
    idCardNo?: string; //身份证号码
    idCardName?: string; //身份证姓名
    frontIdCardUrl?: string; //身份证正面照片
    negativeIdCardUrl?: string; //身份证反面照片
    idCardEffectDate?: string; //身份证有效期
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            haveIdCardNoFlag: this.haveIdCardNoFlag,
            idCardNo: this.idCardNo,
            idCardName: this.idCardName,
            frontIdCardUrl: this.frontIdCardUrl,
            negativeIdCardUrl: this.negativeIdCardUrl,
            idCardEffectDate: this.idCardEffectDate,
        };
        return super.post('mms-app/platform/authentication/memberAuthAddIdCard', ResultData);
    }
}
