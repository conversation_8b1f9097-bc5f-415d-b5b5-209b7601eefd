import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：道路运输经营许可证详情
 * 时间：2024/12/24 14:02
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqQueryTransportPermissionLicenseDetail extends BaseRequest {
    vehicleId?: string; // 车辆id
    public async request(): Promise<BaseResponse<RspQueryTransportPermissionLicenseDetail>> {
        this.params = {
            vehicleId: this.vehicleId,
        };
        return super.post('mms-app/vehicle/queryTransportPermissionLicenseDetail', RspQueryTransportPermissionLicenseDetail);
    }
}

export class RspQueryTransportPermissionLicenseDetail extends ResultData {
    vehicleId?: string; //  车辆id
    plateNumber?: string; //   车牌号
    ownerName?: string; //   业户名称
    licenseState?: string; //    道路运输许可证状态  -1：未上传 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    transportPermissionLicenseUrl?: string; //     道路运输许可证，hjhdjgh.jpg
    transportPermissionRiskAudit?: string; //     道路运输许可证审核状态 0：审核中 1：审核通过 2：审核驳回  4：风险通过 5：风险待审核 6：风险审核驳回
    transportPermissionAuditContent?: string; //     道路运输许可证驳回理由
}
