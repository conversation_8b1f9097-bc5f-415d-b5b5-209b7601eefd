import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResDriverLicenseOcrCheck} from '../models/ResDriverLicenseOcrCheck';

/**
 * 注释: 驾驶证OCR识别
 * 时间: 2025/2/24 星期一 10:53
 * <AUTHOR>
 */
export class ReqDriverLicenseOcrCheck extends BaseRequest {
    //从业资格证
    qualificationLicUrl: string;
    driverLicUrl: string;
    //是否电子版驾驶证 0-纸质版 1-电子版
    isElectronic: string;

    async request(): Promise<BaseResponse<ResDriverLicenseOcrCheck>> {
        this.params = {
            qualificationLicUrl: this.qualificationLicUrl,
            driverLicUrl: this.driverLicUrl,
            isElectronic: this.isElectronic,
        };
        return super.post('/mms-app/whiteUrl/member/driverLicenseOcrCheck', ResDriverLicenseOcrCheck);
    }
}
