import {BaseResponse} from '../../http/BaseResponse';
import {BaseRequest} from '../../http/BaseRequest';
import {ResMemberLicenseList} from '../models/ResMemberLicenseList';
import {Method} from '../../util/NativeModulesTools';

/**
 * 注释: 会员证件列表查询接口
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=65187083
 * 时间: 2024/8/27 0027 16:00
 * <AUTHOR>
 */
export class ReqQueryMemberLicenseList extends BaseRequest {
    async request(): Promise<BaseResponse<ResMemberLicenseList>> {
        let login = Method.getLogin();
        this.params = {
            userId: login.userId,
        };
        return super.post('/mms-app/member/queryMemberLicenseList', ResMemberLicenseList);
    }
}
