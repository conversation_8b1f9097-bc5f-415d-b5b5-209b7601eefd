import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

// http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
/**
 * 注释: 10.3 行驶证OCR完整性校验
 * 描述：行驶证url、行驶证年检页url，2选一必传，当行驶证url传参时，车辆类型必传
 */

// 出参
export class DrivingOCR {
    data: DrivingOCRData;
}

export class DrivingOCRData {
    plateNumber?: string; //车牌号
    checkExpiryDate?: string; //年检有效期止
    resultCode: string;
    resultMsg: string;
}

export class ReqVehicleLicenseOcrCheck extends BaseRequest {
    vehicleLicUrl?: string; //行驶证ur
    vehicleFlag?: string; //车辆类型(1-普通货车，2-牵引车，3-挂车)
    annualVehicleLicenseUrl?: string; //行驶证年检页url
    async request(): Promise<BaseResponse<DrivingOCR>> {
        this.params = {
            annualVehicleLicenseUrl: this.annualVehicleLicenseUrl,
            vehicleFlag: this.vehicleFlag,
            vehicleLicUrl: this.vehicleLicUrl,
        };
        return super.post('mms-app/whiteUrl/vehicle/vehicleLicenseOcrCheck', DrivingOCR);
    }
}

// 年检页过期ocr接口
export class ReqVehicleLicenseOcrCheckExpire extends BaseRequest {
    annualVehicleLicenseUrl?: string; //行驶证年检页url
    async request(): Promise<BaseResponse<DrivingOCR>> {
        this.params = {
            annualVehicleLicenseUrl: this.annualVehicleLicenseUrl,
        };
        return super.post('mms-app/member/annualVehicleLicenseOcr', DrivingOCR);
    }
}
