import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：身份证OCR
 * 时间：2024/12/20 10:33
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqIdCardLicenseOcr extends BaseRequest {
    idCardFrontLicenseUrl?: string; //身份证正面照
    idCardBackLicenseUrl?: string; //身份证反面照
    public async request(): Promise<BaseResponse<RspIdCardLicenseOcr>> {
        this.params = {
            idCardFrontLicenseUrl: this.idCardFrontLicenseUrl,
            idCardBackLicenseUrl: this.idCardBackLicenseUrl,
        };
        return super.post('mms-app/member/idCardLicenseOcr', RspIdCardLicenseOcr);
    }
}

export class RspIdCardLicenseOcr extends ResultData {
    userName?: string; //	姓名	String
    idCardNo?: string; //	身份证号	String
    sex?: string; //	性别 1:男 2:女	Integer
    birthday?: string; //	出生日期	Date
    address?: string; //	地址	String
    nation?: string; //	民族	String
    organization?: string; //	签发机关	String
    validityTxt?: string; //	有效期	String
    validityStart?: string; //	有效期开始日期	String
    validityEnd?: string; //	有效期结束日期	String
}
