import { Method } from '../../util/NativeModulesTools';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

//查询驾驶证审核状态
export class ReqIfDriverLicenseAuditing extends BaseRequest {
   
    public async request(): Promise<BaseResponse<RspIfDriverLicenseAuditing>> {
        this.params = {
            carrierId: Method.getLogin().userId,
        };
        return super.post('mms-app/vehicle/ifDriverLicenseAuditing', RspIfDriverLicenseAuditing);
    }
}

export class RspIfDriverLicenseAuditing  extends ResultData {
   public data?: string  //返回0-非审核中，1-审核中
}
 