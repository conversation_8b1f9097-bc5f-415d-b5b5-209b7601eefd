import {BaseRequest} from '../../http/BaseRequest';
import PageList from '../../http/PageList';
import {BaseResponse} from '../../http/BaseResponse';

/**
 * 注释：查询企业信息
 * 时间：2024/12/18 11:45
 * 作者：宋双朋
 * wiki：
 * 对接：
 * */
export class ReqQueryCompanyInfo extends BaseRequest {
    companyName?: string; //企业名称
    nowPage?: number; //当前页
    pageSize?: number; //每页个数
    public async request(): Promise<BaseResponse<PageList<RspCompanyInfo>>> {
        this.params = {
            companyName: this.companyName,
            nowPage: this.nowPage ?? 1,
            pageSize: this.pageSize ?? 20,
        };
        return super.postList('mms-app/custMerge/queryCompanyInfo', RspCompanyInfo);
    }
}

export class RspCompanyInfo {
    businessLicenseName?: string; // 企业名称
    taxId?: string; // 统一社会信用代码
}
