import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：
 * 时间：2024/12/9 15:14
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqFaceInfo extends BaseRequest {
    userId?: string;
    idCardName?: string;
    idCardNo?: string;
    metaInfo?: string;

    public async request(): Promise<BaseResponse<FaceInfo>> {
        this.params = {
            userId: this.userId,
            idCardName: this.idCardName,
            idCardNo: this.idCardNo,
            metaInfo: this.metaInfo,
            //返回数据解析解密
            deCheckCode: true,
            //解密标识
            encryption: true,
        };
        return super.post('/mms-app/mms/upgrade/initFaceRecognition', FaceInfo);
    }
}

export class FaceInfo extends ResultData {
    public orderNo?: string; //
    public faceId?: string; //
    public nonceStr?: string; //
    public sign?: string; //
    public userId?: string; //
    public certifyId?: string; //
}
