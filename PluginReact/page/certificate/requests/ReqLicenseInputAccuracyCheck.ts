import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {Method} from '../../util/NativeModulesTools';
import ResLicenseInputAccuracyCheck from '../models/ResLicenseInputAccuracyCheck';

/**
 * 注释: 证件准确性校验接口
 * 时间: 2025/2/21 星期五 11:05
 * <AUTHOR>
 */
export class ReqLicenseInputAccuracyCheck extends BaseRequest {
    //1、身份认证 2、驾驶证信息 5、从业资格证 6、行驶证 7、道路运输证 8、道路运输经营许可证 9、挂车行驶证 10、超限临牌 11、人车合影 12、证明材料
    type: string;
    idCardNo?: string;
    driverLicUrl?: string;
    //0-纸质版 1-电子版
    isElectronic?: string;
    //准驾车型
    quasiVehicleType?: string;
    //驾驶证号
    driverLicNo?: string;
    //驾驶证有效期
    driverLicEffectDate?: string;
    //0、非自有车辆  1、自有车辆
    ownFlag: string;
    // 1、挂靠关系 3、购买关系
    ownRelation: string;
    // 是否需要暂存 认证、添加车辆传 1-是 证件更新传 0-否
    isNeedSave: string;
    //车老板挂靠证明
    proofPictureUrl: string;
    // 购车证明
    carPurchaseProofPictureUrl: string;
    // 支付证明
    paymentProofPictureUrl: string;
    //是否为亲属关系 
    domesticRelationFlag: string;
    // 亲属关系证明
    domesticRelationUrl: string;
    //超限临牌正面照
    overloadPlateFrontUrl?: string;
    //超限临牌反面照
    overloadPlateBackUrl?: string;
    //人车合影照片（人脸+车牌号照片）
    personCarUrl1?: string;
    //人车合影照片（车头车牌+车身照片）
    personCarUrl2?: string;
    //从业资格证照片
    qualificationLicUrl?: string;
    //从业资格证姓名
    qualificationLicName?: string;
    //从业资格证有效期
    qualificationLicEffectDate?: string;
    //身份证正面照片
    frontIdCardUrl?: string;
    //身份证反面照片
    negativeIdCardUrl?: string;
    //身份证号
    idCardName?: string;
    //身份证有效期
    idCardEffectDate?: string;
    //行驶证车牌号
    transportPermissionLicenseUrl?: string;
    //	道路运输证车牌号
    transportLicensePlateNumber?: string;
    //道路运输证经营许可证号
    businessLicenceNo?: string;
    //道路运输证
    transportLicenseUrl?: string;
    // 行驶证车牌号
    plateNumber?: string;
    // 车辆id
    vehicleId?: string;

    vehicleLicenseUrl?: string;
    annualVehicleLicenseUrl?: string;
    vehicleFlag?: string;
    backup2?: string;
    plateColor?: string;
    vehicleLicenseEffectDate?: string;
    emissionStandard?: string;
    vehicleBodyLicenseUrl?: string;
    trailerVehicleLicenseUrl?: string;
    vehicleBodyLicensePlateNumber?: string;
    trailerVehicleLicenseEffectDate?: string;
    backup1?: string;
    
    async request(): Promise<BaseResponse<ResLicenseInputAccuracyCheck>> {
        let loginInfo = Method.getLogin();
        this.params = {
            userId: loginInfo.userId,
            type: this.type,
            idCardNo: this.idCardNo,
            driverLicUrl: this.driverLicUrl,
            isElectronic: this.isElectronic,
            quasiVehicleType: this.quasiVehicleType,
            driverLicNo: this.driverLicNo,
            driverLicEffectDate: this.driverLicEffectDate,
            ownFlag: this.ownFlag,
            ownRelation: this.ownRelation,
            isNeedSave: this.isNeedSave,
            proofPictureUrl: this.proofPictureUrl,
            carPurchaseProofPictureUrl: this.carPurchaseProofPictureUrl,
            paymentProofPictureUrl: this.paymentProofPictureUrl,
            domesticRelationUrl: this.domesticRelationUrl,
            overloadPlateFrontUrl: this.overloadPlateFrontUrl,
            overloadPlateBackUrl: this.overloadPlateBackUrl,
            personCarUrl1: this.personCarUrl1,
            personCarUrl2: this.personCarUrl2,
            qualificationLicUrl: this.qualificationLicUrl,
            qualificationLicName: this.qualificationLicName,
            qualificationLicEffectDate: this.qualificationLicEffectDate,
            frontIdCardUrl: this.frontIdCardUrl,
            negativeIdCardUrl: this.negativeIdCardUrl,
            idCardName: this.idCardName,
            idCardEffectDate: this.idCardEffectDate,
            transportPermissionLicenseUrl: this.transportPermissionLicenseUrl,
            transportLicensePlateNumber: this.transportLicensePlateNumber,
            businessLicenceNo: this.businessLicenceNo,
            transportLicenseUrl: this.transportLicenseUrl,
            plateNumber: this.plateNumber,
            vehicleId: this.vehicleId,

            vehicleLicenseUrl: this.vehicleLicenseUrl,
            annualVehicleLicenseUrl: this.annualVehicleLicenseUrl,
            vehicleFlag: this.vehicleFlag,
            backup2: this.backup2,
            plateColor: this.plateColor,
            vehicleLicenseEffectDate: this.vehicleLicenseEffectDate,
            emissionStandard: this.emissionStandard,
            vehicleBodyLicenseUrl: this.vehicleBodyLicenseUrl,
            trailerVehicleLicenseUrl: this.trailerVehicleLicenseUrl,
            vehicleBodyLicensePlateNumber: this.vehicleBodyLicensePlateNumber,
            trailerVehicleLicenseEffectDate: this.trailerVehicleLicenseEffectDate,
            backup1: this.backup1,
            domesticRelationFlag: this.domesticRelationFlag,
        };
        return super.post('mms-app/member/licenseInputAccuracyCheck', ResLicenseInputAccuracyCheck);
    }
}
