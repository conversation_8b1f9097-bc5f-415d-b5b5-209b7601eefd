import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：
 * 时间：2024/12/4 14:04
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqUpdateMemberInfo extends BaseRequest {
    public frontIdCardUrl?: string; //身份证正面照
    public negativeIdCardUrl?: string; //身份证反面照
    public qualificateNo?: string; //从业资格证号
    public roadTransportPermitUrl?: string; //道路运输证照片
    public driverLicUrl?: string; //驾驶证照片
    public personCarUrl?: string; //人车合照
    public vehicleId?: string; //车辆ID修改车前照、行驶证时必传
    public plantainUrl?: string; //车前照
    public triverPermitUrl?: string; //车头行驶证||行驶证主副页
    public trailerNewUrl?: string; //挂车行驶证主副页
    public driverLicUrlSupplement?: string; //驾驶证补充
    public roadTransportPermitSupplement?: string; //道路运输证补充
    public drivingLicenseSupplement?: string; //行驶证补充
    public carBodyDrivingLicenseSupplement?: string; //车身行驶证补充
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {};
        return super.post('mms-app/member/updateSeniorMemberInfo', ResultData);
    }
}
