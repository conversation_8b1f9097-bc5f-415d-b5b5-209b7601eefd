import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：车老板身份认证驳回重新上传
 * 时间：2025/2/27 19:53
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqMemberUpdateIdCard extends BaseRequest {
    frontIdCardUrl?: string; //身份证正面
    negativeIdCardUrl?: string; //身份证反面
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            frontIdCardUrl: this.frontIdCardUrl,
            negativeIdCardUrl: this.negativeIdCardUrl,
        };
        return super.post('mms-app/platform/authentication/memberUpdateIdCard', ResultData);
    }
}
