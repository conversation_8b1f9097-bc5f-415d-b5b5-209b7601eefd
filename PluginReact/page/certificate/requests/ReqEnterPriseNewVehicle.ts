import TextUtils from '../../util/TextUtils';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import { VehicleDetailsBean } from './ReqVehicleDetails';

/**
 * 功能描述: 物流企业添加车辆
 * wiki:http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
 * 对接人
 *
 */
export class ReqEnterPriseNewVehicle extends BaseRequest {
    public vehicle?: EEnterPriseNewVehicle;
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = this.vehicle ?? {};
        return super.post('mms-app/vehicle/logistics/add', ResultData);
    }
}

export class EEnterPriseNewVehicle {
    vehicleId?: string; //
    carriageType?: string; //承运人类型 1-个体司机 2-承运商 3-初级会员 4-车老板
    plateNumber?: string; // 车牌号
    newEnergyType?: string; //    是否新能源：0、否（默认） 1、是
    ownFlag?: string; //   车辆所属：0、非自有车辆  1、自有车辆
    plateNumberColor?: string; //车牌颜色

    roadTransportPermitUrl?: string; // 道路运输证
    vehicleOwner?: string; // 车辆所属证明
    vehicleFlag?: string; // vehicleFlag 车辆类型(1.普通 2.半挂)
    vehicleType?: string; // 车型要求(高栏车，低栏车，厢式车，等等。。。)
    triverPermitUrl?: string; // 车头行驶证 普通货车的行驶证主副页
    trailerNewUrl?: string; // 挂车行驶证主副页
    remark?: string; // 备注

    backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    backup2?: string; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    overloadPlateFrontUrl?: string; //	超限临牌正面照     挂车，超限车辆必传
    overloadPlateBackUrl?: string; //超限临牌反面照     挂车，超限车辆必传
    proofPictureUrl1?: string;

    annualVehicleLicenseUrl?: string; // 行驶证年检页
    tractorVehicleLicenseUrl?: string; // 牵引车/车头行驶证年检页
    trailerVehicleLicenseUrl?: string; // 车身行驶证年检页
    emissionStandard?: string; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)

    transportLicensePlateNumber?: string; //	道路运输证车牌号	String	是
    vehicleBodyLicensePlateNumber?: string; //	车身行驶证车牌号	String	否
    vehicleLicenseEffectDate?: string; //	行驶证年检页有效期	String	是
    trailerVehicleLicenseEffectDate?: string; //	车身行驶证年检页有效期	String	否
}

export function handlerEnterPriseNewVehicle(data: VehicleDetailsBean):EEnterPriseNewVehicle{
    let vehicle = new EEnterPriseNewVehicle();
    vehicle.vehicleId = data.vehicleId;
    vehicle.carriageType = '2'; //承运人类型 1-个体司机 2-承运商 3-初级会员 4-车老板
    vehicle.plateNumber= data.plateNumber; // 车牌号
    vehicle.ownFlag= '1'; //   车辆所属：0、非自有车辆  1、自有车辆
    vehicle.plateNumberColor= data.plateNumberColor; //车牌颜色

    vehicle.roadTransportPermitUrl= data.roadTransportPermitUrl; // 道路运输证
    vehicle.vehicleOwner= data.vehicleOwner; // 车辆所属证明
    vehicle.vehicleFlag= TextUtils.isNoEmpty(data.vehicleFlag)?data.vehicleFlag:'1'; // vehicleFlag 车辆类型(1.普通 2.半挂)
    vehicle.vehicleType= data.vehicleType; // 车型要求(高栏车，低栏车，厢式车，等等。。。)
    vehicle.triverPermitUrl= data.triverPermitUrl; // 车头行驶证 普通货车的行驶证主副页
    vehicle.trailerNewUrl= data.trailerNewUrl; // 挂车行驶证主副页
    vehicle.remark= data.remark; // 备注

    vehicle.backup1=  TextUtils.isNoEmpty(data.backup1)? data.backup1:'0'; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    vehicle.backup2= TextUtils.isNoEmpty(data.backup2)? data.backup2:'0'; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    vehicle.overloadPlateFrontUrl= data.overloadPlateFrontUrl; //	超限临牌正面照     挂车，超限车辆必传
    vehicle.overloadPlateBackUrl= data.overloadPlateBackUrl; //超限临牌反面照     挂车，超限车辆必传
    vehicle.proofPictureUrl1= data.proofPictureUrl1;

    vehicle.annualVehicleLicenseUrl= data.annualVehicleLicenseUrl; // 行驶证年检页
    vehicle.tractorVehicleLicenseUrl= data.tractorVehicleLicenseUrl; // 牵引车/车头行驶证年检页
    vehicle.trailerVehicleLicenseUrl= data.trailerVehicleLicenseUrl; // 车身行驶证年检页
    vehicle.emissionStandard= data.emissionStandard; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)

    vehicle.transportLicensePlateNumber= data.transportLicensePlateNumber; //	道路运输证车牌号	String	是
    vehicle.vehicleBodyLicensePlateNumber= data.vehicleBodyLicensePlateNumber; //	车身行驶证车牌号	String	否
    vehicle.vehicleLicenseEffectDate= data.vehicleLicenseEffectDate; //	行驶证年检页有效期	String	是
    vehicle.trailerVehicleLicenseEffectDate= data.trailerVehicleLicenseEffectDate; //	车身行驶证年检页有效期	String	否
    return vehicle
}