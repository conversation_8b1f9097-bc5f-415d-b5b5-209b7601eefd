import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 清除缓存接口
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
 */
export class ReqIntelligentAudit2CacheClear extends BaseRequest {
    isDelQualification?: string; //传 1 时，同时删除从业资格证
    vehicleId?: string; // 车辆id
    userId?: string; //用户id
    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            isDelQualification: this.isDelQualification,
            vehicleId: this.vehicleId,
            userId: this.userId,
        };
        return super.post('/mms-app/vehicle/intelligentAudit2CacheClear', ResultData);
    }
}
