import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import PageList from '../../http/PageList';

/**
 * 注释：车辆管理列表
 * 时间：2024/11/29 9:27
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqQueryVehicleList extends BaseRequest {
    examineType?: string; //审核状态 0-审核中 1-审核通过 2-未通过 3-未认证 4-待车老板确认 5-待完善(车老板同意) 6-车老板拒绝
    currentPage?: number; //
    pageSize?: number; //
    plateNumber?: string; //
    public async request(): Promise<BaseResponse<PageList<VehicleBean>>> {
        this.params = {
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            plateNumber: this.plateNumber,
            examineType: this.examineType,
        };
        return super.post('/mms-app/vehicle/queryVehicleList', PageList<VehicleBean>);
    }
}

export class VehicleBean extends ResultData {
    /**
     * plateNumber : 京A88888
     * createdTime : 2018-09-13 10:20:34
     * plantainUrl : cheqianzhao.png
     * vehicleId : 12343
     * "vehicleLoad": "12.00", //载重
     * "vehicleFlag": "2", //车辆类型 1：普通 2：半挂
     * "vehicleStatus": "复审通过" //车辆状态
     * "examineType":"1",//审核状态 0-审核中 1-审核通过 2-未通过 3-未认证 4-待车老板确认 5-待完善(车老板同意) 6-车老板拒绝
     * "freezeType":"0",//冻结状态 0-未冻结 1-已冻结
     * "examineNewType":"1"// 1-复核通过 2-待复审
     */
    public plateNumber: string;
    public createdTime: string;
    public plantainUrl: string;
    public vehicleId: string;
    public vehicleLoad: string;
    public vehicleFlag: string;
    public vehicleStatus: string;
    //审核状态 0-审核中 1-审核通过 2-未通过 3-未认证 4-待车老板确认 5-待完善(车老板同意) 6-车老板拒绝
    public examineType: string;
    public freezeType: string;
    // 1-复核通过 2-待复审 4 终审通过
    public examineNewType: string;
    public ownRelation: string;
    public emissionStandard: string; //	6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    //按钮显示，true代表显示，false为不显示
    public showAgreeBtn: boolean; // 同意
    public showRefuseBtn: boolean; // 拒绝
    public showSubmitBtn: boolean; //重新提交
    public showFinalExamineBtn: boolean; //去终审
    public showPerfectBtn: boolean; //去完善
    public showRemindAuditBtn: boolean; //是否展示催审核按钮
    public personCarBtn: boolean; //人车合影补录按钮是否展示
    public personCarBtnTxt: string; //人车合影补录按钮文案
    //更换挂车按钮是否展示  showChangeTrailerBtn 类型：boolean
    //
    //更换挂车详情按钮是否展示 showChangeTrailerDetailBtn 类型：boolean
    //
    //更换挂车重新提交按钮是否展示 showChangeTrailerResubmitBtn 类型：boolean
    //
    //换挂申请挂车id trailerVehicleId（这个id只有在牵引车换挂申请审核中\审核驳回的情况才会返）
    public showChangeTrailerBtn: boolean;
    public showChangeTrailerDetailBtn: boolean;
    public showChangeTrailerResubmitBtn: boolean;
    public trailerVehicleId: string;
    public showEmissionStandardBtn: boolean; // 是否展示完善排放标准按钮
    public showRiskBtn: boolean; // 风险通过去完善
    public showSupplementLicenseBtn: boolean; // 补充资料按钮是否展示
    public applyUnfreezingBtn: boolean; // 补充资料按钮是否展示
    public originateBy: string; // 详情是否展示证明材料三，2展示
    public riskAudit: string; // 3 风险审核中
    public orderVerify: string; // 摘单验证 0-关 1-开
    public expireFlag: string; //0代表正常，1：代表存在过期
}

export const isView = (vehicle: VehicleBean) => {
    return vehicle.showAgreeBtn || vehicle.showRefuseBtn || vehicle.showSubmitBtn || vehicle.showFinalExamineBtn || vehicle.showPerfectBtn || vehicle.showRemindAuditBtn || vehicle.showRiskBtn;
};
