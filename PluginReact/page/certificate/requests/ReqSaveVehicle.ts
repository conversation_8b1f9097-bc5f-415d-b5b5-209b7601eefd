import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {VehicleDetailsBean} from './ReqVehicleDetails';
import TextUtils from '../../util/TextUtils';

/**
 *  desc: 司机添加车辆
 */
export class ReqSaveVehicle extends BaseRequest {
    vehicle?: ESaveVehicle;
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = this.vehicle??{};
        return super.post('mms-app/vehicle/carrier/add', ResultData);
    }
}
export class ESaveVehicle {
    vehicleId?: string; // 车辆ID
    carriageType?: string; //承运人类型 1-个体司机 2-承运商 3-初级会员 4-车老板
    plateNumber?: string; // 车牌号
    roadTransportPermitUrl?: string; // 道路运输证
    transportPermissionLicenseUrl?: string; // 道路运输经营许可证
    transportBusinessNo?: string; // 道路运输经营许可证号
    vehicleOwner?: string; // 车辆所属证明
    vehicleFlag?: string; // vehicleFlag 车辆类型(1.普通 2.半挂)
    vehicleType?: string; // 车型要求(高栏车，低栏车，厢式车，等等。。。)
    triverPermitUrl?: string; // 车头行驶证/   普通货车的行驶证主副页
    annualVehicleLicenseUrl?: string; //行驶证年检页url
    tractorVehicleLicenseUrl?: string; //牵引车/车头行驶证年检页url
    trailerVehicleLicenseUrl?: string; //车身行驶证年检页url
    trailerNewUrl?: string; // 挂车行驶证主副页
    roadTransportDate?: string; // 道路运输证有效期
    vehicleLength?: string; // 车长
    vehicleWidth?: string; // 车长
    vehicleLoad?: string; // 车辆载重
    plantainUrl?: string; // 车头照片url
    remark?: string; // 备注
    vehicleHeight?: string;
    proofPictureUrl1?: string; // 证明材料一
    proofPictureUrl2?: string; // 证明材料二
    orderVerify?: string; // 摘单验证 0-关 1-开
    selectOil?: string; // 选择油品 0-关 1-开
    businessPermit?: string; // 经营许可证
    newEnergyType?: string; //    是否新能源：0、否（默认） 1、是
    plateNumberColor?: string; //车牌颜色
    backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    backup2?: string; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    overloadPlateFrontUrl?: string; //	超限临牌正面照     挂车，超限车辆必传
    overloadPlateBackUrl?: string; //超限临牌反面照     挂车，超限车辆必传
    qualificateUrl?: string; //从业资格证照片,否（普通货车总质量4.5吨以上 挂车 超限 必传）
    emissionStandard?: string; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    mobile?: string; //手机号
    verifyCode?: string; //验证码
    verifyCodeType?: string; //短信验证码类型 1.短信 2.语音
    moduleType?: string; //	模块类型
    twoCheck?: string; //校验两次
    personCarUrl1?: string; //人车合影照片（人脸+车牌号照片）
    personCarUrl2?: string; //人车合影照片（车头车牌+车身照片）

    transportLicensePlateNumber?: string; //	道路运输证车牌号	String	是
    vehicleBodyLicensePlateNumber?: string; //		车身行驶证车牌号	String	否
    vehicleLicenseEffectDate?: string; //		行驶证年检页有效期	String	是
    trailerVehicleLicenseEffectDate?: string; //		车身行驶证年检页有效期	String	否
    qualificationLicName?: string; //		从业资格证姓名	String	是
    qualificationLicEffectDate?: string; //		从业资格证有效期	String	是
}

export function handlerESaveVehicle(data: VehicleDetailsBean): ESaveVehicle {
    let vehicle = new ESaveVehicle();
    vehicle.carriageType= '1';
    vehicle.vehicleId = data.vehicleId; // 车辆ID
    vehicle.plateNumber = data.plateNumber; // 车牌号
    vehicle.roadTransportPermitUrl = data.roadTransportPermitUrl; // 道路运输证
    vehicle.transportPermissionLicenseUrl = data.transportPermissionLicenseUrl; // 道路运输经营许可证
    vehicle.transportBusinessNo = data.businessLicenceNo; // 道路运输经营许可证号
    vehicle.vehicleOwner = data.vehicleOwner; // 车辆所属证明
    vehicle.vehicleFlag = data.vehicleFlag; // vehicleFlag 车辆类型(1.普通 2.半挂)
    vehicle.vehicleType = data.vehicleType; // 车型要求(高栏车，低栏车，厢式车，等等。。。)
    vehicle.triverPermitUrl = data.triverPermitUrl; // 车头行驶证/   普通货车的行驶证主副页
    vehicle.annualVehicleLicenseUrl = data.annualVehicleLicenseUrl; //行驶证年检页url
    vehicle.tractorVehicleLicenseUrl = data.tractorVehicleLicenseUrl; //牵引车/车头行驶证年检页url
    vehicle.trailerVehicleLicenseUrl = data.trailerVehicleLicenseUrl; //车身行驶证年检页url
    vehicle.trailerNewUrl = data.trailerNewUrl; // 挂车行驶证主副页
    vehicle.roadTransportDate = data.roadTransportDate; // 道路运输证有效期
    vehicle.vehicleLength = data.vehicleLength; // 车长
    vehicle.vehicleWidth = data.vehicleWidth; // 车长
    vehicle.vehicleLoad = data.vehicleLoad; // 车辆载重
    vehicle.plantainUrl = data.plantainUrl; // 车头照片url
    vehicle.remark = data.remark; // 备注
    vehicle.vehicleHeight = data.vehicleHeight;
    vehicle.proofPictureUrl1 = data.proofPictureUrl1; // 证明材料一
    vehicle.proofPictureUrl2 = data.proofPictureUrl2; // 证明材料二
    vehicle.orderVerify = data.orderVerify; // 摘单验证 0-关 1-开
    vehicle.selectOil = data.selectOil; // 选择油品 0-关 1-开
    vehicle.businessPermit = data.businessPermit; // 经营许可证
    vehicle.plateNumberColor = data.plateNumberColor; //车牌颜色
    vehicle.backup1 = TextUtils.isNoEmpty(data.backup1)? data.backup1:'0'; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    vehicle.backup2 = TextUtils.isNoEmpty(data.backup2)? data.backup2:'0'; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    vehicle.overloadPlateFrontUrl = data.overloadPlateFrontUrl; //	超限临牌正面照     挂车，超限车辆必传
    vehicle.overloadPlateBackUrl = data.overloadPlateBackUrl; //超限临牌反面照     挂车，超限车辆必传
    vehicle.qualificateUrl = data.qualificateUrl; //从业资格证照片,否（普通货车总质量4.5吨以上 挂车 超限 必传）
    vehicle.emissionStandard = data.emissionStandard; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    
    vehicle.twoCheck = '1'; //校验两次
    vehicle.personCarUrl1 = data.personCarUrl1; //人车合影照片（人脸+车牌号照片）
    vehicle.personCarUrl2 = data.personCarUrl2; //人车合影照片（车头车牌+车身照片）

    vehicle.transportLicensePlateNumber = data.transportLicensePlateNumber; //	道路运输证车牌号	String	是
    vehicle.vehicleBodyLicensePlateNumber = data.vehicleBodyLicensePlateNumber; //		车身行驶证车牌号	String	否
    vehicle.vehicleLicenseEffectDate = data.vehicleLicenseEffectDate; //		行驶证年检页有效期	String	是
    vehicle.trailerVehicleLicenseEffectDate = data.trailerVehicleLicenseEffectDate; //		车身行驶证年检页有效期	String	否
    vehicle.qualificationLicName = data.qualificationLicName; //		从业资格证姓名	String	是
    vehicle.qualificationLicEffectDate = data.qualificationLicEffectDate; //		从业资格证有效期	String	是
    return vehicle;
}
