import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：
 * 时间：2024/12/10 14:22
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqCarrierBossSubmit2Examing extends BaseRequest {
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {};
        return super.post('mms-app/mms/upgrade/carrierBossSubmit2Examing', ResultData);
    }
}
