import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释:MMS-14255 年检页过期、更换挂车、人车合影补录入口提示优化
 * 时间: 2025/4/17 19:09
 * <AUTHOR>
 */
export class ReqCheckExpireChangeVehicleTrailer extends BaseRequest {
    vehicleId: string;

    async request(): Promise<BaseResponse<RsqCheckExpireChangeVehicleTrailer>> {
        this.params = {
            vehicleId: this.vehicleId,
        };
        return super.post('mms-app/vehicle/checkExpireChangeVehicleTrailer', RsqCheckExpireChangeVehicleTrailer);
    }
}

export class RsqCheckExpireChangeVehicleTrailer extends ResultData {
    vehicleId: string;
    trailerVehicleId: string;
    plateNumber: string;
}
