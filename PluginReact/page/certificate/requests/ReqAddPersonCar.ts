import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 *  desc: 人车合影上传
 *  user: 宋双朋
 *  time: 2025/3/11 15:30
 */
export class ReqAddPersonCar extends BaseRequest {
    vehicleId?: string; //车辆id
    personCarUrl1?: string; //人车合影照片（人脸+车牌号照片）
    personCarUrl2?: string; //人车合影照片（车头车牌+车身照片）
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            vehicleId: this.vehicleId,
            personCarUrl1: this.personCarUrl1,
            personCarUrl2: this.personCarUrl2,
        };
        return super.post('mms-app/vehicle/addPersonCar', ResultData);
    }
}
