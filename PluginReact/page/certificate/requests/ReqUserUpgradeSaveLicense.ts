import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {Method} from '../../util/NativeModulesTools';

/**
 * 注释: 提交认证
 * 时间: 2025/2/21 星期五 15:54
 * <AUTHOR>
 */
export class ReqUserUpgradeSaveLicense extends BaseRequest {
    type: string; //    证件类型 1、身份认证 2、驾驶证信息 3、车辆信息 4、资料补充
    driverLicense?: ReqDriverLicense; //   驾驶证信息
    vehicle?: ReqVehicle; // 车辆信息
    idCard?: ReqIdCard; //  身份认证
    replenish?: ReqReplenish; //   资料补充
    qualification?: Qualification;
    async request(): Promise<BaseResponse<RespSaveLicense>> {
        let loginInfo = Method.getLogin();
        this.params = {
            userId: loginInfo.userId,
            type: this.type,
            driverLicense: this.driverLicense,
            vehicle: this.vehicle,
            idCard: this.idCard,
            replenish: this.replenish,
            qualification: this.qualification,
        };
        return super.post('mms-app/platform/authentication/userUpgradeSaveLicense', RespSaveLicense);
    }
}
export class RespSaveLicense extends ResultData {
    public isElectronic?: string; //	是否电子版驾驶证	String	是	0-纸质版 1-电子版
    public driverLicNo?: string; //	驾驶证号	String	是
    public driverLicEffectDate?: string; //	驾驶证有效期	String	是
}
export class Qualification {
    qualificatePicUrl?: string;
    qualificationLicName?: string;
    qualificationLicEffectDate?: string;
}
export class ReqDriverLicense {
    isElectronic?: string;
    driverLicUrl?: string;
    quasiVehicleType?: string;
    driverLicNo?: string;
    driverLicEffectDate?: string;
}

export class ReqVehicle {
    vehicleId?: string; // 车辆ID 修改，重新提交时必传
    vehicleFlag?: string; // 车辆类型（车辆类别） 1、普通货车 2、挂车
    plateColor?: string; // 车牌颜色
    plateNumber?: string; // 车牌号
    backup1?: string; // 临时/超限车辆 0、否 1、挂车必传
    backup2?: string; // 总质量是否>4.5吨 0、否
    vehicleLicenseUrl?: string; // （车头）行驶证
    annualVehicleLicenseUrl?: string; // 行驶证年检页url
    tractorVehicleLicenseUrl?: string; // 牵引车/车头行驶证年检页url
    trailerVehicleLicenseUrl?: string; // 车身行驶证年检页url
    vehicleBodyLicenseUrl?: string; // （挂车）车身行驶证
    transportLicenseUrl?: string; // 道路运输证
    transportBusinessNo?: string; // 道路运输证经营许可证号
    transportPermissionLicenseUrl?: string; // 道路运输证经营许可证
    overloadPlateFrontUrl?: string; // 超限临牌正面照
    overloadPlateBackUrl?: string; // 超限临牌反面照
    remark?: string; // 备注说明
    qualificatePicUrl?: string; // 从业资格证照片
    emissionStandard?: string; // 6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)
    personCarUrl1?: string; // 人车合影照片（人脸+车牌号照片）
    personCarUrl2?: string; // 人车合影照片（车头车牌+车身照片）
    transportLicensePlateNumber?: string; // 	道路运输证车牌号	String	是
    vehicleBodyLicensePlateNumber?: string; // 	车身行驶证车牌号	String	否
    vehicleLicenseEffectDate?: string; // 	行驶证年检页有效期	String	是
    trailerVehicleLicenseEffectDate?: string; // 	车身行驶证年检页有效期	String	否
    qualificationLicName?: string; // 	从业资格证姓名	String	是
    qualificationLicEffectDate?: string; // 	从业资格证有效期	String	是
}

export class ReqIdCard {
    idCardNo?: string; // 身份证号
    idCardName?: string; // 姓名
    frontIdCardUrl?: string; // 身份证正面照片
    negativeIdCardUrl?: string; // 身份证反面照片
    idCardEffectDate?: string; // 身份证有效期
    haveIdCardNoFlag?: string; // 是否带身份证 0否 1是
}

export class ReqReplenish {
    personCarUrl?: string; // 人车合影照片
    qualificateUrl?: string; // 从业资格证
}
