import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 认证详情接口
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=78545773
 * 对接：
 * */
export class ReqQueryUserLicense extends BaseRequest {
    public type?: string; //查询单个证件类型 1-身份认证，2-驾驶证信息，3-车辆信息，4-资料补充（非必传）
    public async request(): Promise<BaseResponse<EUserLicense>> {
        this.params = {
            type: this.type,
        };
        return super.post('/mms-app/mms/upgrade/queryUserLicense', EUserLicense);
    }
}

export class EUserLicense extends ResultData {
    public isSubmitMap?: {[key: string]: string}; //阶段状态标识
    public idCard?: EIDCard; //  身份认证
    public driverLicense?: EDriverLicense; //   驾驶证信息
    public vehicle?: EVehicle; // 车辆信息
    public isPickUpTheListBySelf?: string; //是否自主摘单 1、是 0、否
    public replenish?: EReplenish; //   资料补充
    public qualification: EQualification;
}

export class EQualification {
    public qualificatePicUrl?: string; // 从业资格证
    public qualificateRiskAudit?: string; //从业资格证审核状态 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    public qualificateUrlAuditContent?: string; // 从业资格证驳回理由
    public qualificationLicName?: string; //  从业资格证姓名
    public qualificationLicEffectDate?: string; //  从业资格证有效期
    public licenseStatus?: string; //    证件状态   1-通过 2-驳回 3-去完成 4-去修改
}

export class EIDCard {
    public idCardNo?: string; //    身份证号
    public idCardName?: string; //  姓名
    public frontIdCardUrl?: string; //  身份证正面照片
    public frontIdCardRiskAudit?: string; //  身份证正面审核状态  0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public frontIdCardRiskAuditContent?: string; // 身份证正面驳回理由
    public negativeIdCardUrl?: string; //   身份证反面照片
    public negativeIdCardRiskAudit?: string; //   身份证反面审核状态  0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public negativeIdCardRiskAuditContent?: string; //   身份证反面驳回理由
    public idCardEffectDate?: string; //    身份证有效期
    public haveIdCardNoFlag?: string; //    是否带身份证 0 否 1 是
    public licenseStatus?: string; //    证件状态   1-通过 2-驳回 3-去完成 4-去修改
    public picStatus?: string; //    0 没有驳回 1 正面驳回 2 反面驳回 3 正面反面都驳回
}

export class EDriverLicense {
    public driverLicUrl?: string; //    驾驶证照片
    public driverLicRiskAudit?: string; //    驾驶证审核状态   0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public driverLicRiskAuditContent?: string; //    驾驶证驳回理由
    public quasiVehicleType?: string; // 准驾车型
    public licenseStatus?: string; // 证件状态  1-通过 2-驳回 3-去完成 4-去修改
    public driverLicNo?: string; // 驾驶证号
    public driverLicEffectDate?: string; // 驾驶证有效期
    public isElectronic?: string; // 是否电子版驾驶证  0-纸质版 1-电子版
}

export class EVehicle {
    public vehicleId?: string; //车辆ID 修改，重新提交时必传
    public vehicleFlag?: string; //车辆类型（车辆类别） 1、普通货车 2、挂车
    public plateColor?: string; //车牌颜色
    public plateNumber?: string; //	车牌号
    public backup1?: string; //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    public backup2?: string; //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    public emissionStandard?: string; //6-国Ⅵ(国六)  5-国Ⅴ(国五)  4-国Ⅳ(国四)  3-国Ⅲ(国三)  2-国Ⅱ(国二)  1-国Ⅰ(国一)

    public vehicleLicenseUrl?: string; //（车头）行驶证
    public vehicleLicenseRiskAudit?: string; //（车头）行驶证 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public vehicleLicenseRiskAuditContent?: string; //（车头）行驶证驳回理由
    public vehicleLicenseEffectDate?: string; //  行驶证年检页有效期

    public annualVehicleLicenseRiskAudit?: string; // 行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public annualVehicleLicenseRiskAuditContent?: string; // 行驶证年检页 驳回理由
    public annualVehicleLicenseUrl?: string; // 行驶证年检页

    public transportLicenseUrl?: string; //	道路运输证   普通货车总质量4.5吨以上 或 挂车 必传
    public transportLicenseRiskAudit?: string; //	道路运输证 审核标识  0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public transportLicenseRiskAuditContent?: string; //	道路运输证 驳回理由
    public transportLicensePlateNumber?: string; //  道路运输证车牌号

    public transportPermissionLicenseUrl?: string; // 	道路运输许可证 （OCR未识别到道路运输证号时必传）
    public transportPermissionRiskAudit?: string; //道路运输许可证审核状态  0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    public transportPermissionAuditContent?: string; // 道路运输许可证驳回理由
    public transportLicenseNo?: string; // 道路运输证号
    public businessLicenceNo?: string; //道路运输证经营许可证号   2025-02-25
    public overloadPlateFrontUrl?: string; //	超限临牌正面照
    public overloadFrontRiskAudit?: string; //	  超限临牌正面照 审核标识  0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public overloadFrontRiskAuditContent?: string; //	超限临牌正面照 驳回理由
    public overloadPlateBackUrl?: string; //超限临牌反面照     挂车，超限车辆必传
    public overloadBackRiskAudit?: string; //超限临牌反面照 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public overloadBackRiskAuditContent?: string; //超限临牌反面照 驳回理由

    public tractorVehicleLicenseUrl?: string; // 牵引车/车头行驶证年检页
    public tractorVehicleLicenseRiskAudit?: string; // 牵引车/车头行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public tractorVehicleLicenseRiskAuditContent?: string; // 牵引车/车头行驶证年检页 驳回理由

    public vehicleBodyLicenseUrl?: string; //（挂车）车身行驶证  挂车，非超限车辆必传）
    public vehicleBodyLicenseRiskAudit?: string; //（挂车）车身行驶证 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public vehicleBodyLicenseRiskAuditContent?: string; //（挂车）车身行驶证驳回理由
    public vehicleBodyLicensePlateNumber?: string; //  车身行驶证车牌号

    public trailerVehicleLicenseUrl?: string; // 车身行驶证年检页
    public trailerVehicleLicenseRiskAudit?: string; // 车身行驶证年检页 审核标识 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public trailerVehicleLicenseRiskAuditContent?: string; // 车身行驶证年检页 驳回理由
    public trailerVehicleLicenseEffectDate?: string; //  车身行驶证年检页有效期

    public qualificatePicUrl?: string; // 从业资格证
    public qualificateRiskAudit?: string; //从业资格证审核状态 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
    public qualificateUrlAuditContent?: string; // 从业资格证驳回理由
    public qualificationLicName?: string; //  从业资格证姓名
    public qualificationLicEffectDate?: string; //  从业资格证有效期

    public personCarUrl1?: string; // 人车合影照片（人脸+车牌号照片）
    public personCarUrl1RiskAudit?: string; // 人车合影照片（人脸+车牌号照片）审核状态 -1、未上传 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public personCarUrl1AuditContent?: string; //人车合影照片（人脸+车牌号照片）驳回理由
    public personCarUrl2?: string; // 人车合影照片（车头车牌+车身照片）
    public personCarUrl2RiskAudit?: string; // 人车合影照片（车头车牌+车身照片）审核状态	-1、未上传 0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public personCarUrl2AuditContent?: string; //人车合影照片（车头车牌+车身照片）驳回理由

    public remark?: string; //备注说明
    public licenseStatus?: string; // 证件状态  1-通过 2-驳回 3-去完成 4-去修改
    public matchCarrierVehicleType?: boolean; //    准驾车型与车辆类型是否匹配
}

export class EReplenish {
    public personCarUrl?: string; //人车合影照片
    public personCarRiskAudit?: string; //人车合影照片审核状态    0、待审核 1、不通过 2、风险通过 3、异常 5、通过
    public personCarRiskAuditContent?: string; //人车合影照片驳回理由
    public qualificateUrl?: string; //从业资格证
    public licenseStatus?: string; // 证件状态  1-通过 2-驳回 3-去完成 4-去修改
    public personCarText?: string; //人车合影提示文案
}

export function showErrorToast(licenseStatus?: string) {
    switch (licenseStatus) {
        case '3': {
            //需要校验相关信息
            return '请先去完成个人信息/车辆信息维护。';
        }
        case '2': {
            //需要校验相关信息
            return '请先去完成个人信息/车辆信息维护。';
        }
        default:
            return '';
    }
}

export function getLicenseTitle(licenseStatus?: string) {
    // 证件状态  1-通过 2-驳回 3-去完成 4-去修改
    console.log('getLicenseTitle', licenseStatus);
    switch (licenseStatus) {
        case '3': {
            return '证件过期上传';
        }
        case '2': {
            return '证件过期上传';
        }
        case '6': {
            return '证件更新上传';
        }
        default: {
            return '身份认证';
        }
    }
}
