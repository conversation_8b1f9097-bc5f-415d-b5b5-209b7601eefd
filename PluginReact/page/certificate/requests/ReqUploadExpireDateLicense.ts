import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {PicUrlBean} from '../models/PicUrlBean';

/**
 * 注释: 过期证件上传
 * 时间: 2025/2/21 星期五 11:22
 * <AUTHOR>
 */
export class ReqUploadExpireDateLicense extends BaseRequest {
    picUrlList: PicUrlBean[];
    targetId?: string;
    newLicenseExpire?: string; //有效期

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            picUrlList: this.picUrlList,
            targetId: this.targetId,
            newLicenseExpire: this.newLicenseExpire,
        };
        return super.post('mms-app/member/uploadExpireDateLicense', ResultData);
    }
}
