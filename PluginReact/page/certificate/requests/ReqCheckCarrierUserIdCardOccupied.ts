import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：CO-5079 【网络货运】承运方APP找回账号流程简化 个体司机身份认证校验身份证是否被占用接口
 * 时间：2025/2/26 14:04
 * 作者：王家辉
 * wiki：http://wiki.zczy56.com/pages/viewpage.action?pageId=65185819
 * 对接：张 嘉豪
 * */
export class ReqCheckCarrierUserIdCardOccupied extends BaseRequest {
    idCard?: string; //身份证号
    public async request(): Promise<BaseResponse<RespCarrierUserIdCardOccupied>> {
        this.params = {};
        return super.post('/mms-app/platform/authentication/checkCarrierUserIdCardOccupied', RespCarrierUserIdCardOccupied);
    }
}

export class RespCarrierUserIdCardOccupied extends ResultData {
    carrierIdCardOccupiedUserInfoResp?: ECarrierUserIdCardOccupied;
}

export class ECarrierUserIdCardOccupied {
    userName?: string; //用户名称
    mobile?: string; //手机号
    occupiedNum: number = 0; //占用数量
}
