import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 角色变更
 * 时间: 2024/12/20 星期五 10:43
 * <AUTHOR>
 */
export class ReqRoleChange extends BaseRequest {
    changeUserType: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {changeUserType: this.changeUserType};
        return super.post('mms-app/member/primaryMemberChangeRole', ResultData);
    }
}
