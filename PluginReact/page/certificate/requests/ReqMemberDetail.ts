import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：
 * 时间：2024/12/9 15:19
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqMemberDetail extends BaseRequest {
    public async request(): Promise<BaseResponse<MemberDetails>> {
        this.params = {};
        return super.post('/mms-app/mms/userSensitiveImage/getMemberDetail', MemberDetails);
    }
}

export class MemberDetails extends ResultData {
    provinceName?: string; //注册省份
    cityName?: string; //注册城市
    areaName?: string; //注册区域
    registerSystem?: string; //注册系统：船运、汽运
    registerChannel?: string; //注册渠道
    userNm?: string; // 用户名
    isEntrustRegister?: string; // 1非法人注册 0 法人注册
    nonLegalIdCardUrl?: string; //非法人注册身份证图片正面
    nonLegalAuthUrl?: string; //非法人注册授权委托书
    legalIdCardUrl?: string; //法人身份证照
    frontIdCardUrl?: string;
    companyName?: string;
    idCardNo?: string;
    roadTransportPermitUrl?: string; // 道路运输证照片
    busiLicUrl?: string; // 营业执照
    vehicleHeight?: string;
    vehicleId?: string;
    trailerNewUrl?: string;
    roadCertificateNum?: string;
    vehicleType?: string;
    negativeIdCardUrl?: string;
    transportUrl?: string; //路运输许可证照片
    triverPermitUrl?: string; ////车头行驶证||行驶证主副页
    personCarUrl?: string; // 人车合影照片
    vehicleLoad?: string;
    mobile?: string;
    vehicleFlag?: string;
    plateNumber?: string;
    userId?: string;
    roadTransportDate?: string;
    customerName?: string; // 用户名称
    idCardEffectDate?: string;
    contacter?: string;
    qualificateNo?: string;
    vehicleLength?: string;
    vehicleWidth?: string;
    contacterPhone?: string;
    businessPermit?: string;
    plantainUrl?: string; // 车前清晰照片
    userType?: string;
    driverLicUrl?: string; // 驾驶证照片
    receiptTemplateUrl?: string; //回单样板照片
    frontIdCardRiskAudit?: string; //身份证正面风险审核标识",
    negativeIdCardRiskAudit?: string; //身份证反面风险审核标识",
    driverLicRiskAudit?: string; //人的驾驶证照片风险审核标识",
    personCarRiskAudit?: string; //人车合照风险审核标识",
    busiLicRiskAudit?: string; //路运输许可证照片风险审核标识",
    transportRiskAudit?: string; //人的道路运输证",
    nonLegalIdCardRiskAudit?: string; //非法人注册身份证图片正面风险审核标识",
    nonLegalAuthRiskAudit?: string; //非法人注册授权委托书风险审核标识",
    legalIdCardRiskAudit?: string; //法人注册授权委托书风险审核标识",
    receiptTemplateRiskAudit?: string; //回单样板照片风险审核标识",
    roadTransportPermitRiskAudit?: string; //道路运输证照片风险审核标识",
    plantainRiskAudit?: string; //车前清晰照片风险审核标识",
    triverPermitRiskAudit?: string; //牵引车行驶证主副页风险审核标识",
    trailerNewRiskAudit?: string; //挂车行驶证主副页风险审核标识"
    notPassReason?: string; //审核未通过原因"
    transportNo?: string; // 道路运输经营许可证号
    remark?: string; //备注说明
    roadTransportPermitRiskAuditText?: string;
    personCarRiskAuditText?: string;
    triverPermitRiskAuditText?: string; ////牵引车行驶证主副页风险审核标识",
    trailerNewRiskAuditText?: string;
    frontIdCardRiskAuditText?: string; //身份证正面风险审核",
    negativeIdCardRiskAuditText?: string; //身份证反面风险审核标识",
    driverLicRiskAuditText?: string; //人的驾驶证照片风险审核标识",
    // 终审补充证件照片（3.0.5需求ZCZY-474新增）
    personCarText?: string;
    driverLicUrlSupplement?: string; //:驾驶证补充照片
    roadTransportPermitSupplement?: string; //:道路运输证补充照片
    drivingLicenseSupplement?: string; //:行驶证补充照片
    carBodyDrivingLicenseSupplement?: string; //:车身行驶证补充照片

    //终审补充证件照片标识（3.0.5需求ZCZY-474新增）
    driverLicUrlSupplementFlag?: string; //// 驾驶证补充照片标识(1：需要补充)
    driverLicUrlSupplementFlagText?: string;
    roadTransportPermitSupplementFlag?: string; ////道路运输证补充照片标识(1：需要补充)
    roadTransportPermitSupplementFlagText?: string;
    drivingLicenseSupplementFlag?: string; ////行驶证补充照片标识(1：需要补充)
    drivingLicenseSupplementFlagText?: string;
    carBodyDrivingLicenseSupplementFlag?: string; //车身行驶证补充照片标识(1：需要补充)
    carBodyDrivingLicenseSupplementFlagText?: string;
    plateNumberColor?: string; // 车牌颜色
    idCardAuditState?: string; //1-审核中
    driverLicAuditState?: string; //1-审核中
    companyNameEditFlag?: string; //0-不能改，其他情况都能改
    taxId?: string; //统一社会信用代码
}
