import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释：查询当前用户是否需要静默签
 * 时间：2024/12/5 15:50
 * 作者：王家辉
 * wiki：
 * 对接：
 * */
export class ReqQueryMemberSilentSignState extends BaseRequest {
    public userId?: string;

    public async request(): Promise<BaseResponse<QueryMemberSilentSignState>> {
        this.params = {};
        return super.post('/mms-app/member/queryMemberSilentSignState', QueryMemberSilentSignState);
    }
}

export class QueryMemberSilentSignState extends ResultData {
    faceAuthState?: string; //人脸认证是否通过，1通过，0未通过
    silentSignState?: string; //静默签是否需要签署，0不需要，1需要
    riskSubmit?: string; //是否申诉过,0否，1是
    faceReason?: string; //实名原因
}
