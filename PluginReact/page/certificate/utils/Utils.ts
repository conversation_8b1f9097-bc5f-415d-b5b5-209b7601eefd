import {Method} from '../../util/NativeModulesTools';
import TextUtils from '../../util/TextUtils';
import {dateParse} from '../../util/DateUtil';

export const canRegister = (idCardNo: string, age: number): boolean => {
    // 获取服务器时间并设置为当天的起始时间
    const systemTime = Method.getSystemTime();
    const sys1 = new Date(systemTime);
    sys1.setHours(0, 0, 0, 0);

    // 检查身份证号是否有效
    if (!TextUtils.isNoEmpty(idCardNo) || idCardNo.length !== 18) {
        // 如果身份证号无效，直接返回 false（或根据业务需求返回 true）
        return false;
    }

    // 从身份证号中提取出生日期
    const birthDateStr = idCardNo.substring(6, 14); // 格式：YYYYMMDD
    const birthYear = parseInt(birthDateStr.substring(0, 4), 10);
    const birthMonth = parseInt(birthDateStr.substring(4, 6), 10) - 1; // 月份从 0 开始
    const birthDay = parseInt(birthDateStr.substring(6, 8), 10);

    // 计算用户达到指定年龄的日期
    const targetDate = new Date(birthYear + age, birthMonth, birthDay);
    targetDate.setHours(0, 0, 0, 0);
    console.log(1111111111111111, sys1, targetDate);
    // 比较服务器时间和目标日期
    return sys1 <= targetDate;
};

export const dateToStamp = (age) => {
    if (TextUtils.isEmpty(age)) {
        return -1;
    } else {
        let parse = dateParse(age, 'yyyyMMdd');
        if (parse == null) {
            return -1;
        } else {
            return parse.getTime();
        }
    }
};

export const isIDCardTakePhote = () => {
    return TextUtils.equals('1', Method.getStringExtra('AUTH_IDCARD_UPLOAD_SWITCH') ?? '');
};
