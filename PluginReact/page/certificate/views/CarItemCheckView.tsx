import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import UIImage from '../../widget/UIImage';

interface CheckViewProps {
  title: string; // 标题
  flag: boolean; // 是否显示必填标志（*）
  edit: boolean; // 是否可编辑
  checkLeftRight: number; // 当前选中的选项（1 或 2）
  leftTxt: string; // 左侧选项文本
  rightText: string; // 右侧选项文本
  onChange: (type: number) => void; // 选项改变回调
}

const CarItemCheckView: React.FC<CheckViewProps> = ({
  title,
  flag,
  edit,
  checkLeftRight,
  leftTxt,
  rightText,
  onChange,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      {flag && <Text style={styles.required}>*</Text>}
      <View style={styles.spacer} />
      <TouchableOpacity
        style={styles.optionContainer}
        onPress={() => {
          edit && onChange(1);
        }}
      >
        <UIImage
          style={styles.checkbox}
          source={checkLeftRight === 1 ? 'lease_checked' : 'lease_unchecked'}
        />
        <Text style={styles.optionText}>{leftTxt}</Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.optionContainer, styles.rightOption]}
        onPress={() => {
          edit && onChange(2);
        }}
      >
        <UIImage
          style={styles.checkbox}
          source={checkLeftRight === 2 ? 'lease_checked' : 'lease_unchecked'}
        />
        <Text style={styles.optionText}>{rightText}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 14,
    backgroundColor: '#fff',
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    color: '#3D3D3D',
    fontSize: 16,
  },
  required: {
    color: '#FF602E',
    fontSize: 20,
    marginLeft: 5,
    fontWeight: 'bold',
  },
  spacer: {
    flex: 1,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightOption: {
    marginLeft: 24,
  },
  checkbox: {
    width: 15,
    height: 15,
  },
  optionText: {
    marginLeft: 8,
    alignSelf: 'center',
    color: '#666666',
    fontSize: 16,
  },
});

export default CarItemCheckView;