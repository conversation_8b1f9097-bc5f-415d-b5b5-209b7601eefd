import React, {ComponentState, useEffect, useImperativeHandle, useState} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import UIImage from '../../widget/UIImage';
import {DialogNewBuilder} from '../../base/Dialog';
import {ReqIntelligentAudit2CacheClear} from '../requests/ReqIntelligentAudit2CacheClear';
import {EUserLicense} from '../requests/ReqQueryUserLicense';
import TextUtils from '../../util/TextUtils';

interface Props {
    callBack: Function;
    shouldNotShift?: boolean;
    data?: EUserLicense;
    chooseType?: string;
}

export type CyrCertificationChooseType = {
    getChooseType: () => string;
};
const CyrCertificationChooseFun = (props: Props, ref: React.Ref<CyrCertificationChooseType>) => {
    const [needSelfOrder, setNeedSelfOrder] = useState('');
    useImperativeHandle(
        ref,
        () => ({
            getChooseType: () => {
                return needSelfOrder;
            },
        }),
        [needSelfOrder],
    );
    useEffect(() => {
        setNeedSelfOrder(props.chooseType ?? '');
    }, [props.chooseType]);
    return (
        <>
            {/* 是否需要自主摘单卡片式选择 */}
            <View style={{backgroundColor: '#fff', borderRadius: 8, padding: 15, paddingBottom: 5, paddingTop: 5, marginTop: 3}}>
                <Text style={{fontSize: 16, fontWeight: 'bold', color: '#333', marginBottom: 15}}>
                    是否需要自主摘单 <Text style={{fontSize: 14, color: '#999'}}>(必填)</Text>
                </Text>
                <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                    {/* 是选项卡片 */}
                    <TouchableOpacity
                        style={{
                            flex: 1,
                            marginRight: 8,
                            padding: 10,
                            borderRadius: 8,
                            backgroundColor: TextUtils.equals('1', needSelfOrder) ? '#E8F0FE' : '#F3F3F3',
                            borderWidth: TextUtils.equals('1', needSelfOrder) ? 0 : 1,
                            borderColor: '#E0E0E0',
                            position: 'relative',
                        }}
                        onPress={() => {
                            if (props.shouldNotShift && !TextUtils.equals('1', needSelfOrder)) {
                                //点自身不应该有响应
                                let dilaog = new DialogNewBuilder();
                                dilaog.title = '温馨提示';
                                dilaog.msg = '切换后您当前维护的车辆证件信息/从业资格证信息将全部清空，是否继续操作？';
                                dilaog.rightTxt = '确认';
                                dilaog.rightOnClick = (dialog) => {
                                    dialog.dismiss();
                                    // 清空缓存
                                    const req = new ReqIntelligentAudit2CacheClear();
                                    req.vehicleId = props.data?.vehicle?.vehicleId;
                                    req.isDelQualification = '1';
                                    req.request().then(() => {});
                                    setNeedSelfOrder('1');
                                    props.callBack && props.callBack('1');
                                };
                                dilaog.show();
                            } else {
                                setNeedSelfOrder('1');
                                props.callBack && props.callBack('1');
                            }
                        }}>
                        {/* 选中状态图标 */}
                        {TextUtils.equals('1', needSelfOrder) && (
                            <View
                                style={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    width: 20,
                                    height: 20,
                                    borderRadius: 10,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                <UIImage source="http://img.zczy56.com/202507220957385275346.png" style={{width: 20, height: 20}} />
                            </View>
                        )}
                        {/* 未选中状态图标 */}
                        {!TextUtils.equals('1', needSelfOrder) && (
                            <View
                                style={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    width: 20,
                                    height: 20,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: '#CCCCCC',
                                    backgroundColor: '#fff',
                                }}
                            />
                        )}
                        <Text
                            style={{
                                fontSize: 18,
                                fontWeight: 'bold',
                                color: '#333',
                                marginBottom: 8,
                            }}>
                            是
                        </Text>
                        <Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                                lineHeight: 16,
                            }}>
                            自有车辆，可自行摘单，运费结算给自己
                        </Text>
                    </TouchableOpacity>

                    {/* 否选项卡片 */}
                    <TouchableOpacity
                        style={{
                            flex: 1,
                            marginLeft: 8,
                            padding: 10,
                            borderRadius: 8,
                            backgroundColor: TextUtils.equals('0', needSelfOrder) ? '#E8F0FE' : '#F3F3F3',
                            borderWidth: TextUtils.equals('0', needSelfOrder) ? 0 : 1,
                            borderColor: '#E0E0E0',
                            position: 'relative',
                        }}
                        onPress={() => {
                            if (props.shouldNotShift && !TextUtils.equals('0', needSelfOrder)) {
                                let dilaog = new DialogNewBuilder();
                                dilaog.title = '温馨提示';
                                dilaog.msg = '切换后您当前维护的车辆证件信息/从业资格证信息将全部清空，是否继续操作？';
                                dilaog.rightTxt = '确认';
                                dilaog.rightOnClick = (dialog) => {
                                    dialog.dismiss();
                                    // 清空缓存
                                    const req = new ReqIntelligentAudit2CacheClear();
                                    req.vehicleId = props.data?.vehicle?.vehicleId;
                                    req.isDelQualification = '1';
                                    req.request().then(() => {});
                                    setNeedSelfOrder('0');
                                    props.callBack && props.callBack('0');
                                };
                                dilaog.show();
                            } else {
                                setNeedSelfOrder('0');
                                props.callBack && props.callBack('0');
                            }
                        }}>
                        {/* 选中状态图标 */}
                        {TextUtils.equals('0', needSelfOrder) && (
                            <View
                                style={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    width: 20,
                                    height: 20,
                                    borderRadius: 10,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                <UIImage source="http://img.zczy56.com/202507220957385275346.png" style={{width: 20, height: 20}} />
                            </View>
                        )}
                        {/* 未选中状态图标 */}
                        {!TextUtils.equals('0', needSelfOrder) && (
                            <View
                                style={{
                                    position: 'absolute',
                                    top: 8,
                                    right: 8,
                                    width: 20,
                                    height: 20,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: '#CCCCCC',
                                    backgroundColor: '#fff',
                                }}
                            />
                        )}
                        <Text
                            style={{
                                fontSize: 18,
                                fontWeight: 'bold',
                                color: '#333',
                                marginBottom: 8,
                            }}>
                            否
                        </Text>
                        <Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                                lineHeight: 16,
                            }}>
                            他人指派运单，运费结算给车老板、物流企业
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </>
    );
};
export const CyrCertificationChooseTypeView = React.forwardRef(CyrCertificationChooseFun);
