import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect} from 'react';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import {gScreen_width} from '../../util/scaled-style';
import Modal from 'react-native-modal';
import UIImageBackground from '../../widget/UIImageBackground';
import TextUtils from '../../util/TextUtils';
import {Method} from '../../util/NativeModulesTools';

interface Props {
    plateNumLen: number;
    onClose?: () => void;
    onChange?: (value: string) => void;
}

/**
 * 注释: 选择车牌号弹窗
 * 时间: 2024/12/31 8:57
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function PlateNumberDialog(props: Props) {
    const [plateNumber, setPlateNumber] = React.useState('');
    const [isChinese, setIsChinese] = React.useState(true);
    const province = ['京', '皖', '闽', '甘', '粤', '桂', '贵', '琼', '冀', '豫', '黑', '鄂', '湘', '吉', '苏', '辽', '蒙', '宁', '青', '鲁', '晋', '陕', '沪', '川', '津', '新', '云', '浙', '渝', '赣', '藏', '港', '澳', '台', '', ''];

    useEffect(() => {
        //清空回到省份
        if (TextUtils.isEmpty(plateNumber)) {
            setIsChinese(true);
        }
    }, [plateNumber]);

    /**
     * 字母数字
     */
    function getNum() {
        let nums: string[] = [];
        for (let i = 0; i < 26; i++) {
            if (!TextUtils.equals(`${String.fromCharCode(65 + i).toUpperCase()}`, 'O') && !TextUtils.equals(`${String.fromCharCode(65 + i).toUpperCase()}`, 'I')) {
                nums.push(`${String.fromCharCode(65 + i).toUpperCase()}`);
            }
        }
        for (let i = 0; i < 12; i++) {
            if (i > 9) {
                if (plateNumber.length >= 6 && props.plateNumLen == 7) {
                    nums.push('挂');
                } else {
                    nums.push('');
                }
            } else {
                nums.push(`${i}`);
            }
        }
        return nums;
    }

    function getCarType() {
        let nums: string[] = ['挂', '超'];
        for (let i = 2; i < 36; i++) {
            nums.push('');
        }
        return nums;
    }
    function renderPlateNumView() {
        let list: any[] = [];
        for (let i = 0; i < props.plateNumLen; i++) {
            if (i === 0) {
                list.push(
                    <View style={styles.itemBlue}>
                        <Text style={styles.textStyle}>{plateNumber.length > 0 ? plateNumber[0] : ''}</Text>
                    </View>,
                );
            } else if (i === 7) {
                list.push(
                    <View style={[styles.itemGray, {marginRight: 0, backgroundColor: '#EDFAE6'}]}>
                        {plateNumber.length >= 8 ? (
                            <Text style={styles.textStyle}>{plateNumber.length >= 8 ? plateNumber[7] : ''}</Text>
                        ) : (
                            <View style={{alignItems: 'center'}}>
                                <Text style={{color: '#6DC269', fontSize: 23}}>+</Text>
                                <Text style={{fontSize: 9, color: '#818181'}}>新能源</Text>
                            </View>
                        )}
                    </View>,
                );
            } else {
                list.push(
                    <View style={styles.itemGray}>
                        <Text style={styles.textStyle}>{plateNumber.length >= i + 1 ? plateNumber[i] : ''}</Text>
                    </View>,
                );
            }
        }
        return list;
    }
    return (
        <Modal
            onBackButtonPress={() => {}} // 响应返回键
            onBackdropPress={() => {}} // 点击背景遮罩层
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}
            style={{justifyContent: 'flex-end', padding: 0}}>
            <View style={{alignItems: 'center'}}>
                <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingVertical: 20, width: gScreen_width, flexDirection: 'row', justifyContent: 'center', alignItems: 'center'}}>
                    {renderPlateNumView().map((item, index) => {
                        return item;
                    })}
                </View>
                <View style={{backgroundColor: '#fff', flexDirection: 'row', alignItems: 'center', width: gScreen_width}}>
                    <Text
                        style={{fontSize: 17, color: '#5086fc', padding: 14}}
                        onPress={() => {
                            props.onClose && props.onClose();
                        }}>
                        取消
                    </Text>
                    <Text style={{fontSize: 17, color: '#333', flex: 1, textAlign: 'center'}}>请选择车牌号</Text>
                    <Text
                        style={{fontSize: 17, color: '#5086fc', padding: 14}}
                        onPress={() => {
                            if (plateNumber.length < 7) {
                                Method.showToast('请输入完整车牌号');
                                return;
                            }
                            props.onChange && props.onChange(plateNumber);
                            props.onClose && props.onClose();
                        }}>
                        确认
                    </Text>
                </View>
                {isChinese ? (
                    <GridView9>
                        {province.map((item, index) => {
                            return index != province.length - 1 ? (
                                <Text
                                    style={styles.gridItem}
                                    onPress={() => {
                                        setPlateNumber(plateNumber + item);
                                        setIsChinese(false);
                                    }}>
                                    {item}
                                </Text>
                            ) : (
                                <UITouchableOpacity
                                    style={styles.gridItem}
                                    onPress={() => {
                                        plateNumber && setPlateNumber(plateNumber.slice(0, plateNumber.length - 1));
                                    }}>
                                    <UIImage source={'driver_peccancy_dialog_delete'} style={{width: 26, height: 19, marginHorizontal: 14}} />
                                </UITouchableOpacity>
                            );
                        })}
                    </GridView9>
                ) : (
                    <GridView9>
                        {(plateNumber.length >= 6 && props.plateNumLen == 7 ? getCarType() : getNum()).map((item, index) => {
                            return index != province.length - 1 ? (
                                <Text
                                    style={styles.gridItem}
                                    onPress={() => {
                                        if (TextUtils.isNoEmpty(item) && (plateNumber + item).length <= props.plateNumLen) {
                                            setPlateNumber(plateNumber + item);
                                        }
                                    }}>
                                    {item}
                                </Text>
                            ) : (
                                <UITouchableOpacity
                                    style={styles.gridItem}
                                    onPress={() => {
                                        plateNumber && setPlateNumber(plateNumber.slice(0, plateNumber.length - 1));
                                    }}>
                                    <UIImage source={'driver_peccancy_dialog_delete'} style={{width: 26, height: 19, marginHorizontal: 14}} />
                                </UITouchableOpacity>
                            );
                        })}
                    </GridView9>
                )}
            </View>
        </Modal>
    );
}

export const GridView9 = ({children}) => {
    let numColumns = 6;
    const columnCount = Math.ceil(children.length / numColumns);
    return (
        <View style={styles.grid}>
            {[...Array(columnCount)].map((_, i) => (
                <View key={i} style={styles.column}>
                    {children.slice(i * numColumns, (i + 1) * numColumns)}
                </View>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    grid: {
        backgroundColor: '#EFF0F3',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        width: gScreen_width,
    },
    column: {
        flexDirection: 'row',
    },
    gridItem: {
        flex: 1,
        backgroundColor: '#fff',
        margin: 0.5,
        fontSize: 17,
        color: '#555',
        paddingTop: 10,
        paddingBottom: 10,
        textAlign: 'center',
    },
    itemGray: {backgroundColor: '#F7F7F7', width: 34, height: 45, borderRadius: 5, marginRight: 8, alignItems: 'center', justifyContent: 'center'},
    itemBlue: {backgroundColor: '#EBF5FD', width: 34, height: 45, borderRadius: 5, marginRight: 8, alignItems: 'center', justifyContent: 'center'},
    textStyle: {
        fontSize: 18,
        color: '#333333',
    },
});
