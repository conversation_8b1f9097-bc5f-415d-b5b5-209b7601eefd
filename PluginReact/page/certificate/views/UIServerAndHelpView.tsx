import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {Method} from '../../util/NativeModulesTools';
import UIImage from '../../widget/UIImage';
import {http} from '../../const.global';
import { onEvent } from '../../base/native/UITrackingAction';

interface Props {
    style?: StyleProp<ViewStyle>;
    letfText?: string;
    rightText?: string;
}

/**
 * 注释:
 * 时间: 2024/12/17 9:40
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function UIServerAndHelpView(props: Props) {
    return (
            <UITouchableOpacity
                activeOpacity={1}
                style={{flexDirection: 'row', alignItems: 'center'}}
                onPress={() => {
                    Method.openWeb('认证帮助', `${http.url()}/form_h5/order/index.html?_t=${new Date().getTime()}#/idAuthentication?type=2`);
                    onEvent({pageId: 'sfzscy', tableId: 'sfzscy_rzydwt',event: 'click'});
                }}>
                <UIImage source={'driver_certificaiton_regiest_wenhao'} style={{width: 12, height: 12, marginRight: 7}} />
                <Text style={{fontSize: 13, color: '#FF602E'}}>{props.rightText ?? '认证帮助'}</Text>
            </UITouchableOpacity>
    );
}

