import {Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import UIImage from '../../widget/UIImage';
import UIListView from '../../widget/UIListView';
import UIImageBackground from '../../widget/UIImageBackground';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import CertificateReactExtension from '../CertificateReactExtension';
import EventBus from '../../util/EventBus';
import {Constant} from '../../base/Constant';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {ReqQueryMemberLicenseList} from '../requests/ReqQueryMemberLicenseList';
import {LicenseList} from '../models/ResMemberLicenseList';

interface Props {}

/**
 * 注释:  会员证件管理
 * 时间: 2024/8/27 0027 11:33
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function MembershipIDCardView(props: Props) {
    //会员状态
    const [statue, setStatue] = useState<string>('ic_tag_zc');
    //证件列表
    const [licenseList, setLicenseList] = useState<Array<LicenseList>>([]);
    //页面初始化
    useEffect(() => {
        queryMemberLicenseList();
        EventBus.getInstance().addListener(Constant.event_certification_refresh, queryMemberLicenseList);
        return () => {
            EventBus.getInstance().removeListener(queryMemberLicenseList);
        };
    }, []);

    /**
     * 注释:
     * 时间: 2024/8/27 0027 16:19
     * <AUTHOR>
     */
    const queryMemberLicenseList = () => {
        let request = new ReqQueryMemberLicenseList();
        request.request().then((res) => {
            if (res.isSuccess() && res.data != null) {
                //会员状态
                if (res.data.status == 0) {
                    setStatue('ic_tag_zc');
                } else if (res.data.status == 2) {
                    setStatue('ic_tag_dj');
                } else if (res.data.status == 3) {
                    setStatue('ic_tag_yc');
                }
                //刷新列表
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh_subscript, null);
                setLicenseList(res.data.licenseList ?? []);
            }
        });
    };

    /**
     * 注释: 绘制头部视图
     * 时间: 2024/8/27 0027 16:33
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderStatusView() {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: '#fff',
                    paddingHorizontal: 15,
                    paddingVertical: 10,
                }}>
                <Text style={{fontSize: 18, color: '#3D3D3D', fontWeight: 'bold'}}>会员状态</Text>
                <UIImage source={statue} style={{width: 55, height: 21}} />
            </View>
        );
    }

    /**
     * 注释: 绘制ItemView
     * 时间: 2024/8/27 0027 16:52
     * <AUTHOR>
     * @param item
     * @returns {JSX.Element}
     */
    function renderItemView(item: LicenseList) {
        let licenseStatusText;
        let licenseBackImg;
        if (item.licenseStatus == 0) {
            licenseStatusText = '查看 >';
            licenseBackImg = 'img_mask_zc';
        } else if (item.licenseStatus == 5) {
            licenseStatusText = '不通过 >';
            licenseBackImg = 'img_mask_yc';
        } else if (item.licenseStatus == 6) {
            if (item.licenseCode == '101' || item.licenseCode == '102') {
                licenseStatusText = '查看 >';
            } else {
                licenseStatusText = '可更新 >';
            }
            licenseBackImg = 'img_mask_zc';
        } else if (item.licenseStatus == 7) {
            licenseStatusText = '审核中...';
            licenseBackImg = 'img_mask_zc';
        } else {
            licenseStatusText = `${item.licenseStatusText} >`;
            licenseBackImg = 'img_mask_yc';
        }
        let licenseImg;
        if (item.licenseCode == '101') {
            licenseImg = 'ic_sfz';
        } else if (item.licenseCode == '102') {
            licenseImg = 'ic_jsz';
        } else {
            licenseImg = 'ic_cyzgz';
        }
        return (
            <UITouchableOpacity onPress={() => onItemClick(item)}>
                <UIImageBackground
                    source={licenseBackImg}
                    resizeMode={'stretch'}
                    style={{
                        marginHorizontal: 15,
                        marginTop: 15,
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingHorizontal: 15,
                            paddingVertical: 20,
                        }}>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <UIImage source={licenseImg} style={{width: 23, height: 23}} />
                            <Text style={{fontSize: 17, color: '#333', marginLeft: 6}}>{item.licenseName}</Text>
                        </View>
                        {/*非审核中的证件*/}
                        {item.licenseStatus != 7 && (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                {item.licenseStatus != 0 && item.licenseStatus != 6 && <UIImage source={'ic_warning'} style={{width: 14, height: 14}} />}
                                <Text
                                    style={{
                                        fontSize: 13,
                                        color: item.licenseStatus == 0 || item.licenseStatus == 6 ? '#5086FC' : '#f00',
                                        marginLeft: 5,
                                    }}>
                                    {licenseStatusText}
                                </Text>
                            </View>
                        )}
                        {/*审核中的证件*/}
                        {item.licenseStatus == 7 && (
                            <View style={{flexDirection: 'column'}}>
                                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                    <UIImage source={'ic_waiting'} style={{width: 14, height: 14}} />
                                    <Text
                                        style={{
                                            color: '#5086FC',
                                            fontSize: 13,
                                            marginLeft: 5,
                                        }}>
                                        {licenseStatusText}
                                    </Text>
                                </View>
                                <Text style={{fontSize: 13, color: '#5086FC', marginTop: 5}}>{'资料补充 >'}</Text>
                            </View>
                        )}
                    </View>
                </UIImageBackground>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 预览证件信息
     * 时间: 2024/11/21 星期四 17:21
     * <AUTHOR>
     * @param item
     */
    function perViewCardInfo(item: LicenseList) {
        if (item.licenseCode == '210') {
            //证明材料
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ProofMaterialsPreViewPage',
                data: {
                    urlList: item.urlList,
                    label: item.licenseName,
                },
            });
        } else {
            //其他证件
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'CertificateMaterialsPreViewPage',
                data: {
                    urlList: item.urlList,
                    label: item.licenseName,
                    licenseCode: item.licenseCode,
                    licenseStatus: item.licenseStatus,
                },
                callBack: () => {
                    CertificateReactExtension.skipMemberCertificateUpdate(item);
                },
            });
        }
    }

    /**
     * 注释: Item点击事件
     * 时间: 2024/8/29 0029 16:24
     * <AUTHOR>
     * @param item
     */
    function onItemClick(item: LicenseList) {
        //证件正常统一跳转查看页
        if (item.licenseStatus == 0) {
            perViewCardInfo(item);
            return;
        }
        if (item.licenseStatus == 6 && (item.licenseCode == '101' || item.licenseCode == '102')) {
            perViewCardInfo(item);
            return;
        }
        //证件异常跳转会员证件处理页
        CertificateReactExtension.skipMemberCertificateUpdate(item);
    }

    return (
        <View style={{flex: 1, backgroundColor: '#fff'}}>
            {/*会员状态*/}
            {renderStatusView()}
            {/*证件列表*/}
            <UIListView
                renderItem={({item}) => renderItemView(item)}
                dataList={licenseList}
                onRefresh={() => {
                    queryMemberLicenseList();
                }}
            />
        </View>
    );
}
