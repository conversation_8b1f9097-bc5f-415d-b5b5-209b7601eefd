import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import UIImage from '../../widget/UIImage';
import {gScreen_width} from '../../util/scaled-style';
import Modal from 'react-native-modal';
import UIButton from '../../widget/UIButton';

interface Props {
    onClose?: Function;
    onOkAction?: Function;
    typeStr?: string;
    imgSrc?: string;
}

/**
 * 注释: 行驶证OCR识别提示
 * 时间: 2025/1/6 16:22
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function VehiclePerfectOcrTipDialog(props: Props) {
    return (
        <Modal
            onBackButtonPress={() => props.onClose && props.onClose()} // 响应返回键
            onBackdropPress={() => props.onClose && props.onClose()} // 点击背景遮罩层
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}>
            <View style={styles.body}>
                <UIImage source={props.imgSrc ?? 'icon_com_driver_licenseocrtip_dialog_bg1'} style={{width: gScreen_width * 0.75, height: 160, marginTop: 5}} resizeMode="stretch" />
                <Text style={{fontSize: 16, color: '#333', paddingHorizontal: 20, paddingTop: 12}}>
                    请上传一张<Text style={{color: '#FF4D4D'}}>{props.typeStr ?? ''}</Text>(包含：<Text style={{color: '#FF4D4D'}}>主+副页</Text>）的驾驶证照片，确保清晰无遮挡
                </Text>
                <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', margin: 20}}>
                    <UIButton text={'忽略提示'} style={{marginRight: 7}} fontSize={17} textColor={'#666'} fontWeight={true} backgroundColor={'#fff'} borderColor={'#666'} height={45} width={120} onPress={() => props.onClose && props.onClose()} />
                    <UIButton
                        text={'重新上传'}
                        fontSize={17}
                        backgroundColor={'#FF4D44'}
                        height={45}
                        width={120}
                        onPress={() => {
                            props.onOkAction && props.onOkAction();
                            props.onClose && props.onClose();
                        }}
                    />
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    body: {
        width: gScreen_width * 0.8,
        backgroundColor: '#fff',
        borderRadius: 10,
        paddingBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        flexDirection: 'column',
    },
    title: {
        padding: 15,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        backgroundColor: '#F2F8FF',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
});
