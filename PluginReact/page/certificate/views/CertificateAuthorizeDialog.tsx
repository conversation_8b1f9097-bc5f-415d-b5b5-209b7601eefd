import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import UIDialog from '../../widget/UIDialog';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {Method} from '../../util/NativeModulesTools';

interface Props {
    dismiss?: Function;
}

/**
 * 注释: 从业资格证弹窗
 * 时间: 2024/12/20 星期五 13:52
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function CertificateAuthorizeDialog(props: Props) {
    return (
        <UIDialog
            title={'个人信息委托处理告知同意书'}
            views={
                <Text style={styles.mainText}>
                    尊敬的用户，感谢您对中储智运（下称“我们”）的信任。<Text>{`\n请您知悉，为了核验您上传的道路运输从业人员资格证的真实性、有效性，我们将把您的`}</Text>
                    <Text style={styles.tipText}>道路运输从业人员资格证</Text>
                    提供给我们委托的第三方机构，由该第三方机构对其真实性、有效性进行核验。
                    <Text>{`\n您理解并同意，道路从业人员资格证可能构成您的敏感个人信息，如您拒绝我们将您的道路从业人员资格证提供给第三方机构进行核验的，您将无法完成司机身份认证，但这不影响您使用我们所提供的其他功能。\n关于我们如何处理和保护您的个人信息，请见`}</Text>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openWebView('https://api.zczy56.com/form_h5/documents/privacy.html', '隐私政策');
                        }}>
                        <Text style={{color: '#5086FC', fontSize: 13}}>《中储智运隐私政策》</Text>
                    </UITouchableOpacity>
                </Text>
            }
            cancelTxt={'不同意'}
            okTxt={'我已充分理解并同意'}
            leftStyle={{fontSize: 14}}
            rightStyle={{fontSize: 14}}
            leftOnClick={() => {
                props.dismiss && props.dismiss();
            }}
            rightOnClick={() => {
                let login = Method.getLogin();
                Method.putStringExtra(`cyzgz_${login.userId}`, '1');
                props.dismiss && props.dismiss();
            }}
        />
    );
}

const styles = StyleSheet.create({
    mainText: {
        color: '#666',
        fontSize: 13,
        paddingHorizontal: 10,
        paddingVertical: 5,
    },
    tipText: {
        color: '#333',
        fontSize: 16,
    },
});
