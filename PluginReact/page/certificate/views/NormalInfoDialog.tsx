import {StyleSheet, Text, View} from 'react-native';
import React, {ReactNode} from 'react';
import Modal from 'react-native-modal';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import UIButton from '../../widget/UIButton';

interface Props {
    title?: string;
    content?: string;
    selectAlbum?: () => void;
    selectCamera?: () => void;
    onClose?: () => void;
    showLeftBtn: boolean;
    contentViews?: ReactNode;
}

/**
 * 注释: 一般通用 信息展示 dilaog 淡蓝色顶 有X 下面 确认取消（可自定义） 中间需要自己填充展
 * 时间: 2024/12/9 11:29
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function NormalInfoDialog(props: Props) {
    return (
        <Modal
            style={{justifyContent: 'center', padding: 0}}
            animationIn={'slideInUp'}
            backdropOpacity={0.3}
            useNativeDriver={true}
            isVisible={true}
            onBackButtonPress={() => {
                props.onClose && props.onClose();
            }} // 响应返回键
            onBackdropPress={() => {
                props.onClose && props.onClose();
            }} //点击阴影部分
        >
            <View>
                <View
                    style={{
                        backgroundColor: '#E6F2FF',
                        minHeight: 85,
                        borderTopLeftRadius: 8,
                        borderTopRightRadius: 8,
                        paddingHorizontal: 15,
                        paddingBottom: 15,
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 15}}>
                        <Text style={{color: '#5086fc', fontSize: 18, flex: 1, marginTop: 7, textAlign: 'center'}} ellipsizeMode={'tail'} numberOfLines={1}>
                            {props.title ?? '标题'}
                        </Text>
                        <UITouchableOpacity
                            onPress={() => {
                                props.onClose && props.onClose();
                            }}
                            style={{padding: 12, position: 'absolute', right: -12}}>
                            <UIImage source={'base_close_x_blue'} style={{width: 12, height: 12}} />
                        </UITouchableOpacity>
                    </View>
                    {props.contentViews ?? <Text style={{fontSize: 14, color: '#999', marginTop: 3}}>{props.content ?? ''}</Text>}
                </View>
                <View
                    style={{
                        backgroundColor: '#fff',
                        borderBottomLeftRadius: 8,
                        borderBottomRightRadius: 8,
                        paddingVertical: 8,
                        paddingHorizontal: 30,
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                    }}>
                    {props.showLeftBtn && <UIButton text={'从相册选择'} height={38} width={120} backgroundColor={'#fff'} textColor={'#5086fc'} onPress={() => props.selectAlbum && props.selectAlbum()} />}
                    <UIButton text={'拍照'} height={38} width={120} onPress={() => props.selectCamera && props.selectCamera()} />
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({});
