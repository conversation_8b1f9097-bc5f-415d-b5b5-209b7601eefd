import React, {memo} from 'react';
import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {gScreen_width} from '../../util/scaled-style';
import TextUtils from '../../util/TextUtils';
import UIImage from '../../widget/UIImage';
import type {Props as UIImageSigleFileProps} from '../../widget/UIImageSingleFileView';
import UIImageSigleFileView from '../../widget/UIImageSingleFileView';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

export type IngProps = {
    title?: string; //标题
} & UIImageSigleFileProps;

/**
 * 功能描述: 认证相关 上传图片
 * <AUTHOR>
 * @date 2022/7/4-16:22
 */
export type Props = {
    ivWarn?: boolean; //显示警告图标
    tvTitle1?: string; //标题
    tvTitle1View?: React.ReactNode; //标题
    tvTitle2?: string; //副标题
    tvDesc?: string; //说明
    tvRedAsterisk?: boolean;
    tvTitle3?: string; //模板提示文字
    showTemplateImg?: boolean; //是否显示模板
    onClickImgTemplate?: () => void; //点击模板
    onDownLoadFile?: () => void; //范本下载
    auditText?: string | string[]; //驳回理由
    showViewBg?: boolean; //是否显示背景颜色
    initBgColor?: string[]; //自定义背景颜色
    leftImg?: IngProps; //左侧图片
    rightImg?: IngProps; //右侧图片
    layout?: number; //设置上传图片控件布局方向 1 横向 2 纵向
    tvDescView?: React.ReactNode; //描述视图
    style?: StyleProp<ViewStyle>;
};

const ComCertificationUploadImgView: React.FC<Props> = ({ivWarn = false, showViewBg = false, tvRedAsterisk = false, layout = 1, ...restProps}) => {
    // 图片宽度 高度
    let imagWidth = layout == 1 && restProps.leftImg && restProps.rightImg ? (gScreen_width - 14) * 0.47 : gScreen_width * 0.92;
    let imagHeigth = layout == 1 && restProps.leftImg && restProps.rightImg ? 110 : 165;

    // 驳回理由
    const showAuditView = () => {
        return (
            <View style={styles.audidView}>
                {restProps.auditText instanceof Array ? (
                    restProps.auditText.map((item, index) => (
                        <Text style={styles.auditTxt} key={item.toString() + index}>
                            {item}
                        </Text>
                    ))
                ) : (
                    <Text style={styles.auditTxt}>{restProps.auditText}</Text>
                )}
            </View>
        );
    };

    return (
        <LinearGradient colors={showViewBg ? (restProps.initBgColor ? restProps.initBgColor : ['#FFF3EF', '#FFFFFF']) : ['#FFFFFF00', '#FFFFFF00']} useAngle={true} angle={180} style={restProps.style}>
            {/*头部*/}
            {(TextUtils.isNoEmpty(restProps.tvTitle1) || TextUtils.isNoEmpty(restProps.tvTitle2) || TextUtils.isNoEmpty(restProps.tvTitle3) || TextUtils.isNoEmpty(restProps.tvTitle1View)) && (
                <View style={styles.title}>
                    <View style={{flexDirection: 'row', alignItems: 'center', flex: 1}}>
                        {ivWarn && <UIImage source="base_warning" style={styles.title_warning} />}
                        {restProps.tvTitle1View ? restProps.tvTitle1View : <Text style={styles.title1}>{restProps.tvTitle1}</Text>}
                        {TextUtils.isNoEmpty(restProps.tvTitle2) && <Text style={styles.title2}>{restProps.tvTitle2}</Text>}
                        {tvRedAsterisk && <Text style={styles.asterisk}>{'*'}</Text>}
                        {TextUtils.isNoEmpty(restProps.tvTitle3) && (
                            <Text style={styles.tvTitle3} onPress={restProps.onDownLoadFile}>
                                {restProps.tvTitle3}
                            </Text>
                        )}
                    </View>
                    {restProps.showTemplateImg && (
                        <UITouchableOpacity style={{position: 'absolute', top: 5, right: -5}} onPress={() => restProps.onClickImgTemplate && restProps.onClickImgTemplate()}>
                            <UIImage source="icon_templte" style={styles.icon_templte} />
                        </UITouchableOpacity>
                    )}
                </View>
            )}
            {/*描述视图*/}
            {restProps.tvDescView ? restProps.tvDescView : restProps.tvDesc && <Text style={styles.tvDesc}>{restProps.tvDesc}</Text>}
            {/*证件上传视图*/}
            <View style={layout == 1 ? styles.img_body : styles.img_body2}>
                {restProps.leftImg && (
                    <View style={styles.img_content}>
                        <UIImageSigleFileView {...restProps.leftImg} imgStyle={[{width: imagWidth, height: imagHeigth}, restProps.leftImg.imgStyle]} />
                        {TextUtils.isNoEmpty(restProps.leftImg.title) && <Text style={styles.img_title}>{restProps.leftImg.title}</Text>}
                    </View>
                )}
                {restProps.leftImg && restProps.rightImg && <View style={{width: 10}} />}
                {restProps.rightImg && (
                    <View style={styles.img_content}>
                        <UIImageSigleFileView {...restProps.rightImg} imgStyle={[{width: imagWidth, height: imagHeigth}, restProps.rightImg.imgStyle]} />
                        {TextUtils.isNoEmpty(restProps.rightImg.title) && <Text style={styles.img_title}>{restProps.rightImg.title}</Text>}
                    </View>
                )}
            </View>
            {TextUtils.isNoEmpty(restProps.auditText) && showAuditView()}
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    title: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    title_warning: {
        height: 20,
        width: 20,
        marginRight: 10,
    },
    title1: {
        fontSize: 15,
        color: '#3d3d3d',
        fontWeight: 'bold',
    },
    title2: {fontSize: 13, color: '#3d3d3d'},
    asterisk: {fontSize: 18, color: '#ff602e', fontWeight: 'bold'},
    tvTitle3: {fontSize: 13, color: '#5086fc', marginRight: 10, flex: 1, textAlign: 'right'},

    icon_templte: {width: 64, height: 20, marginRight: 10},
    tvDesc: {fontSize: 13, color: '#666', marginTop: 5},
    img_body: {flexDirection: 'row', alignItems: 'center', marginTop: 5},
    img_body2: {alignItems: 'center', marginTop: 5},
    img_content: {alignItems: 'center', marginTop: 5},
    img_title: {fontSize: 12, color: '#999', marginTop: 5},
    audidView: {justifyContent: 'center', marginTop: 5, flexWrap: 'nowrap'},
    auditTxt: {fontSize: 12, color: '#fd5353'},
});

export default memo(ComCertificationUploadImgView);
