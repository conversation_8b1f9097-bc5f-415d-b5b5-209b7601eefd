import {StyleProp, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import UIImage from '../../widget/UIImage';
import UIButton from '../../widget/UIButton';
import {Constant} from '../../base/Constant';
import {ImageStyle} from 'react-native-fast-image';

interface Props {
    image: string;
    mainTip: string;
    subTip?: string;
    imageStyle?: StyleProp<ImageStyle> & StyleProp<ViewStyle>;
    onCancel?: Function;
    showContinue?: boolean;
    onResetUpload?: Function;
    onContinueUpload?: Function;
}

/**
 * 注释: OCR识别失败弹窗
 * 时间: 2024/12/9 星期一 13:52
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function OcrFailTipDialog(props: Props) {
    return (
        <Modal
            onBackButtonPress={() => {
                props.onCancel && props.onCancel();
            }} // 响应返回键
            onBackdropPress={() => {
                props.onCancel && props.onCancel();
            }} // 点击背景遮罩层
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}>
            <View style={{flexDirection: 'column', backgroundColor: '#fff', borderRadius: 12}}>
                <UIImage source={props.image} style={[{width: '100%', height: 176}, props.imageStyle]} resizeMode={'stretch'} />
                <View
                    style={{
                        backgroundColor: '#fff',
                        padding: 20,
                        borderBottomLeftRadius: 12,
                        borderBottomRightRadius: 12,
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    <Text style={styles.mainStyle}>{props.mainTip}</Text>
                    <Text style={{fontSize: 13, color: '#666'}}>{props.subTip ?? '确保四角完整、亮度均匀、照片清晰'}</Text>
                    <UIButton
                        text={'重新上传证件'}
                        style={styles.mainButtonStyle}
                        onPress={() => {
                            props.onResetUpload && props.onResetUpload();
                        }}
                    />
                    {props.showContinue && (
                        <UIButton
                            text={'我知道了，仍要提交'}
                            textColor={'#666'}
                            style={styles.continueButtonStyle}
                            onPress={() => {
                                props.onContinueUpload && props.onContinueUpload();
                            }}
                        />
                    )}
                    {props.showContinue && <Text style={styles.tipStyle}>当前证件信息有误直接提交证件会有审核不通过的风险！</Text>}
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    mainStyle: {
        fontSize: 15,
        color: '#333',
        fontWeight: 'bold',
        marginBottom: 6,
    },
    tipStyle: {
        fontSize: 12,
        color: '#F4615A',
        marginTop: 12,
        alignSelf: 'center',
        textAlign: 'center',
    },
    continueButtonStyle: {
        height: 40,
        width: '100%',
        backgroundColor: '#fff',
        borderWidth: 0.5,
        borderColor: '#CCC',
        marginTop: 6,
    },
    mainButtonStyle: {
        backgroundColor: '#F4615A',
        height: 40,
        marginTop: 20,
        borderColor: '#F4615A',
        width: '100%',
    },
});
