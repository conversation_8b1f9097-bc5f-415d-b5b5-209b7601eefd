import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import UIImageBackground from '../../widget/UIImageBackground';
import UIImage from '../../widget/UIImage';
import UIButton from '../../widget/UIButton';

interface Props {
    onClose?: () => void;
    title?: string;
    content?: string;
    cancelable?: boolean;
    onRightCallback?: Function;
    data?: any;
}

/**
 * 注释: 23506 【安卓】【网络货运】承运方APP找回账号流程简化
 * 时间: 2025/2/26 14:14
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function CertificationForFriverExistSuccessDailog(props: Props) {
    const cancelable = props.cancelable ?? true;
    return (
        <Modal
            onBackButtonPress={props.onClose} // 响应返回键
            onBackdropPress={() => {
                cancelable && props.onClose && props.onClose();
            }} // 点击背景遮罩层
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}>
            <UIImageBackground source={'com_certification_for_driver_exit_dialog_bg'} style={{borderRadius: 8}}>
                <Text
                    style={{
                        fontSize: 18,
                        color: '#333',
                        fontWeight: 'bold',
                        marginTop: 25,
                        marginBottom: 25,
                        alignSelf: 'center',
                    }}>
                    提示
                </Text>
                <Text
                    style={{
                        fontSize: 13,
                        color: '#333',
                        padding: 20,
                        alignSelf: 'center',
                    }}>
                    {props.content}
                </Text>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        paddingHorizontal: 10,
                        marginTop: 55,
                        paddingVertical: 10,
                    }}>
                    <UIButton
                        text={'返回登录'}
                        style={{alignItems: 'center'}}
                        width={150}
                        height={35}
                        borderRadius={25}
                        subFontSize={11}
                        onPress={() => {
                            props.onRightCallback && props.onRightCallback(props.data);
                        }}
                    />
                </View>
            </UIImageBackground>
        </Modal>
    );
}

const styles = StyleSheet.create({});
