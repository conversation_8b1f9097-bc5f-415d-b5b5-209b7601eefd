import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import UIPopup from '../../widget/UIPopup';
import UISearchView from '../../widget/UISearchView';
import {ReqQueryCompanyInfo, RspCompanyInfo} from '../requests/ReqQueryCompanyInfo';
import {ArrayUtils} from '../../util/ArrayUtils';
import UIListView from '../../widget/UIListView';
import TextUtils from '../../util/TextUtils';
import {Method} from '../../util/NativeModulesTools';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

interface Props {
    onClose?: Function;
    onSelect?: Function;
}

/**
 * 注释: 选择企业弹窗
 * 时间: 2024/12/24 星期二 10:04
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function SelectCompanyInfoDialog(props: Props) {
    // 企业列表
    const [companyInfoList, setCompanyInfoList] = useState<Array<RspCompanyInfo>>([]);
    // 初始化
    useEffect(() => {
        queryCompanyInfo({});
    }, []);

    /**
     *  注释: 搜索企业信息
     * 时间: 2024/12/24 星期二 11:08
     * <AUTHOR>
     * @param companyName
     * @param nowPage
     * @param pageSize
     */
    function queryCompanyInfo({companyName = '', nowPage = 1, pageSize = 200}) {
        Method.showWaitDialog();
        let req = new ReqQueryCompanyInfo();
        if (TextUtils.isNoEmpty(companyName)) {
            req.companyName = companyName;
        }
        req.nowPage = nowPage;
        req.pageSize = pageSize;
        req.request().then((response) => {
            Method.hideWaitDialog();
            if (response.isSuccess() && ArrayUtils.isNoEmpty(response.data?.rootArray)) {
                setCompanyInfoList(response.data?.rootArray ?? []);
            } else {
                setCompanyInfoList([]);
            }
        });
    }

    /**
     * 注释: 绘制Item
     * 时间: 2024/12/24 星期二 11:22
     * <AUTHOR>
     * @param item
     * @returns {JSX.Element}
     */
    function renderItem(item: RspCompanyInfo) {
        return (
            <UITouchableOpacity
                style={styles.itemContainStyle}
                onPress={() => {
                    props.onSelect && props.onSelect(item);
                    props.onClose && props.onClose();
                }}>
                <Text style={styles.itemStyle}>{item.businessLicenseName}</Text>
            </UITouchableOpacity>
        );
    }

    return (
        <UIPopup
            title={'填写企业名称'}
            showSubTitle={false}
            onClose={() => {
                props.onClose && props.onClose();
            }}>
            <View style={{backgroundColor: '#fff', flexDirection: 'column'}}>
                <UISearchView
                    clear={true}
                    placeholder={'搜索企业名称'}
                    onSubmitEditing={(name: string) => {
                        queryCompanyInfo({companyName: name});
                    }}
                />
                <View style={{height: 350}}>
                    <UIListView renderItem={(item) => renderItem(item.item)} dataList={companyInfoList} />
                </View>
            </View>
        </UIPopup>
    );
}

const styles = StyleSheet.create({
    itemStyle: {
        fontSize: 14,
        color: '#333',
        padding: 7,
        fontWeight: 'bold',
    },
    itemContainStyle: {backgroundColor: '#fff', minHeight: 50, flexDirection: 'row', paddingLeft: 15},
});
