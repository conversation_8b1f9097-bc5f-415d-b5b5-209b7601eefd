import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {ReqQueryMemberVehicleLicenseList} from '../requests/ReqQueryMemberVehicleLicenseList';
import {VehicleList} from '../models/ResMemberVehicleLicenseList';
import UIListView from '../../widget/UIListView';
import {LicenseList} from '../models/ResMemberLicenseList';
import UIImageBackground from '../../widget/UIImageBackground';
import UIImage from '../../widget/UIImage';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import EventBus from '../../util/EventBus';
import {Constant} from '../../base/Constant';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import TextUtils from '../../util/TextUtils';

interface Props {}

/**
 * 注释: 车辆证件管理
 * 时间: 2024/8/27 0027 11:39
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function VehicleIdCardView(props: Props) {
    const [vehicleList, setVechicleList] = useState<Array<VehicleList>>([]);

    //页面初始化
    useEffect(() => {
        queryMemberVehicleLicenseList();
        EventBus.getInstance().addListener(Constant.event_certification_refresh, queryMemberVehicleLicenseList);
        //司机摘单车辆证件跳转
        const listener = ({vehicleId: vehicleId}) => {
            let vehicle = new VehicleList();
            if (TextUtils.isNoEmpty(vehicle)) {
                vehicle.vehicleId = vehicleId;
                onItemClick(vehicle);
            }
        };
        EventBus.getInstance().addListener(Constant.event_certification_cyr_pick_vehicle_license, listener);
        return () => {
            EventBus.getInstance().removeListener(queryMemberVehicleLicenseList);
            EventBus.getInstance().removeListener(listener);
        };
    }, []);

    /**
     * 注释:车辆证件列表查询接口
     * 时间: 2024/8/28 0028 9:33
     * <AUTHOR>
     */
    const queryMemberVehicleLicenseList = () => {
        let request = new ReqQueryMemberVehicleLicenseList();
        request.request().then((res) => {
            if (res.isSuccess()) {
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh_subscript, null);
                setVechicleList(res.data?.vehicleList ?? []);
            }
        });
    };

    /**
     * 注释: 绘制ItemView
     * 时间: 2024/8/27 0027 16:52
     * <AUTHOR>
     * @param item
     * @returns {JSX.Element}
     */
    function renderItemView(item: VehicleList) {
        let licenseStatusText;
        let licenseBackImg;
        if (item.status == 0) {
            licenseStatusText = item.vehicleLicensePendingNum == 0 ? '正常 >' : `正常(${item.vehicleLicensePendingNum}) >`;
            licenseBackImg = 'img_mask_zc';
        } else if (item.status == 1) {
            licenseStatusText = '报废 >';
            licenseBackImg = 'img_mask_zf';
        } else if (item.status == 2) {
            licenseStatusText = `冻结(${item.vehicleLicensePendingNum}) >`;
            licenseBackImg = 'img_mask_yc';
        } else if (item.status == 5) {
            licenseStatusText = `不通过(${item.vehicleLicensePendingNum}) >`;
            licenseBackImg = 'img_mask_yc';
        } else {
            licenseStatusText = `异常(${item.vehicleLicensePendingNum}) >`;
            licenseBackImg = 'img_mask_yc';
        }
        return (
            <UITouchableOpacity onPress={() => onItemClick(item)}>
                <UIImageBackground
                    source={licenseBackImg}
                    resizeMode={'stretch'}
                    style={{
                        marginHorizontal: 15,
                        marginTop: 15,
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingHorizontal: 15,
                            paddingVertical: 20,
                        }}>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <UIImage source={'ic_xsz'} style={{width: 23, height: 23}} />
                            <Text style={{fontSize: 17, color: '#333', marginLeft: 6}}>{item.plateNumber}</Text>
                        </View>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            {item.status != 0 && item.status != 1 && <UIImage source={'ic_warning'} style={{width: 14, height: 14}} />}
                            <Text
                                style={{
                                    fontSize: 13,
                                    color: item.status == 0 ? '#5086FC' : item.status == 1 ? '#666' : '#f00',
                                    marginLeft: 5,
                                }}>
                                {licenseStatusText}
                            </Text>
                        </View>
                    </View>
                </UIImageBackground>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: Item点击事件
     * 时间: 2024/8/28 0028 14:53
     * <AUTHOR>
     * @param item
     */
    function onItemClick(item: VehicleList) {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'VehicleIdCardManagerPage',
            data: {vehicleId: item.vehicleId},
        });
    }

    return (
        <View style={{backgroundColor: '#fff', flex: 1}}>
            {/*证件列表*/}
            <UIListView
                renderItem={({item}) => renderItemView(item)}
                dataList={vehicleList}
                onRefresh={() => {
                    queryMemberVehicleLicenseList();
                }}
            />
        </View>
    );
}

const styles = StyleSheet.create({});
