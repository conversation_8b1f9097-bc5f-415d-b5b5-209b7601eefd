import React from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import UIImage from '../../widget/UIImage';
import {EUserLicense} from 'certificate/requests/ReqQueryUserLicense';
import TextUtils from '../../util/TextUtils';
import LinearGradient from 'react-native-linear-gradient';

/**
 * 从业资格证
 */
export type Props = {
    data?: EUserLicense;
    onClick: (type: number) => void;
};
export const CyrStepAuthCertificationView = (props: Props) => {
    const showOnClickView = (licenseStatus?: string) => {
        //    证件状态   1-通过 2-驳回 3-去完成 4-去修改
        console.log('从业资格证 =licenseStatus' + licenseStatus);
        //上一个身份验证码未填写 or 驾驶证
        if (
            TextUtils.isEmpty(props.data?.idCard?.licenseStatus) ||
            TextUtils.equals('3', props.data?.idCard?.licenseStatus) ||
            TextUtils.isEmpty(props.data?.driverLicense?.licenseStatus) ||
            TextUtils.equals('3', props.data?.driverLicense?.licenseStatus)
        ) {
            return <Text style={{fontSize: 13, color: '#FFF', paddingLeft: 10, paddingRight: 10, paddingTop: 4, paddingBottom: 4, backgroundColor: '#CCCCCC', borderRadius: 3}}>去完成</Text>;
        }

        switch (licenseStatus) {
            case '1': {
                return <></>;
            }
            case '2': {
                return (
                    <TouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            props.onClick(2);
                        }}>
                        <UIImage source="base_warning" style={{width: 15, height: 15}} />
                        <Text style={{fontSize: 13, color: '#999', paddingLeft: 10, paddingRight: 10, paddingTop: 4, paddingBottom: 4}}>重新上传</Text>
                        <UIImage source="base_right_arrow_gray" style={{width: 6, height: 12}}></UIImage>
                    </TouchableOpacity>
                );
            }
            case '5':
            case '4': {
                return (
                    <TouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            props.onClick(1);
                        }}>
                        <UIImage source="icon_green" style={{width: 15, height: 15}}></UIImage>
                        <Text style={{fontSize: 13, color: '#5086FC', marginLeft: 10}}>去修改</Text>
                        <UIImage source="arrow_blue" style={{width: 6, height: 12, marginLeft: 5}}></UIImage>
                    </TouchableOpacity>
                );
            }
        }
        return (
            <Text
                onPress={() => {
                    props.onClick(0);
                }}
                style={{fontSize: 13, color: '#FFF', paddingLeft: 10, paddingRight: 10, paddingTop: 4, paddingBottom: 4, backgroundColor: '#5086FC', borderRadius: 3}}>
                去完成
            </Text>
        );
    };

    return (
        <View style={{backgroundColor: '#fff', paddingLeft: 14, paddingRight: 14, paddingBottom: 17, marginBottom: 8, marginTop: 10}}>

            <LinearGradient
                colors={['#ffffff', TextUtils.equals('2', props.data?.qualification?.licenseStatus) ? '#FFF3EF' : '#FFFFFF']}
                useAngle={true}
                angle={180}
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 15,
                    borderRadius: 6,
                    paddingTop: 18,
                    paddingLeft: 14,
                    paddingRight: 14,
                    paddingBottom: 18,
                    borderWidth: 0.4,
                    borderColor: '#5086FC',
                }}>
                <View style={{flex: 1}}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source="com_certification_id_card_v1" style={{width: 22, height: 22}}></UIImage>
                        <Text style={{fontSize: 16, color: '#333', marginLeft: 6}}>从业资格证</Text>
                    </View>
                    <Text style={{fontSize: 12, color: '#8E8C8C', marginTop: 8}}>如您使用总质量4.5吨以上车辆承运需上传从业资格证</Text>
                </View>
                {showOnClickView(`${props.data?.qualification?.licenseStatus}`)}
                {/* 认证通过图标 */}
                {TextUtils.equals('1', props.data?.qualification?.licenseStatus) && <UIImage source="certification_carrier_pass" style={{width: 60, height: 25, position: 'absolute', right: 0, top: -3}} />}
            </LinearGradient>
        </View>
    );
};
