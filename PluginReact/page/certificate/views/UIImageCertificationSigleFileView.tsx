import React, {ReactNode, useState} from 'react';
import {ImageStyle, StyleProp, View} from 'react-native';
import TextUtils from '../../util/TextUtils';
import {http} from '../../const.global';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import {gScreen_width} from '../../util/scaled-style';
import NormalInfoDialog from './NormalInfoDialog';
import {Method} from '../../util/NativeModulesTools';

/**
 * 单图片上传(选择，拍照，上传,删除，查看大图)
 */

export type Props = {
    url: string; //图片
    defaultValue?: string; //默认图片
    takePhoto?: boolean; //只拍照
    edit?: boolean; //是否可编辑
    imgStyle?: StyleProp<ImageStyle>; //图片样式
    onDelPic?: () => void; //删除
    onPostSuccess?: (url: string) => void; //上传成功回调
    transformUrl?: (url: string) => string; //转换可以显示完整路径
    showLoading?: (show: boolean) => void; //上传显示loading
    onSelectFile?: (callback: (file: string) => void) => void; //自定义选择文件
    onSelectDialog?: (callback: Function) => void;
    closeStyle?: StyleProp<ImageStyle>; //图片样式
    content?: string; //示例弹窗文案
    contentViews?: ReactNode; // 示例弹窗文案布局
};

const UIImageCertificationSigleFileView: React.FC<Props> = ({url, defaultValue = 'certification_cys_take_picture', takePhoto = false, edit = true, imgStyle, ...restProps}) => {
    const [imgUrl, setImgUrl] = React.useState(url);
    const [popViewVisible, setPopViewVisible] = useState(false);
    React.useEffect(() => {
        if (!TextUtils.equals(imgUrl, url)) {
            console.log('更新图片');
            setImgUrl(url);
        }
    }, [url]);
    //当前运行状态
    const running = React.useRef<boolean>(true);

    React.useEffect(() => {
        return () => {
            running.current = false;
        };
    }, []);

    const delPic = React.useCallback(() => {
        //删除
        Method.showDialog({
            title: '提示',
            message: '确定删除图片吗？',
            cancelText: '取消',
            okText: '确定',
            okAction: () => {
                setImgUrl('');
                restProps.onDelPic && restProps.onDelPic();
            },
        });
    }, []);

    /***上传文件*/
    const upFile = React.useCallback(async (file) => {
        //上传图片文件是集合  显示loading
        restProps.showLoading && restProps.showLoading(true);
        let json = await Method.upFileNew(file);
        if (running.current) {
            let result = JSON.parse(json);
            if (TextUtils.equals('200', result.code)) {
                //更新显示
                console.log('上传图片');
                setImgUrl(result.url);
                restProps.onPostSuccess && restProps.onPostSuccess(result.url);
            } else {
                Method.showToast(result.msg);
            }
            //隐藏loading
            restProps.showLoading && restProps.showLoading(false);
        }
    }, []);

    /***接收返回拍照或选择图片(无水印)*/
    const activityNoWaterResultFile = React.useCallback((code, json) => {
        try {
            let result = JSON.parse(json);
            if (code == 200) {
                result.map(async (item: string) => await upFile(item));
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            Method.showToast('请重新选择上传文件(R1001');
        }
    }, []);

    const onClickImg = React.useCallback((takePhoto1 = false) => {
        if (edit) {
            if (restProps.onSelectFile) {
                restProps.onSelectFile((file: string) => {
                    upFile(file);
                });
            } else {
                //不加水印
                Method.openCameraAndLibNew(takePhoto1, 1, activityNoWaterResultFile);
            }
        } else if (TextUtils.isNoEmpty(imgUrl)) {
            //查看大图
            let imageUrl = restProps.transformUrl ? restProps.transformUrl(imgUrl) : imgUrl;
            Method.onLookImageClick2(imageUrl);
        }
    }, []);

    return (
        <View>
            <UITouchableOpacity
                activeOpacity={0.9}
                onPress={() => {
                    if (TextUtils.isNoEmpty(imgUrl)) {
                        //查看大图
                        let imageUrl = restProps.transformUrl ? restProps.transformUrl(imgUrl) : imgUrl;
                        Method.onLookImageClick2(imageUrl);
                    } else {
                        setPopViewVisible(true);
                    }
                }}>
                <UIImage
                    resizeMode="stretch"
                    style={[{width: gScreen_width, height: 150, alignSelf: 'center'}, imgStyle]}
                    source={TextUtils.isEmpty(imgUrl) ? defaultValue : restProps.transformUrl ? restProps.transformUrl(imgUrl) : http.imagUrl(imgUrl)}
                />
            </UITouchableOpacity>
            {edit && TextUtils.isNoEmpty(imgUrl) && (
                <UITouchableOpacity onPress={delPic} style={{alignSelf: 'flex-end', position: 'absolute'}}>
                    <UIImage style={[{width: 27, height: 27}, restProps.closeStyle]} source={'review_alterdialog_skip'} />
                </UITouchableOpacity>
            )}
            {popViewVisible && (
                <NormalInfoDialog
                    onClose={() => setPopViewVisible(false)}
                    content={restProps.content ?? ''}
                    contentViews={restProps.contentViews}
                    title={'使用照片须知'}
                    selectAlbum={() => {
                        setPopViewVisible(false);
                        onClickImg();
                    }}
                    showLeftBtn={!takePhoto}
                    selectCamera={() => {
                        setPopViewVisible(false);
                        onClickImg(true);
                    }}
                />
            )}
        </View>
    );
};

export default React.memo(UIImageCertificationSigleFileView);
