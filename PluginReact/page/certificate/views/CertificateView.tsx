import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import UIImage from '../../widget/UIImage';
import UIImageBackground from '../../widget/UIImageBackground';
import LinearGradient from 'react-native-linear-gradient';
import TextUtils from '../../util/TextUtils';

interface Props {
    dismiss?: () => void;
    image: string;
    errorImg?: string;
    sampleDescription?: string;
    showTip?: boolean;
}

/**
 * 注释: 认证视图控件举例弹窗
 * 时间: 2024/12/20 星期五 15:30
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function CertificateView(props: Props) {
    return (
        <View style={{width: '100%', flexDirection: 'column', backgroundColor: '#fff', borderRadius: 6}}>
            {/*主示例图片*/}
            <UIImageBackground source={props.image} style={{width: '100%', height: 196}} resizeMode={'contain'}>
                {/*范本*/}
                <UIImage source={'certification_model'} style={{width: 33, height: 33, position: 'absolute', top: 10, left: 10}} />
                <UIImage source={'ic_right'} style={{width: 22, height: 22, position: 'absolute', bottom: 20, alignSelf: 'center'}} />
            </UIImageBackground>
            {/*范本描述*/}
            <Text style={styles.sampleDescriptionStyle}>{props.sampleDescription}</Text>
            {/*非营运提示*/}
            {props.showTip && (
                <LinearGradient
                    colors={['#ffe3cd', '#fff1d8']}
                    style={{
                        marginBottom: 10,
                        marginHorizontal: 17,
                        borderRadius: 3,
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                    }}
                    angle={180}>
                    <Text style={{fontSize: 12, color: '#da6844'}}>平台不支持使用性质为非营运/营转非的车辆注册</Text>
                </LinearGradient>
            )}
            {/*拍照提示*/}
            <UIImage source={'view_photo_prompt'} style={{width: '100%', height: 12, marginBottom: 15}} />
            {/*错误示范*/}
            {TextUtils.isNoEmpty(props.errorImg) && (
                <UIImage
                    source={props.errorImg ?? ''}
                    style={{
                        width: '100%',
                        height: 50,
                        marginTop: 14,
                        marginBottom: 7,
                    }}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    sampleDescriptionStyle: {
        fontSize: 14,
        color: '#999',
        marginLeft: 10,
        marginBottom: 10,
        alignSelf: 'center',
    },
});
