import {ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {ReactNode} from 'react';
import Modal from 'react-native-modal';
import UIImage from '../../widget/UIImage';
import UIImageBackGround from '../../widget/UIImageBackground';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {DictConfigBean} from '../../pick/requests/ReqDictConfig';
import {ReqQueryDictConfig} from '../../certificate/requests/ReqQueryDictConfig';
import {Method} from '../../util/NativeModulesTools';
import {ReqSaveUserWithdrawReason} from '../../certificate/requests/ReqSaveUserWithdrawReason';

interface Props {
    onClose?: () => void;
    onCloseBack?: () => void;
}

/**
 * 	MMS-15806 🏷 运力促活-司机注册挽留策略-认证挽留对话框
 */
export default function CyrAuthDetainDialog(props: Props) {
    const [showType, setShowType] = React.useState(1);
    const [configList, seConfigList] = React.useState<DictConfigBean[]>([]);
    const [selectItem, setSelectItemn] = React.useState<DictConfigBean[]>([]);

    React.useEffect(() => {
        // 弹窗一次，之后关闭
        Method.putStringExtra('CyrAuthAllPage_onClickBack','1')
    }, []);
    
    const handleExit = () => {
        // 处理退出认证的逻辑,请求配置问题列表
        Method.showWaitDialog();
        let req = new ReqQueryDictConfig();
        req.dictCode = 'USER_WITHDRAW_REASON';
        req.request().then((res) => {
            Method.hideWaitDialog();
            if (res.isSuccess()) {
                setShowType(2);
               seConfigList(res.data?.records??[]);
            } else {
                props?.onClose?.();
            }
        });
    };
    const handleExit2 = () => {
        // 继续退出认证的逻辑,提交退出原因
        if (selectItem.length == 0) {
            props?.onCloseBack?.();
        } else {
            Method.showWaitDialog();
            let req = new ReqSaveUserWithdrawReason();
            req.reason = selectItem.map(item=>item.value).join(',');
            req.request().then((res) => {
                Method.hideWaitDialog();
                Method.showToast(res.getMsg());
                
                if (res.isSuccess()) {
                    props?.onCloseBack?.();
                }
            });
        }
    };
    const renderContent = () => {
        return (
            <UIImageBackGround source="http://img.zczy56.com/202506061107560504817.png" style={styles.modalContent} resizeMode="stretch">
                <Text style={styles.modalTitle}>确定要退出认证吗？</Text>
                <Text style={styles.modalSubtitle}>{`当前${Math.floor(100 + Math.random() * 900)}名司机正在认证 完成认证立享优质资源`}</Text>
                <UIImage source="http://img.zczy56.com/202506061108231023738.png" style={styles.img} />
                <TouchableOpacity onPress={props.onClose} style={styles.button}>
                    <Text style={styles.buttonText}>继续认证</Text>
                </TouchableOpacity>
                <TouchableOpacity onPress={handleExit} style={styles.button2}>
                    <Text style={styles.buttonText2}>仍要退出</Text>
                </TouchableOpacity>
            </UIImageBackGround>
        );
    };

    const onPressSelctItem = (item: DictConfigBean) => {
        let list = [...selectItem];
        if (list.includes(item)) {
            list = list.filter((i) => i.value !== item.value);
        } else {
            list.push(item);
        }
        setSelectItemn(list);
    };
    const renderChildItemView = (item: DictConfigBean, index) => {
        return (
            <TouchableOpacity style={{flexDirection: 'row', paddingLeft: 14, paddingRight: 14, paddingTop: 8, paddingBottom: 8,alignItems:'center'}} onPress={() => onPressSelctItem(item)} key={item.value + '_' + index}>
                <UIImage source={selectItem.includes(item) ? 'check_circle_filled_v3' : 'lease_unchecked'} style={{width: 20, height: 20}} />
                <Text style={{marginLeft: 10, fontSize: 14, color:selectItem.includes(item) ?'#5086FC':'#333'}}>{item.value}</Text>
            </TouchableOpacity>
        );
    };
    const renderContent2 = () => {
        return (
            <View style={{borderTopLeftRadius: 15, borderTopRightRadius: 15, backgroundColor: '#FFF', height: '70%'}}>
                <View style={styles.headStyle}>
                    {/*关闭按钮*/}
                    <UITouchableOpacity onPress={props.onClose} style={styles.closeImage}>
                        <UIImage source={'base_close_x_grey'} style={styles.close_icon} />
                    </UITouchableOpacity>
                    {/*主标题*/}
                    <Text style={styles.main_title_txt}>请选择退出原因</Text>
                </View>
                <ScrollView>{configList?.map((item, index) => renderChildItemView(item, index))}</ScrollView>

                <View style={{flexDirection: 'row',marginBottom:7,marginTop:7}}>
                    <TouchableOpacity onPress={handleExit2} style={styles.button3}>
                        <Text style={styles.buttonText3}>保存并退出</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={props.onClose} style={styles.button4}>
                        <Text style={styles.buttonText}>继续认证</Text>
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    return (
        <Modal
            style={showType == 1 ? styles.container : styles.container2}
            animationIn={'slideInUp'}
            backdropOpacity={0.3}
            useNativeDriver={true}
            isVisible={true}
            onBackButtonPress={props.onClose} // 响应返回键
            onBackdropPress={props.onClose} //点击阴影部分
        >
            {showType == 1 ? renderContent() : renderContent2()}
        </Modal>
    );
}

const styles = StyleSheet.create({
    headStyle: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        height: 45,
    },
    main_title_txt: {fontSize: 17, color: '#333', fontWeight: 'bold'},
    closeImage: {
        position: 'absolute',
        left: 0,
        width: 45,
        height: 45,
        justifyContent: 'center',
        alignItems: 'center',
    },
    close_icon: {width: 15, height: 15},
    container: {
        justifyContent: 'center',
        padding: 0,
    },
    container2: {justifyContent: 'flex-end', padding: 0, margin: 0},
    img: {width: 264, height: 100, alignSelf: 'center', marginTop: 16, marginBottom: 12},
    modalContent: {
        backgroundColor: 'white',
        paddingTop: 30,
        borderRadius: 10,
    },
    modalTitle: {
        marginLeft: 28,
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    modalSubtitle: {
        width: 128,
        marginLeft: 28,
        fontSize: 12,
        color: '#666',
    },

    button: {
        marginBottom: 12,
        alignSelf: 'center',
        width: '80%',
        backgroundColor: '#007BFF',
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
    },
    button2: {
        marginBottom: 12,
        alignSelf: 'center',
        width: '80%',
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
    },

    button3: {
        flex: 1,
        marginBottom: 12,
        alignSelf: 'center',
        borderColor: '#C2D5FF',
        borderWidth: 1,
        marginLeft: 12,
        marginRight: 10,
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
    },

    button4: {
        flex: 1,
        marginLeft: 10,
        marginRight: 12,
        marginBottom: 12,
        alignSelf: 'center',
        width: '80%',
        backgroundColor: '#007BFF',
        padding: 10,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
    },
    buttonText3: {
        color: '#5086FC',
        fontSize: 16,
    },
    buttonText2: {
        color: '#666666',
        fontSize: 16,
    },
});
