import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import UIImage from '../../widget/UIImage';

interface SelectViewProps {
  title: string; // 标题
  value: string; // 显示的值
  onClick: () => void; // 点击回调
}

const CarItemSelectView: React.FC<SelectViewProps> = ({ title, value, onClick }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.spacer} />
      <TouchableOpacity style={styles.valueContainer} onPress={onClick}>
        <Text style={[styles.valueText, !value && styles.placeholder]}>
          {value || '请选择'}
        </Text>
        <UIImage style={styles.arrowIcon} source={'order_route_add_arrow'} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 14,
    backgroundColor: '#fff',
    marginBottom: 8,
    alignItems: 'center',
  },
  title: {
    color: '#3D3D3D',
    fontSize: 16,
  },
  spacer: {
    flex: 1,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueText: {
    marginLeft: 8,
    color: '#333',
    fontSize: 16,
  },
  placeholder: {
    color: '#B2B2B2',
  },
  arrowIcon: {
    marginLeft: 5,
    width: 6,
    height: 12,
  },
});

export default CarItemSelectView;