import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import UIImage from '../../widget/UIImage';

interface CertificateViewProps {
  title: string; // 标题
  flag: number; // 标识符（用于区分不同的证件）
  must: boolean; // 是否显示必填标志（*）
  edit: boolean; // 是否可编辑
  add: boolean; // 是否显示“去上传”按钮
  onClick: (type: number, status: number) => void; // 点击回调
}

const CarItemCertificateView: React.FC<CertificateViewProps> = ({
  title,
  flag,
  must,
  edit,
  add,
  onClick,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      {must && <Text style={styles.required}>*</Text>}
      <View style={styles.spacer} />
      {edit ? (
        add ? (
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={() => onClick(flag, 1)}
          >
            <Text style={styles.uploadButtonText}>去上传</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.modifyContainer}
            onPress={() => onClick(flag, 2)}
          >
            <UIImage style={styles.icon} source={'ic_right'} />
            <Text style={styles.modifyText}>修改证件</Text>
            <UIImage style={styles.arrowIcon} source={'base_arrow_right_blue'} />
          </TouchableOpacity>
        )
      ) : (
        <View style={styles.disabledButton}>
          <Text style={styles.disabledButtonText}>去上传</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 14,
    backgroundColor: '#fff',
    marginBottom: 1,
    alignItems: 'center',
  },
  title: {
    color: '#3D3D3D',
    fontSize: 16,
  },
  required: {
    color: '#FF602E',
    fontSize: 20,
    marginLeft: 5,
    fontWeight: 'bold',
  },
  spacer: {
    flex: 1,
  },
  uploadButton: {
    backgroundColor: '#5086FC',
    borderRadius: 3,
    paddingHorizontal: 10,
    paddingVertical: 2,
  },
  uploadButtonText: {
    color: '#FFF',
    fontSize: 13,
  },
  modifyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 15,
    height: 15,
  },
  modifyText: {
    marginLeft: 8,
    color: '#5071FC',
  },
  arrowIcon: {
    marginLeft: 5,
    width: 6,
    height: 12,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
    borderRadius: 3,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  disabledButtonText: {
    fontSize: 13,
    color: '#FFF',
  },
});

export default CarItemCertificateView;