import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import UIImageBackground from '../../widget/UIImageBackground';
import UIButton from '../../widget/UIButton';
import UIImage from '../../widget/UIImage';

interface Props {
    onClose?: () => void;
    title?: string;
    content?: string;
    cancelable?: boolean;
    onLeftCallback?: Function;
    onRightCallback?: Function;
    data?:any
}

/**
 * 注释: 三要素核验提示框
 * 时间: 2024/12/16 9:59
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function ThreeElementsTipsDialog(props: Props) {
    const cancelable = props.cancelable ?? true;
    return (
        <Modal
            onBackButtonPress={props.onClose} // 响应返回键
            onBackdropPress={() => {
                cancelable && props.onClose && props.onClose();
            }} // 点击背景遮罩层
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}>
            <UIImageBackground source={'img_three_elements_dialog_bg'} style={{borderRadius: 8}}>
                <Text
                    style={{
                        fontSize: 18,
                        color: '#333',
                        fontWeight: 'bold',
                        marginTop: 50,
                        alignSelf: 'center',
                    }}>
                    {props.title}
                </Text>
                <Text
                    style={{
                        fontSize: 15,
                        color: '#333',
                        marginTop: 20,
                        alignSelf: 'center',
                    }}>
                    {props.content}
                </Text>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'flex-end',
                        paddingHorizontal: 10,
                        marginTop: 55,
                        paddingVertical: 10,
                    }}>
                    <UIImage source={'img_change_phone_tips'} style={{width: 102, height: 18, position: 'absolute', top: 0, left: 8, zIndex: 1}} />
                    <UIButton
                        text={'视频申诉'}
                        backgroundColor={'#fff'}
                        textColor={'#5086FC'}
                        width={150}
                        height={50}
                        borderRadius={0}
                        onPress={() => {
                            props.onLeftCallback && props.onLeftCallback(props.data);
                        }}
                    />
                    <UIButton
                        text={'修改手机号'}
                        style={{alignItems: 'center'}}
                        subText={'（本人身份证办理）'}
                        width={150}
                        height={50}
                        borderRadius={0}
                        subFontSize={11}
                        onPress={() => {
                            props.onRightCallback && props.onRightCallback(props.data);
                        }}
                    />
                </View>
            </UIImageBackground>
        </Modal>
    );
}

const styles = StyleSheet.create({});
