import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {Method} from '../../util/NativeModulesTools';
import TextUtils from '../../util/TextUtils';
import {StyleProp, Text, View, ViewStyle} from 'react-native';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import ComCertificationUIImagePostItem from './ComCertificationUIImagePostItem';
import {EImage} from '../models/EImage';

/**
 * 证明材料模版
 */

interface Props {
    imgs?: EImage[]; //图片集合
    takePhoto?: boolean; //只拍照
    onChange?: Function; //图片改变回调
    edit?: boolean; //是否可编辑
    max: number; //最大上传数量
    rowSize: number;
    tvRedAsterisk?: boolean;
    tvTitle1?: string; //标题
    tvDoubt?: boolean; //是否显示提示
    onDoubtClick?: Function; //问号点击事件回调
    onClick?: Function; //点击事件
    style?: StyleProp<ViewStyle>;

    transformUrl(url: string): string;

    showLoading(show: boolean): void;
}

function ComCertificationMultipleImageView(props: Props, ref: React.Ref<ComCertificationMultipleImageViewRef>) {
    const [imgsList, setImageList] = useState(props.imgs ?? []);

    //当前运行状态
    const running = useRef<boolean>(true);

    const imgsListRef = useRef<EImage[]>(getImage(props.imgs ?? []));

    useEffect(() => {
        return () => {
            running.current = false;
        };
    }, []);

    const getImages = (): string[] => {
        //获取数据
        return getImage(imgsList).map((item) => {
            return item.imageId ?? '';
        });
    };

    //对外部公开调用方法
    useImperativeHandle(ref, () => ({getImages}));

    function getImage(imgs: EImage[]): EImage[] {
        return (imgs ?? []).map((item) => {
            let eImage = new EImage();
            eImage.imageId = item.imageId ?? '';
            return eImage;
        });
    }

    const lookImagePage = (item, imageUrl) => {
        if (props.onClick) {
            props.onClick();
            return;
        }
        //查看大图
        Method.onLookImageClick2(imageUrl);
    };

    const delPic = (index, item, imageUrl) => {
        //删除
        let newList = imgsList.filter((value) => !TextUtils.equals(value.imageId, item));
        setImageList(newList);
        imgsListRef.current = getImage(newList);

        Method.showToast('已删除图片');
        props.onChange && props.onChange(newList);
    };

    /***上传文件*/
    const upFile = async (imageWater, imageOld, compateCallback = () => {}) => {
        let json = await Method.upFileNew(imageWater);
        let result = JSON.parse(json);

        if (TextUtils.equals('200', result.code)) {
            let eImage = new EImage();
            eImage.imageId = result.url;
            let newList = [...imgsListRef.current, eImage];
            //更新显示
            imgsListRef.current = newList;
            setImageList(newList);
            props.onChange && props.onChange(newList);
        }

        compateCallback();
        console.log('上传图片==结束====' + imageWater);
    };

    /***接收返回拍照或选择图片(无水印)*/
    const activityNoWaterResultFile = (code, json) => {
        try {
            let result = JSON.parse(json);
            if (code == 200) {
                if (imgsList.length >= props.max) {
                    Method.showToast('图片数量已达上限！');
                    return;
                }
                //上传图片文件是集合  显示loading
                props.showLoading && props.showLoading(true);
                let count = 0;
                let compateCallback = () => {
                    count++;
                    if (count >= result.length) {
                        //隐藏loading
                        props.showLoading && props.showLoading(false);
                    }
                };
                result.map(async (item) => {
                    await upFile(item, '', compateCallback);
                });
            } else if (code == -2 || code == -3) {
                Method.showToast(result.msg);
            }
        } catch (e) {
            props.showLoading && props.showLoading(false);
            Method.showToast('请重新选择上传文件(R1001');
        }
    };

    /**
     * 选择图片
     */
    const choosePic = () => {
        let count = props.max - imgsList.length;
        //不加水印
        Method.openCameraAndLibNew(props.takePhoto ?? false, count > 0 ? count : 1, activityNoWaterResultFile);
    };

    return (
        <View
            style={[
                props.style,
                {
                    paddingTop: 17,
                    paddingBottom: 7,
                },
            ]}>
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginLeft: 15,
                    marginRight: 15,
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text style={{fontSize: 15, color: '#3d3d3d'}}>{props.tvTitle1}</Text>
                    <UITouchableOpacity
                        onPress={() => {
                            props.onDoubtClick && props.onDoubtClick(props.tvTitle1);
                        }}>
                        <UIImage source={'certification_doubt_blue'} style={{width: 16, height: 16, marginStart: 5}} />
                    </UITouchableOpacity>
                    {props.tvRedAsterisk && <Text style={{color: 'red'}}> *</Text>}
                    <Text style={{color: '#999999', fontSize: 12, marginStart: 7}}>{`(≤${props.max}张照片)`}</Text>
                </View>
                {props.tvDoubt && <Text style={{fontSize: 12, color: '#ff602e'}}>{'未上传则运费会被冻结'}</Text>}
            </View>
            <ComCertificationUIImagePostItem item={imgsList} edit={props.edit ?? true} max={props.max} rowSize={props.rowSize} delPic={delPic} lookImagePage={lookImagePage} choosePic={choosePic} transformUrl={props.transformUrl} />
        </View>
    );
}

export interface ComCertificationMultipleImageViewRef {
    getImages: () => string[];
}

export default forwardRef<ComCertificationMultipleImageViewRef, Props>(ComCertificationMultipleImageView);
