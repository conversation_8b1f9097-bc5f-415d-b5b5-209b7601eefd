import {StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useEffect, useImperativeHandle, useState} from 'react';
import UIImage from '../../widget/UIImage';
import TextUtils from '../../util/TextUtils';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

interface Props {
    showError?: boolean;
    label: string;
    value?: string;
    error?: string;
    edit?: boolean;
    isRequired?: boolean;
    isSelect?: boolean;
    onChangeText?: Function;
    onSelect?: Function;
    onBlur?: Function;
}

/**
 * 注释: 带报错输入视图
 * 时间: 2025/2/20 星期四 19:50
 * <AUTHOR>
 * @param props
 * @constructor
 */
function UIFormInputView(props: Props, ref: React.Ref<UIFormInputViewRef>) {
    const [inputValue, setInputValue] = useState<string>(props.value ?? '');
    const [showError, setShowError] = useState<boolean>(props.showError ?? false);
    const [errorMsg, setErrorMsg] = useState<string>(props.error ?? '');

    useEffect(() => {
        setShowError(props.showError ?? false);
        setErrorMsg(props.error ?? '');
    }, [props.showError, props.error]);

    useEffect(() => {
        setInputValue(props.value ?? '');
    }, [props.value]);

    // 对外部公开调用方法
    useImperativeHandle(ref, () => ({getValue: () => inputValue, setValue: (value: string) => setInputValue(value)}));

    return (
        <View style={{backgroundColor: '#fff', padding: 15, borderBottomWidth: 0.5, borderColor: '#eee'}}>
            <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
                <View style={{alignItems: 'center', flexDirection: 'row'}}>
                    {showError && <UIImage source={'icon_warning_new'} style={{width: 15, height: 15, marginRight: 5}} />}
                    <Text style={{fontSize: 16, color: '#333'}}>{props.label}</Text>
                    {props.isRequired && <Text style={{color: '#FF602E', fontSize: 16, marginLeft: 5}}>*</Text>}
                </View>
                <UITouchableOpacity
                    activeOpacity={1}
                    style={[
                        {
                            justifyContent: 'center',
                            alignItems: 'center',
                            flexDirection: 'row',
                            backgroundColor: showError ? '#FFF5F1' : undefined,
                            borderColor: showError ? '#FF5E1C' : undefined,
                            borderWidth: showError ? 0.5 : 0,
                            borderRadius: showError ? 5 : 0,
                            marginRight: 9,
                            paddingVertical: 7,
                            paddingRight: 5,
                        },
                    ]}
                    onPress={() => {
                        props.isSelect && props.onSelect && props.onSelect();
                    }}>
                    {(!props.isSelect ?? true) && (
                        <TextInput
                            style={{fontSize: 14, color: '#333', maxWidth: 220, minWidth: 80, paddingRight: 10, paddingLeft: 14}}
                            textAlign={'right'}
                            value={inputValue}
                            placeholder={'请输入'}
                            editable={props.edit ?? true}
                            onChangeText={(e) => {
                                setInputValue(e);
                                props.onChangeText && props.onChangeText();
                            }}
                        />
                    )}
                    {props.isSelect && (
                        <Text
                            style={{
                                marginRight: 10,
                                fontSize: 14,
                                color: inputValue ? '#333' : '#999',
                                textAlign: 'right',
                                maxWidth: 220,
                                minWidth: 80,
                                paddingLeft: 14,
                            }}>
                            {inputValue ? inputValue : '请选择'}
                        </Text>
                    )}
                    <UIImage source={showError ? 'certification_fill_in_red' : 'certification_fill_in_gray'} style={{width: 15, height: 15}} />
                </UITouchableOpacity>
            </View>
            {showError && <Text style={{fontSize: 13, color: '#FF4747', marginTop: 5}}>{errorMsg}</Text>}
        </View>
    );
}

export interface UIFormInputViewRef {
    getValue: () => string;
    setValue: (value: string) => void;
}

export default React.forwardRef<UIFormInputViewRef, Props>(UIFormInputView);

const styles = StyleSheet.create({});
