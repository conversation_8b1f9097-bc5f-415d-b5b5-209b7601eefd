import React, {memo} from 'react';
import {Text, StyleSheet} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {gScreen_width} from '../../util/scaled-style';
import UIImageSigleFileView from '../../widget/UIImageSingleFileView';
import type {Props as UIImageSigleFileProps} from '../../widget/UIImageSingleFileView';

/***
 * 承运商 证明材料
 */

type Props = {
    showBg?: boolean; //是否显示背景颜色
    toastText?: string;
} & UIImageSigleFileProps;

const CysFileInfoErrorView: React.FC<Props> = (props: Props) => {
    return (
        <LinearGradient style={styles.body} colors={props.showBg ? ['#FFF3EF', '#FFFFFF'] : ['#FFFFFF', '#FFFFFF']} useAngle={true} angle={180}>
            <UIImageSigleFileView imgStyle={styles.imgSzie} {...props} />
            <Text style={styles.txt}>{props.toastText}</Text>
        </LinearGradient>
    );
};
const styles = StyleSheet.create({
    body: {paddingLeft: 14, paddingRight: 14, flexDirection: 'column', paddingTop: 5, paddingBottom: 5},
    imgSzie: {width: gScreen_width * 0.92, height: 150},
    txt: {fontSize: 13, color: '#fd5353', marginTop: 10},
});
export default memo(CysFileInfoErrorView);
