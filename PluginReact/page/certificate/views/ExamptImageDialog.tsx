import React from 'react';
import {View, StyleSheet, Text} from 'react-native';
import Modal from 'react-native-modal';
import {gScreen_width} from '../../util/scaled-style';
import UIImage from '../../widget/UIImage';
import UITouchableOpacity from '../../widget/UITouchableOpacity';

interface Props {
    onClose?: () => void;
}

const ExamptImageDialog = (props: Props) => {
    return (
        <Modal
            onBackButtonPress={props.onClose} // 响应返回键
            onBackdropPress={props.onClose} // 点击背景遮罩层
            animationIn={'zoomInDown'}
            animationOut={'zoomOutUp'}
            backdropOpacity={0.3}
            hideModalContentWhileAnimating={true}
            useNativeDriver={false}
            useNativeDriverForBackdrop={false}
            isVisible={true}>
            <View style={styles.body}>
                <View style={styles.title}>
                    <Text style={{color: '#333', fontSize: 16, fontWeight: 'bold'}}>示例模板</Text>
                    <Text style={{color: '#666', fontSize: 14, flex: 1}}>（您可上传任意一种样式）</Text>
                    <UITouchableOpacity onPress={props.onClose}>
                        <UIImage source="erro_x" style={{width: 20, height: 20}} />
                    </UITouchableOpacity>
                </View>
                <UIImage source="congye01" style={{width: gScreen_width * 0.75, height: 160, marginTop: 5}} resizeMode="stretch" />
                <UIImage source="congye02" style={{width: gScreen_width * 0.75, height: 160, marginTop: 5}} resizeMode="stretch" />
                <UIImage source="congye03" style={{width: gScreen_width * 0.75, height: 160, marginTop: 5}} resizeMode="stretch" />
            </View>
        </Modal>
    );
};
const styles = StyleSheet.create({
    body: {
        width: gScreen_width * 0.8,
        backgroundColor: '#fff',
        borderRadius: 10,
        paddingBottom: 10,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        flexDirection: 'column',
    },
    title: {
        padding: 15,
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        backgroundColor: '#F2F8FF',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },
});
export default ExamptImageDialog;
