import {StyleSheet, View} from 'react-native';
import FastImage from 'react-native-fast-image';
import React from 'react';
import {gScreen_width} from '../../util/scaled-style';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import {EImage} from '../models/EImage';

/**
 *
 * @param item 数据结合
 * @param transformUrl 转换图片地址
 * @param edit 是否可编辑
 * @param lookImagePage 查看大图(item,imageUrl) imageUrl 网络地址
 * @param delPic 删除(index,item,imageUrl) imageUrl 网络地址
 * @param choosePic 选择图片或拍照
 * @param max 最多数量
 * @param rowSize 每行显示
 * @constructor
 */

interface Props {
    item: EImage[]; // 数据结合
    transformUrl: Function; //自定义转换可显示图片完整地址
    lookImagePage: Function; //查看大图(item,imageUrl) imageUrl 图片完整地址
    delPic: Function; //删除(index,item,imageUrl) imageUrl图片完整地址
    choosePic: Function; //选择图片或拍照
    max: number; //最多数量
    rowSize: number; //每行显示
    edit: boolean; //是否可编辑
}

export default function ComCertificationUIImagePostItem(props: Props) {
    //图片大小 = (屏幕宽度-（14 * (每行数量-1 + 左右两边margin值）) / 每行数量
    const size = React.useMemo(() => {
        return (gScreen_width - 12 * (props.rowSize - 1 + 2)) / props.rowSize;
    }, []);

    const marginLeftSize = (index: number) => (index % props.rowSize == 0 ? 0 : 7);

    return (
        <View style={styles.body}>
            {props.item.map((item, index) => {
                let imageUrl = props.transformUrl(item.imageId);
                if (props.edit) {
                    return (
                        <View
                            style={{
                                marginLeft: marginLeftSize(index),
                                marginBottom: 7,
                                padding: item.bgColor ? 1 : 0,
                                backgroundColor: item.bgColor,
                            }}
                            key={imageUrl}>
                            <UITouchableOpacity activeOpacity={0.9} onPress={() => props.lookImagePage(item.imageId, imageUrl)}>
                                <FastImage style={{width: size, height: size}} source={{uri: imageUrl}} />
                            </UITouchableOpacity>
                            <UITouchableOpacity onPress={() => props.delPic(index, item.imageId, imageUrl)} style={styles.del_icon}>
                                <UIImage style={{width: 27, height: 27}} source={'review_alterdialog_skip'} />
                            </UITouchableOpacity>
                        </View>
                    );
                } else {
                    return (
                        <View style={{marginLeft: marginLeftSize(index), marginBottom: 7}} key={item.imageId}>
                            <UITouchableOpacity activeOpacity={0.9} onPress={() => props.lookImagePage(item.imageId, imageUrl)}>
                                <FastImage style={{width: size, height: size}} source={{uri: imageUrl}} />
                            </UITouchableOpacity>
                        </View>
                    );
                }
            })}
            {props.item.length < props.max && props.edit && (
                <UITouchableOpacity activeOpacity={0.9} style={{marginLeft: marginLeftSize(props.item.length), marginBottom: 7}} onPress={() => props.choosePic()} key={'selector_view_photo_add' + props.max}>
                    <UIImage style={{width: size, height: size}} source={'base_selector_view_photo_2'} key={'selector_view_photo_i_add' + props.max} />
                </UITouchableOpacity>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    body: {flexDirection: 'row', flexWrap: 'wrap', marginTop: 7, marginLeft: 20},
    del_icon: {alignSelf: 'flex-end', position: 'absolute'},
});
