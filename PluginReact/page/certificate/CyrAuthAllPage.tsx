import React from 'react';
import {ParamListBase} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {Constant} from '../base/Constant';
import {ReactNode} from 'react';
import {ScrollView, Text, View, TouchableOpacity} from 'react-native';
import {gScreen_width} from '../util/scaled-style';
import UIImage from '../widget/UIImage';
import UITitleView from '../widget/UITitleView';
import {CyrStepAuthIDCardView} from './views/CyrStepAuthIDCardView';
import {CyrStepAuthDriverLicenseView} from './views/CyrStepAuthDriverLicenseView';
import {CyrStepAuthVehicleView} from './views/CyrStepAuthVehicleView';
import {EUserLicense, EVehicle, ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import TextUtils from '../util/TextUtils';
import {EQuserPromoteNew, ReqUserPromoteNew} from './requests/ReqUserPromoteNew';
import {DialogNewBuilder} from '../base/Dialog';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {Method} from '../util/NativeModulesTools';
import ThreeElementsTipsDialog from './views/ThreeElementsTipsDialog';
import {ReqFaceRegoin} from './requests/ReqFaceRegoin';
import EventBus from '../util/EventBus';
import CyrAuthDetainDialog from './views/CyrAuthDetainDialog';
import {onEvent} from '../base/native/UITrackingAction';
import {CyrCertificationChooseType, CyrCertificationChooseTypeView} from './views/CyrCertificationChooseTypeView';
import {CyrStepAuthCertificationView} from './views/CyrStepAuthCertificationView';

/**
 * 个体司机认证
 */
interface State extends BaseState {
    init?: boolean;
    mEUserLicense?: EUserLicense;
    showThreeElementsTipsDialog?: boolean;
    quserPromoteNew?: EQuserPromoteNew;
    showDetainDialog?: boolean; //认证挽留对话框
    needSelfOrder?: string; // 是否需要自主摘单 1-是 0-否
    showClearVehicleDialog?: boolean; // 切换弹窗
    pendingNeedSelfOrder?: string; // 临时存储切换目标
    chooseType?: string;
    isReject: boolean;
}

export default class CyrAuthAllPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    chooseRef = React.createRef<CyrCertificationChooseType>();

    constructor(props) {
        super(props);
        this.state = {
            init: true,
            mEUserLicense: new EUserLicense(),
            chooseType: '',
            isReject: false,
        };
    }

    componentDidMount(): void {
        super.componentDidMount();
        this._showWaitDialog();
        this.reqQueryUserLicense();
        // 获取原生跳转携带信息
        console.log(this.getParams(), '==============================');

        EventBus.getInstance().addListener(Constant.event_certification_cyr_refresh_subscript, this.onRefresh);
        onEvent({pageId: 'rzym', tableId: 'rzym', event: 'view'});
    }

    reqQueryUserLicense() {
        new ReqQueryUserLicense().request().then((resp) => {
            this._dismissWait();
            if (resp.isSuccess()) {
                this.setState({
                    init: true,
                    mEUserLicense: resp.data,
                    isReject:
                        TextUtils.equals('2', resp.data?.idCard?.licenseStatus) ||
                        TextUtils.equals('2', resp.data?.driverLicense?.licenseStatus) ||
                        TextUtils.equals('2', resp.data?.vehicle?.licenseStatus) ||
                        TextUtils.equals('2', resp.data?.replenish?.licenseStatus) ||
                        TextUtils.equals('2', resp.data?.qualification?.licenseStatus),
                    chooseType: this.getChooseType(resp.data?.vehicle ?? new EVehicle()),
                });
            } else {
                this._showMsgDialog(resp.getMsg());
            }
        });
    }

    componentWillUnmount(): void {
        super.componentWillUnmount();
        EventBus.getInstance().removeListener(this.onRefresh);
    }

    getCommitType() {
        //所有证件已上传且认证通过
        if (TextUtils.isEmpty(this.state.mEUserLicense?.idCard?.licenseStatus) || TextUtils.equals('3', this.state.mEUserLicense?.idCard?.licenseStatus) || TextUtils.equals('2', this.state.mEUserLicense?.idCard?.licenseStatus)) {
            return false;
        }
        if (TextUtils.isEmpty(this.state.mEUserLicense?.driverLicense?.licenseStatus) || TextUtils.equals('3', this.state.mEUserLicense?.driverLicense?.licenseStatus) || TextUtils.equals('2', this.state.mEUserLicense?.driverLicense?.licenseStatus)) {
            return false;
        }
        // 必须选择自主摘单
        if (TextUtils.isEmpty(this.state.chooseType) && !this.state.isReject) {
            return false;
        }
        if (TextUtils.equals('1', this.state.chooseType)) {
            if (TextUtils.isEmpty(this.state.mEUserLicense?.vehicle?.licenseStatus) || TextUtils.equals('3', this.state.mEUserLicense?.vehicle?.licenseStatus) || TextUtils.equals('2', this.state.mEUserLicense?.vehicle?.licenseStatus)) {
                return false;
            }
        }
        if (!TextUtils.equals('A2', this.state.mEUserLicense?.driverLicense?.quasiVehicleType) && TextUtils.equals(this.state.mEUserLicense?.vehicle?.vehicleFlag, '2')) {
            return false;
        }
        return true;
    }

    onClickBack = () => {
        let value = Method.getStringExtra('CyrAuthAllPage_onClickBack');
        if (TextUtils.equals('1', value)) {
            this._goBack();
        } else {
            //显示挽留对话框
            this.setState({
                showDetainDialog: true,
            });
        }
        onEvent({pageId: 'rzym', tableId: 'rzym_fh', event: 'click'});
    };

    render(): ReactNode {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UIImage
                    source={'icon_cyr_auth_bg'}
                    style={{
                        width: gScreen_width,
                        height: (gScreen_width / 375) * 190,
                        position: 'absolute',
                        top: 0,
                    }}
                    resizeMode={'cover'}
                />
                <UITitleView clickBack={this.onClickBack} title={'个体司机认证'} style={{backgroundColor: Constant.color_transparent, marginBottom: 5}} showLine={false} textStyle={{color: '#fff'}} backIcon={'base_back_white'} />
                <ScrollView>
                    {(TextUtils.equals('2', this.state.mEUserLicense?.idCard?.licenseStatus) ||
                        TextUtils.equals('2', this.state.mEUserLicense?.vehicle?.licenseStatus) ||
                        TextUtils.equals('2', this.state.mEUserLicense?.driverLicense?.licenseStatus)) && (
                        <Text
                            style={{
                                backgroundColor: '#000000',
                                color: '#fff',
                                fontSize: 16,
                                paddingLeft: 14,
                                paddingRight: 14,
                                paddingTop: 5,
                                paddingBottom: 5,
                            }}>
                            部分材料与认证内容不符合 <Text style={{color: '#F36159'}}>请重新提交</Text>
                        </Text>
                    )}
                    <View style={{marginTop: 30}} />
                    <CyrStepAuthIDCardView data={this.state.mEUserLicense} onClick={this.onClickIDCard} />
                    <CyrStepAuthDriverLicenseView data={this.state.mEUserLicense} onClick={this.onClickDriverLicense} />
                    {!this.state.isReject && (
                        <CyrCertificationChooseTypeView
                            ref={this.chooseRef}
                            callBack={(value) => {
                                this.setState({chooseType: value});
                                this.onRefresh();
                            }}
                            chooseType={this.state.chooseType}
                            data={this.state.mEUserLicense}
                            shouldNotShift={this.getShouldNotChooseFun()}
                        />
                    )}
                    {/* 只有选"是"才展示车辆认证 */}
                    {TextUtils.equals('1', this.state.chooseType) && <CyrStepAuthVehicleView data={this.state.mEUserLicense} onClick={this.onClickVeicle} />}
                    {!this.state.isReject && TextUtils.equals('0', this.state.chooseType) && <CyrStepAuthCertificationView data={this.state.mEUserLicense} onClick={this.onClickQualification} />}
                </ScrollView>
                <TouchableOpacity disabled={!this.getCommitType()} onPress={this.onCliclSave}>
                    <Text
                        style={{
                            backgroundColor: !this.getCommitType() ? '#CCCCCC' : '#5086FC',
                            color: '#fff',
                            fontSize: 18,
                            textAlign: 'center',
                            paddingTop: 15,
                            paddingBottom: 15,
                        }}>
                        确认提交
                    </Text>
                </TouchableOpacity>
                {this._initCommView()}
                {this.state.showThreeElementsTipsDialog && (
                    <ThreeElementsTipsDialog
                        data={this.state.quserPromoteNew}
                        title="当前账号非本人身份证办理的手机号"
                        content="为保障您的账号及后续运输安全，请确保注册手机号是以本人身份证办理"
                        cancelable={false}
                        onClose={() => {
                            this.setState({showThreeElementsTipsDialog: false});
                        }}
                        onLeftCallback={(quserPromoteNew) => {
                            //视频申诉
                            this.setState({showThreeElementsTipsDialog: false});
                            if (TextUtils.equals('1', quserPromoteNew.riskSubmit)) {
                                // 申诉过
                                // 待审核/审核驳回
                                RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                                    faceReason: quserPromoteNew.faceReason,
                                    FromFrag: '13',
                                    isLogin: '1',
                                    idCardNo: quserPromoteNew.threeElementsIdCardNo,
                                });
                            } else {
                                //跳转常用手机号申诉
                                RouterUtils.skipRouter(RouterUrl.UserComUsePhoneApplyActivity, {
                                    threeElementsName: quserPromoteNew.threeElementsName,
                                    threeElementsIdCardNo: quserPromoteNew.threeElementsIdCardNo,
                                    faceReason: quserPromoteNew.faceReason,
                                    riskSubmit: quserPromoteNew.riskSubmit,
                                    userUpdate: '1',
                                });
                            }
                        }}
                        onRightCallback={(quserPromoteNew) => {
                            //修改手机号
                            this.setState({showThreeElementsTipsDialog: false});
                            RouterUtils.skipRouter(RouterUrl.UserChangeSelfPhoneActivity, {
                                threeElementsName: quserPromoteNew.threeElementsName,
                                threeElementsIdCardNo: quserPromoteNew.threeElementsIdCardNo,
                                faceReason: quserPromoteNew.faceReason,
                                riskSubmit: quserPromoteNew.riskSubmit,
                                userUpdate: '1',
                            });
                        }}
                    />
                )}
                {this.state.showDetainDialog && (
                    <CyrAuthDetainDialog
                        onClose={() => this.setState({showDetainDialog: false})}
                        onCloseBack={() => {
                            this.setState({showDetainDialog: false}, () => {
                                this._goBack();
                            });
                        }}
                    />
                )}
            </View>
        );
    }

    onRefresh = () => {
        //刷新当前界面认证数据
        new ReqQueryUserLicense().request().then((resp) => {
            if (resp.isSuccess()) {
                this.setState({mEUserLicense: resp.data});
            }
        });
    };

    onClickIDCard = (type: number) => {
        //身份认证  0去完成 1 去修改 2 重新上传
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: type === 0 ? 'CertificationAuthenticationPage' : 'CertificationAuthenticationNoPassPage',
            callBack: this.onRefresh,
        });
        onEvent({pageId: 'rzym', tableId: type === 0 ? 'rzym_sfrz_qwc' : 'rzym_sfz_xgzj', event: 'click'});
    };

    onClickDriverLicense = (type: number) => {
        //驾驶证  0去完成 1 去修改 2 重新上传
        // updateType: 1认证页面
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'DriverLicenseAuthPage',
            data: {mode: 0, updateType: type},
            callBack: this.onRefresh,
        });
        onEvent({pageId: 'rzym', tableId: 'rzym_jsz_qwc', event: 'click'});
    };

    onClickVeicle = (_type: number) => {
        //车辆  0去完成 1 去修改 2 重新上传
        if (TextUtils.equals('2', this.state.mEUserLicense?.vehicle?.licenseStatus)) {
            RouterUtils.skipRouter(RouterUrl.CertificationVehicleAuditNotPassedActivity);
        } else {
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'CyrAuthCarPage',
                callBack: () => {
                    this.onRefresh();
                },
            });
        }
    };

    onClickQualification = () => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'QualificationCertificatePage',
            callBack: this.onRefresh,
            data: {
                newType: '5', //单独新增type传5
                qualification: this.state.mEUserLicense?.qualification,
            },
        });
    };

    getShouldNotChooseFun() {
        //     切换的时候不应该调用清理缓存接口?
        if (TextUtils.equals('1', this.state.chooseType)) {
            //停留在是,切换到否,需要判断是否维护了车辆信息
            if (TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.licenseStatus) && !TextUtils.equals('3', this.state.mEUserLicense?.vehicle?.licenseStatus)) {
                return true;
            } else if (TextUtils.equals('3', this.state.mEUserLicense?.vehicle?.licenseStatus)) {
                // 如果上传了任何一个证件,就返回true
                if (
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.vehicleLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.annualVehicleLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.transportPermissionLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.tractorVehicleLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.vehicleBodyLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.trailerVehicleLicenseUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.qualificatePicUrl) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.personCarUrl1) ||
                    TextUtils.isNoEmpty(this.state.mEUserLicense?.vehicle?.personCarUrl2)
                )
                    return true;
            }
        } else {
            if (TextUtils.isNoEmpty(this.state.mEUserLicense?.qualification?.licenseStatus) && !TextUtils.equals('3', this.state.mEUserLicense?.qualification?.licenseStatus)) return true;
        }
        return false;
    }

    getChooseType(vehicle: EVehicle): string {
        const hasVehicleInfo = [
            vehicle?.vehicleLicenseUrl,
            vehicle?.annualVehicleLicenseUrl,
            vehicle?.transportPermissionLicenseUrl,
            vehicle?.tractorVehicleLicenseUrl,
            vehicle?.vehicleBodyLicenseUrl,
            vehicle?.trailerVehicleLicenseUrl,
            vehicle?.personCarUrl1,
            vehicle?.personCarUrl2,
        ].some((url) => TextUtils.isNoEmpty(url));

        if (hasVehicleInfo) {
            return '1';
        } else if (TextUtils.isNoEmpty(vehicle?.qualificatePicUrl)) {
            return '0';
        } else {
            return '';
        }
    }

    onCliclSave = () => {
        onEvent({pageId: 'rzym', tableId: 'rzym_qrtj', event: 'click'});
        let req = new ReqUserPromoteNew();
        req.idCardEffectDate = this.state.mEUserLicense?.idCard?.idCardEffectDate;
        req.quasiVehicleType = this.state.mEUserLicense?.driverLicense?.quasiVehicleType;
        req.driverLicEffectDate = this.state.mEUserLicense?.driverLicense?.driverLicEffectDate;
        req.qualificationLicEffectDate = this.state.mEUserLicense?.vehicle?.qualificationLicEffectDate;
        req.vehicleLicenseEffectDate = this.state.mEUserLicense?.vehicle?.vehicleLicenseEffectDate;
        req.trailerVehicleLicenseEffectDate = this.state.mEUserLicense?.vehicle?.trailerVehicleLicenseEffectDate;
        req.isPickUpTheListBySelf = this.state.chooseType;

        this._showWaitDialog();
        req.request().then((resp) => {
            this._dismissWait();
            if (resp.isSuccess()) {
                this.onUserPromoteNewSuccess(resp.data);
            } else {
                this._showMsgDialog(resp.getMsg());
            }
        });
    };

    onUserPromoteNewSuccess = (quserPromoteNew?: EQuserPromoteNew) => {
        //司机认证审批
        if (quserPromoteNew) {
            /*认证审核调用三要素统一逻辑判断*/
            if (TextUtils.equals('1', quserPromoteNew.threeFactorRiskState)) {
                //三要素核验不通过，弹三要素校验弹框
                this.setState({showThreeElementsTipsDialog: true});
            } else if (TextUtils.equals('1', quserPromoteNew.verifyState)) {
                if (TextUtils.equals('1', quserPromoteNew.silentSignState)) {
                    //判断是否需要签署电子签,弹电子签签署弹框
                    let dialogBuilder = new DialogNewBuilder();
                    dialogBuilder.setModel(1);
                    dialogBuilder.setCancel(false);
                    dialogBuilder.setTitle('温馨提示');
                    dialogBuilder.setMsg('为保障您的合法权益及在线协议的法律效力，后续运输合同需基于电子签名方式签署。需要您先完成实名认证，同时为免去后续每次签署运输合同需操作意愿认证，本次实名会同步为您完成《静默签授权书》的签署。');
                    dialogBuilder.setRightTxt('同意并继续');
                    dialogBuilder.setRightOnClick((dialog) => {
                        dialog.dismiss(),
                            //跳转H5进行电签
                            RouterUtils.skipRouter(RouterUrl.SilentProtocolWeActivity, {
                                fromType: '1',
                                faceReason: quserPromoteNew.faceReason,
                                fromFrag: '5',
                                userUpdate: '1',
                                riskSubmit: quserPromoteNew.riskSubmit ?? '',
                                idCardName: this.state.mEUserLicense?.idCard?.idCardName ?? '',
                                idCardNo: this.state.mEUserLicense?.idCard?.idCardNo ?? '',
                                carrierOrBoss: '2',
                            });
                    });
                    dialogBuilder.show();
                } else {
                    //实名认证未通过去认证
                    let dialog = new DialogNewBuilder();
                    dialog.setCancel(false);
                    dialog.setLeftTxt('关闭');
                    dialog.setTitle('提示');
                    dialog.setMsg('当前账号存在风险,请优先完成人脸认证');
                    dialog.setRightTxt('人脸认证');
                    dialog.setRightOnClick((dialog) => {
                        dialog?.dismiss();
                        //再次调用接口判断实名认证状态
                        if (TextUtils.equals('1', quserPromoteNew.riskSubmit)) {
                            //人脸视频审核中
                            RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                                faceReason: '1',
                                FromFrag: '5',
                                userUpdate: '1',
                            });
                        } else {
                            this.liveFace(quserPromoteNew);
                        }
                        onEvent({pageId: 'tszrlrz', tableId: 'tszrlrz_rlrz', event: 'click'});
                    });
                    dialog.show();
                    onEvent({pageId: 'tszrlrz', tableId: 'tszrlrz', event: 'click'});
                }
            } else {
                // 跳转到首页
                Method.openHomeTab(1);
            }
        }
    };

    liveFace = (quserPromoteNew?: EQuserPromoteNew) => {
        Method.startFaceRecognition({
            idCardName: this.state.mEUserLicense?.idCard?.idCardName ?? '',
            idCardNo: this.state.mEUserLicense?.idCard?.idCardNo ?? '',
            callBack: (data) => {
                this._showWaitDialog();
                let json = JSON.parse(data);
                let req = new ReqFaceRegoin();
                req.orderNo = JSON.parse(json.faceInfo)?.orderNo;
                req.idCardName = this.state.mEUserLicense?.idCard?.idCardName;
                req.idCardNo = this.state.mEUserLicense?.idCard?.idCardNo;
                req.faceReason = quserPromoteNew?.faceReason ?? '';
                req.request().then((resp) => {
                    this._dismissWait();
                    // 人脸识别成功
                    if (resp.isSuccess()) {
                        //跳转提交资料成功中继页
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {
                            page: 'CertificationSubmitSuccessPage',
                            action: RouterUtils.ACTION_REPLACE,
                        });
                    } else {
                        //人脸不通过或人脸视频审核驳回
                        //跳转人脸识别失败逻辑
                        RouterUtils.skipRouter(RouterUrl.LoginFaceRecognitionFailureRouter, {
                            faceReason: '1',
                            isLogin: '0',
                            FromFrag: '5',
                            userUpdate: '1',
                            chooseType: this.state.chooseType,
                        });
                        onEvent({pageId: 'rlsbwcg', tableId: 'rlsbwcg', event: 'view'});
                    }
                });
            },
        });
    };
}
