import {ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState, DialogBuilder} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {Method} from '../util/NativeModulesTools';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {RouterUtils} from '../util/RouterUtils';
import UIButton from '../widget/UIButton';
import {RouterUrl} from '../base/RouterUrl';
import TextUtils from '../util/TextUtils';
import {ReqFaceRegoin} from './requests/ReqFaceRegoin';
import {onEvent} from '../base/native/UITrackingAction';
import {EDriverLicense, EIDCard, EReplenish, EUserLicense, EVehicle, getLicenseTitle, ReqQueryUserLicense, showErrorToast} from './requests/ReqQueryUserLicense';
import ComCertificationUploadImgView, {IngProps} from './views/ComCertificationUploadImgView';
import {QuserPromote, ReqUserPromote} from './requests/ReqUserPromote';
import {ReqCarrierBossSubmit2Examing} from './requests/ReqCarrierBossSubmit2Examing';
import UIServerAndHelpView from './views/UIServerAndHelpView';
import ThreeElementsTipsDialog from './views/ThreeElementsTipsDialog';
import {ReqMemberUpdateIdCard} from './requests/ReqMemberUpdateIdCard';
import {ReqIdCardLicenseOcr} from './requests/ReqIdCardLicenseOcr';
import {UserType} from '../user/models/UserType';
import LanguageType from '../util/language/LanguageType';
import UIImage from '../widget/UIImage';
import UIDatePicker from '../widget/UIDatePicker';
import {dateFormat} from '../util/DateUtil';
import {isIDCardTakePhote} from './utils/Utils';
import CertificationForFriverExistDailog from './views/CertificationForFriverExistDailog';
import CertificationForFriverExistSuccessDailog from './views/CertificationForFriverExistSuccessDailog';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ReqCheckCarrierUserIdCardOccupied} from './requests/ReqCheckCarrierUserIdCardOccupied';
import {ReqUserUpgradeSaveLicense} from './requests/ReqUserUpgradeSaveLicense';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import {RspQueryUserExpireLicenseDetail} from './models/RspQueryUserExpireLicenseDetail';
import {LicenseList} from './models/ResMemberLicenseList';
import {ReqUpdateMemberLicense} from './requests/ReqUpdateMemberLicense';
import {PicUrlBean} from './models/PicUrlBean';

interface State extends BaseState {
    init: boolean;
    showDialog: boolean; //三要素弹窗
    cl2: boolean; //确认信息
    cl3: boolean; //有效期
    llIdCardInfo: boolean; //信息
    clTop1: boolean; //上传模式
    timeShow: boolean; //时间选择弹窗
    idCardNo?: string; //身份证号
    idCardName?: string; //姓名
    idCardEffectDate?: string; //身份证有效期
    idCardNameText?: string; //身份证姓名错误提示
    idCardNoText?: string; //身份证号错误提示
    idCardEffectDateText?: string; //身份证有效期错误提示
    showCertificationForFriverExistDailog: boolean; //承运方APP找回账号流程简化弹窗
    showCertificationForFriverExistSuccessDailog: boolean; //承运方APP找回账号流程简化弹窗
    showTip: boolean;
    autoInput?: boolean; //是否自动录入
}

/**
 * 注释: 身份认证-驳回
 * 时间: 2024/12/23 9:19
 * <AUTHOR>
 */
export default class CertificationAuthenticationNoPassPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageDetail?: EUserLicense;
    pageExpiredDetail?: RspQueryUserExpireLicenseDetail;
    frontIdCardUrl?: string; //正面
    negativeIdCardUrl?: string; //背面
    rightImg: IngProps;
    leftImg: IngProps;
    errorMsgs: string[];
    // 三要素弹窗
    dialogTitle = '';
    dialogContent = '';
    dialogCancelable = true;
    dialogLeftCallback: Function;
    dialogRightCallback: Function;
    // 承运方APP找回账号流程简化弹窗
    dialog2Content = '';
    dialog2LeftCallback: Function;
    dialog2RightCallback: Function;
    isReturn: boolean = false;
    showViewBg: boolean = false;
    toSave?: string; //司机用
    occupiedMobile?: string; //司机用
    callback?: Function;
    typeOcr = '';
    mode = 0;
    //证件过期
    rspQueryUserExpireLicenseB: LicenseList = new LicenseList();
    constructor(props) {
        super(props);
        this.state = {
            init: false,
            showDialog: false,
            cl2: false,
            cl3: true,
            llIdCardInfo: false,
            timeShow: false,
            showTip: false,
            clTop1: true,
            showCertificationForFriverExistDailog: false,
            showCertificationForFriverExistSuccessDailog: false,
        };

        this.callback = this.getCallBack();
        this.mode = this.getParams()?.mode ?? 0;
        this.rspQueryUserExpireLicenseB = this.getParams()?.rspQueryUserExpireLicenseB ?? new LicenseList();
    }

    componentDidMount() {
        super.componentDidMount();
        // 认证详情
        if (this.mode == 2 || TextUtils.equals('6', `${this.rspQueryUserExpireLicenseB.licenseStatus}`) || TextUtils.equals('0', `${this.rspQueryUserExpireLicenseB.licenseStatus}`)) {
            //过期 || 自主更新
            this.setState({showTip: true});
            this.queryUserExpireLicenseDetail();
            return;
        }
        if (UserType.isBoss()) {
            this.queryUserLicenseBoss();
        } else {
            this.queryUserLicenseDriver();
        }
    }
    componentWillUnmount() {
        super.componentWillUnmount();
        this.state.timeShow && this.setState({timeShow: false});
    }

    /**
     * 证件详情
     */
    queryUserExpireLicenseDetail() {
        let req = new ReqQueryUserExpireLicenseDetail();
        req.licenseType = '100001';
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this.pageExpiredDetail = res.data;
                switch (res.data?.licenseType) {
                    case '100001': {
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_4',
                            title: '身份证人像面',
                            takePhoto: isIDCardTakePhote(),
                            onPostSuccess: (url) => {
                                this.frontIdCardUrl = url;
                                this.idCardLicenseOcr('1');
                            },
                            onDelPic: () => {
                                this.frontIdCardUrl = '';
                            },
                        };
                        this.rightImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_5',
                            title: '身份证国徽面',
                            takePhoto: isIDCardTakePhote(),
                            onPostSuccess: (url) => {
                                this.negativeIdCardUrl = url;
                                this.idCardLicenseOcr('2');
                            },
                            onDelPic: () => {
                                this.negativeIdCardUrl = '';
                            },
                        };
                        break;
                    }
                    default: {
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_4',
                            title: '身份证人像面',
                            takePhoto: isIDCardTakePhote(),
                            onPostSuccess: (url) => {
                                this.frontIdCardUrl = url;
                                this.idCardLicenseOcr('1');
                            },
                            onDelPic: () => {
                                this.frontIdCardUrl = '';
                            },
                        };
                        this.rightImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_5',
                            title: '身份证国徽面',
                            takePhoto: isIDCardTakePhote(),
                            onPostSuccess: (url) => {
                                this.negativeIdCardUrl = url;
                                this.idCardLicenseOcr('2');
                            },
                            onDelPic: () => {
                                this.negativeIdCardUrl = '';
                            },
                        };
                        break;
                    }
                }
                //0：待上传；1：审核中;  3：审核失败
                switch (res.data?.licenseState) {
                    case '3': {
                        //审核不通过
                        this.leftImg.canDelete = true;
                        this.errorMsgs = res.data?.licenseReason?.split(';') ?? [];
                        this.showViewBg = true;
                        break;
                    }
                    default: {
                        // 0:待完善 || 1:待审核 || else:审核通过
                        if (TextUtils.isEmpty(res.data?.licenseUrl)) {
                            this.leftImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                            this.rightImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                        } else {
                            if (!TextUtils.equals('6', this.rspQueryUserExpireLicenseB.licenseStatus) && !TextUtils.equals('0', this.rspQueryUserExpireLicenseB.licenseStatus)) {
                                this.frontIdCardUrl = res.data?.licenseUrl.split(',')[0];
                                this.negativeIdCardUrl = res.data?.licenseUrl.split(',')[1];
                                this.leftImg.url = this.frontIdCardUrl ?? '';
                                this.rightImg.url = this.negativeIdCardUrl ?? '';
                                this.leftImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                                this.rightImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                            }
                        }
                        break;
                    }
                }
                this.setState({init: true, clTop1: false});
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 认证详情
     */
    queryUserLicenseBoss() {
        let req = new ReqQueryUserLicense();
        req.type = '1';
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                if (res.data != null && res.data.idCard != null) {
                    this.pageDetail = res.data;
                    if (UserType.isCarrier()) {
                        let mSaveDriverStatus = res.data?.driverLicense?.licenseStatus;
                        let mSaveCarPremissionStatus = res.data?.vehicle?.licenseStatus;
                        /*身份证的时候，判断其他两项有没有完成*/
                        let flag = (TextUtils.equals('1', mSaveDriverStatus) || TextUtils.equals('4', mSaveDriverStatus)) && (TextUtils.equals('1', mSaveCarPremissionStatus) || TextUtils.equals('4', mSaveCarPremissionStatus));
                        this.toSave = flag ? '1' : '0';
                    }
                    this.showViewBg = TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1') || TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1');
                    this.setState({clTop1: !(TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1') || TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1'))});
                    if (TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1') && TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1')) {
                        //正反面审核不通过
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_4',
                            title: '身份证人像面',
                            onPostSuccess: (url) => {
                                this.frontIdCardUrl = url;
                                this.idCardLicenseOcr('1');
                            },
                            onDelPic: () => {
                                this.frontIdCardUrl = '';
                            },
                        };
                        this.rightImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_5',
                            title: '身份证国徽面',
                            onPostSuccess: (url) => {
                                this.negativeIdCardUrl = url;
                                this.idCardLicenseOcr('2');
                            },
                            onDelPic: () => {
                                this.negativeIdCardUrl = '';
                            },
                        };
                        this.errorMsgs = (this.pageDetail?.idCard?.negativeIdCardRiskAuditContent ?? '').split(';') ?? [];
                        this.errorMsgs = this.errorMsgs.concat((this.pageDetail?.idCard?.frontIdCardRiskAuditContent ?? '').split(';') ?? []);
                    } else if (TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1')) {
                        this.negativeIdCardUrl = res.data?.idCard?.negativeIdCardUrl;
                        this.setState({idCardEffectDate: res.data?.idCard?.idCardEffectDate});
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_4',
                            title: '身份证人像面',
                            onPostSuccess: (url) => {
                                this.frontIdCardUrl = url;
                                this.idCardLicenseOcr('1');
                            },
                            onDelPic: () => {
                                this.frontIdCardUrl = '';
                            },
                        };
                        this.errorMsgs = (this.pageDetail?.idCard?.frontIdCardRiskAuditContent ?? '').split(';') ?? [];
                    } else if (TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1')) {
                        this.frontIdCardUrl = res.data?.idCard?.frontIdCardUrl;
                        this.setState({idCardNo: res.data?.idCard?.idCardNo, idCardName: res.data?.idCard?.idCardName});
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_5',
                            title: '身份证国徽面',
                            onPostSuccess: (url) => {
                                this.negativeIdCardUrl = url;
                                this.idCardLicenseOcr('2');
                            },
                            onDelPic: () => {
                                this.negativeIdCardUrl = '';
                            },
                        };
                        this.errorMsgs = (this.pageDetail?.idCard?.negativeIdCardRiskAuditContent ?? '').split(';') ?? [];
                    } else {
                        this.frontIdCardUrl = res.data.idCard.frontIdCardUrl;
                        this.negativeIdCardUrl = res.data.idCard.negativeIdCardUrl;
                        this.leftImg = {
                            url: this.frontIdCardUrl ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_4',
                            title: '身份证人像面',
                            onPostSuccess: (url) => {
                                this.frontIdCardUrl = url;
                                this.idCardLicenseOcr('1');
                            },
                            onDelPic: () => {
                                this.frontIdCardUrl = '';
                            },
                        };
                        this.rightImg = {
                            url: this.negativeIdCardUrl ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_5',
                            title: '身份证国徽面',
                            onPostSuccess: (url) => {
                                this.negativeIdCardUrl = url;
                                this.idCardLicenseOcr('2');
                            },
                            onDelPic: () => {
                                this.negativeIdCardUrl = '';
                            },
                        };
                        if (!TextUtils.isEmpty(this.pageDetail?.idCard?.frontIdCardUrl) && !TextUtils.isEmpty(this.pageDetail?.idCard?.negativeIdCardUrl)) {
                            this.frontIdCardUrl = this.pageDetail?.idCard?.frontIdCardUrl;
                            this.negativeIdCardUrl = this.pageDetail?.idCard?.negativeIdCardUrl;
                            this.setState({
                                idCardNo: this.pageDetail?.idCard?.idCardNo,
                                idCardName: this.pageDetail?.idCard?.idCardName,
                                idCardEffectDate: this.pageDetail?.idCard?.idCardEffectDate,
                                cl2: true,
                                llIdCardInfo: true,
                                cl3: true, //有效期
                            });
                        }
                    }
                }
                this.setState({init: true});
            } else {
                this.pageDetail = new EUserLicense();
                this.pageDetail.replenish = new EReplenish();
                this.pageDetail.vehicle = new EVehicle();
                this.pageDetail.vehicle.licenseStatus = '3';
                this.pageDetail.driverLicense = new EDriverLicense();
                this.pageDetail.driverLicense.licenseStatus = '3';
                this.pageDetail.idCard = new EIDCard();
                this.pageDetail.idCard.licenseStatus = '3';
                this.pageDetail.idCard.haveIdCardNoFlag = '1';
                this.leftImg = {
                    url: '',
                    defaultValue: 'certification_driver_license_template_diagram_4',
                    title: '身份证人像面',
                    onPostSuccess: (url) => {
                        this.frontIdCardUrl = url;
                    },
                    onDelPic: () => {
                        this.frontIdCardUrl = '';
                    },
                };
                this.rightImg = {
                    url: '',
                    defaultValue: 'certification_driver_license_template_diagram_5',
                    title: '身份证国徽面',
                    onPostSuccess: (url) => {
                        this.negativeIdCardUrl = url;
                    },
                    onDelPic: () => {
                        this.negativeIdCardUrl = '';
                    },
                };
                this.setState({init: true});
                this._showToast(res.getMsg());
            }
        });
    }

    queryUserLicenseDriver() {
        let req = new ReqQueryUserLicense();
        req.type = '1';
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                if (res.data != null && res.data.idCard != null) {
                    this.pageDetail = res.data;
                    let idCard = res.data.idCard;
                    switch (`${idCard.licenseStatus}`) {
                        case '1': {
                            this.frontIdCardUrl = res.data.idCard.frontIdCardUrl ?? '';
                            this.negativeIdCardUrl = res.data.idCard.negativeIdCardUrl ?? '';
                            this.rightImg = {
                                url: this.negativeIdCardUrl,
                                defaultValue: 'certification_driver_license_template_diagram_5',
                                title: '身份证国徽面',
                                canDelete: false,
                                edit: false,
                            };
                            this.leftImg = {
                                url: this.frontIdCardUrl,
                                defaultValue: 'certification_driver_license_template_diagram_4',
                                title: '身份证人像面',
                                canDelete: false,
                                edit: false,
                            };
                            this.setState({idCardName: idCard.idCardName ?? '--', idCardNo: idCard.idCardNo ?? '--', idCardEffectDate: idCard.idCardEffectDate ?? '--'});
                            break;
                        }
                        case '2': {
                            this.isReturn = true;
                            this.showViewBg = true;
                            this.setState({
                                idCardName: idCard.idCardName ?? '--',
                                idCardNo: idCard.idCardNo ?? '--',
                                idCardEffectDate: idCard.idCardEffectDate ?? '--',
                                clTop1: false,
                                cl2: false,
                                cl3: false,
                            });
                            this.leftImg = {
                                url: '',
                                defaultValue: TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') ? 'certification_driver_license_template_diagram_4' : 'certification_driver_license_template_diagram_5',
                                title: TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') ? '身份证人像面' : '身份证国徽面',
                                onPostSuccess: (url) => {
                                    if (TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1')) {
                                        this.frontIdCardUrl = url;
                                        this.idCardLicenseOcr('1');
                                    } else {
                                        this.negativeIdCardUrl = url;
                                        this.idCardLicenseOcr('2');
                                    }
                                },
                                onDelPic: () => {
                                    if (TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1')) {
                                        this.frontIdCardUrl = '';
                                    } else {
                                        this.negativeIdCardUrl = '';
                                    }
                                },
                            };
                            if (TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1') && TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1')) {
                                //正反面审核不通过
                                this.rightImg = {
                                    url: '',
                                    defaultValue: 'certification_driver_license_template_diagram_5',
                                    title: '身份证国徽面',
                                    onPostSuccess: (url) => {
                                        this.negativeIdCardUrl = url;
                                        this.idCardLicenseOcr('2');
                                    },
                                    onDelPic: () => {
                                        this.negativeIdCardUrl = '';
                                    },
                                };
                                this.errorMsgs = (this.pageDetail?.idCard?.negativeIdCardRiskAuditContent ?? '').split(';') ?? [];
                                this.errorMsgs = this.errorMsgs.concat((this.pageDetail?.idCard?.frontIdCardRiskAuditContent ?? '').split(';') ?? []);
                            } else if (TextUtils.equals(res.data.idCard.frontIdCardRiskAudit, '1')) {
                                this.negativeIdCardUrl = res.data?.idCard?.negativeIdCardUrl;
                                this.errorMsgs = (this.pageDetail?.idCard?.frontIdCardRiskAuditContent ?? '').split(';') ?? [];
                                this.setState({idCardEffectDate: res.data?.idCard?.idCardEffectDate});
                            } else if (TextUtils.equals(res.data.idCard.negativeIdCardRiskAudit, '1')) {
                                this.frontIdCardUrl = res.data?.idCard?.frontIdCardUrl;
                                this.errorMsgs = (this.pageDetail?.idCard?.negativeIdCardRiskAuditContent ?? '').split(';') ?? [];
                                this.setState({idCardNo: res.data?.idCard?.idCardNo, idCardName: res.data?.idCard?.idCardName});
                            }
                            break;
                        }
                        default: {
                            this.frontIdCardUrl = res.data.idCard.frontIdCardUrl ?? '';
                            this.negativeIdCardUrl = res.data.idCard.negativeIdCardUrl ?? '';
                            this.leftImg = {
                                url: this.frontIdCardUrl ?? '',
                                defaultValue: 'certification_driver_license_template_diagram_4',
                                title: '身份证人像面',
                                onPostSuccess: (url) => {
                                    this.frontIdCardUrl = url;
                                    this.idCardLicenseOcr('1');
                                },
                                onDelPic: () => {
                                    this.frontIdCardUrl = '';
                                },
                            };
                            this.rightImg = {
                                url: this.negativeIdCardUrl ?? '',
                                defaultValue: 'certification_driver_license_template_diagram_5',
                                title: '身份证国徽面',
                                onPostSuccess: (url) => {
                                    this.negativeIdCardUrl = url;
                                    this.idCardLicenseOcr('2');
                                },
                                onDelPic: () => {
                                    this.negativeIdCardUrl = '';
                                },
                            };
                            this.setState({idCardName: idCard.idCardName ?? '--', idCardNo: idCard.idCardNo ?? '--', idCardEffectDate: idCard.idCardEffectDate ?? '--', llIdCardInfo: true});
                            break;
                        }
                    }
                }
                this.setState({init: true});
            } else {
                this.pageDetail = new EUserLicense();
                this.pageDetail.replenish = new EReplenish();
                this.pageDetail.vehicle = new EVehicle();
                this.pageDetail.vehicle.licenseStatus = '3';
                this.pageDetail.driverLicense = new EDriverLicense();
                this.pageDetail.driverLicense.licenseStatus = '3';
                this.pageDetail.idCard = new EIDCard();
                this.pageDetail.idCard.licenseStatus = '3';
                this.pageDetail.idCard.haveIdCardNoFlag = '1';
                this.leftImg = {
                    url: '',
                    defaultValue: 'certification_driver_license_template_diagram_4',
                    title: '身份证人像面',
                    onPostSuccess: (url) => {
                        this.frontIdCardUrl = url;
                        this.idCardLicenseOcr('1');
                    },
                    onDelPic: () => {
                        this.frontIdCardUrl = '';
                    },
                };
                this.rightImg = {
                    url: '',
                    defaultValue: 'certification_driver_license_template_diagram_5',
                    title: '身份证国徽面',
                    onPostSuccess: (url) => {
                        this.negativeIdCardUrl = url;
                        this.idCardLicenseOcr('2');
                    },
                    onDelPic: () => {
                        this.negativeIdCardUrl = '';
                    },
                };
                this.setState({init: true});
                this._showToast(res.getMsg());
            }
            if (TextUtils.equals(res.data?.idCard?.haveIdCardNoFlag, '1')) {
                this.showView(true);
            } else if (TextUtils.equals(res.data?.idCard?.haveIdCardNoFlag, '0')) {
                this.showView(false);
            } else {
                this.showView(true);
            }
        });
    }

    /**
     * ocr识别
     * @param type
     */
    idCardLicenseOcr(type: string) {
        let req = new ReqIdCardLicenseOcr();
        if (TextUtils.equals(type, '1')) {
            req.idCardFrontLicenseUrl = this.frontIdCardUrl;
        } else {
            req.idCardBackLicenseUrl = this.negativeIdCardUrl;
        }
        req.request().then((res) => {
            if (res.isSuccess()) {
                if (TextUtils.equals(type, '1')) {
                    this.setState({
                        idCardName: res.data?.userName ?? '',
                        idCardNo: res.data?.idCardNo ?? '',
                    });
                } else {
                    this.setState({
                        idCardEffectDate: res.data?.validityEnd ?? '',
                    });
                }
            } else {
                this._showMsgDialog(res.getMsg());
            }
            if (TextUtils.equals(type, '2')) {
                this.setState({cl3: true});
            }
            this.setState({llIdCardInfo: true});
        });
    }

    commit() {
        if (TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') && TextUtils.equals(this.pageDetail?.idCard?.negativeIdCardRiskAudit, '1')) {
            if (TextUtils.isEmpty(this.frontIdCardUrl)) {
                this._showToast('请上传身份证正面照片！');
                return;
            }
            if (TextUtils.isEmpty(this.negativeIdCardUrl)) {
                this._showToast('请上传身份证反面照片！');
                return;
            }
        } else if (TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') && TextUtils.isEmpty(this.frontIdCardUrl)) {
            this._showToast('请上传身份证正面照片！');
            return;
        } else if (TextUtils.equals(this.pageDetail?.idCard?.negativeIdCardRiskAudit, '1') && TextUtils.isEmpty(this.negativeIdCardUrl)) {
            this._showToast('请上传身份证反面照片！');
            return;
        } else {
            if (this.leftImg != null) {
                if (TextUtils.equals('身份证人像面', this.leftImg.title) && TextUtils.isEmpty(this.frontIdCardUrl)) {
                    this._showToast('请上传身份证正面照片！');
                    return;
                }
                if (TextUtils.equals('身份证国徽面', this.leftImg.title) && TextUtils.isEmpty(this.negativeIdCardUrl)) {
                    this._showToast('请上传身份证反面照片！');
                    return;
                }
            }
            if (this.rightImg != null && TextUtils.isEmpty(this.negativeIdCardUrl)) {
                this._showToast('请上传身份证反面照片！');
                return;
            }
        }
        this.licenseInputAccuracyCheck();
    }
    /**
     * 证件准确性校验接口
     */
    licenseInputAccuracyCheck() {
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '1';
        req.frontIdCardUrl = this.frontIdCardUrl;
        req.negativeIdCardUrl = this.negativeIdCardUrl;
        req.idCardName = this.state.idCardName;
        req.idCardNo = this.state.idCardNo;
        req.idCardEffectDate = this.state.idCardEffectDate;
        this._showWaitDialog();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                if (this.mode == 2) {
                    let req = new ReqUploadExpireDateLicense();
                    req.picUrlList = [
                        {
                            picUrl: this.frontIdCardUrl + ',' + this.negativeIdCardUrl,
                            type: this.pageExpiredDetail?.licenseType,
                        },
                    ];
                    req.targetId = this.rspQueryUserExpireLicenseB.targetId ?? '';
                    this._showWaitDialog();
                    req.request().then((res) => {
                        this._dismissWait();
                        if (res.isSuccess()) {
                            RouterUtils.skipPop();
                            this.callback && this.callback();
                            EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                        } else {
                            this._showMsgDialog(res.getMsg());
                        }
                    });
                    return;
                }
                if (TextUtils.equals('6', this.rspQueryUserExpireLicenseB.licenseStatus)) {
                    this.updateMemberLicense();
                } else {
                    if (UserType.isBoss()) {
                        // 车老板认证
                        this.memberUpdateIdCard();
                    } else {
                        // 司机认证
                        this.checkCarrierUserIdCardOccupied();
                    }
                }
            } else {
                let idCardEffectDateText = response.data?.data?.idCardEffectDateText;
                let idCardNameText = response.data?.data?.idCardNameText;
                let idCardNoText = response.data?.data?.idCardNoText;
                this.setState({idCardEffectDateText: idCardEffectDateText, idCardNameText: idCardNameText, idCardNoText: idCardNoText});
                if (TextUtils.isNoEmpty(idCardEffectDateText) || TextUtils.isNoEmpty(idCardNameText) || TextUtils.isNoEmpty(idCardNoText)) {
                    this._showToast('请核实身份信息');
                    return;
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            }
        });
    }

    updateMemberLicense() {
        let req = new ReqUpdateMemberLicense();
        let bean = new PicUrlBean();
        bean.picUrl = this.frontIdCardUrl + ',' + this.negativeIdCardUrl;
        bean.type = this.rspQueryUserExpireLicenseB?.licenseType;
        bean.targetId = this.rspQueryUserExpireLicenseB?.targetId;
        req.picUrlList = [bean];
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                RouterUtils.skipPop();
                this.callback && this.callback();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    memberUpdateIdCard() {
        let req = new ReqMemberUpdateIdCard();
        req.negativeIdCardUrl = this.negativeIdCardUrl;
        req.frontIdCardUrl = this.frontIdCardUrl;
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                let req = new ReqUserPromote();
                req.request().then((res) => {
                    if (res.isSuccess()) {
                        this.onUserPromoteSuccess(res.data);
                    } else {
                        this._showMsgDialog(res.getMsg());
                    }
                });
            } else {
                this._showToast(res.getMsg());
            }
        });
    }
    checkCarrierUserIdCardOccupied() {
        let req = new ReqCheckCarrierUserIdCardOccupied();
        req.idCard = this.state.idCardNo;
        this._showWaitDialog();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                let size = response.data?.carrierIdCardOccupiedUserInfoResp?.occupiedNum ?? 0;
                if (size > 1) {
                    let dialog = new DialogBuilder();
                    dialog.title = '提示';
                    dialog.msg = '该身份证下有多个已认证司机账号，请联系客服处理';
                    dialog.model = 1;
                    dialog.okTxt = '我知道了';
                    this._showDialog(dialog);
                } else if (size == 1) {
                    this.occupiedMobile = response.data?.carrierIdCardOccupiedUserInfoResp?.mobile;
                    this.dialog2Content = `该身份证号已被账号【%s】手机号【%s】占用，请确认是否直接使用当前登录手机号替换原账号？${response.data?.carrierIdCardOccupiedUserInfoResp?.userName}${this.occupiedMobile}`;
                    this.dialog2LeftCallback = () => {
                        //放弃
                        this.setState({showCertificationForFriverExistDailog: false});
                        this.userUpgradeSaveLicense();
                    };
                    this.dialog2RightCallback = () => {
                        this.setState({showCertificationForFriverExistDailog: false});
                        this.liveImage();
                    };
                    this.setState({showCertificationForFriverExistDailog: true});
                } else {
                    this.userUpgradeSaveLicense();
                }
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    /**
     * 提交认证
     */
    userUpgradeSaveLicense() {
        let req = new ReqUserUpgradeSaveLicense();
        req.idCard = new EIDCard();
        req.idCard.haveIdCardNoFlag = '1';
        req.idCard.idCardName = this.state.idCardName;
        req.idCard.idCardNo = this.state.idCardNo;
        req.idCard.idCardEffectDate = this.state.idCardEffectDate;
        req.idCard.frontIdCardUrl = this.frontIdCardUrl;
        req.idCard.negativeIdCardUrl = this.negativeIdCardUrl;
        req.type = '1';
        this._showWaitDialog();
        req.request().then((response) => {
            this._dismissWait();
            if (response.isSuccess()) {
                // 做提交操作
                if (TextUtils.equals('1', this.toSave)) {
                    this.doComitAllMeans();
                } else {
                    this.callback && this.callback();
                    RouterUtils.skipPop();
                    EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                }
            } else {
                if (`${response.getMsg()}`.startsWith('该身份证已被占用')) {
                    let dialog = new DialogBuilder();
                    dialog.title = '提示';
                    dialog.msg = response.getMsg();
                    dialog.okTxt = '找回帐号';
                    dialog.onOkEvent = () => {
                        RouterUtils.skipRouter(RouterUrl.AccountRetrieveActivity);
                    };
                    this._showDialog(dialog);
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            }
        });
    }

    doComitAllMeans() {
        let showErrorToast2 = showErrorToast(this.pageDetail?.driverLicense?.licenseStatus);
        if (!TextUtils.isEmpty(showErrorToast2)) {
            let dialog = new DialogBuilder();
            dialog.title = '提示';
            dialog.msg = showErrorToast2;
            dialog.model = 1;
            dialog.okTxt = '我知道了';
            this._showDialog(dialog);
            return;
        }
        let showErrorToast3 = showErrorToast(this.pageDetail?.vehicle?.licenseStatus);
        if (!TextUtils.isEmpty(showErrorToast3)) {
            let dialog = new DialogBuilder();
            dialog.title = '提示';
            dialog.msg = showErrorToast2;
            dialog.model = 1;
            dialog.okTxt = '我知道了';
            this._showDialog(dialog);
            return;
        }
        let vehicleFlag = this.pageDetail?.vehicle?.vehicleFlag;
        let quasiVehicleType = this.pageDetail?.driverLicense?.quasiVehicleType;
        if (!TextUtils.equals('A2', quasiVehicleType) && TextUtils.equals(vehicleFlag, '2')) {
            this._showMsgDialog('准驾车型与车辆类型不符，请修改准驾车型，或前往车辆认证模块修改车辆类型。');
            return;
        }
        RouterUtils.skipPop();
        this.callback && this.callback();
        EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
    }

    /**
     * 司机认证审批
     */
    onUserPromoteSuccess(quserPromote?: QuserPromote) {
        if (quserPromote == null) {
            return;
        }
        if (TextUtils.equals('1', quserPromote.threeFactorRiskState)) {
            this.dialogTitle = '当前账号非本人身份证办理的手机号';
            this.dialogContent = '为保障您的账号及后续运输安全，请确保注册手机号是以本人身份证办理';
            this.dialogCancelable = false;
            this.dialogLeftCallback = () => {
                //修改本人手机号
                this.setState({showDialog: false});
                RouterUtils.skipRouter(RouterUrl.UserChangeSelfPhoneActivity, {
                    threeElementsName: quserPromote.threeElementsName,
                    threeElementsIdCardNo: quserPromote.threeElementsIdCardNo,
                    faceReason: quserPromote.faceReason,
                    riskSubmit: quserPromote.riskSubmit,
                    userUpdate: '1',
                });
            };
            this.dialogRightCallback = () => {
                this.setState({showDialog: false});
                if (TextUtils.equals('1', quserPromote.riskSubmit)) {
                    // 申诉过
                    // 待审核/审核驳回
                    // 申诉过
                    // 待审核/审核驳回
                    RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                        faceReason: quserPromote.faceReason,
                        idCardNo: quserPromote.threeElementsIdCardNo,
                        FromFrag: '13',
                        isLogin: '1',
                    });
                } else {
                    //跳转常用手机号申诉
                    RouterUtils.skipRouter(RouterUrl.UserComUsePhoneApplyActivity, {
                        threeElementsName: quserPromote.threeElementsName,
                        threeElementsIdCardNo: quserPromote.threeElementsIdCardNo,
                        faceReason: quserPromote.faceReason,
                        riskSubmit: quserPromote.riskSubmit,
                        userUpdate: '1',
                    });
                }
            };
            this.setState({showDialog: true});
        } else if (TextUtils.equals('1', quserPromote.verifyState)) {
            let dialog = new DialogBuilder();
            dialog.title = '温馨提示';
            dialog.model = 1;
            dialog.canBack = false;
            dialog.msg = '请完成人脸识别身份验证后操作';
            dialog.okTxt = '去验证';
            dialog.onOkEvent = () => {
                //再次调用接口判断实名认证状态
                if (TextUtils.equals('1', quserPromote.riskSubmit)) {
                    RouterUtils.skipRouter(RouterUrl.CommVidioDetailRouter, {
                        faceReason: '1',
                        FromFrag: '5',
                        userUpdate: '1',
                    });
                } else {
                    //人脸识别
                    this.liveImage();
                }
            };
            this._showDialog(dialog);
        } else {
            this.carrierBossSubmit2Examing();
        }
    }

    liveImage() {
        Method.getFaceInfo().then((responseData) => {
            let req = new ReqFaceRegoin();
            let res = JSON.parse(responseData);
            let faceInfo = JSON.parse(res.faceInfo);
            req.idCardName = this.pageDetail?.idCard?.idCardName;
            req.idCardNo = this.pageDetail?.idCard?.idCardNo;
            req.orderNo = faceInfo?.orderNo;
            req.request().then((response) => {
                if (response.isSuccess()) {
                    this.carrierBossSubmit2Examing();
                } else {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'FaceRecognitionFailurePage',
                        data: {
                            idcardFrontUrl: this.frontIdCardUrl,
                            idcardReverseUrl: this.negativeIdCardUrl,
                        },
                        callBack: () => {
                            this.liveImage();
                        },
                    });
                }
            });
        });
    }

    carrierBossSubmit2Examing() {
        let req = new ReqCarrierBossSubmit2Examing();
        req.request().then((response) => {
            if (response.isSuccess()) {
                onEvent({
                    pageId: UserType.isBoss() ? 'BossCertificationAuthenticationNoPassPage' : 'DriverCertificationAuthenticationNoPassPage',
                    tableId: UserType.isBoss() ? 'carownerCertificationSuccessCarrier' : 'carrierCertificationSuccessCarrier',
                });
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'CertificationSubmitSuccessPage',
                    data: {
                        update: false,
                    },
                });
            } else {
                this._showMsgDialog(response.getMsg());
            }
        });
    }

    getInput(title?: string, value?: string) {
        switch (title) {
            case LanguageType.getTxt('姓名'):
                this.setState({idCardName: value ?? '', idCardNameText: ''});
                return;
            case LanguageType.getTxt('身份证'):
                this.setState({idCardNo: value ?? '', idCardNoText: ''});
                return;
        }
    }

    showView(show: boolean) {
        this.setState({autoInput: show});
        if (this.isReturn) {
            this.setState({cl2: false, llIdCardInfo: false});
        } else {
            if (show) {
                //拍摄身份证
                if (this.leftImg != null && this.rightImg != null) {
                    this.leftImg.onSelectFile = async (callback: Function) => {
                        this.typeOcr = 'WBOCRSDKTypeFrontSide';
                        await this.idCardScan('WBOCRSDKTypeFrontSide', callback);
                    };
                    this.leftImg.onPostSuccess = (url) => {
                        this.frontIdCardUrl = url;
                    };
                    this.rightImg.onSelectFile = async (callback: Function) => {
                        this.typeOcr = 'WBOCRSDKTypeBackSide';
                        await this.idCardScan('WBOCRSDKTypeBackSide', callback);
                    };
                    this.rightImg.onPostSuccess = (url) => {
                        this.negativeIdCardUrl = url;
                    };
                } else {
                    this.leftImg.onSelectFile = async (callback: Function) => {
                        if (TextUtils.equals('身份证人像面', this.leftImg.title)) {
                            this.typeOcr = 'WBOCRSDKTypeFrontSide';
                            await this.idCardScan('WBOCRSDKTypeFrontSide', callback);
                        } else {
                            this.typeOcr = 'WBOCRSDKTypeFrontSide';
                            await this.idCardScan('WBOCRSDKTypeFrontSide', callback);
                        }
                    };
                    this.leftImg.onPostSuccess = (url) => {
                        if (TextUtils.equals('身份证人像面', this.leftImg.title)) {
                            this.frontIdCardUrl = url;
                        } else {
                            this.negativeIdCardUrl = url;
                        }
                    };
                }
                if (TextUtils.isEmpty(this.frontIdCardUrl) && TextUtils.isEmpty(this.negativeIdCardUrl)) {
                    this.setState({cl2: false, llIdCardInfo: false, cl3: false});
                } else {
                    this.setState({cl2: show, llIdCardInfo: show, cl3: true});
                }
            } else {
                //手动输入身份证号
                if (this.leftImg != null && this.rightImg != null) {
                    this.leftImg.onSelectFile = undefined;
                    this.rightImg.onSelectFile = undefined;
                    this.leftImg.onPostSuccess = (url) => {
                        this.frontIdCardUrl = url;
                        this.idCardLicenseOcr('1');
                    };
                    this.rightImg.onPostSuccess = (url) => {
                        this.frontIdCardUrl = url;
                        this.idCardLicenseOcr('2');
                    };
                } else {
                    this.leftImg.onSelectFile = undefined;
                    this.leftImg.onPostSuccess = (url) => {
                        if (TextUtils.equals('身份证人像面', this.leftImg.title)) {
                            this.frontIdCardUrl = url;
                            this.idCardLicenseOcr('1');
                        } else {
                            this.negativeIdCardUrl = url;
                            this.idCardLicenseOcr('2');
                        }
                    };
                }
                this.setState({cl2: show, llIdCardInfo: true, cl3: true});
            }
        }
    }

    /**
     * OCR识别
     * @param type
     * @returns {Promise<void>}
     * @param callBack
     */
    async idCardScan(type?: string, callBack?: Function) {
        Method.startOcrInfo(type ?? '', (res) => {
            if (res != null) {
                let filepath = this.refreshTencentCard(JSON.parse(res), this.typeOcr);
                callBack && callBack(filepath);
            } else {
                this._showToast('OCR识别失败');
                return '';
            }
        });
    }

    refreshTencentCard(result: any, type?: string): string {
        if (result == null) {
            return '';
        }
        if (TextUtils.equals('WBOCRSDKTypeFrontSide', type)) {
            if (TextUtils.isEmpty(result.frontFullImageSrc)) {
                this._showMsgDialog('文件损坏，请重新选择文件');
                return '';
            }
            if (!this.isReturn) {
                this.setState({idCardNo: result.cardNum, idCardName: result.name});
            }
            Method.putStringExtra('result_front', JSON.stringify(result));
            return result.frontFullImageSrc ?? '';
        } else {
            if (TextUtils.isEmpty(result.backFullImageSrc)) {
                this._showMsgDialog('文件损坏，请重新选择文件');
                return '';
            }
            let validDate = result.validDate;
            if (!TextUtils.isEmpty(validDate)) {
                let validDates: string = validDate.substring(validDate.indexOf('-') + 1);
                let idCardValidDates: string = '长期';

                if (validDates.length === 8) {
                    idCardValidDates = validDates.substring(0, 4) + '-' + validDates.substring(4, 6) + '-' + validDates.substring(6, 8);
                }
                this.setState({idCardEffectDate: idCardValidDates, cl3: true});
            }
            Method.putStringExtra('result_front', JSON.stringify(result));
            return result.backFullImageSrc ?? '';
        }
    }

    render() {
        let title = '';
        if (this.mode == 0) {
            title = '身份认证';
        } else {
            title = getLicenseTitle(`${this.pageDetail?.idCard?.licenseStatus ?? this.rspQueryUserExpireLicenseB.licenseStatus}`);
        }
        return (
            <View style={{flex: 1}}>
                <UITitleView title={title} />
                {this.state.showTip && (
                    <View
                        style={{
                            backgroundColor: '#FFF4EE',
                            paddingHorizontal: 15,
                            paddingVertical: 12,
                            flexDirection: 'row',
                            alignItems: 'flex-start',
                            justifyContent: 'center',
                        }}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#333',
                                marginRight: 12,
                            }}>
                            {TextUtils.equals('6', `${this.rspQueryUserExpireLicenseB.licenseStatus}`) ? '如果您已更换证件可上传最新证件，如果证件未过期或未更换则无需上传。' : '证件即将 / 已经过期，为保障您的业务不受影响，请及时更新'}
                        </Text>
                        <UITouchableOpacity
                            onPress={() => {
                                this.setState({showTip: false});
                            }}>
                            <UIImage source={'close_red'} style={{width: 15, height: 15}} />
                        </UITouchableOpacity>
                    </View>
                )}
                {this.state.init && (
                    <ScrollView>
                        {this.state.clTop1 && (
                            <View
                                style={[
                                    styles.row,
                                    {
                                        paddingTop: 14,
                                        paddingBottom: 8,
                                        paddingHorizontal: 14,
                                        backgroundColor: '#fff',
                                    },
                                ]}>
                                <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold'}}>
                                    {LanguageType.getTxt(this.state.autoInput ? '身份证信息' : '填写身份证信息')}
                                    <Text style={{color: 'red'}}>*</Text>
                                </Text>
                                {!isIDCardTakePhote() && (
                                    <UITouchableOpacity
                                        style={styles.row}
                                        onPress={() => {
                                            this.showView(!this.state.autoInput);
                                        }}>
                                        <Text style={{fontSize: 13, color: '#5086fc', marginRight: 5}}>{this.state.autoInput ? '手动输入身份证号' : '去拍摄身份证'}</Text>
                                        <UIImage source={'base_right_arrow_blue'} style={{width: 5, height: 9}} />
                                    </UITouchableOpacity>
                                )}
                            </View>
                        )}
                        <View style={{backgroundColor: '#fff', alignItems: 'center'}}>
                            <ComCertificationUploadImgView
                                style={{paddingVertical: 15, width: '100%', paddingHorizontal: 10}}
                                tvTitle1={'身份证照片'}
                                ivWarn={false}
                                showViewBg={this.showViewBg}
                                tvDesc={'拍摄身份证正反面，避免反光，确认文字内容清晰可见'}
                                tvTitle3={''}
                                layout={
                                    !(TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') || TextUtils.equals(this.pageDetail?.idCard?.negativeIdCardRiskAudit, '1')) ||
                                    (TextUtils.equals(this.pageDetail?.idCard?.frontIdCardRiskAudit, '1') && TextUtils.equals(this.pageDetail?.idCard?.negativeIdCardRiskAudit, '1'))
                                        ? 1
                                        : 2
                                }
                                leftImg={this.leftImg}
                                rightImg={this.rightImg}
                                auditText={this.errorMsgs}
                            />
                        </View>
                        {this.state.cl2 && (
                            <View
                                style={{
                                    paddingTop: 14,
                                    paddingBottom: 8,
                                    paddingHorizontal: 14,
                                    backgroundColor: '#fff',
                                    marginTop: 8,
                                }}>
                                <Text style={{fontSize: 18, color: '#333', fontWeight: 'bold'}}>
                                    {LanguageType.getTxt('确认信息')}
                                    <Text style={{color: 'red'}}>*</Text>
                                </Text>
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: '#666',
                                        marginTop: 8,
                                    }}>
                                    请确认以下信息，如有误请点击上图重拍摄或点击修改信息内容
                                </Text>
                            </View>
                        )}
                        {this.state.llIdCardInfo && (
                            <View style={{backgroundColor: '#fff', marginTop: 8}}>
                                {this.renderInputItemView(LanguageType.getTxt('姓名'), this.state.idCardName, true, true, this.state.idCardNameText)}
                                <View style={{height: 0.5, backgroundColor: '#e3e3e3'}} />
                                {this.renderInputItemView(LanguageType.getTxt('身份证'), this.state.idCardNo, true, true, this.state.idCardNoText)}
                                <View style={{height: 0.5, backgroundColor: '#e3e3e3'}} />
                                {this.state.cl3 && this.renderInputItemView(LanguageType.getTxt('有效期'), this.state.idCardEffectDate, false, true, this.state.idCardEffectDateText)}
                            </View>
                        )}

                        <UIButton text={'提交上传'} height={55} style={{marginTop: 35, marginHorizontal: 18}} onPress={() => this.commit()} />
                        <UITouchableOpacity
                            style={{marginTop: 20, alignItems: 'center', marginBottom: 25}}
                            onPress={() => {
                                //跳转角色变更
                                RouterUtils.skipRouter(RouterUrl.CertificateRoleChangeActivity, {});
                            }}>
                            <Text style={{fontSize: 14, color: '#5086fc'}}>{'会员类型变更 >'}</Text>
                        </UITouchableOpacity>
                    </ScrollView>
                )}
                <UIServerAndHelpView style={{marginTop: 15}} rightText={'认证遇到问题'} />
                {/*初始化基础组件*/}
                {this._initCommView()}
                {/*日期弹窗*/}
                {this.state.timeShow && (
                    <UIDatePicker
                        mode={'date'}
                        title={'请选择有效期'}
                        onHideEvent={() => {
                            this.setState({timeShow: false});
                        }}
                        onSelectEvent={(date) => {
                            this.setState({idCardEffectDate: dateFormat(date, 'yyyy-MM-dd'), idCardEffectDateText: ''});
                        }}
                    />
                )}
                {this.state.showDialog && (
                    <ThreeElementsTipsDialog
                        title={this.dialogTitle}
                        content={this.dialogContent}
                        cancelable={this.dialogCancelable}
                        onRightCallback={this.dialogRightCallback}
                        onLeftCallback={this.dialogLeftCallback}
                        onClose={() => this.setState({showDialog: false})}
                    />
                )}
                {this.state.showCertificationForFriverExistDailog && (
                    <CertificationForFriverExistDailog
                        content={this.dialog2Content}
                        cancelable={false}
                        onRightCallback={this.dialog2RightCallback}
                        onLeftCallback={this.dialog2LeftCallback}
                        onClose={() => this.setState({showCertificationForFriverExistDailog: false})}
                    />
                )}
                {this.state.showCertificationForFriverExistSuccessDailog && (
                    <CertificationForFriverExistSuccessDailog
                        content={`账号替换处理成功，请使用当新手机号【${Method.getLogin().mobile}】重新登录`}
                        cancelable={false}
                        onRightCallback={() => {
                            this.setState({showCertificationForFriverExistSuccessDailog: false});
                            //退出应用
                            Method.onLoseToken();
                        }}
                        onClose={() => this.setState({showCertificationForFriverExistSuccessDailog: false})}
                    />
                )}
            </View>
        );
    }

    renderInputItemView(title: string, content?: string, isInput?: boolean, editable?: boolean, errorMsg?: string) {
        return (
            <View style={{paddingVertical: 7}}>
                <View style={[styles.row, {paddingHorizontal: 14}]}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        {TextUtils.isNoEmpty(errorMsg) && <UIImage source={'icon_warning_new'} style={{width: 15, height: 15, marginRight: 5}} />}
                        <Text style={{fontSize: 16, color: '#333'}}>{title}</Text>
                    </View>
                    <UITouchableOpacity
                        activeOpacity={1}
                        style={[
                            styles.row,
                            {
                                justifyContent: 'center',
                                backgroundColor: TextUtils.isNoEmpty(errorMsg) ? '#FFF5F1' : undefined,
                                borderColor: TextUtils.isNoEmpty(errorMsg) ? '#FF5E1C' : undefined,
                                borderWidth: TextUtils.isNoEmpty(errorMsg) ? 0.5 : 0,
                                borderRadius: TextUtils.isNoEmpty(errorMsg) ? 5 : 0,
                                marginRight: 9,
                                paddingVertical: 7,
                                paddingRight: 5,
                            },
                        ]}
                        onPress={() => !isInput && editable && this.setState({timeShow: true})}>
                        {isInput ? (
                            <TextInput
                                style={{fontSize: 16, paddingRight: 10, textAlign: 'right', maxWidth: 220, minWidth: 80, paddingLeft: 14}}
                                value={content}
                                editable={editable}
                                placeholder={editable ? '请输入' : ''}
                                onChangeText={(text) => {
                                    this.getInput(title, text);
                                }}
                            />
                        ) : (
                            <Text
                                style={{
                                    fontSize: 16,
                                    color: content ? '#333' : '#999',
                                    maxWidth: 220,
                                    minWidth: 80,
                                    paddingRight: 10,
                                    paddingLeft: 14,
                                    textAlign: 'right',
                                }}>
                                {content ? content : '请选择'}
                            </Text>
                        )}
                        <UIImage source={TextUtils.isNoEmpty(errorMsg) ? 'certification_fill_in_red' : 'certification_fill_in_gray'} style={{width: 15, height: 15}} />
                    </UITouchableOpacity>
                </View>
                {TextUtils.isNoEmpty(errorMsg) && (
                    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center', paddingLeft: 15, paddingRight: 15}}>
                        <Text style={{fontSize: 13, color: '#FF4747', flex: 1}}>{errorMsg}</Text>
                    </View>
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
});
