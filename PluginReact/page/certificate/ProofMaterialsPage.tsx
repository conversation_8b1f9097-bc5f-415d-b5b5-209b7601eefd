import {ScrollView, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState, DialogBuilder} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import {http} from '../const.global';
import ComCertificationMultipleImageView from './views/ComCertificationMultipleImageView';
import {EImage} from './models/EImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ArrayUtils} from '../util/ArrayUtils';
import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import {ReqVehicleDetails, toImageList} from './requests/ReqVehicleDetails';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';

interface State extends BaseState {
    ownRelation: string; // 1、挂靠关系 3、购买关系
    proofPictureUrl?: EImage[]; //车老板挂靠证明
    carPurchaseProofPictureUrl?: EImage[]; // 购车证明
    paymentProofPictureUrl?: EImage[]; // 支付证明
    isInit: boolean;
}

/**
 * 注释: 证明材料新增-编辑
 * 时间: 2025/2/24 10:42
 * <AUTHOR>
 */
export default class ProofMaterialsPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    // 0:上传 1:更新 2:过期
    mode: number;
    pageParams: any;
    //挂靠证明说明
    illustrate1: string = '证明中必须包括挂靠日期起止，车辆的车架号、车牌号、发动机号，以及车老板姓名和身份证号，双方权利与义务，企业材料需要加盖公章，个体证明需签字加盖手印；';
    //购车证明说明
    illustrate2: string =
        '车老板购车合同（如：车老板购车贷款合同、或车辆买卖合同、或融资租赁合同、或售后回租合同），必须包括日期，车辆的车架号、车牌号、发动机号，以及车老板姓名和身份证号，双方权利与义务，企业材料需要加盖公章，个体证明需签字加盖手印；';
    //支付证明说明
    illustrate3: string = '车老板支付的购买车辆全款转账记录、首付款和连续近3个月及以上的分期还款转账记录。\n以行驶证发证日期为准，购买不足3个月的，提供行驶证发证日期以来的首付款和分期还款转账记录。';
    //成功回调
    callBack: Function = this.getCallBack();
    //车辆id
    vehicleId?: string;
    //车辆所属  0、非自有车辆  1、自有车辆
    ownFlag?: string;

    constructor(props) {
        super(props);
        this.state = {
            ownRelation: '1',
            isInit: false,
        };
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        this.vehicleId = this.pageParams?.vehicleId;
        this.ownFlag = this.pageParams?.ownFlag;
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryVehicleDetail();
    }

    /**
     * 注释：请求车辆详情
     * 时间：2025/2/25 10:23
     * @author：宋双朋
     */
    queryVehicleDetail() {
        //车辆id不为空 请求车辆详情
        let req = new ReqVehicleDetails();
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                this.setState({
                    ownRelation: item?.ownRelation ?? '1',
                    proofPictureUrl: toImageList(item, 1),
                    carPurchaseProofPictureUrl: toImageList(item, 2),
                    paymentProofPictureUrl: toImageList(item, 3),
                    isInit: true,
                });
            } else {
                this._showToast(res.getMsg());
                this.setState({
                    isInit: true,
                });
            }
        });
    }

    /**
     * 注释：是否展示挂靠证明
     * 时间：2025/2/25 13:53
     * @author：宋双朋
     * @returns {boolean}
     */
    showAnchored = (): boolean => {
        return TextUtils.equals(this.state.ownRelation, '1');
    };
    /**
     * 注释：提交上传
     * 时间：2025/2/25 13:53
     * @author：宋双朋
     */
    licenseInputAccuracyCheck = () => {
        let req = new ReqLicenseInputAccuracyCheck();
        //车辆所属关系
        req.ownRelation = this.state.ownRelation;
        req.type = '12';
        req.isNeedSave = this.mode == 0 ? '1' : '0';
        req.vehicleId = this.vehicleId;
        req.ownFlag = this.ownFlag ?? '0';
        if (this.showAnchored()) {
            //挂靠证明
            let proofPictureUrlList =
                this.state.proofPictureUrl?.map((item) => {
                    return item.imageId ?? '';
                }) ?? [];
            if (ArrayUtils.isEmpty(proofPictureUrlList)) {
                this._showMsgDialog('挂靠证明不能为空');
                return;
            }
            req.proofPictureUrl = proofPictureUrlList.join(',');
        }
        //购车证明
        let carPurchaseProofPictureUrl =
            this.state.carPurchaseProofPictureUrl?.map((item) => {
                return item.imageId ?? '';
            }) ?? [];
        if (ArrayUtils.isEmpty(carPurchaseProofPictureUrl)) {
            this._showMsgDialog('购车证明不能为空');
            return;
        }
        req.carPurchaseProofPictureUrl = carPurchaseProofPictureUrl.join(',');
        //支付证明
        let paymentProofPictureUrl =
            this.state.paymentProofPictureUrl?.map((item) => {
                return item.imageId ?? '';
            }) ?? [];
        if (ArrayUtils.isEmpty(paymentProofPictureUrl)) {
            this._showMsgDialog('支付证明不能为空');
            return;
        }
        req.paymentProofPictureUrl = paymentProofPictureUrl.join(',');
        req.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    /**
     * 注释：切换点击事件
     * 时间：2025/2/25 13:54
     * @author：宋双朋
     * @param title
     * @param value
     */
    selectType(title: string, value: boolean) {
        switch (title) {
            case '车辆所属关系':
                this.setState({ownRelation: value ? '1' : '3'});
                break;
            default:
                break;
        }
    }

    renderRadioOrSelectView = (title: string, isMust: boolean, btnValue: boolean, radio1?: string, radio2?: string) => {
        return (
            <View style={styles.itemStyle}>
                <Text style={{fontSize: 16, color: '#333'}}>
                    {title}
                    {isMust && <Text style={{color: 'red'}}>*</Text>}
                </Text>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UITouchableOpacity onPress={() => this.selectType(title, true)} style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={btnValue ? 'base_check_select' : 'base_check_unselect'} style={{width: 12, height: 12, paddingRight: 5}} />
                        <Text style={{fontSize: 16, color: '#666', marginRight: 14, marginStart: 5}}>{radio1}</Text>
                    </UITouchableOpacity>
                    <UITouchableOpacity onPress={() => this.selectType(title, false)} style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={btnValue ? 'base_check_unselect' : 'base_check_select'} style={{width: 12, height: 12, paddingRight: 5}} />
                        <Text style={{fontSize: 16, color: '#666', marginStart: 5}}>{radio2}</Text>
                    </UITouchableOpacity>
                </View>
            </View>
        );
    };

    render() {
        let title = '车辆认证';
        if (this.mode == 1) {
            title = '证件过期上传';
        }
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={title}
                    rightText={'客服'}
                    clickRight={() => {
                        //打开客服
                        Method.openServerPopWindow();
                    }}
                />
                {this.renderRadioOrSelectView('车辆所属关系', true, this.showAnchored(), '挂靠关系', '购买关系')}
                {this.state.isInit && (
                    <ScrollView style={{flex: 1}}>
                        {this.showAnchored() && (
                            <ComCertificationMultipleImageView
                                tvTitle1={'挂靠证明'}
                                style={{marginTop: 1, backgroundColor: '#FFFFFF'}}
                                imgs={this.state.proofPictureUrl}
                                max={5}
                                rowSize={4}
                                transformUrl={(url: string) => http.imagUrl(url)}
                                showLoading={(show: boolean) => {
                                    show ? this._showWaitDialog() : this._dismissWait();
                                }}
                                tvDoubt={true}
                                tvRedAsterisk={true}
                                onDoubtClick={(title?: string) => {
                                    let dialog = new DialogBuilder();
                                    dialog.title = title ?? '';
                                    dialog.views = <Text style={styles.alertMsgStyle}>{this.illustrate1}</Text>;
                                    dialog.model = 1;
                                    dialog.okTxt = '我知道了';
                                    this._showDialog(dialog);
                                }}
                                onChange={(img) => {
                                    this.setState({proofPictureUrl: img});
                                }}
                                edit={true}
                            />
                        )}
                        <ComCertificationMultipleImageView
                            tvTitle1={'购车证明'}
                            style={{marginTop: 7, backgroundColor: '#FFFFFF'}}
                            imgs={this.state.carPurchaseProofPictureUrl}
                            max={5}
                            rowSize={4}
                            transformUrl={(url: string) => http.imagUrl(url)}
                            showLoading={(show: boolean) => {
                                show ? this._showWaitDialog() : this._dismissWait();
                            }}
                            tvDoubt={true}
                            tvRedAsterisk={true}
                            onDoubtClick={(title?: string) => {
                                let dialog = new DialogBuilder();
                                dialog.title = title ?? '';
                                dialog.views = <Text style={styles.alertMsgStyle}>{this.illustrate2}</Text>;
                                dialog.model = 1;
                                dialog.okTxt = '我知道了';
                                this._showDialog(dialog);
                            }}
                            onChange={(img) => {
                                this.setState({carPurchaseProofPictureUrl: img});
                            }}
                            edit={true}
                        />
                        <ComCertificationMultipleImageView
                            tvTitle1={'支付证明'}
                            style={{marginTop: 7, backgroundColor: '#FFFFFF'}}
                            imgs={this.state.paymentProofPictureUrl}
                            max={10}
                            rowSize={4}
                            transformUrl={(url: string) => http.imagUrl(url)}
                            showLoading={(show: boolean) => {
                                show ? this._showWaitDialog() : this._dismissWait();
                            }}
                            tvDoubt={true}
                            tvRedAsterisk={true}
                            onDoubtClick={(title?: string) => {
                                let dialog = new DialogBuilder();
                                dialog.title = title ?? '';
                                dialog.views = <Text style={styles.alertMsgStyle}>{this.illustrate3}</Text>;
                                dialog.model = 1;
                                dialog.okTxt = '我知道了';
                                this._showDialog(dialog);
                            }}
                            onChange={(img) => {
                                this.setState({paymentProofPictureUrl: img});
                            }}
                            edit={true}
                        />
                    </ScrollView>
                )}
                <UIButton
                    text={'提交上传'}
                    style={{borderRadius: 0}}
                    fontWeight={true}
                    onPress={() => {
                        this.licenseInputAccuracyCheck();
                    }}
                />
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    itemStyle: {
        backgroundColor: '#fff',
        paddingHorizontal: 14,
        paddingVertical: 14,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    alertMsgStyle: {
        color: '#333',
        textAlign: 'left',
        paddingLeft: 15,
        paddingRight: 15,
        paddingBottom: 7,
    },
});
