import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {BackHandler, KeyboardAvoidingView, ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import UITitleView from '../widget/UITitleView';
import UIButton from '../widget/UIButton';
import React from 'react';
import TextUtils from '../util/TextUtils';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import {DrivingOCR, ReqVehicleLicenseOcrCheck, ReqVehicleLicenseOcrCheckExpire} from './requests/ReqVehicleLicenseOcrCheck';
import OcrFailTipDialog from './views/OcrFailTipDialog';
import {UIImageSingleFileViewRef} from '../widget/UIImageSingleFileView';
import Picker from 'react-native-picker';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import {RouterUtils} from '../util/RouterUtils';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import {ReqQueryDictConfig} from './requests/ReqQueryDictConfig';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import {dateFormat} from '../util/DateUtil';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import {Constant} from '../base/Constant';
import EventBus from '../util/EventBus';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import PlateNumberDialog from './views/PlateNumberDialog';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import {ReqUpdateMemberLicense} from './requests/ReqUpdateMemberLicense';
import UIPopup from '../widget/UIPopup';
import {FlashList} from '@shopify/flash-list';
import UIDatePicker from '../widget/UIDatePicker';

// 牵引车行驶证
interface State extends BaseState {
    //行驶证图片地址
    vehicleLicUrl?: string;
    // 年检页图片地址
    annualVehicleLicenseUrl?: string;
    // 行驶证有效期至
    checkExpiryDate?: string;
    // 车牌号
    plateNumber?: string;
    //     一开始是否展示表单
    showForm?: boolean;
    // 是否展示年检页
    showAnnualPage?: boolean;
    // 显示OCR识别失败弹窗
    showOcrFailDialog?: boolean;
    // 车牌颜色
    drivingColor?: string;
    plateNumberText?: string; //车牌号错误提示
    vehicleLicenseEffectDateText?: string; //行驶证年检页有效期错误提示
    //OCR失败次数
    ocrFailNum: number;
    //     展示时间选择器
    showDatePicker: boolean;
    emissionStandard?: string;
    pNumber?: string; //过期用到的车牌号
    canEdit?: boolean; //是否不能点击上传年检页
    licenseReason?: string;
    showTopTip?: boolean;
    showPlateNumberDialog?: boolean;
    showPlateColor?: boolean;
}

export default class DrivingHeadLicensePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // OCR最大识别次数
    maxOcrFailNum: number;
    // 0:上传  其他更新、过期
    mode: number;
    //1认证  2添加车辆
    addOrManage: number;
    // 行驶证ref
    imagePaperUploadRef = React.createRef<UIImageSingleFileViewRef>();
    // 年检页ref
    imagePaperUploadRefV1 = React.createRef<UIImageSingleFileViewRef>();
    //总质量是否>4.5吨 0、否 1、  普通货车必传，挂车可不传（null）或传0
    backup2?: string;
    //临时/超限车辆 0、否 1、  挂车必传，普通货车可不传（null）或传0
    backup1?: string;

    //回调
    callBack: Function = this.getCallBack();
    //vehicleFlag 车辆类型(1.普通 2.半挂)
    flag?: string;
    //车牌颜色
    colorOptions: string[] = [];

    constructor(props) {
        super(props);
        this.state = {
            showDatePicker: false,
            vehicleLicUrl: '',
            checkExpiryDate: '',
            plateNumber: '',
            showForm: false,
            showOcrFailDialog: false,
            showAnnualPage: true,
            annualVehicleLicenseUrl: '',
            drivingColor: '',
            plateNumberText: undefined,
            vehicleLicenseEffectDateText: undefined,
            ocrFailNum: 0,
            emissionStandard: '',
            pNumber: '', //过期用到的车牌号
            canEdit: this.getParams().mode == 2,
            licenseReason: '', //驳回理由
        };
        this.pageParams = this.getParams();
        // 获取种类
        this.mode = this.pageParams?.mode ?? 0;
        // 新增或者车辆认证 1 证件新增 2车辆认证
        this.addOrManage = this.pageParams?.addOrManage ?? 0;
        this.flag = this.pageParams?.flag ?? '1';
        // 查ocr次数
        this.queryDriverLicenseNumber();
        this.backup2 = this.pageParams?.backup2 ?? '0';
        this.backup1 = this.pageParams?.backup1 ?? '0';
    }

    handleBackPress = () => {
        //阻断系统返回
        Picker.hide();
        RouterUtils.skipPop();
        return true;
    };

    componentDidMount() {
        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
        if (this.mode != 2) {
            if (this.addOrManage == 1) {
                this.queryCacheLiscense();
            } else if (this.addOrManage == 2) {
                this.queryCarCacheLiscense();
            } else {
                this.queryUserExpireLicenseDetail();
            }
        } else {
            this.queryUserExpireLicenseDetail();
        }
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        BackHandler.removeEventListener('hardwareBackPress', this.handleBackPress);
    }

    // 查询证件新增详情
    queryCacheLiscense() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({
                    annualVehicleLicenseUrl: res?.data?.vehicle?.tractorVehicleLicenseUrl,
                    checkExpiryDate: res?.data?.vehicle?.vehicleLicenseEffectDate,
                    showForm: true,
                    //行驶证图片地址
                    vehicleLicUrl: res?.data?.vehicle?.vehicleLicenseUrl,
                    // 车牌号
                    plateNumber: res?.data?.vehicle?.plateNumber,
                    pNumber: res?.data?.vehicle?.plateNumber,
                    showAnnualPage: true,
                    // 车牌颜色
                    drivingColor: res?.data?.vehicle?.plateColor,
                });
                // 行驶证为空,就说明是第一次进来,展示不可编辑的年检页
                if (TextUtils.isEmpty(res?.data?.vehicle?.vehicleLicenseUrl)) {
                    this.setState({
                        showAnnualPage: true,
                        canEdit: false,
                        showForm: false,
                    });
                } else {
                    if (TextUtils.isEmpty(res?.data?.vehicle?.tractorVehicleLicenseUrl)) {
                        this.setState({
                            showAnnualPage: false,
                        });
                    } else {
                        this.setState({
                            showAnnualPage: true,
                            showForm: true,
                            canEdit: true,
                        });
                    }
                }

                console.log(res?.data?.vehicle, '车辆认证详情');
            }
        });
    }

    // 查询车辆认证详情
    async queryCarCacheLiscense() {
        let reqCache = await new ReqVehicleDetails().request();
        if (reqCache.isSuccess() && reqCache.data) {
            this.setState({
                annualVehicleLicenseUrl: reqCache?.data?.tractorVehicleLicenseUrl,
                checkExpiryDate: reqCache?.data?.vehicleLicenseEffectDate,
                showForm: true,
                //行驶证图片地址
                vehicleLicUrl: reqCache.data.triverPermitUrl,
                // 车牌号
                plateNumber: reqCache.data.plateNumber,
                pNumber: reqCache.data.plateNumber,
                showAnnualPage: true,
                // 车牌颜色
                drivingColor: reqCache?.data?.plateNumberColor,
            });
            // 行驶证为空,就说明是第一次进来,展示不可编辑的年检页
            if (TextUtils.isEmpty(reqCache?.data?.triverPermitUrl)) {
                this.setState({
                    showAnnualPage: true,
                    canEdit: false,
                    showForm: false,
                });
            } else {
                // 行驶证不为空,那么就根据年检页是否有值来判断,如果年检页有值,那么就展示年检页,如果没有,那么就隐藏
                if (TextUtils.isEmpty(reqCache?.data?.tractorVehicleLicenseUrl)) {
                    this.setState({
                        showAnnualPage: false,
                    });
                } else {
                    this.setState({
                        showAnnualPage: true,
                        showForm: true,
                        canEdit: true,
                    });
                }
            }
            console.log(reqCache, '添加车辆详情');
        }
    }

    /**
     * 证件过期接口查询
     * @param targetId
     * @param licenseType
     */
    queryUserExpireLicenseDetail() {
        let req = new ReqQueryUserExpireLicenseDetail();
        req.targetId = this.getParams()?.vehicleId ?? '';
        req.licenseType = '200008';
        this.setState({
            canEdit: true,
            showAnnualPage: true,
        });
        req.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({
                    licenseReason: res.data?.licenseReason ?? '',
                    showTopTip: TextUtils.isNoEmpty(res.data?.licenseReason),
                });
            }

            console.log(res, '证件过期详情');
        });
    }

    // 获取ocr次数
    queryDriverLicenseNumber() {
        let request = new ReqQueryDictConfig();
        request.dictCode = 'LICENSE_UPLOAD_VALIDATE_SETTINGS';
        request.request().then((res) => {
            if (res.isSuccess()) {
                let config = res.data?.records?.find((e) => {
                    return e.dictKey == 'VEHICLE_LICENSE';
                });
                if (config) {
                    this.maxOcrFailNum = parseFloat(config.value ?? '2');
                }
            }
        });
    }

    // 主副页完整性校验
    licenseOCRCheck(url: string) {
        let request = new ReqVehicleLicenseOcrCheck();
        request.vehicleLicUrl = url;
        request.vehicleFlag = '2'; //牵引车
        request.request().then((res) => {
            if (res.isSuccess()) {
                // 校验年间有效期是否已经过期,过期则年检页必传,没过期隐藏年检页
                if (res.data?.data?.checkExpiryDate?.includes('长期') || (res.data?.data?.checkExpiryDate && dateFormat(new Date(), 'yyyy-MM-dd') < res.data.data.checkExpiryDate)) {
                    this.setState({
                        checkExpiryDate: res?.data?.data?.checkExpiryDate?.includes('长期') ? '2099-01-01' : res.data?.data?.checkExpiryDate,
                        plateNumber: res.data?.data?.plateNumber,
                        showForm: true,
                        plateNumberText: '',
                        vehicleLicenseEffectDateText: '',
                    });
                    if (this.mode != 2 && this.state.annualVehicleLicenseUrl == '') this.setState({showAnnualPage: false});
                } else {
                    // 如果证件过期,只要设置车牌号
                    this.setState({
                        plateNumber: res.data?.data?.plateNumber,
                        showForm: true,
                        showAnnualPage: true,
                        canEdit: true,
                    });
                }
                //大于4.5t
                if (res.data?.data.plateNumber?.length == 7) {
                    this.setState({
                        drivingColor: '黄牌',
                    });
                } else if (res.data?.data.plateNumber?.length == 8) {
                    this.setState({
                        drivingColor: '黄绿牌',
                    });
                }
                console.log('完整性校验成功');
            } else {
                if (this.state.ocrFailNum >= this.maxOcrFailNum) {
                    this.setState({
                        showAnnualPage: true,
                        canEdit: true,
                        vehicleLicUrl: url,
                        showForm: true,
                    });
                }
                this.setState({showOcrFailDialog: true, ocrFailNum: this.state.ocrFailNum + 1});
                console.log('完整性校验失败');
            }
        });
    }

    // ocr校验年检页
    putLicenseDetail(url: string) {
        let request;
        if (this.mode != 2) request = new ReqVehicleLicenseOcrCheck();
        else request = new ReqVehicleLicenseOcrCheckExpire();
        request.annualVehicleLicenseUrl = url;
        request.vehicleFlag = '2';
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({
                    checkExpiryDate: res?.data?.data?.checkExpiryDate?.includes('长期') ? '2099-01-01' : res.data?.data?.checkExpiryDate,
                    showForm: true,
                    vehicleLicenseEffectDateText: '',
                    plateNumberText: '',
                });
                if (res.data?.data?.plateNumber != null && res.data?.data?.plateNumber != '')
                    this.setState({
                        plateNumber: res.data?.data?.plateNumber,
                    });
            } else {
                if (this.mode != 2) {
                    this.setState({
                        // showOcrFailDialogV1: true,
                    });
                } else {
                    this.setState({
                        showForm: true,
                    });
                }
            }
        });
    }

    //  过期年检页上传  type 200006（行驶证年检页）200008（牵引车/车头行驶证年检页）200007（车身行驶证年检页）
    updateDriverAnnualLicense() {
        let request = new ReqUploadExpireDateLicense();
        request.targetId = this.getParams()?.vehicleId ?? '';
        request.newLicenseExpire = this.state.checkExpiryDate;
        request.picUrlList = [
            {
                type: '200008',
                picUrl: this.state.annualVehicleLicenseUrl,
                newLicenseExpire: this.state.checkExpiryDate,
                targetId: this.getParams()?.vehicleId ?? '',
            },
        ];
        request.request().then((res) => {
            RouterUtils.skipPop();
            this.callBack && this.callBack();
            EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
        });
    }

    updateDriverLicense() {
        let request = new ReqUpdateMemberLicense();
        request.picUrlList = [
            {
                type: '200008',
                picUrl: this.state.annualVehicleLicenseUrl,
                newLicenseExpire: this.state.checkExpiryDate,
                isElectronic: '0',
            },
        ];
        request.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast('更新成功');

                this.callBack && this.callBack();
                RouterUtils.skipPop();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    upgradeSaveLicense() {
        this._showToast('上传成功');

        this.callBack && this.callBack();
        RouterUtils.skipPop();
        EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
    }

    // 准确性校验
    licenseAllGetCheck() {
        // 已经上传了行驶证
        if (this.mode != 2) {
            if (TextUtils.isEmpty(this.state.vehicleLicUrl)) {
                this._showMsgDialog('请上传行驶证');
                return;
            }
        }
        // 是否必传行驶证
        if (this.state.showAnnualPage) {
            if (TextUtils.isEmpty(this.state.annualVehicleLicenseUrl)) {
                this._showToast('请上传年检页');
                return;
            }
        }
        if (TextUtils.isEmpty(this.state.plateNumber)) {
            this._showToast('请输入车牌号');
            return;
        }
        if (this.mode != 2) {
            if (TextUtils.isEmpty(this.state.drivingColor)) {
                this._showToast('请选择车牌颜色');
                return;
            }
        }
        if (TextUtils.isEmpty(this.state.checkExpiryDate)) {
            this._showToast('请选择年检有效期止');
            return;
        }

        // 行驶证
        let request = new ReqLicenseInputAccuracyCheck();
        request.type = '6';
        request.isNeedSave = this.mode != 2 ? '1' : '0';
        request.vehicleLicenseUrl = this.state.vehicleLicUrl;
        request.annualVehicleLicenseUrl = this.state.annualVehicleLicenseUrl;
        request.vehicleFlag = this.flag;
        request.backup2 = this.backup2;
        request.backup1 = this.backup1;
        request.plateColor = this.state.drivingColor;
        request.plateNumber = this.state.plateNumber;
        request.vehicleLicenseEffectDate = this.state.checkExpiryDate;
        request.emissionStandard = this.state.emissionStandard;
        request.vehicleId = this.getParams()?.vehicleId ?? '';
        this._showWaitDialog();
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.mode == 0 || this.addOrManage == 2 || this.addOrManage == 1) {
                    this.upgradeSaveLicense();
                } else if (this.mode == 1) {
                    this.updateDriverLicense();
                } else if (this.mode == 2) {
                    this.updateDriverAnnualLicense();
                    this._showToast('更新成功');
                }
                this._dismissWait();
            } else {
                console.log('准确性校验失败失败调用');
                this.setState({
                    plateNumberText: res?.data?.data?.plateNumberText,
                    vehicleLicenseEffectDateText: res?.data?.data?.vehicleLicenseEffectDateText,
                });
                this._dismissWait();
            }
        });
    }

    // 行驶证(主页+副)
    renderView1() {
        return (
            <>
                <View style={{backgroundColor: 'white', marginTop: 10, alignItems: 'center'}}>
                    <ComCertificationUploadImgView
                        // ivWarn={TextUtils.isNoEmpty(this.state.rejectReason)}
                        style={{marginTop: 15}}
                        tvTitle1View={
                            <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                                牵引车/车头行驶证<Text style={{fontSize: 16, color: '#3D3D3D'}}> (主页+副页) </Text>
                                <Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                            </Text>
                        }
                        leftImg={{
                            url: this.state.vehicleLicUrl ?? '',
                            defaultValue: 'certification_driver_license_template_diagram_8',
                            onDelPic: () => {
                                this.setState({
                                    vehicleLicUrl: '',
                                    annualVehicleLicenseUrl: '',
                                });
                            },
                            onPostSuccess: (url) => {
                                this.setState({vehicleLicUrl: url});
                                //   校验完整性主副页
                                this.licenseOCRCheck(url);
                            },
                            ref: this.imagePaperUploadRef,
                        }}
                    />
                </View>
            </>
        );
    }

    //过期页
    renderExpiredView() {
        return (
            <>
                {this.mode == 2 && (
                    <View style={{backgroundColor: 'white', alignItems: 'center'}}>
                        <View style={{backgroundColor: '#FFF3EF', padding: 5}}>
                            <Text
                                style={{
                                    fontSize: 14,
                                    color: 'red',
                                    marginRight: 5,
                                }}>
                                证件即将 / 已经过期，为保障您的业务不受影响，请及时更新。
                            </Text>
                        </View>
                    </View>
                )}
                <View
                    style={{
                        backgroundColor: '#FAFAFA',
                        padding: 5,
                        height: 80,
                        alignItems: 'flex-start',
                        justifyContent: 'center',
                        flex: 1,
                    }}>
                    <Text
                        style={{
                            fontSize: 16,
                            color: 'black',
                            marginRight: 5,
                        }}>
                        {this.getParams().plateNumber ?? ''}
                    </Text>
                </View>
            </>
        );
    }

    //行驶证年检页
    renderView2() {
        return (
            <>
                <View style={{backgroundColor: 'white', marginTop: 5, alignItems: 'center'}}>
                    <ComCertificationUploadImgView
                        style={{marginTop: 15}}
                        tvTitle1View={
                            <Text style={{fontSize: 16, color: '#333', fontWeight: 'bold'}}>
                                牵引车/车头行驶证<Text style={{fontSize: 16, color: '#3D3D3D'}}> (年检页) </Text>
                                <Text style={{fontSize: 16, color: '#FF602E'}}>*</Text>
                            </Text>
                        }
                        leftImg={{
                            url: this.state.annualVehicleLicenseUrl ?? '',
                            edit: this.state.canEdit,
                            defaultValue: this.state.canEdit ? 'certification_driver_license_template_diagram_19' : 'http://img.zczy56.com/202503060834181766860.png',
                            onDelPic: () => {
                                this.setState({annualVehicleLicenseUrl: ''});
                            },
                            onPostSuccess: (url) => {
                                this.setState({annualVehicleLicenseUrl: url});
                                // 年检页准确性校验
                                this.putLicenseDetail(url);
                            },
                            ref: this.imagePaperUploadRefV1,
                        }}
                    />
                    {this.state.licenseReason != null && this.state.licenseReason != '' && (
                        <Text
                            style={{
                                color: 'red',
                                alignSelf: 'flex-start',
                                marginLeft: 14,
                                height: 20,
                                marginTop: 5,
                            }}>
                            驳回理由:{this.state.licenseReason}
                        </Text>
                    )}
                </View>
            </>
        );
    }

    // 绘制表单
    renderFormView() {
        return (
            <View style={{backgroundColor: '#fff', marginTop: 8}}>
                <UITouchableOpacity onPress={() => this.setState({showPlateNumberDialog: true})}>
                    {this.RenderNormalBox({
                        label: '车牌号',
                        placeholder: '请输入',
                        errorMsg: this.state.plateNumberText ?? '车牌号格式错误请核实',
                        isRequired: true,
                        edit: true,
                        onChangeText: (text) => {
                            this.setState({plateNumberText: ''});
                            // 不允许输入'挂'字和'超'字
                            if (!text.includes('挂') && !text.includes('超')) this.setState({plateNumber: text});
                            else this._showToast('车牌号不能输入挂字和超字');
                        },
                        showErrorV1: this.state.plateNumberText,
                        value: this.state.plateNumber,
                        isInput: false,
                    })}
                </UITouchableOpacity>
                {this.mode != 2 && (
                    <UITouchableOpacity
                        onPress={() => {
                            this.showPicker(this.state.plateNumber?.length);
                        }}>
                        {this.RenderNormalBox({
                            label: '车牌颜色',
                            placeholder: '请选择',
                            errorMsg: '车型与车牌颜色不匹配',
                            isRequired: true,
                            edit: false,
                            onChangeText: (text) => {},
                            showErrorV1: '',
                            value: this.state.drivingColor,
                            isInput: true,
                        })}
                    </UITouchableOpacity>
                )}
                <UITouchableOpacity
                    onPress={() => {
                        this.setState({showDatePicker: true});
                        //     刷新错误信息
                        this.setState({vehicleLicenseEffectDateText: ''});
                    }}>
                    {this.RenderNormalBox({
                        label: '年检有效期止',
                        placeholder: '请选择',
                        errorMsg: this.state.vehicleLicenseEffectDateText ?? '年检有效期已过期',
                        isRequired: true,
                        edit: false,
                        onChangeText: '',
                        showErrorV1: this.state.vehicleLicenseEffectDateText,
                        value: this.state.checkExpiryDate,
                        isInput: true,
                    })}
                </UITouchableOpacity>
            </View>
        );
    }

    render() {
        let title = '车辆认证';
        if (this.mode == 2) {
            title = '证件过期上传';
        }
        return (
            <>
                <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                    <UITitleView
                        title={title}
                        clickBack={() => {
                            Picker.hide();
                            RouterUtils.skipPop();
                        }}
                    />
                    <KeyboardAvoidingView behavior={'padding'} style={{flex: 1}}>
                        <ScrollView>
                            {/*驳回提示*/}
                            {this.renderRejectedReason()}
                            {/*标题1*/}
                            {this.mode != 2 && this.renderView1()}
                            {/*行驶证过期文案*/}
                            {this.mode == 2 && this.renderExpiredView()}
                            {/*标题2*/}
                            {this.state.showAnnualPage && this.renderView2()}
                            {/*    绘制表单*/}
                            {this.state.showForm && this.renderFormView()}
                        </ScrollView>
                    </KeyboardAvoidingView>
                    <View style={{backgroundColor: '#fff', paddingHorizontal: 15, paddingBottom: 25, paddingTop: 10}}>
                        <UIButton
                            text={'提交上传'}
                            onPress={() => {
                                this.licenseAllGetCheck();
                            }}
                        />
                    </View>
                    {this.state.showDatePicker && (
                        <UIDatePicker
                            mode={'date'}
                            title={'请选择有效期'}
                            onHideEvent={() => {
                                this.setState({showDatePicker: false});
                            }}
                            onSelectEvent={(date) => {
                                this.setState({checkExpiryDate: dateFormat(date, 'yyyy-MM-dd')});
                            }}
                        />
                    )}
                    {this.state.showOcrFailDialog && this.renderOcrResultFailDialog()}
                    {/*{this.state.showOcrFailDialogV1 && this.renderOcrResultFailDialogV1()}*/}
                    {this.state.showPlateNumberDialog && (
                        <PlateNumberDialog
                            plateNumLen={8}
                            onClose={() => this.setState({showPlateNumberDialog: false})}
                            onChange={(plateNumber: string) => {
                                this.setState({plateNumber: plateNumber, plateNumberText: undefined});
                                //大于4.5t
                                if (plateNumber?.length == 7) {
                                    this.setState({
                                        drivingColor: '黄牌',
                                    });
                                } else if (plateNumber?.length == 8) {
                                    this.setState({
                                        drivingColor: '黄绿牌',
                                    });
                                }
                            }}
                        />
                    )}
                    {this.renderPlateColorView()}
                    {this.initCommView()}
                </View>
            </>
        );
    }

    renderRejectedReason() {
        return this.state.showTopTip ? (
            <View
                style={{
                    backgroundColor: '#FFF4EE',
                    paddingHorizontal: 15,
                    paddingVertical: 12,
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    justifyContent: 'center',
                }}>
                <Text
                    style={{
                        fontSize: 14,
                        color: '#D24F4F',
                        marginRight: 12,
                    }}>
                    红色部分资料需要重新填写
                </Text>
                <UITouchableOpacity
                    onPress={() => {
                        this.setState({showTopTip: false});
                    }}>
                    <UIImage source={'close_red'} style={{width: 15, height: 15}} />
                </UITouchableOpacity>
            </View>
        ) : (
            <></>
        );
    }

    renderOcrResultFailDialog() {
        return (
            <OcrFailTipDialog
                image={'certification_driver_license_template_diagram_8'}
                mainTip={'行驶证模糊或不完整，请重新上传'}
                showContinue={this.state.ocrFailNum > this.maxOcrFailNum}
                onResetUpload={() => {
                    this.setState({showOcrFailDialog: false, vehicleLicUrl: undefined});
                    this.imagePaperUploadRef.current?.clear();
                    // 延迟触发上传，确保状态更新完成
                    setTimeout(() => {
                        this.imagePaperUploadRef.current?.triggerUpload();
                    }, 100);
                }}
                onContinueUpload={() => {
                    this.setState({showOcrFailDialog: false, showForm: true, showAnnualPage: true, canEdit: true});
                }}
            />
        );
    }

    renderOcrResultFailDialogV1() {
        return (
            <OcrFailTipDialog
                image={'certification_driver_license_template_diagram_19'}
                mainTip={'年检页模糊或不完整，请重新上传'}
                showContinue={true}
                onResetUpload={() => {
                    this.setState({annualVehicleLicenseUrl: '', showForm: true});
                    this.imagePaperUploadRefV1.current?.clear();
                    // 延迟触发上传，确保状态更新完成
                    setTimeout(() => {
                        this.imagePaperUploadRefV1.current?.triggerUpload();
                    }, 100);
                }}
                onContinueUpload={() => {
                    this.setState({showForm: true});
                }}
            />
        );
    }

    showPicker(plateLength?: number) {
        // 根据车牌位数设置颜色选项
        this.colorOptions = plateLength !== 7 && plateLength !== 8 ? ['蓝牌', '黄牌', '绿牌', '黄绿牌'] : plateLength === 7 ? ['蓝牌', '黄牌'] : ['绿牌', '黄绿牌'];
        this.setState({showPlateColor: true});
    }

    renderPlateColorView() {
        return this.state.showPlateColor && <UIPopup title={'请选择车牌颜色'} titleStyle={{fontSize: 17, color: '#999', fontWeight: 'normal'}} onClose={() => this.setState({showPlateColor: false})} children={this.renderColorListView()} />;
    }

    renderColorListView() {
        return <FlashList renderItem={(data) => this.renderColorItemView(data.item)} data={this.colorOptions} estimatedItemSize={59} />;
    }

    renderColorItemView(item: string) {
        return (
            <UITouchableOpacity
                style={styles.itemPopContainer}
                onPress={() => {
                    this.setState({drivingColor: item ?? '', showPlateColor: false});
                }}>
                <Text style={{fontSize: 15, color: '#333'}}>{item}</Text>
            </UITouchableOpacity>
        );
    }

    //     绘制普通框子
    RenderNormalBox({showErrorV1, label, errorMsg, edit, isRequired, onChangeText, value, placeholder, isInput}) {
        return (
            <View
                style={{
                    backgroundColor: '#fff',
                    paddingHorizontal: 15,
                    paddingVertical: 7,
                    borderBottomWidth: 0.5,
                    borderColor: '#eee',
                }}>
                <View style={styles.row}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        {showErrorV1 != null && showErrorV1 != '' && <UIImage source={'icon_warning_new'} style={{width: 15, height: 15, marginRight: 5}} />}
                        <Text style={{fontSize: 16, color: '#333'}}>{label}</Text>
                        {isRequired && <Text style={{color: '#FF602E', fontSize: 16, marginLeft: 5}}>*</Text>}
                    </View>
                    <View
                        style={[
                            styles.row,
                            {
                                justifyContent: 'center',
                                backgroundColor: TextUtils.isNoEmpty(showErrorV1) ? '#FFF5F1' : undefined,
                                borderColor: TextUtils.isNoEmpty(showErrorV1) ? '#FF5E1C' : undefined,
                                borderWidth: TextUtils.isNoEmpty(showErrorV1) ? 0.5 : 0,
                                borderRadius: TextUtils.isNoEmpty(showErrorV1) ? 5 : 0,
                                marginRight: 9,
                                paddingVertical: 7,
                                paddingRight: 5,
                            },
                        ]}>
                        {isInput ? (
                            <TextInput
                                style={{
                                    maxWidth: 220,
                                    minWidth: 80,
                                    paddingLeft: 14,
                                    marginRight: 10,
                                    fontSize: 14,
                                    color: '#333',
                                }}
                                textAlign={'right'}
                                placeholder={placeholder ?? '请输入'}
                                value={value}
                                editable={edit ?? true}
                                onChangeText={(e) => {
                                    onChangeText && onChangeText(e);
                                }}
                            />
                        ) : (
                            <Text
                                style={{
                                    fontSize: 16,
                                    color: value ? '#333' : '#999',
                                    maxWidth: 220,
                                    minWidth: 80,
                                    paddingRight: 10,
                                    paddingLeft: 14,
                                    textAlign: 'right',
                                }}>
                                {value ? value : '请选择'}
                            </Text>
                        )}
                        <UIImage source={TextUtils.isNoEmpty(showErrorV1) ? 'certification_fill_in_red' : 'certification_fill_in_gray'} style={{width: 15, height: 15}} />
                    </View>
                </View>
                {TextUtils.isNoEmpty(showErrorV1) && <Text style={{fontSize: 13, color: '#FF4747', marginTop: 5}}>{errorMsg}</Text>}
            </View>
        );
    }
}
const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    itemPopContainer: {
        height: 48,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        borderTopWidth: 0.5,
        borderColor: '#e3e3e3',
    },
});
