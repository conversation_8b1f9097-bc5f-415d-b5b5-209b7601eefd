import {StyleSheet, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import {http} from '../const.global';
import ComCertificationMultipleImageView from './views/ComCertificationMultipleImageView';
import {EImage} from './models/EImage';
import UIButton from '../widget/UIButton';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ArrayUtils} from '../util/ArrayUtils';
import {Method} from '../util/NativeModulesTools';
import {RouterUtils} from '../util/RouterUtils';
import TextUtils from '../util/TextUtils';
import {ReqVehicleDetails, toImageList} from './requests/ReqVehicleDetails';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';

interface State extends BaseState {
    ownFlag: string; // 0、非自有车辆  1、自有车辆
    domesticRelationUrl?: EImage[]; // 亲属关系证明 （最多五张）
    isInit: boolean;
}

/**
 * 注释: 亲属关系证明新增-编辑
 * 时间: 2025/2/24 10:42
 * <AUTHOR>
 */
export default class KindredMaterialsPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 0:上传 1:更新 2:过期
    mode: number;
    //成功回调
    callBack: Function = this.getCallBack();
    //车辆id
    vehicleId?: string;
    //车辆所属  0、非自有车辆  1、自有车辆
    ownFlag?: string;

    constructor(props) {
        super(props);
        this.state = {
            ownFlag: '',
            isInit: false,
        };
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode;
        this.vehicleId = this.pageParams?.vehicleId;
        this.ownFlag = this.pageParams?.ownFlag;
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryVehicleDetail();
    }

    /**
     * 注释：请求车辆详情
     * 时间：2025/2/25 10:23
     * @author：宋双朋
     */
    queryVehicleDetail() {
        //车辆id不为空 请求车辆详情
        let req = new ReqVehicleDetails();
        req.request().then((res) => {
            if (res.isSuccess()) {
                let item = res.data;
                this.setState({
                    domesticRelationUrl: toImageList(item, 4),
                    isInit: true,
                });
            } else {
                this._showToast(res.getMsg());
                this.setState({isInit: true});
            }
        });
    }

    /**
     * 注释：提交上传
     * 时间：2025/2/25 13:54
     * @author：宋双朋
     */
    licenseInputAccuracyCheck = () => {
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '12';
        req.isNeedSave = this.mode == 0 ? '1' : '0';
        req.vehicleId = this.vehicleId;
        req.ownFlag = this.ownFlag ?? '0';
        req.domesticRelationFlag = '1';
        //亲属关系证明
        let domesticRelationUrl =
            this.state.domesticRelationUrl?.map((item) => {
                return item.imageId ?? '';
            }) ?? [];
        if (ArrayUtils.isEmpty(domesticRelationUrl)) {
            this._showMsgDialog('亲属关系证明不能为空');
            return;
        }
        req.domesticRelationUrl = domesticRelationUrl.join(',');
        req.request().then((res) => {
            if (res.isSuccess()) {
                this._showToast(res.getMsg());
                this.callBack && this.callBack();
                EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                RouterUtils.skipPop();
            } else {
                this._showMsgDialog(res.getMsg());
            }
        });
    };

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={'车辆认证'}
                    rightText={'客服'}
                    clickRight={() => {
                        //打开客服
                        Method.openServerPopWindow();
                    }}
                />
                {this.state.isInit && (
                    <ComCertificationMultipleImageView
                        tvTitle1={'亲属关系证明'}
                        style={{marginTop: 7, backgroundColor: '#FFFFFF'}}
                        imgs={this.state.domesticRelationUrl}
                        max={5}
                        rowSize={4}
                        transformUrl={(url: string) => http.imagUrl(url)}
                        showLoading={(show: boolean) => {
                            show ? this._showWaitDialog() : this._dismissWait();
                        }}
                        tvDoubt={false}
                        tvRedAsterisk={true}
                        onChange={(img) => {
                            this.setState({domesticRelationUrl: img});
                        }}
                        edit={true}
                    />
                )}
                <View style={{flex: 1}} />
                <UIButton
                    text={'提交上传'}
                    style={{borderRadius: 0}}
                    fontWeight={true}
                    onPress={() => {
                        this.licenseInputAccuracyCheck();
                    }}
                />
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    itemStyle: {
        backgroundColor: '#fff',
        paddingHorizontal: 14,
        paddingVertical: 14,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
});
