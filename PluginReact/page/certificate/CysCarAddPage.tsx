import React, {ReactNode} from 'react';
import {ParamListBase} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {Text, TouchableOpacity, View} from 'react-native';
import UIImage from '../widget/UIImage';
import UITitleView from '../widget/UITitleView';
import TextUtils from '../util/TextUtils';
import UIPopup from '../widget/UIPopup';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import {ESaveVehicle} from './requests/ReqSaveVehicle';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {EEnterPriseNewVehicle, handlerEnterPriseNewVehicle, ReqEnterPriseNewVehicle} from './requests/ReqEnterPriseNewVehicle';
import {Method} from '../util/NativeModulesTools';
import {ReqIntelligentAudit2CacheClear} from './requests/ReqIntelligentAudit2CacheClear';
import {DialogNewBuilder} from '../base/Dialog';
import CarItemCheckView from './views/CarItemCheckView';
import CarItemCertificateView from './views/CarItemCertificateView';
import CarItemSelectView from './views/CarItemSelectView';

/**
 * 物流企业添加车辆
 */
interface State extends BaseState {
    init?: boolean;
    vehicle?: EEnterPriseNewVehicle;
    showEmissionStandardDialog?: boolean;
    postOnClickEnable?: boolean; //提交按钮是否可点击
}

export default class CysCarAddPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    convertedEmissionStandardList = ['国Ⅰ(国一)', '国Ⅱ(国二)', '国Ⅲ(国三)', '国Ⅳ(国四)', '国Ⅴ(国五)', '国Ⅵ(国六)'];
    mobile?: string;
    verifyCode?: string;
    verifyCodeType?: string;

    constructor(props) {
        super(props);
        this.mobile = this.getParams().mobile;
        this.verifyCode = this.getParams().verifyCode;
        this.verifyCodeType = this.getParams().verifyCodeType;
    }

    componentDidMount(): void {
        super.componentDidMount();
        this.queryInitData();
    }

    queryInitData = async () => {
        this._showWaitDialog();
        let vehicle = {} as EEnterPriseNewVehicle;

        // 车辆信息缓存接口
        let reqCache = await new ReqVehicleDetails().request();
        if (reqCache.isSuccess() && reqCache.data) {
            //有缓存数据走缓存数据逻辑
            vehicle = handlerEnterPriseNewVehicle(reqCache.data);
        }
        this.setState(
            {
                init: true,
                vehicle: vehicle,
            },
            () => {
                this.handlerPostOnClickEnable();
            },
        );
        this._dismissWait();
    };

    renderOrdinaryViews = (vehicle?: ESaveVehicle) => {
        //普通车 1、身份认证 2、驾驶证信息 5、从业资格证 6、行驶证 7、道路运输证 8、道路运输经营许可证 9、挂车行驶证 10、超限临牌 11、人车合影 12、证明材料
        //行驶证 上传证件 or 修改证件 (必须先上传,其他证件才可上传)
        const isAddVehicleLicense = TextUtils.isEmpty(vehicle?.triverPermitUrl);
        //道路运输证
        const isAddTransportLicense = TextUtils.isEmpty(vehicle?.roadTransportPermitUrl);

        return (
            <>
                <CarItemCheckView title={'总质量是否大于4.5吨'} flag={true} edit={true} checkLeftRight={TextUtils.equals('1', vehicle?.backup2) ? 1 : 2} leftTxt={'是'} rightText={'否'} onChange={this.onSelectBackup2} />
                <CarItemCertificateView title={'行驶证'} flag={1} must={true} edit={true} add={isAddVehicleLicense} onClick={this.onClickVehicle} />
                {TextUtils.equals('1', vehicle?.backup2) && <CarItemCertificateView title={'道路运输证'} flag={1} must={true} edit={!isAddVehicleLicense} add={isAddTransportLicense} onClick={this.onClickTransportLicense} />}
            </>
        );
    };
    renderOtherViews = (vehicle?: ESaveVehicle) => {
        //挂车  1、身份认证 2、驾驶证信息 5、从业资格证 6、行驶证 7、道路运输证 8、道路运输经营许可证 9、挂车行驶证 10、超限临牌 11、人车合影 12、证明材料
        //行驶证+ 行驶证年检页 上传证件 or 修改证件 (必须先上传,其他证件才可上传)
        const isAddVehicleLicense = TextUtils.isEmpty(vehicle?.triverPermitUrl);
        //道路运输证
        const isAddTransportLicense = TextUtils.isEmpty(vehicle?.roadTransportPermitUrl);
        //挂车行驶证+年检页
        const isAddVehicleBodyLicense = TextUtils.isEmpty(vehicle?.trailerNewUrl);
        //临时牌照
        const isAddOverloadBack = TextUtils.isEmpty(vehicle?.overloadPlateFrontUrl);

        return (
            <>
                <CarItemCheckView title={'是否为临时/超牌车辆'} flag={true} edit={true} checkLeftRight={TextUtils.equals('1', vehicle?.backup1) ? 1 : 2} leftTxt={'是'} rightText={'否'} onChange={this.onSelectBackup1} />
                <CarItemCertificateView title={'牵引车/车头行驶证'} flag={2} must={true} edit={true} add={isAddVehicleLicense} onClick={this.onClickVehicle} />
                <CarItemCertificateView title={'道路运输证(牵引车/车头)'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddTransportLicense} onClick={this.onClickTransportLicense} />
                {TextUtils.equals('1', vehicle?.backup1) ? (
                    <CarItemCertificateView title={'临时牌照'} flag={2} must={true} edit={!isAddVehicleLicense} add={isAddOverloadBack} onClick={this.onClickOverload} />
                ) : (
                    <CarItemCertificateView title={'挂车/车身行驶证'} flag={3} must={true} edit={!isAddVehicleLicense} add={isAddVehicleBodyLicense} onClick={this.onClickVehicle} />
                )}
            </>
        );
    };
    getEmissionStandardTxt = (emissionStandard?: string) => {
        //'国Ⅰ(国一)', '国Ⅱ(国二)', '国Ⅲ(国三)', '国Ⅳ(国四)', '国Ⅴ(国五)', '国Ⅵ(国六)'
        switch (`${emissionStandard}`) {
            case '1': {
                return '国Ⅰ(国一)';
            }
            case '2': {
                return '国Ⅱ(国二)';
            }
            case '3': {
                return '国Ⅲ(国三)';
            }
            case '4': {
                return '国Ⅳ(国四)';
            }
            case '5': {
                return '国Ⅴ(国五)';
            }
            case '6': {
                return '国Ⅵ(国六)';
            }
        }
        return '';
    };

    render(): ReactNode {
        const vehicle = this.state.vehicle;
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView
                    title={'添加车辆'}
                    rightText="客服"
                    clickRight={() => {
                        //打开客服
                        Method.openServerPopWindow();
                    }}
                />
                {this.state.init && (
                    <>
                        <CarItemCheckView title={'车辆类型'} flag={true} edit={true} checkLeftRight={TextUtils.equals('2', vehicle?.vehicleFlag) ? 2 : 1} leftTxt={'普通货车'} rightText={'半挂车'} onChange={this.onSelectVehicleFlag} />
                        <CarItemSelectView title={'排放标准'} value={this.getEmissionStandardTxt(vehicle?.emissionStandard)} onClick={this.onSelectVehicleEmissionStandard} />
                        {TextUtils.equals('2', vehicle?.vehicleFlag) ? this.renderOtherViews(vehicle) : this.renderOrdinaryViews(vehicle)}
                    </>
                )}
                <View style={{flex: 1}} />
                <Text
                    onPress={this.onCliclSave}
                    style={{
                        backgroundColor: this.state.postOnClickEnable ? '#5086FC' : '#CCC',
                        color: '#fff',
                        fontSize: 18,
                        textAlign: 'center',
                        paddingTop: 15,
                        paddingBottom: 15,
                    }}>
                    提交上传
                </Text>
                {this.state.showEmissionStandardDialog && (
                    <UIPopup title="请选择排放标准" onClose={() => this.setState({showEmissionStandardDialog: false})}>
                        {this.convertedEmissionStandardList.flatMap((item, index) => (
                            <Text
                                onPress={() => {
                                    this.setState({
                                        showEmissionStandardDialog: false,
                                        vehicle: {
                                            ...this.state.vehicle,
                                            emissionStandard: `${index + 1}`,
                                        },
                                    });
                                }}
                                style={{
                                    color: '#333',
                                    fontSize: 14,
                                    textAlign: 'center',
                                    paddingTop: 7,
                                    paddingBottom: 7,
                                    backgroundColor: '#fff',
                                }}>
                                {item}
                            </Text>
                        ))}
                    </UIPopup>
                )}
                {this._initCommView()}
            </View>
        );
    }

    //车辆类型
    onSelectVehicleFlag = (type: number) => {
        //用户更新证件后切换了车辆类型，清空所有证件全部重新上传
        let dialog = new DialogNewBuilder();
        dialog.setTitle('提示');
        dialog.setMsg('更换车辆类型后需要重新上传所有证件，是否继续操作？');
        dialog.setRightTxt('确认更换');
        dialog.setLeftTxt('取消');
        dialog.setRightOnClick((dialog) => {
            dialog.dismiss();
            //用户更新证件后切换了车辆类型，清空所有证件全部重新上传，
            this._showWaitDialog();
            let req = new ReqIntelligentAudit2CacheClear();
            req.vehicleId = this.state.vehicle?.vehicleId;
            req.request().then((response) => {
                this._dismissWait();
                if (response.isSuccess()) {
                    this.setState({
                        vehicle: {
                            vehicleFlag: type === 1 ? '1' : '2',
                            backup2: type === 1 ? '1' : '', //普通车辆总质量是否>4.5吨
                        },
                    });
                    this.onRefresh();
                } else {
                    this._showMsgDialog(response.getMsg());
                }
            });
        });
        dialog?.show();
    };

    //排放标准
    onSelectVehicleEmissionStandard = () => {
        this.setState({
            showEmissionStandardDialog: true,
        });
    };

    onSelectBackup1 = (type: number) => {
        //是否为临时/超牌车辆
        this.setState(
            {
                vehicle: {
                    ...this.state.vehicle,
                    backup1: type === 1 ? '1' : '0',
                },
            },
            () => {
                //处理按钮是否可以点击
                this.handlerPostOnClickEnable();
            },
        );
    };

    onSelectBackup2 = (type: number) => {
        //总质量是否>4.5吨
        this.setState(
            {
                vehicle: {
                    ...this.state.vehicle,
                    backup2: type === 1 ? '1' : '0',
                },
            },
            () => {
                //处理按钮是否可以点击
                this.handlerPostOnClickEnable();
            },
        );
    };

    onRefresh = () => {
        //刷新当前界面车辆认证数据
        new ReqVehicleDetails().request().then((resp) => {
            if (resp.isSuccess() && resp.data) {
                //有缓存数据走缓存数据逻辑
                let vehicleFlag = this.state.vehicle?.vehicleFlag;
                let emissionStandard = this.state.vehicle?.emissionStandard;
                let backup1 = this.state.vehicle?.backup1;
                let backup2 = this.state.vehicle?.backup2;

                this.setState(
                    {
                        vehicle: {
                            ...handlerEnterPriseNewVehicle(resp.data),
                            vehicleFlag: vehicleFlag,
                            emissionStandard: emissionStandard,
                            backup1: backup1,
                            backup2: backup2,
                        },
                    },
                    () => {
                        this.handlerPostOnClickEnable();
                    },
                );
            }
        });
    };
    /**
     * 行驶证
     * @param type  1 普通车行驶证 2 车头行驶证 3 挂车行驶证
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickVehicle = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: type === 2 ? 'DrivingHeadLicensePage' : type === 3 ? 'DrivingGuaLicensePage' : 'DrivingLicensePage',
            data: {
                mode: statue == 1 ? 0 : 1, // 0:上传 1:更新 2:过期
                addOrManage: 2,
                vehicleId: this.state.vehicle?.vehicleId,
                flag: this.state.vehicle?.vehicleFlag,
                backup1: this.state.vehicle?.backup1,
                backup2: this.state.vehicle?.backup2,
            },
            callBack: this.onRefresh,
        });
    };
    /**
     * 道路运输证
     * @param type  1 普通车 2 道路运输证（牵引车/车头）
     * @param statue 1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickTransportLicense = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'ExpiredTransportUpdatePage',
            data: {
                rspQueryUserExpireLicenseB: {
                    targetId: this.state.vehicle?.vehicleId,
                    licensePlateNumber: this.state.vehicle?.plateNumber,
                },
                mode: statue == 1 ? 0 : 1, // 0:上传 1:更新 2:过期
                addOrManage: 2,
            },
            callBack: this.onRefresh,
        });
    };

    /**
     * 临时牌照
     * @param type 1 普通车 2 挂车
     * @param statue   1 去上传 2 修改证件  更新完成callback 回调刷新界面数据
     */
    onClickOverload = (type: number, statue: number) => {
        RouterUtils.skipRouter(RouterUrl.ReactPage, {
            page: 'ProCardPage',
            data: {
                vehicleId: this.state.vehicle?.vehicleId,
                mode: 0, // 0:上传 1:更新 2:过期
                isAdd: true,
            },
            callBack: this.onRefresh,
        });
    };

    handlerPostOnClickEnable = () => {
        if (this.state.vehicle) {
            if (TextUtils.equals('2', this.state.vehicle?.vehicleFlag)) {
                //2、挂车
                if (TextUtils.equals('1', this.state.vehicle?.backup1)) {
                    //临时/超限车辆
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.triverPermitUrl) || //牵引车行驶证
                        TextUtils.isEmpty(this.state.vehicle?.roadTransportPermitUrl) || //道路运输证
                        TextUtils.isEmpty(this.state.vehicle?.overloadPlateFrontUrl) //临时牌照
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                } else {
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.triverPermitUrl) || //牵引车行驶证
                        TextUtils.isEmpty(this.state.vehicle?.roadTransportPermitUrl) || //道路运输证照片
                        TextUtils.isEmpty(this.state.vehicle?.trailerNewUrl) //挂车行驶证
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                }
            } else {
                //1、普通货车
                if (TextUtils.equals('1', this.state.vehicle?.backup2)) {
                    //总质量是否>4.5吨
                    if (
                        TextUtils.isEmpty(this.state.vehicle?.triverPermitUrl) || //传行驶证
                        TextUtils.isEmpty(this.state.vehicle?.roadTransportPermitUrl) //道路运输证照片
                    ) {
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                } else {
                    if (TextUtils.isEmpty(this.state.vehicle?.triverPermitUrl)) {
                        //行驶证
                        this.setState({postOnClickEnable: false});
                        return;
                    }
                }
            }

            this.setState({postOnClickEnable: true});
        } else {
            //没有数据不能点击提交
            this.setState({postOnClickEnable: false});
        }
    };
    onCliclSave = () => {
        if (this.state.postOnClickEnable) {
            this._showWaitDialog();

            let vehicle = {...this.state.vehicle};

            if (TextUtils.equals('2', vehicle.vehicleFlag)) {
                //2、挂车
                vehicle.vehicleFlag = '2';
                //临时/超限车辆
                vehicle.backup1 = TextUtils.equals('1', vehicle.backup1) ? '1' : '0';
                vehicle.backup2 = '';
            } else {
                //1、普通货车
                vehicle.vehicleFlag = '1';
                //总质量是否>4.5吨
                vehicle.backup2 = TextUtils.equals('1', vehicle.backup2) ? '1' : '0';
                vehicle.backup1 = '';
            }

            let req = new ReqEnterPriseNewVehicle();
            req.vehicle = vehicle;

            req.request().then((resp) => {
                this._dismissWait();

                if (resp.isSuccess()) {
                    //CarOwnerAddcarSubmitSuccessActivity
                    RouterUtils.skipRouter(RouterUrl.CarOwnerAddcarSubmitSuccessActivity);
                    this._goBack();
                } else {
                    this._showMsgDialog(resp.getMsg());
                }
            });
        } else {
            this._showMsgDialog('请先完善车辆信息');
        }
    };
}
