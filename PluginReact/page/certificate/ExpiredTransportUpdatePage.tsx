import {StyleSheet, Text, View} from 'react-native';
import React, {ReactNode} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import TextUtils from '../util/TextUtils';
import UIButton from '../widget/UIButton';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import {Method} from '../util/NativeModulesTools';
import {Constant} from '../base/Constant';
import {RouterUtils} from '../util/RouterUtils';
import EventBus from '../util/EventBus';
import ComCertificationUploadImgView, {IngProps} from './views/ComCertificationUploadImgView';
import {RspQueryUserExpireLicenseDetail} from './models/RspQueryUserExpireLicenseDetail';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import ExamptImageDialogV1 from './views/ExamptImageDialogV1';
import {LicenseList} from './models/ResMemberLicenseList';
import UIFormInputView, {UIFormInputViewRef} from './views/UIFormInputView';
import {ReqCheckVehicleTransportLicenseOcr} from './requests/ReqCheckVehicleTransportLicenseOcr';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ReqUpdateMemberLicense} from './requests/ReqUpdateMemberLicense';
import {PicUrlBean} from './models/PicUrlBean';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import {LincenseTipInfo} from './models/ResLicenseInputAccuracyCheck';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';
import PlateNumberDialog from './views/PlateNumberDialog';

interface State extends BaseState {
    init: boolean;
    showTopTip?: boolean;
    showPlateNumber?: boolean;
    showTemplatePop?: boolean;
    transportLicensePlateNumber?: string; //道路运输中车牌号
    plateNumber?: string; //行驶证车牌号
    //证件报错提示
    licenseTipInfo?: LincenseTipInfo;
    isRejected: boolean; //是否是驳回
    showPlateNumberDialog: boolean;
}

const LicenseType_4 = '200001'; //道路运输证

/**
 * 注释: 运输证过期上传页
 * 时间: 2024/12/25 8:54
 * <AUTHOR>
 */
export default class ExpiredTransportUpdatePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    rspQueryUserExpireLicenseB: LicenseList = new LicenseList();
    pageDetail?: RspQueryUserExpireLicenseDetail;
    leftImg: IngProps;
    templateDialog = (<ExamptImageDialogV1 onClose={() => this.setState({showTemplatePop: false})} />);
    url1?: string;
    errorMsg = '';
    showViewBg = false;
    plateNumberRef = React.createRef<UIFormInputViewRef>();
    // 0:上传 1:更新 2:过期
    mode: number;
    // 道路运输经营许可证号
    transportBusinessNo?: string;
    callback: Function;
    //1 车辆认证 2添加车辆
    addOrManage: number;
    constructor(props) {
        super(props);
        this.pageParams = this.getParams();
        this.mode = this.pageParams?.mode ?? 0;
        // 新增或者车辆认证 1 证件新增 2车辆认证
        this.addOrManage = this.pageParams?.addOrManage ?? 0;
        this.rspQueryUserExpireLicenseB = this.pageParams?.rspQueryUserExpireLicenseB ?? new LicenseList();
        this.callback = this.getCallBack();
        this.state = {
            init: false,
            showTopTip: ['2', '3'].includes(`${this.rspQueryUserExpireLicenseB?.licenseStatus}`) ? TextUtils.equals('204', this.rspQueryUserExpireLicenseB?.licenseCode) : false,
            showTemplatePop: false,
            isRejected: false,
            showPlateNumberDialog: false,
        };
        this.leftImg = {
            url: '',
            defaultValue: 'certification_driver_license_template_diagram_10',
            resizeMode: 'cover',
            onPostSuccess: (url) => {
                this.url1 = url;
                this.checkVehicleTransportLicenseOcr();
            },
            onDelPic: () => {
                this.url1 = '';
                this.transportBusinessNo = '';
                this.setState({transportLicensePlateNumber: ''});
            },
            imgStyle: {height: 210},
        };
    }

    componentDidMount() {
        super.componentDidMount();
        if (this.mode == 0 || this.mode == 1) {
            if (this.addOrManage == 1) {
                this.queryCacheLiscense();
            } else if (this.addOrManage == 2) {
                this.queryCarCacheLiscense();
            } else {
                this.queryUserExpireLicenseDetail();
            }
        } else {
            this.queryUserExpireLicenseDetail();
        }
    }

    // 车辆认证
    queryCacheLiscense() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.url1 = res.data?.vehicle?.transportLicenseUrl ?? '';
                this.leftImg = {
                    url: res.data?.vehicle?.transportLicenseUrl ?? '',
                    defaultValue: 'certification_driver_license_template_diagram_10',
                    resizeMode: 'cover',
                    onPostSuccess: (url) => {
                        this.url1 = url;
                        this.checkVehicleTransportLicenseOcr();
                    },
                    onDelPic: () => {
                        this.url1 = '';
                        this.transportBusinessNo = '';
                        this.setState({transportLicensePlateNumber: ''});
                    },
                    imgStyle: {height: 210},
                };
                this.setState({
                    init: true,
                    showPlateNumber: TextUtils.isNoEmpty(res.data?.vehicle?.transportLicenseUrl) && TextUtils.isNoEmpty(res.data?.vehicle?.transportLicensePlateNumber),
                    plateNumber: res.data?.vehicle?.plateNumber,
                    transportLicensePlateNumber: res.data?.vehicle?.transportLicensePlateNumber,
                });
            }
        });
    }

    // 添加车辆
    async queryCarCacheLiscense() {
        this._showWaitDialog();
        let reqCache = await new ReqVehicleDetails().request();
        this._dismissWait();
        if (reqCache.isSuccess() && reqCache.data) {
            this.url1 = reqCache.data.roadTransportPermitUrl ?? '';
            this.leftImg = {
                url: reqCache.data.roadTransportPermitUrl ?? '',
                defaultValue: 'certification_driver_license_template_diagram_10',
                resizeMode: 'cover',
                onPostSuccess: (url) => {
                    this.url1 = url;
                    this.checkVehicleTransportLicenseOcr();
                },
                onDelPic: () => {
                    this.url1 = '';
                    this.transportBusinessNo = '';
                    this.setState({transportLicensePlateNumber: ''});
                },
                imgStyle: {height: 210},
            };
            this.setState({
                init: true,
                showPlateNumber: TextUtils.isNoEmpty(reqCache.data.roadTransportPermitUrl) && TextUtils.isNoEmpty(reqCache.data.transportLicensePlateNumber),
                plateNumber: reqCache.data?.plateNumber,
                transportLicensePlateNumber: reqCache.data?.transportLicensePlateNumber,
            });
        }
    }

    /**
     * 证件准确性校验接口
     */
    licenseInputAccuracyCheck() {
        if (TextUtils.isEmpty(this.url1)) {
            this._showToast('请上传道路运输证');
            return;
        }
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '7';
        req.isNeedSave = this.mode == 0 || this.addOrManage == 2 || this.addOrManage == 1 ? '1' : '0';
        req.transportLicensePlateNumber = this.plateNumberRef.current?.getValue();
        req.plateNumber = this.state.plateNumber;
        req.transportLicenseUrl = this.url1;
        req.businessLicenceNo = this.transportBusinessNo;
        req.vehicleId = this.rspQueryUserExpireLicenseB?.targetId ?? '';
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                if (this.mode == 0 || this.addOrManage == 2 || this.addOrManage == 1) {
                    this._showToast('上传道路运输证成功');
                    RouterUtils.skipPop();
                    this.callback && this.callback();
                } else {
                    this.commit();
                }
            } else {
                this.setState({licenseTipInfo: res.data?.data});
            }
        });
    }

    commit() {
        if (this.pageDetail != null) {
            if (this.mode == 2) {
                let req = new ReqUploadExpireDateLicense();
                req.picUrlList = [
                    {
                        picUrl: this.url1,
                        type: this.pageDetail?.licenseType,
                    },
                ];
                req.targetId = this.rspQueryUserExpireLicenseB?.targetId;
                this._showWaitDialog();
                req.request().then((res) => {
                    this._dismissWait();
                    if (res.isSuccess()) {
                        RouterUtils.skipPop();
                        this.callback && this.callback();
                        EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                    } else {
                        this._showMsgDialog(res.getMsg());
                    }
                });
            } else {
                let req = new ReqUpdateMemberLicense();
                let bean = new PicUrlBean();
                bean.picUrl = this.url1;
                bean.type = this.pageDetail?.licenseType;
                bean.targetId = this.rspQueryUserExpireLicenseB?.targetId;
                req.picUrlList = [bean];
                this._showWaitDialog();
                req.request().then((res) => {
                    this._dismissWait();
                    if (res.isSuccess()) {
                        RouterUtils.skipPop();
                        this.callback && this.callback();
                        EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                    } else {
                        this._showMsgDialog(res.getMsg());
                    }
                });
            }
        }
    }

    /**
     * 证件详情
     */
    queryUserExpireLicenseDetail() {
        let req = new ReqQueryUserExpireLicenseDetail();
        req.targetId = this.rspQueryUserExpireLicenseB?.targetId ?? '';
        req.licenseType = '200001';
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                this.pageDetail = res.data;
                switch (res.data?.licenseType) {
                    case LicenseType_4: {
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_10',
                            resizeMode: 'cover',
                            takePhoto: this.getCanTakePhoto(),
                            onPostSuccess: (url) => {
                                this.url1 = url;
                                this.checkVehicleTransportLicenseOcr();
                            },
                            onDelPic: () => {
                                this.url1 = '';
                                this.transportBusinessNo = '';
                                this.setState({transportLicensePlateNumber: ''});
                            },
                            imgStyle: {height: 210},
                        };
                        break;
                    }
                    default: {
                        this.leftImg = {
                            url: '',
                            defaultValue: 'certification_driver_license_template_diagram_10',
                            resizeMode: 'cover',
                            takePhoto: this.getCanTakePhoto(),
                            onPostSuccess: (url) => {
                                this.url1 = url;
                                this.checkVehicleTransportLicenseOcr();
                            },
                            onDelPic: () => {
                                this.url1 = '';
                                this.transportBusinessNo = '';
                                this.setState({transportLicensePlateNumber: ''});
                            },
                            imgStyle: {height: 210},
                        };
                        break;
                    }
                }

                switch (res.data?.licenseState) {
                    case '3': {
                        //审核不通过
                        this.leftImg.canDelete = true;
                        this.errorMsg = res.data?.licenseReason ?? '';
                        this.showViewBg = true;
                        this.setState({isRejected: true, showTopTip: true});
                        break;
                    }
                    default: {
                        // 0:待完善 || 1:待审核 || else:审核通过
                        if (TextUtils.isEmpty(res.data?.licenseUrl)) {
                            this.leftImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                        } else {
                            if (!TextUtils.equals('6', this.rspQueryUserExpireLicenseB.licenseStatus) && !TextUtils.equals('0', this.rspQueryUserExpireLicenseB.licenseStatus)) {
                                this.url1 = res.data?.licenseUrl;
                                this.leftImg.url = res.data?.licenseUrl ?? '';
                                this.leftImg.canDelete = !TextUtils.equals('1', res.data?.licenseState);
                            } else {
                                // 自主更新 this.mode === 1
                                this.setState({showTopTip: true});
                            }
                        }
                        break;
                    }
                }
                this.setState({init: true});
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    getCanTakePhoto() {
        return ['200001'].includes(this.pageDetail?.licenseType ?? '') && TextUtils.equals('1', Method.getStringExtra('LICENSE_LOCAL_UPLOAD_SWITCH'));
    }

    checkVehicleTransportLicenseOcr() {
        let req = new ReqCheckVehicleTransportLicenseOcr();
        req.img = this.url1;
        req.request().then((res) => {
            if (res.isSuccess()) {
                this.transportBusinessNo = res.data?.transportBusinessNo ?? '';
            } else {
                this._showMsgDialog(res.getMsg());
            }
            this.transportBusinessNo = res.data?.transportBusinessNo ?? '';
            this.setState({showPlateNumber: true, transportLicensePlateNumber: res.data?.plateNumber, licenseTipInfo: undefined});
        });
    }

    render() {
        let title = '身份认证';
        if (this.mode == 1) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        if (this.addOrManage == 2 || this.addOrManage == 1) {
            title = '车辆认证';
        }
        return (
            <View style={{flex: 1}}>
                <UITitleView title={title} />
                {this.state.showTopTip && (
                    <View>
                        <View style={styles.topTipContainer}>
                            {this.state.isRejected ? (
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: '#D24F4F',
                                        marginRight: 12,
                                    }}>
                                    红色部分资料需要重新填写
                                </Text>
                            ) : (
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: '#d24f4f',
                                        marginRight: 5,
                                    }}>
                                    {this.mode == 1 && (TextUtils.equals('6', this.rspQueryUserExpireLicenseB?.licenseStatus) || TextUtils.equals('0', this.rspQueryUserExpireLicenseB?.licenseStatus))
                                        ? '如果您已更换证件可上传最新证件，如果证件未过期或未更换则无需上传。'
                                        : '证件即将 / 已经过期，为保障您的业务不受影响，请及时更新。'}
                                </Text>
                            )}
                            <UITouchableOpacity
                                onPress={() => {
                                    this.setState({showTopTip: false});
                                }}>
                                <UIImage source={'close_red'} style={{width: 12, height: 12}} />
                            </UITouchableOpacity>
                        </View>
                    </View>
                )}
                {this.state.init && (
                    <View style={{marginTop: 8, backgroundColor: '#fff', alignItems: 'center'}}>
                        <View
                            style={{
                                paddingHorizontal: 15,
                                height: 55,
                                alignItems: 'flex-start',
                                justifyContent: 'center',
                                alignSelf: 'flex-start',
                            }}>
                            <Text
                                style={{
                                    fontSize: 16,
                                    color: 'black',
                                    marginRight: 5,
                                }}>
                                {this.rspQueryUserExpireLicenseB?.licensePlateNumber ?? ''}
                            </Text>
                        </View>
                        <ComCertificationUploadImgView
                            tvTitle1={'道路运输证'}
                            onClickImgTemplate={() => this.setState({showTemplatePop: true})}
                            showViewBg={this.showViewBg}
                            showTemplateImg={true}
                            tvTitle3={''}
                            layout={1}
                            leftImg={this.leftImg ?? {}}
                            auditText={this.errorMsg}
                        />
                    </View>
                )}
                {this.state.showPlateNumber && (
                    <View style={{backgroundColor: '#fff', marginTop: 8}}>
                        <UIFormInputView
                            value={this.state.transportLicensePlateNumber}
                            label={'车牌号'}
                            showError={TextUtils.isNoEmpty(this.state.licenseTipInfo?.transportLicensePlateNumberText)}
                            error={this.state.licenseTipInfo?.transportLicensePlateNumberText}
                            isRequired={true}
                            ref={this.plateNumberRef}
                            isSelect={true}
                            onSelect={() => this.setState({showPlateNumberDialog: true})}
                        />
                    </View>
                )}
                <View style={{flex: 1}} />
                <UIButton text={'确认提交'} borderRadius={0} height={55} onPress={() => this.licenseInputAccuracyCheck()} />
                {this.state.showTemplatePop && this.templateDialog}
                {this.state.showPlateNumberDialog && (
                    <PlateNumberDialog
                        plateNumLen={8}
                        onClose={() => this.setState({showPlateNumberDialog: false})}
                        onChange={(plateNumber: string) => {
                            this.setState({transportLicensePlateNumber: plateNumber, licenseTipInfo: undefined});
                        }}
                    />
                )}
                {/*初始化基础组件*/}
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    vehicle: {
        fontSize: 18,
        color: '#3D3D3D',
        paddingRight: 12,
        paddingLeft: 12,
        paddingTop: 16,
        paddingBottom: 16,
        backgroundColor: '#fff',
    },
    topTipContainer: {
        backgroundColor: '#FFF3EF',
        paddingHorizontal: 14,
        paddingVertical: 7,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
});
