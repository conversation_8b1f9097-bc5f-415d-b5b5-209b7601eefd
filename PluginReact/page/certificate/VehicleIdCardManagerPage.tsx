import {StyleSheet, Text, View} from 'react-native';
import React, {useRef} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import {gScreen_width} from '../util/scaled-style';
import {Constant} from '../base/Constant';
import {LicenseList} from './models/ResQueryMemberVehicleLicense';
import {ReqQueryMemberVehicleLicense} from './requests/ReqQueryMemberVehicleLicense';
import UIListView from '../widget/UIListView';
import UIImageBackground from '../widget/UIImageBackground';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import CertificateReactExtension from './CertificateReactExtension';
import EventBus from '../util/EventBus';
import UITouchableOpacity from '../widget/UITouchableOpacity';

interface State extends BaseState {
    statue: string;
    plateNumber?: string;
    licenseList: LicenseList[];
}

/**
 * 注释:  车辆证件管理页
 * 时间: 2024/8/28 0028 13:59
 * <AUTHOR>
 */
export default class VehicleIdCardManagerPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    vehicleId: string;
    eventListener: Function;
    backup1 = '';
    constructor(props) {
        super(props);
        this.state = {
            statue: 'ic_tag_zc',
            licenseList: [],
        };
        this.pageParams = this.getParams();
        this.vehicleId = this.pageParams?.vehicleId ?? '';
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryMemberVehicleLicense();
        this.eventListener = () => {
            this.queryMemberVehicleLicense();
        };
        EventBus.getInstance().addListener(Constant.event_certification_refresh, this.eventListener);
    }

    componentWillUnmount() {
        EventBus.getInstance().removeListener(this.eventListener);
        super.componentWillUnmount();
    }

    /**
     * 注释: 查询车辆相关证件
     * 时间: 2024/8/28 0028 14:27
     * <AUTHOR>
     */
    queryMemberVehicleLicense() {
        let request = new ReqQueryMemberVehicleLicense();
        request.vehicleId = this.vehicleId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({licenseList: res.data?.licenseList ?? []});
                this.setState({plateNumber: res.data?.plateNumber ?? ''});
                if (res.data?.status == 0) {
                    this.setState({statue: 'ic_tag_zc'});
                } else if (res.data?.status == 1) {
                    this.setState({statue: 'ic_tag_bf'});
                } else if (res.data?.status == 2) {
                    this.setState({statue: 'ic_tag_dj'});
                } else {
                    this.setState({statue: 'ic_tag_yc'});
                }
                this.backup1 = `${res.data?.backup1 ?? ''}`;
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#fff'}}>
                <UIImage
                    source={'img_certificate_bg'}
                    style={{
                        width: gScreen_width,
                        height: (gScreen_width / 375) * 180,
                        position: 'absolute',
                        top: 0,
                    }}
                    resizeMode={'cover'}
                />
                <UITitleView title={'证件管理'} style={{backgroundColor: Constant.color_transparent, marginBottom: 18}} showLine={false} textStyle={{color: '#fff'}} backIcon={'base_back_white'} />
                {this.renderMainView()}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制ItemView
     * 时间: 2024/8/27 0027 16:52
     * <AUTHOR>
     * @param item
     * @returns {JSX.Element}
     */
    renderItemView(item: LicenseList) {
        let licenseStatusText;
        let licenseBackImg;
        if (item.licenseStatus == 0) {
            licenseStatusText = '查看 >';
            licenseBackImg = 'img_mask_zc';
        } else if (item.licenseStatus == 5) {
            licenseStatusText = '不通过 >';
            licenseBackImg = 'img_mask_yc';
        } else if (item.licenseStatus == 6) {
            if (item.licenseCode == '204') {
                licenseStatusText = '查看 >';
            } else {
                licenseStatusText = '可更新 >';
            }
            licenseBackImg = 'img_mask_zc';
        } else if (item.licenseStatus == 7) {
            licenseStatusText = '审核中...';
            licenseBackImg = 'img_mask_zc';
        } else {
            licenseStatusText = `${item.licenseStatusText} >`;
            licenseBackImg = 'img_mask_yc';
        }
        return (
            <UITouchableOpacity onPress={() => this.onItemClick(item)}>
                <UIImageBackground
                    source={licenseBackImg}
                    resizeMode={'stretch'}
                    style={{
                        marginHorizontal: 15,
                        marginTop: 15,
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingHorizontal: 15,
                            paddingVertical: 20,
                        }}>
                        <View style={{flexDirection: 'column', alignItems: 'flex-start'}}>
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <UIImage source={'ic_cyzgz'} style={{width: 23, height: 23}} />
                                <Text style={{fontSize: 17, color: '#333', marginLeft: 6}}>{item.licenseName}</Text>
                            </View>
                            <Text style={{fontSize: 13, color: '#666', marginTop: 8}}>{item.licensePlateNumber}</Text>
                        </View>
                        {/*非审核中的证件*/}
                        {item.licenseStatus != 7 && (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                {item.licenseStatus != 0 && item.licenseStatus != 6 && <UIImage source={'ic_warning'} style={{width: 14, height: 14}} />}
                                <Text
                                    style={{
                                        fontSize: 13,
                                        color: item.licenseStatus == 0 || item.licenseStatus == 6 ? '#5086FC' : '#f00',
                                        marginLeft: 5,
                                    }}>
                                    {licenseStatusText}
                                </Text>
                            </View>
                        )}
                        {/*审核中的证件*/}
                        {item.licenseStatus == 7 && (
                            <View style={{flexDirection: 'column'}}>
                                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                    <UIImage source={'ic_waiting'} style={{width: 14, height: 14}} />
                                    <Text
                                        style={{
                                            color: '#5086FC',
                                            fontSize: 13,
                                            marginLeft: 5,
                                        }}>
                                        {licenseStatusText}
                                    </Text>
                                </View>
                                <Text style={{fontSize: 13, color: '#5086FC', marginTop: 5}}>{'资料补充 >'}</Text>
                            </View>
                        )}
                    </View>
                </UIImageBackground>
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 预览证件信息
     * 时间: 2024/11/21 星期四 17:26
     * <AUTHOR>
     * @param item
     */
    preViewCardInfo(item: LicenseList) {
        if (item.licenseCode == '210') {
            //证明材料
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'ProofMaterialsPreViewPage',
                data: {
                    urlList: item.urlList,
                    label: item.licenseName,
                    licensePlateNumber: item.licensePlateNumber,
                    carPurchaseProofPictureList: item.carPurchaseProofPictureList,
                    paymentProofPictureList: item.paymentProofPictureList,
                },
            });
        } else {
            //其他证件
            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                page: 'CertificateMaterialsPreViewPage',
                data: {
                    urlList: item.urlList,
                    label: item.licenseName,
                    licenseCode: item.licenseCode,
                    licenseStatus: item.licenseStatus,
                    licensePlateNumber: item.licensePlateNumber,
                    backup1: this.backup1,
                },
                callBack: () => {
                    CertificateReactExtension.skipCarCertificateUpdate(item);
                },
            });
        }
    }

    /**
     * 注释:  Item点击事件
     * 时间: 2024/8/29 0029 14:31
     * <AUTHOR>
     * @param item
     */
    onItemClick(item: LicenseList) {
        //证件正常统一跳转查看页
        if (item.licenseStatus == 0) {
            this.preViewCardInfo(item);
            return;
        }
        if (item.licenseStatus == 6 && item.licenseCode == '204') {
            this.preViewCardInfo(item);
            return;
        }
        //证件异常跳转车辆证件处理
        CertificateReactExtension.skipCarCertificateUpdate(item);
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2024/8/28 0028 14:08
     * <AUTHOR>
     */
    renderMainView() {
        return (
            <View style={{flex: 1}}>
                {this.renderStatusView()}
                {/*证件列表*/}
                <View style={{backgroundColor: '#fff', flex: 1}}>
                    <UIListView
                        renderItem={({item}) => this.renderItemView(item)}
                        dataList={this.state.licenseList}
                        onRefresh={() => {
                            this.queryMemberVehicleLicense();
                        }}
                    />
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2024/8/27 0027 16:33
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderStatusView() {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: '#fff',
                    paddingHorizontal: 15,
                    paddingTop: 18,
                    paddingBottom: 10,
                    borderTopLeftRadius: 15,
                    borderTopRightRadius: 15,
                }}>
                <Text style={{fontSize: 18, color: '#3D3D3D', fontWeight: 'bold'}}>{this.state.plateNumber ?? ''}</Text>
                <UIImage source={this.state.statue} style={{width: 55, height: 21}} />
            </View>
        );
    }
}

const styles = StyleSheet.create({});
