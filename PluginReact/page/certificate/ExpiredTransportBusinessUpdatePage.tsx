import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UIButton from '../widget/UIButton';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import ELogin from '../user/personalsafety/models/ELogin';
import {Method} from '../util/NativeModulesTools';
import {RspQueryUserExpireLicenseDetail} from './models/RspQueryUserExpireLicenseDetail';
import {ReqQueryTransportPermissionLicenseDetail, RspQueryTransportPermissionLicenseDetail} from './requests/ReqQueryTransportPermissionLicenseDetail';
import {ReqQueryUserExpireLicenseDetail} from './requests/ReqQueryUserExpireLicenseDetail';
import {ReqUploadExpireDateLicense} from './requests/ReqUploadExpireDateLicense';
import {PicUrlBean} from './models/PicUrlBean';
import {ReqAddTransportPermissionLicense} from './requests/ReqAddTransportPermissionLicense';
import ComCertificationUploadImgView from './views/ComCertificationUploadImgView';
import {ReqLicenseInputAccuracyCheck} from './requests/ReqLicenseInputAccuracyCheck';
import {ReqVehicleDetails} from './requests/ReqVehicleDetails';
import {ReqQueryUserLicense} from './requests/ReqQueryUserLicense';

interface State extends BaseState {
    pageDetail?: RspQueryTransportPermissionLicenseDetail;
    pageDetail2?: RspQueryUserExpireLicenseDetail;
    init: boolean;
}

/**
 * 注释: 路运营运输经营许可证上传
 * 时间: 2024/12/23 15:47
 * <AUTHOR>
 */
export default class ExpiredTransportBusinessUpdatePage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    vehicleId?: string;
    licenseType?: string;
    params: any;
    url?: string;
    login: ELogin = Method.getLogin();
    // 0:上传 1:更新 2:过期
    mode: number;
    callback: Function;
    //1 车辆认证 2添加车辆
    addOrManage: number;
    constructor(props) {
        super(props);
        this.state = {
            init: false,
        };
        this.params = this.getParams();
        this.vehicleId = this.params?.vehicleId ?? '';
        this.licenseType = this.params?.licenseType ?? '';
        this.mode = this.params?.mode ?? 0;
        // 新增或者车辆认证 1 证件新增 2车辆认证
        this.addOrManage = this.params?.addOrManage ?? 0;
        this.callback = this.getCallBack();
    }

    componentDidMount() {
        super.componentDidMount();
        if (this.mode == 0 || this.mode == 1) {
            if (this.addOrManage == 1) {
                this.queryCacheLiscense();
            } else if (this.addOrManage == 2) {
                this.queryCarCacheLiscense();
            } else {
                this.queryTransportPermissionLicenseDetail();
            }
        } else {
            this.queryTransportPermissionLicenseDetail();
        }
    }

    // 车辆认证
    queryCacheLiscense() {
        let request = new ReqQueryUserLicense();
        request.type = '3';
        request.request().then((res) => {
            if (res.isSuccess() && res.data) {
                this.url = res.data.vehicle?.transportPermissionLicenseUrl ?? '';
                this.setState({init: true});
            }
        });
    }

    async queryCarCacheLiscense() {
        this._showWaitDialog();
        let reqCache = await new ReqVehicleDetails().request();
        this._dismissWait();
        if (reqCache.isSuccess() && reqCache.data) {
            this.url = reqCache.data.transportPermissionLicenseUrl ?? '';
            this.setState({init: true});
        }
    }

    /**
     * 道路运输经营许可证详情
     */
    queryTransportPermissionLicenseDetail() {
        let req = new ReqQueryTransportPermissionLicenseDetail();
        req.vehicleId = this.vehicleId;
        this._showWaitDialog();
        req.request().then((res) => {
            this._dismissWait();
            if (res.isSuccess()) {
                if (this.mode == 2) {
                    this.queryUserExpireLicenseDetail(this.vehicleId, this.licenseType);
                    return;
                }
                this.url = this.getImg();
                this.setState({pageDetail: res.data, init: true});
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 证件详情
     * @param targetId
     * @param licenseType
     */
    queryUserExpireLicenseDetail(targetId?: string, licenseType?: string) {
        let req = new ReqQueryUserExpireLicenseDetail();
        req.targetId = targetId ?? '';
        req.licenseType = licenseType ?? '';
        req.request().then((res) => {
            if (res.isSuccess()) {
                if (TextUtils.equals('200012', res.data?.licenseType)) {
                    this.setState({pageDetail2: res.data});
                    this.url = this.getImg();
                }
                this.setState({init: true});
            } else {
                this._showToast(res.getMsg());
            }
        });
    }

    /**
     * 驳回理由
     * @returns {string | undefined}
     */
    getErrorMsg() {
        return this.state.pageDetail
            ? ['2', '4', '6'].includes(`${this.state.pageDetail?.licenseState}`)
                ? this.state.pageDetail?.transportPermissionAuditContent ?? ''
                : ''
            : TextUtils.equals('200012', this.state.pageDetail2?.licenseType)
            ? this.state.pageDetail2?.licenseReason ?? ''
            : '';
    }

    /**
     * 证件图片
     * @returns {string}
     */
    getImg() {
        return this.state.pageDetail && ['2', '4', '6'].includes(`${this.state.pageDetail?.licenseState}`) ? '' : this.state.pageDetail?.transportPermissionLicenseUrl ?? '';
    }

    /**
     * 红色背景
     * @returns {boolean}
     */
    showViewBg() {
        return this.state.pageDetail ? ['2', '6'].includes(`${this.state.pageDetail?.licenseState}`) : this.state.pageDetail2 ? TextUtils.equals('200012', this.state.pageDetail2?.licenseType) : false;
    }

    /**
     * 证件准确性校验接口
     */
    licenseInputAccuracyCheck() {
        if (TextUtils.isEmpty(this.url)) {
            this._showToast('请上传道路运输经营许可证');
            return;
        }
        let req = new ReqLicenseInputAccuracyCheck();
        req.type = '8';
        req.isNeedSave = this.addOrManage == 2 || this.addOrManage == 1 ? '1' : '0';
        req.transportPermissionLicenseUrl = this.url;
        req.vehicleId = this.vehicleId;
        this._showWaitDialog();
        req.request().then((res) => {
            if (res.isSuccess()) {
                if (this.addOrManage == 2 || this.addOrManage == 1) {
                    this._dismissWait();
                    this._showToast('上传道路运输经营许可证成功');
                    RouterUtils.skipPop();
                    this.callback && this.callback();
                } else {
                    this.commit();
                }
            } else {
                this._dismissWait();
                this._showMsgDialog(res.getMsg());
            }
        });
    }

    commit() {
        //-1：未上传 0：审核中 1：审核通过 2：审核驳回 3：异常 4：风险通过 5：风险待审核 6：风险审核驳回 7：车辆提交审核（审核中）
        if (this.mode == 2) {
            let req = new ReqUploadExpireDateLicense();
            let picUrl = new PicUrlBean();
            picUrl.picUrl = this.url;
            picUrl.type = this.licenseType;
            req.targetId = this.vehicleId;
            req.picUrlList = [picUrl];
            req.request().then((res) => {
                this._dismissWait();
                if (res.isSuccess()) {
                    RouterUtils.skipPop();
                    EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                } else {
                    this._showMsgDialog(res.getMsg());
                }
            });
        } else {
            let req = new ReqAddTransportPermissionLicense();
            req.memberName = this.login.memberName;
            req.transportPermissionLicenseUrl = this.url;
            req.vehicleId = this.vehicleId;
            req.request().then((res) => {
                this._dismissWait();
                if (res.isSuccess()) {
                    RouterUtils.skipPop();
                    EventBus.getInstance().fireEvent(Constant.event_certification_refresh, {});
                } else {
                    this._showMsgDialog(res.getMsg());
                }
            });
        }
    }

    render() {
        let title = '身份认证';
        if (this.mode == 1) {
            title = '证件更新上传';
        } else if (this.mode == 2) {
            title = '证件过期上传';
        }
        if (this.addOrManage == 2 || this.addOrManage == 1) {
            title = '车辆认证';
        }
        if (this.state.pageDetail && ['2', '6'].includes(`${this.state.pageDetail?.licenseState}`)) {
            title = '道路运输经营许可证完善资料';
        }
        return (
            <View style={{flex: 1}}>
                <UITitleView title={title} />
                {this.state.init && (
                    <View style={{marginTop: 8, backgroundColor: '#fff', alignItems: 'center'}}>
                        <ComCertificationUploadImgView
                            tvTitle1={'道路运输经营许可证'}
                            ivWarn={TextUtils.equals('4', this.state.pageDetail?.licenseState)}
                            showViewBg={this.showViewBg()}
                            tvRedAsterisk={true}
                            tvTitle3={''}
                            layout={2}
                            auditText={this.getErrorMsg()}
                            style={{paddingVertical: 15, width: '100%', paddingHorizontal: 10}}
                            tvDescView={
                                <View style={{paddingLeft: 9, paddingRight: 15, backgroundColor: '#FFF1DC', paddingVertical: 6.5, flexDirection: 'row'}}>
                                    <View style={{backgroundColor: '#F89900FF', borderRadius: 2.5, width: 5, height: 5, marginTop: 5.5, marginRight: 5}} />
                                    <Text style={{color: '#EC9000', fontSize: 13, flex: 1}}>
                                        要求上传与<Text style={{color: '#FF602E'}}>业户名称</Text>一致的<Text style={{color: '#FF602E'}}>道路运输经营许可证</Text>，请确认无误后再上传。
                                    </Text>
                                </View>
                            }
                            leftImg={{
                                url: this.url ?? '',
                                defaultValue: 'certification_driver_license_template_diagram_9',
                                resizeMode: 'cover',
                                onPostSuccess: (url) => {
                                    this.url = url;
                                },
                                onDelPic: () => {
                                    this.url = '';
                                },
                                imgStyle: {height: 210},
                            }}
                        />
                    </View>
                )}
                <View style={{flex: 1}} />
                <UIButton text={'确认提交'} borderRadius={0} height={55} onPress={() => this.licenseInputAccuracyCheck()} />
                {/*初始化基础组件*/}
                {this._initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    vehicle: {
        fontSize: 18,
        color: '#3D3D3D',
        fontWeight: 'bold',
        paddingRight: 12,
        paddingLeft: 12,
        paddingTop: 16,
        paddingBottom: 16,
    },
});
