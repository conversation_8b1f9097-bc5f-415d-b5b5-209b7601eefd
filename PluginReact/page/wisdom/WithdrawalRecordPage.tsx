import {StyleSheet, Text, View} from 'react-native';
import React, {ReactElement} from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {WithdrawalRecord} from './models/WithdrawalRecord';
import {ReqQueryDepositPassport} from './requests/ReqQueryDepositPassport';
import {ArrayUtils} from '../util/ArrayUtils';
import UITitleView from '../widget/UITitleView';
import UIListView from '../widget/UIListView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import UIPopup from '../widget/UIPopup';
import TextUtils from '../util/TextUtils';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import dayjs from 'dayjs';
import {subtractDaysFromTime} from '../util/DateUtil';

interface State extends BaseState {
    startDate?: string;
    endDate?: string;
    showStatusPop: boolean;
    selectedStatus?: string;
    listData?: WithdrawalRecord[];
}

/**
 * 注释: 提现记录页面
 * 时间: 2025/3/31 星期一 13:59
 * <AUTHOR>
 */
export default class WithdrawalRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    nowPage: number = 1;

    constructor(props) {
        super(props);
        this.state = {
            showStatusPop: false,
            startDate: dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD'),
            endDate: dayjs(new Date()).format('YYYY-MM-DD'),
        };
        this.pageParams = this.getParams();
    }

    componentDidMount() {
        this.queryWithdrawalPassport();
    }

    /**
     * 注释: 查询提现记录
     * 时间: 2025/4/10 星期四 9:40
     * <AUTHOR>
     */
    queryWithdrawalPassport() {
        let request = new ReqQueryDepositPassport();
        request.nowPage = this.nowPage;
        request.startDate = this.state.startDate;
        request.endDate = this.state.endDate;
        request.pageSize = 10;
        request.state = this.getStatus(this.state.selectedStatus);
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.nowPage == 1) {
                    this.setState({listData: res.data?.rootArray});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
                this.nowPage++;
            }
        });
    }

    getStatus(statusStr) {
        switch (statusStr) {
            case '付款中':
                return '2';
            case '成功':
                return '4';
            case '失败':
                return '3';
            case '银行退票':
                return '5';
            default:
                return '';
        }
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'提现记录'} />
                {this.renderHeadView()}
                <UIListView
                    contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                    renderItem={({item}) => this.renderItemView(item)}
                    dataList={this.state.listData}
                    onRefresh={() => {
                        this.nowPage = 1;
                        this.queryWithdrawalPassport();
                    }}
                    onLoadMore={() => {
                        this.queryWithdrawalPassport();
                    }}
                />
                {this.initCommView()}
                {this.renderPickerPop()}
            </View>
        );
    }

    /**
     * 注释: 绘制Picker选择弹窗
     * 时间: 2025/3/31 星期一 17:01
     * <AUTHOR>
     */
    renderPickerPop() {
        if (this.state.showStatusPop) {
            let status = ['付款中', '成功', '失败', '银行退票'];
            let childrens: ReactElement[] = [];
            for (let i = 0; i < status.length; i++) {
                childrens.push(
                    <UITouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => {
                            this.setState({showStatusPop: false, selectedStatus: status[i]}, () => {
                                this.nowPage = 1;
                                this.queryWithdrawalPassport();
                            });
                        }}
                        style={{
                            justifyContent: 'space-between',
                            flexDirection: 'row',
                            backgroundColor: '#fff',
                            alignItems: 'center',
                            paddingVertical: 15,
                            paddingHorizontal: 15,
                        }}>
                        <Text style={{fontSize: 16, color: '#333'}}>{status[i]}</Text>
                        {this.state.selectedStatus == status[i] && <UIImage source={'ic_selected'} style={{width: 21, height: 21}} />}
                    </UITouchableOpacity>,
                );
            }
            return (
                <UIPopup
                    title={'选择付款状态'}
                    onClose={() => {
                        this.setState({showStatusPop: false});
                    }}>
                    <View style={{flexDirection: 'column'}}>{childrens}</View>
                </UIPopup>
            );
        }
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UITouchableOpacity
                    style={styles.searchStyle}
                    onPress={() => {
                        this.setState({showStatusPop: true});
                    }}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={'icon_search'} style={{width: 15, height: 15}} />
                        <Text
                            style={{
                                fontSize: 15,
                                color: TextUtils.isNoEmpty(this.state.selectedStatus) ? '#333' : '#999',
                                marginLeft: 7,
                            }}>{`${TextUtils.isNoEmpty(this.state.selectedStatus) ? this.state.selectedStatus : '请选择付款状态'}`}</Text>
                    </View>
                    <UIImage source={'base_arrow_gray_down'} style={{width: 15, height: 8}} />
                </UITouchableOpacity>
                <UITouchableOpacity
                    style={{flexDirection: 'row', alignItems: 'center', marginTop: 13}}
                    onPress={() => {
                        Method.showStartEndTimePicker().then((res) => {
                            console.log(1111, JSON.stringify(res));
                            let startTime = res['startTime'];
                            let endTime = res['endTime'];
                            this.setState({
                                startDate: startTime.split(' ')[0],
                                endDate: endTime.split(' ')[0],
                            });
                        });
                    }}>
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#1F66FF',
                        }}>
                        {this.state.startDate ? `${this.state.startDate}至 ${this.state.endDate}` : '请选择时间'}
                    </Text>
                    <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/10 星期四 9:43
     * <AUTHOR>
     * @param item
     * @returns {React.JSX.Element}
     */
    renderItemView(item: WithdrawalRecord) {
        return (
            <View
                style={{
                    flexDirection: 'column',
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingBottom: 10,
                    backgroundColor: '#fff',
                }}>
                {/*提现申请时间*/}
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                    <Text style={{fontSize: 16, color: '#666', width: 120}}>提现申请时间：</Text>
                    <Text style={{fontSize: 16, color: '#333', flex: 1}}>{item.applyTime}</Text>
                </View>
                {/*提现金额*/}
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                    <Text style={{fontSize: 16, color: '#666', width: 120}}>提现金额：</Text>
                    <Text style={{fontSize: 16, color: '#333', flex: 1}}>{item.moneyStr}</Text>
                </View>
                {/*付款状态*/}
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                    <Text style={{fontSize: 16, color: '#666', width: 120}}>付款状态：</Text>
                    <Text
                        style={{
                            fontSize: 16,
                            color: item.state == '3' || item.state == '5' ? '#FF2020' : '#666',
                            flex: 1,
                        }}>
                        {item.stateStr}
                    </Text>
                </View>
                {/*收款银行卡*/}
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                    <Text style={{fontSize: 16, color: '#666', width: 120}}>收款银行卡：</Text>
                    <Text style={{fontSize: 16, color: '#333', flex: 1}}>{item.withdrawalAccount}</Text>
                </View>
                {/*返回报错*/}
                {(item.state == '3' || item.state == '5') && (
                    <View style={{flexDirection: 'row', alignItems: 'flex-start', marginTop: 10}}>
                        <Text style={{fontSize: 16, color: '#666', width: 120}}>返回报错：</Text>
                        <Text style={{fontSize: 16, color: '#333', flex: 1}}>{item.descriptionStr}</Text>
                    </View>
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    searchStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: 30,
        backgroundColor: '#EFF0F3',
        paddingHorizontal: 10,
        borderRadius: 4,
    },
});
