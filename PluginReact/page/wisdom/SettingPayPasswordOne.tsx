import {StyleSheet, Text, TextInput, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ReqSendVerifyCode} from './requests/ReqSendVerifyCode';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import TextUtils from '../util/TextUtils';
import {ReqCheckCodeWisdom} from './requests/ReqCheckCodeWisdom';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UITitleView from '../widget/UITitleView';
import UIButton from '../widget/UIButton';
import {SMSCodeImageDialog} from './views/SMSCodeImageDialog';
import UITouchableOpacity from '../widget/UITouchableOpacity';

interface State extends BaseState {
    countdown: number;
    smsCode?: string;
    showSMSCodeImageDialog: boolean;
}

/**
 * 注释: 设置支付密码第一步
 * 时间: 2025/4/9 星期三 13:45
 * <AUTHOR>
 */
export default class SettingPayPasswordOne extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    //0: 设置支付密码 1：忘记支付密码
    type: number;

    constructor(props) {
        super(props);
        this.state = {
            countdown: 0, // 初始化倒计时为0
            showSMSCodeImageDialog: false,
        };
        this.pageParams = this.getParams();
        this.type = this.pageParams.type ?? 0;
    }

    /**
     * 注释: 发送验证码
     * 时间: 2025/4/9 星期三 14:08
     * <AUTHOR>
     */
    sendVerifyCode(imageIdentifyCode?: string) {
        let request = new ReqSendVerifyCode();
        request.mobile = Method.getLogin()?.mobile ?? '';
        request.moduleType = this.type == 0 ? '10' : '5';
        request.plateFormType = '1';
        request.type = '1';
        request.imageIdentifyCode = imageIdentifyCode;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.startCountdown();
            } else if (TextUtils.equals('MI300043', res.getCode()) || TextUtils.equals('MI300042', res.getCode())) {
                this.setState({showSMSCodeImageDialog: true});
            } else {
                Method.showToast(res.getMsg());
            }
        });
    }

    /**
     * 注释: 校验验证码
     * 时间: 2025/4/9 星期三 14:16
     * <AUTHOR>
     */
    checkVerifyCode() {
        if (!TextUtils.isNoEmpty(this.state.smsCode)) {
            Method.showToast('请输入验证码');
            return;
        }
        Method.showLoading();
        let request = new ReqCheckCodeWisdom();
        request.verifyCode = this.state.smsCode ?? '';
        request.mobile = Method.getLogin()?.mobile ?? '';
        request.moduleType = this.type == 0 ? '10' : '5';
        request.verifyCodeType = '1';
        request.request().then((res) => {
            Method.hideLoading();
            if (res.isSuccess()) {
                RouterUtils.skipPop();
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'SettingPayPasswordTwo',
                    data: {token: res.data?.token},
                });
            } else {
                Method.showToast(res.getMsg());
            }
        });
    }

    render() {
        let phone = Method.getLogin()?.mobile ?? '';
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'验证手机号码'} />
                {this.renderSmsInput()}
                <UIButton
                    text={'下一步'}
                    enabled={TextUtils.isNoEmpty(this.state.smsCode)}
                    style={{marginHorizontal: 15, marginTop: 22}}
                    onPress={() => {
                        this.checkVerifyCode();
                    }}
                />
                {/*图形验证码*/}
                {this.state.showSMSCodeImageDialog && (
                    <SMSCodeImageDialog
                        mobile={phone}
                        cancel={() => {
                            this.setState({showSMSCodeImageDialog: false});
                        }}
                        confirm={async (imageCode) => {
                            this.setState({showSMSCodeImageDialog: false});
                            this.sendVerifyCode(imageCode);
                        }}
                    />
                )}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制验证码输入框
     * 时间: 2025/4/3 星期四 9:38
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    private renderSmsInput() {
        const {countdown} = this.state;
        let phone = Method.getLogin()?.mobile ?? '';
        return (
            <View style={{backgroundColor: '#fff', marginTop: 9}}>
                {/* 手机号*/}
                <View
                    style={[
                        styles.itemView,
                        {
                            borderBottomWidth: 0.5,
                            borderColor: '#eee',
                        },
                    ]}>
                    <Text style={{fontSize: 17, color: '#333'}}>手机号</Text>
                    <Text
                        style={{
                            fontSize: 17,
                            color: '#666',
                            marginLeft: 20,
                        }}>{`${phone.slice(0, 3)}****${phone.slice(-4)}`}</Text>
                </View>
                {/*验证码*/}
                <View style={styles.itemView}>
                    <Text style={{fontSize: 17, color: '#333'}}>验证码</Text>
                    <TextInput
                        style={{fontSize: 17, color: '#666', flex: 1, marginLeft: 20}}
                        placeholder={'请输入验证码'}
                        keyboardType={'numeric'}
                        maxLength={6}
                        onChangeText={(text) => {
                            this.setState({smsCode: text});
                        }}
                    />
                    <UITouchableOpacity
                        onPress={() => {
                            this.sendVerifyCode();
                        }}
                        disabled={countdown > 0} // 当倒计时大于0时按钮不可用
                    >
                        <Text
                            style={{
                                fontSize: 17,
                                color: countdown > 0 ? '#ccc' : '#5086FC',
                            }}>
                            {countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码'}
                        </Text>
                    </UITouchableOpacity>
                </View>
            </View>
        );
    }

    /**
     * 注释: 开始倒计时
     * 时间: 2025/4/3 星期四 9:37
     * <AUTHOR>
     */
    startCountdown = () => {
        let countdown = 120;
        this.setState({countdown}, () => {
            const interval = setInterval(() => {
                countdown -= 1;
                this.setState({countdown});
                if (countdown <= 0) {
                    clearInterval(interval);
                }
            }, 1000);
        });
    };
}

const styles = StyleSheet.create({
    itemView: {
        flexDirection: 'row',
        alignItems: 'center',
        height: 50,
        paddingHorizontal: 15,
    },
});
