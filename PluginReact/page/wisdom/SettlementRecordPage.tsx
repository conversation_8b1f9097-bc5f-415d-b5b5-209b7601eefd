import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {FilterPopupBean} from '../widget/filter/FilterPopupBean';
import {SettledListInfo} from './models/SettledListInfo';
import {ArrayUtils} from '../util/ArrayUtils';
import {ReqQuerySettledList} from './requests/ReqQuerySettledList';
import UISearchView from '../widget/UISearchView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {Method} from '../util/NativeModulesTools';
import UIImage from '../widget/UIImage';
import UIListView from '../widget/UIListView';
import {PaymentRecord} from './models/PaymentRecord';
import TextUtils from '../util/TextUtils';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UICommonFilterPopup from '../widget/filter/UICommonFilterPopup';
import dayjs from 'dayjs';
import {subtractDaysFromTime} from '../util/DateUtil';

interface State extends BaseState {
    startDate?: string;
    endDate?: string;
    showFilterDialog?: boolean;
    filterData: FilterPopupBean[];
    listData?: SettledListInfo[];
}

/**
 * 注释: 已结算运费
 * 时间: 2025/4/11 星期五 14:08
 * <AUTHOR>
 */
export default class SettlementRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    nowPage: number = 1;
    orderId: string;
    initFilterData: FilterPopupBean[] = [];

    constructor(props) {
        super(props);
        //货主名称
        let inputHzName = new FilterPopupBean();
        inputHzName.title = '货主名称';
        inputHzName.placeholder = '请输入货主名称';
        inputHzName.filterType = 'input';
        //货物名称
        let inputCargoName = new FilterPopupBean();
        inputCargoName.title = '货物名称';
        inputCargoName.placeholder = '请输入货物名称';
        inputCargoName.filterType = 'input';
        //车牌号
        let inputPlateNumber = new FilterPopupBean();
        inputPlateNumber.title = '车牌号';
        inputPlateNumber.placeholder = '请输入车牌号';
        inputPlateNumber.filterType = 'input';
        this.state = {
            filterData: [inputHzName, inputCargoName, inputPlateNumber],
            showFilterDialog: false,
            startDate: dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD'),
            endDate: dayjs(new Date()).format('YYYY-MM-DD'),
        };
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.initFilterData = ArrayUtils.deepCopy(this.state.filterData, FilterPopupBean);
    }

    componentDidMount() {
        super.componentDidMount();
        this.querySettledList();
    }

    /**
     * 注释: 查询已结算运费
     * 时间: 2025/4/14 星期一 19:26
     * <AUTHOR>
     */
    querySettledList() {
        let request = new ReqQuerySettledList();
        request.nowPage = this.nowPage;
        request.pageSize = 10;
        request.orderId = this.orderId;
        request.startTime = this.state.startDate;
        request.endTime = this.state.endDate;
        request.shipperName = this.state.filterData[0].inputValue;
        request.cargoName = this.state.filterData[1].inputValue;
        request.plateNumber = this.state.filterData[2].inputValue;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.nowPage == 1) {
                    this.setState({listData: res.data?.rootArray});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
                this.nowPage++;
            }
        });
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UISearchView
                    placeholder={'请输入运单号搜索'}
                    searchButton={true}
                    searchView={
                        <UITouchableOpacity
                            onPress={() => {
                                this.nowPage = 1;
                                this.querySettledList();
                            }}>
                            <Text style={{fontSize: 15, color: '#5086FC'}}>搜索</Text>
                        </UITouchableOpacity>
                    }
                    backGroundStyle={{
                        backgroundColor: '#EFF0F3',
                        borderRadius: 4,
                        borderWidth: 0,
                        marginHorizontal: 0,
                        paddingHorizontal: 10,
                    }}
                    onChangeText={(e) => {
                        this.orderId = e;
                    }}
                    onSubmitEditing={(text) => {
                        this.nowPage = 1;
                        this.orderId = text;
                        this.querySettledList();
                    }}
                />
                <View style={{marginTop: 13, flexDirection: 'row'}}>
                    {/*时间选择*/}
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            Method.showStartEndTimePicker().then((res) => {
                                let startTime = res['startTime'];
                                let endTime = res['endTime'];
                                this.setState(
                                    {
                                        startDate: startTime.split(' ')[0],
                                        endDate: endTime.split(' ')[0],
                                    },
                                    () => {
                                        this.querySettledList();
                                    },
                                );
                            });
                        }}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#1F66FF',
                            }}>
                            {this.state.startDate ? `${this.state.startDate}至 ${this.state.endDate}` : '请选择时间'}
                        </Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                    {/*更多*/}
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center', marginLeft: 50}}
                        onPress={() => {
                            this.setState({showFilterDialog: true});
                        }}>
                        <Text style={{fontSize: 14, color: '#1F66FF'}}>更多</Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 9:47
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderItemView(item: SettledListInfo) {
        return (
            <View
                style={{
                    flexDirection: 'column',
                    backgroundColor: '#fff',
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingBottom: 10,
                }}>
                {this.renderItem('结算时间：', item.consignorSettleTimeStr)}
                {this.renderItem('运单号：', item.orderId)}
                {this.renderItem('车牌号：', item.plateNumber)}
                {this.renderItem('货主：', item.consignorName)}
                {this.renderItem('启运地：', item.despatchPlaceStr)}
                {this.renderItem('目的地：', item.deliverPlaceStr)}
                {this.renderItem('货物名称：', item.cargoName)}
                {this.renderItem('成交数量：', item.orderUnitStr)}
                {this.renderItem('运费金额：', item.carrierSettleMoneyStr)}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:02
     * <AUTHOR>
     * @param label
     * @param value
     * @returns {Element}
     * @param orderId
     */
    renderItem(label: string, value: string) {
        return (
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                <Text style={{fontSize: 16, color: '#666'}}>{label}</Text>
                <Text style={{fontSize: 16, color: '#333'}}>{value}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制过滤弹窗
     * 时间: 2025/4/14 星期一 13:58
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderFilterDialog() {
        return (
            <UICommonFilterPopup
                filterData={this.state.filterData}
                onReset={() => {
                    this.setState(
                        {
                            filterData: ArrayUtils.deepCopy(this.initFilterData, FilterPopupBean),
                            showFilterDialog: false,
                        },
                        () => {
                            this.querySettledList();
                        },
                    );
                }}
                onClose={(data: FilterPopupBean[]) => {
                    this.setState({filterData: data, showFilterDialog: false});
                }}
                onSubmit={(data: FilterPopupBean[]) => {
                    this.setState({filterData: data, showFilterDialog: false}, () => {
                        this.querySettledList();
                    });
                }}
            />
        );
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'已结算运费'} />
                {this.renderHeadView()}
                <UIListView
                    contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                    renderItem={({item}) => this.renderItemView(item)}
                    dataList={this.state.listData}
                    onRefresh={() => {
                        this.nowPage = 1;
                        this.querySettledList();
                    }}
                    onLoadMore={() => {
                        this.querySettledList();
                    }}
                />
                {/*过滤弹窗*/}
                {this.state.showFilterDialog && this.renderFilterDialog()}
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
