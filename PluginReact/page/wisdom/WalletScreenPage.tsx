import {ScrollView, StyleSheet, Text, View, Clipboard} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {ReqQueryMerchantRegisterInfo} from './requests/ReqQueryMerchantRegisterInfo';
import {BookInfo, ReqGetPassportAccount, RspGetPassportAccount} from './requests/ReqGetPassportAccount';
import {ReqRechargeAccountInfo, RspRechargeAccountInfo} from './requests/ReqRechargeAccountInfo';
import DeviceAuthDialog from './views/DeviceAuthDialog';
import {Method} from '../util/NativeModulesTools';
import {ReqCheckSameDevice} from './requests/ReqCheckSameDevice';
import {ReqBankList, RsqBankList} from './requests/ReqBankList';
import {ArrayUtils} from '../util/ArrayUtils';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {ReqCheckSetPassword} from './requests/ReqCheckSetPassword';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import LinearGradient from 'react-native-linear-gradient';
import UIImageBackground from '../widget/UIImageBackground';
import {gScreen_statusBarHeight} from '../util/scaled-style';
import TextUtils from '../util/TextUtils';

interface State extends BaseState {
    showDeviceAuthDialog: boolean;
    //开户状态：1-受理中 2-成功 3-失败
    status?: string;
    passportAccount?: BookInfo;
    rechargeAccountInfo?: RspRechargeAccountInfo;
    bankList?: RsqBankList[];
}

/**
 * 注释:  交易通首页
 * 时间: 2025/3/28 星期五 15:55
 * <AUTHOR>
 */
export default class WalletScreenPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;

    constructor(props) {
        super(props);
        this.state = {
            showDeviceAuthDialog: false,
            bankList: [],
        };
        this.pageParams = this.getParams();
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryMerchantRegisterInfo();
        this.queryBankList();
    }

    /**
     * 注释: 查询商户注册信息
     * 时间: 2025/4/8 星期二 14:27
     * <AUTHOR>
     */
    queryMerchantRegisterInfo() {
        let request = new ReqQueryMerchantRegisterInfo();
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({status: res.data?.merchantRegisterInfo?.status});
                //交易通账号已开通
                if (res.data?.merchantRegisterInfo?.applyRegisterFlag == '1') {
                    this.queryPassportAccount();
                }
                if (res.data?.merchantRegisterInfo?.status == '2') {
                    this.queryRechargeAccountInfo();
                }
            }
        });
    }

    /**
     * 注释: 查询交易通账户信息
     * 时间: 2025/4/8 星期二 14:39
     * <AUTHOR>
     */
    queryPassportAccount() {
        let request = new ReqGetPassportAccount();
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({passportAccount: res.data?.bookInfo});
            }
        });
    }

    /**
     * 注释: 查询充值账户信息
     * 时间: 2025/4/8 星期二 14:44
     * <AUTHOR>
     */
    queryRechargeAccountInfo() {
        let request = new ReqRechargeAccountInfo();
        request.request().then((res) => {
            this.setState({
                rechargeAccountInfo: res.data,
            });
        });
    }

    /**
     * 注释: 查询银行卡列表
     * 时间: 2025/4/11 星期五 9:17
     * <AUTHOR>
     */
    queryBankList() {
        let request = new ReqBankList();
        request.request().then((res) => {
            if (res.isSuccess() && ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                this.setState({bankList: res.data?.rootArray});
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <ScrollView style={{flex: 1}}>
                    {/*绘制头部视图*/}
                    {this.renderHeadView()}
                    {/*绘制开通失败视图*/}
                    {this.state.status != '2' && this.renderKaiTongFailedView()}
                    {/*绘制充值说明*/}
                    {this.state.rechargeAccountInfo && this.state.passportAccount?.accountType == '2' && this.renderPayDocView()}
                    {/*绘制菜单列表*/}
                    {this.renderMenuListView()}
                </ScrollView>
                {/*设备认证*/}
                {this.state.showDeviceAuthDialog && (
                    <DeviceAuthDialog
                        onClose={() => {
                            this.setState({showDeviceAuthDialog: false});
                        }}
                        onSubmit={() => {
                            this.checkBindBank();
                        }}
                    />
                )}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 检测相同设备
     * 时间: 2025/4/8 星期二 16:54
     * <AUTHOR>
     */
    async checkSameDevice() {
        let login = Method.getLogin();
        let request = new ReqCheckSameDevice();
        request.macAddress = Method.getMacAddress();
        request.udid = login?.udid;
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.checkBindBank();
            } else {
                this.setState({showDeviceAuthDialog: true});
            }
        });
    }

    /**
     * 注释: 检测是否绑定银行卡
     * 时间: 2025/4/8 星期二 19:25
     * <AUTHOR>
     * @returns {Promise<void>}
     */
    checkBindBank() {
        let request = new ReqBankList();
        request.request().then((res) => {
            if (res.isSuccess() && ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                this.checkPayPassWord();
            } else {
                this.showDialogFromParams({
                    title: '未绑卡提示',
                    msg: '绑卡后可用于资金的提现和结算',
                    leftTxt: '取消',
                    rightTxt: '去绑卡',
                    rightOnClick: () => {
                        if (this.state.passportAccount?.accountType == '1') {
                            //对私
                            RouterUtils.skipRouter(RouterUrl.WisdomAddPersonBankRouter);
                        } else {
                            //对公
                            RouterUtils.skipRouter(RouterUrl.WisdomAddPublicBankRouter);
                        }
                    },
                });
            }
        });
    }

    /**
     * 注释: 检测支付密码
     * 时间: 2025/4/8 星期二 19:33
     * <AUTHOR>
     */
    checkPayPassWord() {
        let request = new ReqCheckSetPassword();
        request.request().then((res) => {
            if (res.isSuccess() && res.data?.isInfo == 1) {
                //跳转体现页面
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'WithdrawalPage',
                    data: {
                        passportAccount: this.state.passportAccount,
                    },
                });
            } else {
                this.showDialogFromParams({
                    title: '还未设置支付密码',
                    msg: '提现至银行卡需要确认支付密码，您还未设置支付密码',
                    leftTxt: '取消',
                    rightTxt: '去设置',
                    rightOnClick: () => {
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'SettingPayPasswordOne'});
                    },
                });
            }
        });
    }

    /**
     * 注释: 绘制菜单列表
     * 时间: 2025/3/28 星期五 17:08
     * <AUTHOR>
     */
    renderMenuListView() {
        let showBankTip = false;
        let bankList = this.state.bankList?.filter((item) => {
            return item.bindState == '2';
        });
        showBankTip = ArrayUtils.isEmpty(bankList);
        return (
            <View
                style={{
                    marginBottom: 25,
                    marginHorizontal: 10,
                    marginTop: 10,
                    borderRadius: 8,
                    backgroundColor: '#fff',
                    paddingHorizontal: 15,
                }}>
                {this.renderMenuItemView('ic_czjl', '支付记录', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'PayRecordPage'});
                })}
                {this.renderMenuItemView('ic_txjl', '提现记录', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'WithdrawalRecordPage'});
                })}
                {this.renderMenuItemView('ic_djsr', '订金记录', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'DepositIncomeRecordPage'});
                })}
                {this.renderMenuItemView('ic_jsyf', '已结算运费', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'SettlementRecordPage'});
                })}
                {this.renderMenuItemView('ic_szmx', '收支明细', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'RevenueRecordPage',
                        data: {bookNo: this.state.passportAccount?.bookNo},
                    });
                })}
                {this.renderMenuItemView('ic_djjl', '冻结记录', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'FreezeRecordPage',
                        data: {bookNo: this.state.passportAccount?.bookNo},
                    });
                })}
                {this.renderMenuItemView('ic_bdyhk', '绑定银行卡', () => {
                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                        page: 'WalletBankSupervisionListPage',
                        data: {subsidiaryId: this.state.passportAccount?.subsidiaryId},
                    });
                })}
                {showBankTip && (
                    <View style={styles.bankTipStyle}>
                        <UIImage source={'ic_warning'} style={{width: 13, height: 13, marginTop: 2, marginRight: 2}} />
                        <Text
                            style={{
                                fontSize: 13,
                                color: '#FF5D1A',
                            }}>
                            您当前尚未绑定银行卡，绑定前将无法进行运费充值操作，请尽快完成绑定。
                        </Text>
                    </View>
                )}
            </View>
        );
    }

    /**
     * 注释: 绘制菜单项
     * 时间: 2025/3/28 星期五 17:08
     * <AUTHOR>
     */
    renderMenuItemView(icon: string, label: string, onPress?: Function) {
        return (
            <UITouchableOpacity
                onPress={onPress}
                style={{
                    flexDirection: 'row',
                    height: 50,
                    borderBottomWidth: 0.5,
                    borderColor: '#eee',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UIImage source={icon} style={{width: 16, height: 16}} />
                    <Text style={{fontSize: 15, color: '#333', fontWeight: 'bold', marginLeft: 12}}>{label}</Text>
                </View>
                <UIImage source={'ic_arrow_right_gray'} style={{width: 6, height: 10}} />
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 绘制开通失败视图
     * 时间: 2025/4/7 星期一 14:52
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderKaiTongFailedView() {
        return (
            <LinearGradient colors={['#FFF4F4', '#FFF']} style={styles.kaiTongFailedStyle}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UIImage source={this.state.status == '1' ? 'ic_wait' : 'ic_warning'} style={{width: 14, height: 14, marginRight: 7}} />
                    <Text
                        style={{
                            fontSize: 15,
                            color: '#333333',
                            fontWeight: 'bold',
                        }}>
                        {this.state.status == '1' ? '监管账户开通中...' : '监管账户开通失败'}
                    </Text>
                </View>
                {this.state.status == '1' && (
                    <Text style={{marginTop: 10, fontSize: 13, color: '#666'}}>
                        请等待开通审核结果。未开通监管账户前将无法线上支付，如有疑问请拨打
                        <Text style={{fontSize: 13, color: '#275CFF'}}>400-888-8888</Text>
                    </Text>
                )}
                {this.state.status == '3' && (
                    <Text style={{marginTop: 10, fontSize: 13, color: '#666'}}>
                        您的监管账户<Text style={{fontSize: 13, color: '#FF1B1B'}}>因证件照信息验证失效开通失败</Text>，无法线上 支付，如有疑问请拨打
                        <Text style={{fontSize: 13, color: '#275CFF'}}>400-888-8888</Text>
                    </Text>
                )}
            </LinearGradient>
        );
    }

    /**
     * 注释: 绘制充值说明
     * 时间: 2025/3/28 星期五 16:40
     * <AUTHOR>
     */
    renderPayDocView() {
        return (
            <LinearGradient
                colors={['#FFFAED', '#FFF']}
                style={{
                    marginHorizontal: 10,
                    paddingVertical: 11,
                    paddingHorizontal: 15,
                    marginTop: 8,
                    borderRadius: 5,
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UIImage source={'ic_pay_doc'} style={{width: 14, height: 14, marginRight: 7}} />
                    <Text style={{fontSize: 15, color: '#333333'}}>充值说明</Text>
                </View>
                <Text style={{fontSize: 13, color: '#666', marginTop: 10}}>
                    请使用企业网银或去银行柜台，使用您绑定的银行卡，通过对公打款至<Text style={{color: '#F39324'}}>“招商银行第三方资金平台账户”</Text>
                    ,进行交易 通钱包充值，充值账号信息如下：
                </Text>
                <View style={{flexDirection: 'row', marginTop: 10}}>
                    <Text style={{fontSize: 13, color: '#666'}}>账户名称：</Text>
                    <Text style={{fontSize: 13, color: '#333'}}>{this.state.rechargeAccountInfo?.accountName}</Text>
                </View>
                <View style={{flexDirection: 'row', marginTop: 10, alignItems: 'center'}}>
                    <Text style={{fontSize: 13, color: '#666'}}>充值账户：</Text>
                    <Text style={{fontSize: 13, color: '#333'}}>{this.state.rechargeAccountInfo?.accountNo}</Text>
                    <UITouchableOpacity
                        style={{width: 25, height: 25, alignItems: 'center', justifyContent: 'center'}}
                        onPress={() => {
                            Clipboard.setString(this.state.rechargeAccountInfo?.accountNo ?? '');
                            Method.showToast('复制成功');
                        }}>
                        <UIImage source={'ic_copy'} style={{width: 13, height: 13}} />
                    </UITouchableOpacity>
                </View>
                <View style={{flexDirection: 'row', marginTop: 10}}>
                    <Text style={{fontSize: 13, color: '#666'}}>开户网点：</Text>
                    <Text style={{fontSize: 13, color: '#333'}}>{this.state.rechargeAccountInfo?.bankSubName}</Text>
                </View>
            </LinearGradient>
        );
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/28 星期五 16:13
     * <AUTHOR>
     */
    renderHeadView() {
        let showFreeze = true;
        if (this.state.status == '2' && parseInt(this.state.passportAccount?.freezeMoney ?? '0') == 0) {
            showFreeze = false;
        }
        return (
            <UIImageBackground source={showFreeze ? 'img_jiaoyitong_head' : 'img_jiaoyitong_head_2'} style={{width: '100%', height: showFreeze ? 235 : 187, paddingHorizontal: 10}}>
                <View style={{flexDirection: 'column', top: gScreen_statusBarHeight}}>
                    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                        <UITouchableOpacity
                            style={{position: 'absolute', left: 15, top: 5}}
                            onPress={() => {
                                RouterUtils.skipPop();
                            }}>
                            <UIImage source={'ic_back'} style={{width: 44, height: 18}} />
                        </UITouchableOpacity>
                        <Text
                            style={{
                                fontSize: 18,
                                color: '#333',
                                fontWeight: 'bold',
                            }}>
                            交易通钱包
                        </Text>
                    </View>
                    <LinearGradient
                        colors={['#FFFFFF5E', '#fff']}
                        style={{
                            flexDirection: 'column',
                            borderRadius: 8,
                            paddingHorizontal: 15,
                            paddingVertical: 12,
                            marginTop: 30,
                        }}>
                        {this.state.passportAccount?.accountType == '2' && (
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Text
                                    style={{
                                        fontSize: 13,
                                        color: '#855000',
                                        fontWeight: 'bold',
                                    }}>
                                    {this.state.passportAccount?.bookName ?? ''}
                                </Text>
                                {this.state.status != '2' && TextUtils.isNoEmpty(this.state.passportAccount?.bookName) && <UIImage source={'user_icon_4'} style={{width: 53, height: 18, marginLeft: 8}} />}
                            </View>
                        )}
                        {/*可用余额*/}
                        <View style={{flexDirection: 'row', justifyContent: 'space-between', marginTop: 10}}>
                            <View style={{flexDirection: 'column'}}>
                                <Text style={{fontSize: 16, color: '#666'}}>可用余额</Text>
                                <Text
                                    style={{
                                        fontSize: 19,
                                        color: '#333',
                                        fontWeight: 'bold',
                                    }}>
                                    {this.state.passportAccount?.depositMoneyStr ?? '¥ 0'}
                                </Text>
                            </View>
                            <UITouchableOpacity
                                onPress={async () => {
                                    if (this.state.status == '2') {
                                        await this.checkSameDevice();
                                    }
                                }}>
                                <UIImage source={this.state.status == '2' ? 'ic_tixian' : 'ic_tixian_gray'} style={{width: 82, height: 31}} />
                            </UITouchableOpacity>
                        </View>
                        {/*冻结余额*/}
                        {showFreeze && (
                            <UITouchableOpacity
                                style={styles.seeMoneyDetail}
                                onPress={() => {
                                    RouterUtils.skipRouter(RouterUrl.ReactPage, {
                                        page: 'FreezeRecordPage',
                                        data: {bookNo: this.state.passportAccount?.bookNo},
                                    });
                                }}>
                                <Text
                                    style={{
                                        fontSize: 13,
                                        color: '#666',
                                    }}>{`冻结金额：${this.state.passportAccount?.freezeMoneyStr ?? 0}元`}</Text>
                                <UIImage source={'ic_arrow_right_glod'} style={{width: 6, height: 10}} />
                            </UITouchableOpacity>
                        )}
                    </LinearGradient>
                </View>
            </UIImageBackground>
        );
    }
}

const styles = StyleSheet.create({
    seeMoneyDetail: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        backgroundColor: '#FFF9E9',
        minHeight: 28,
        alignItems: 'center',
        marginTop: 8,
        paddingHorizontal: 7,
    },
    kaiTongFailedStyle: {
        flexDirection: 'column',
        borderRadius: 6,
        marginHorizontal: 10,
        marginTop: 8,
        paddingHorizontal: 15,
        paddingVertical: 12,
    },
    bankTipStyle: {
        backgroundColor: '#FFF3F1',
        borderRadius: 4,
        width: '100%',
        flexDirection: 'row',
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginVertical: 10,
    },
});
