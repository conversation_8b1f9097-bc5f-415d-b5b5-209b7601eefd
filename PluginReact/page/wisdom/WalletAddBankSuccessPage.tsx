import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {Method} from '../util/NativeModulesTools';

interface State extends BaseState {}

/**
 *  desc: 添加银行卡成功
 *  user: 宋双朋
 *  time: 2025/4/11 11:45
 */
export default class WalletAddBankSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    constructor(props) {
        super(props);
        this.state = {};
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#fff'}}>
                <UITitleView title={'添加成功'} />
                <View style={{flex: 1, alignItems: 'center', marginHorizontal: 25}}>
                    <UIImage source={'wallet_bank_icon_1'} style={{width: 32, height: 32}} />
                    <Text
                        style={{
                            fontSize: 16,
                            color: '#333333',
                            marginTop: 15,
                            fontWeight: 'bold',
                        }}>
                        {'银行卡添加成功'}
                    </Text>
                    <Text
                        style={{
                            fontSize: 16,
                            color: '#333333',
                            marginTop: 15,
                            textAlign: 'center',
                        }}>
                        {'为了保障资金安全并'}
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#F39324',
                                marginTop: 15,
                            }}>
                            {'支持后续提现/充值'}
                        </Text>
                        {'操作，请将此卡与监管账户关联'}
                    </Text>
                </View>
                {/*底部按钮*/}
                <View style={styles.buttonStyle}>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openLineServer();
                        }}>
                        <UIImage source={'img_online_service'} style={{width: 103, height: 31, marginBottom: 35}} />
                    </UITouchableOpacity>
                    <UIButton
                        text={'关闭'}
                        style={{width: 340, height: 44}}
                        onPress={() => {
                            RouterUtils.skipPop();
                        }}
                    />
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    itemStyle: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderTopWidth: 0.5,
        borderColor: '#eee',
        height: 50,
    },
    buttonStyle: {
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 15,
        marginBottom: 30,
    },
    subTitleStyle: {
        fontSize: 14,
        color: '#999',
        marginTop: 2,
        marginBottom: 20,
    },
});
