import {GestureHandlerRootView} from 'react-native-gesture-handler';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {ReqBankList, RsqBankList} from './requests/ReqBankList';
import {Method} from '../util/NativeModulesTools';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UIImage from '../widget/UIImage';
import WalletBankSupervisionItemView from './views/WalletBankSupervisionItemView';
import UITitleView from '../widget/UITitleView';
import {ArrayUtils} from '../util/ArrayUtils';
import UIListView from '../widget/UIListView';
import {View, Text, StyleSheet} from 'react-native';
import React from 'react';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';

interface State extends BaseState {
    rightMargin: number;
    listData?: RsqBankList[];
}

/**
 *  desc: 绑定招商监管户银行卡
 *  user: 宋双朋
 *  time: 2025/4/9 10:04
 */
export default class WalletBankSupervisionListPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    subsidiaryId?: string;
    nowPage = 1;

    constructor(props) {
        super(props);
        this.subsidiaryId = this.getParams().subsidiaryId;
        this.state = {
            rightMargin: 15,
        };
    }

    componentDidMount() {
        super.componentDidMount();
        EventBus.getInstance().addListener(Constant.event_wallet_bank_list, this.refreshListListener);
        this.refreshListListener();
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        EventBus.getInstance().removeListener(this.refreshListListener);
    }

    refreshListListener = () => {
        this.nowPage = 1;
        this.queryBankList();
    };

    // 加载数据
    queryBankList() {
        let request = new ReqBankList();
        request.request().then((res) => {
            if (res.isSuccess() && ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                if (this.nowPage == 1) {
                    let list = ArrayUtils.deepCopy(res.data?.rootArray ?? [], RsqBankList);
                    this.setState({listData: list});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
            }
        });
    }

    /**
     * 注释：银行卡view
     * 时间：2025/4/9 10:31
     * @author：宋双朋
     * @param item
     * @param index
     * @returns {JSX.Element}
     */
    renderItemView = (item: RsqBankList) => {
        return (
            <WalletBankSupervisionItemView
                item={item}
                callBack={() => {
                    this.refreshListListener();
                }}
                subsidiaryId={this.subsidiaryId}
            />
        );
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'绑定招商监管户银行卡'} />
                <View
                    style={{
                        backgroundColor: '#FEF6D9',
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingLeft: 15,
                        paddingRight: 15,
                        paddingTop: 5,
                        paddingBottom: 5,
                    }}>
                    <UIImage source={'icon_comm_wh'} style={{width: 18, height: 18}} />
                    <Text
                        style={{
                            color: '#666666',
                            fontSize: 14,
                            textAlign: 'center',
                            marginLeft: 5,
                        }}>
                        {'您需至少绑定一张银行卡'}
                    </Text>
                </View>
                <GestureHandlerRootView style={{flex: 1}}>
                    <UIListView
                        renderItem={({item}) => {
                            return this.renderItemView(item);
                        }}
                        dataList={this.state.listData}
                    />
                </GestureHandlerRootView>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {flex: 1, backgroundColor: '#EFF0F3'},
});
