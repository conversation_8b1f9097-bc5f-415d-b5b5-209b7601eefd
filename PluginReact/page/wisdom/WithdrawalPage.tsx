import {ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ReqBankList, RsqBankList} from './requests/ReqBankList';
import {BookInfo, RspGetPassportAccount} from './requests/ReqGetPassportAccount';
import {ArrayUtils} from '../util/ArrayUtils';
import {ReqBindBankPassport} from './requests/ReqBindBankPassport';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import React from 'react';
import {ReqCashConfirmDeposit} from './requests/ReqCashConfirmDeposit';
import TextUtils from '../util/TextUtils';
import {Method} from '../util/NativeModulesTools';
import {RouterUrl} from '../base/RouterUrl';
import UITitleView from '../widget/UITitleView';
import UIButton from '../widget/UIButton';
import PayPasswordDialog from './views/PayPasswordDialog';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import {http} from '../const.global';
import {RouterUtils} from '../util/RouterUtils';

interface State extends BaseState {
    showPayPassWordDialog: boolean;
    showError: boolean;
    bankList?: RsqBankList[];
    selectedBank?: RsqBankList;
    money?: string;
}

/**
 * 注释: 提现页面
 * 时间: 2025/4/3 星期四 10:40
 * <AUTHOR>
 */
export default class WithdrawalPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    passportAccount?: BookInfo;
    private inputKey = React.createRef<TextInput>();

    constructor(props) {
        super(props);
        this.state = {showPayPassWordDialog: false, showError: false};
        this.pageParams = this.getParams();
        this.passportAccount = this.pageParams.passportAccount;
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryBankList();
        setTimeout(() => {
            this.inputKey.current?.focus();
        }, 300);
    }

    /**
     * 注释: 查询银行卡列表
     * 时间: 2025/4/9 星期三 15:53
     * <AUTHOR>
     */
    queryBankList() {
        let request = new ReqBankList();
        request.queryBindFlag = '1';
        request.request().then((res) => {
            if (res.isSuccess() && ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                this.setState({bankList: res.data?.rootArray, selectedBank: res.data?.rootArray[0]});
            }
        });
    }

    /**
     * 注释: 绑定监管账户
     * 时间: 2025/4/9 星期三 19:06
     * <AUTHOR>
     */
    bindBankPassport() {
        let request = new ReqBindBankPassport();
        request.cardId = this.state.selectedBank?.cid ?? '';
        request.subsidiaryId = this.passportAccount?.subsidiaryId ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                this.setState({showPayPassWordDialog: true});
            } else {
                this.showDialogFromParams({
                    title: '招行监管账户绑定失败',
                    msg: '请选择一张已绑定监管账户的银行卡',
                    mode: 1,
                    rightTxt: '我知道了',
                });
            }
        });
    }

    /**
     * 注释: 开始提现
     * 时间: 2025/4/9 星期三 19:45
     * <AUTHOR>
     */
    startWithdrawal(code: string) {
        Method.showWaitDialog();
        let request = new ReqCashConfirmDeposit();
        request.depositMoney = this.state.money ?? '';
        request.bindFlag = 1;
        request.depositPwd = TextUtils.md5(code);
        request.cardId = this.state.selectedBank?.cid ?? '';
        request.subsidiaryId = this.passportAccount?.subsidiaryId ?? '';
        request.request().then((res) => {
            Method.hideWaitDialog();
            if (res.isSuccess()) {
                RouterUtils.skipPop();
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'WithdrawalSuccessPage',
                    data: {bankInfo: this.state.selectedBank, money: this.state.money},
                });
            } else {
                RouterUtils.skipPop();
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'WithdrawalFailedPage',
                    data: {reason: res.getMsg(), passportAccount: this.passportAccount},
                });
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView
                    title={'提现'}
                    rightText={'提现记录'}
                    rightTextStyle={{fontSize: 16, color: '#3F7BFF', marginRight: 15}}
                    clickRight={() => {
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {page: 'WithdrawalRecordPage'});
                    }}
                />
                <ScrollView style={{flex: 1}}>
                    {/*绘制提现输入框*/}
                    {this.renderWithdrawalInputView()}
                    {/*绘制提现方式*/}
                    {this.renderWithdrawalBankView()}
                    {/*提交按钮*/}
                    <UIButton
                        text={'确认提交'}
                        style={{marginHorizontal: 30, marginTop: 30, marginBottom: 20}}
                        onPress={() => {
                            if (TextUtils.isEmpty(this.state.money)) {
                                Method.showToast('请输入提现金额');
                                return;
                            }
                            if (parseInt(this.state.money ?? '0') > parseInt(this.passportAccount?.depositMoney ?? '0')) {
                                Method.showToast('提现金额不能大于余额');
                                return;
                            }
                            if (this.state.selectedBank?.bindState == '1') {
                                this.showBindBankTipDialog();
                            } else {
                                this.setState({showPayPassWordDialog: true});
                            }
                        }}
                    />
                </ScrollView>
                {/*支付弹窗*/}
                {this.state.showPayPassWordDialog && (
                    <PayPasswordDialog
                        money={this.state.money}
                        onSubmit={(code) => {
                            this.setState({showPayPassWordDialog: false});
                            this.startWithdrawal(code);
                        }}
                        onClose={() => {
                            this.setState({showPayPassWordDialog: false});
                        }}
                    />
                )}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制银行卡
     * 时间: 2025/4/3 星期四 16:10
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderBankItem(item: RsqBankList) {
        return (
            <UITouchableOpacity
                style={styles.bankItemStyle}
                onPress={() => {
                    this.setState({selectedBank: item});
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UIImage source={http.imagUrl(item.logo)} style={{width: 24, height: 24}} />
                    <Text
                        style={{
                            fontSize: 15,
                            color: '#333',
                            marginLeft: 8,
                        }}>{`${item.bankName} ${item.cardType} (${item.bankCardNo.slice(-4)})`}</Text>
                    {item.bindState == '2' && <UIImage source={'ic_jgzh'} style={{width: 64, height: 23, marginLeft: 10}} />}
                </View>
                {TextUtils.equals(item.bankCardNo, this.state.selectedBank?.bankCardNo) && <UIImage source={'ic_selected'} style={{width: 21, height: 21, marginRight: 10}} />}
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 绘制新增银行卡
     * 时间: 2025/4/3 星期四 16:18
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderAddBankItem() {
        return (
            <UITouchableOpacity
                style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', minHeight: 50}}
                onPress={() => {
                    if (this.passportAccount?.accountType == '1') {
                        //对私
                        RouterUtils.skipRouter(RouterUrl.WisdomAddPersonBankRouter);
                    } else {
                        //对公
                        RouterUtils.skipRouter(RouterUrl.WisdomAddPublicBankRouter);
                    }
                }}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <UIImage source={'ic_bank'} style={{width: 24, height: 24, marginRight: 8}} />
                    <Text style={{fontSize: 15, color: '#333'}}>添加新的银行卡</Text>
                </View>
                <UIImage source={'ic_arrow_right_gray'} style={{width: 6, height: 10, marginRight: 15}} />
            </UITouchableOpacity>
        );
    }

    /**
     * 注释: 绘制提现方式
     * 时间: 2025/4/3 星期四 15:53
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderWithdrawalBankView() {
        let childrens: JSX.Element[] = [];
        if (ArrayUtils.isNoEmpty(this.state.bankList)) {
            this.state.bankList?.forEach((item) => {
                childrens.push(this.renderBankItem(item));
            });
        }
        return (
            <View
                style={{
                    backgroundColor: '#fff',
                    borderRadius: 4,
                    flexDirection: 'column',
                    paddingHorizontal: 11,
                    paddingTop: 15,
                    marginHorizontal: 10,
                }}>
                <Text style={{fontSize: 16, color: '#333'}}>提现方式</Text>
                <Text>（此处仅展示已绑定银行卡）</Text>
                {childrens}
                {this.renderAddBankItem()}
            </View>
        );
    }

    /**
     * 注释: 绘制提现输入框
     * 时间: 2025/4/3 星期四 14:55
     * <AUTHOR>
     */
    renderWithdrawalInputView() {
        return (
            <View style={styles.tipStyle}>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text style={{fontSize: 16, color: '#333'}}>提现金额</Text>
                    {this.state.showError && <Text style={{fontSize: 12, color: '#F00', marginLeft: 7}}>转出金额超限</Text>}
                </View>
                <View style={styles.inputStyle}>
                    <Text style={{fontSize: 24, color: '#333'}}>¥</Text>
                    <TextInput
                        style={{fontSize: 18, color: '#333', marginLeft: 10}}
                        placeholder={'请输入提现金额'}
                        placeholderTextColor={'#999'}
                        keyboardType={'numeric'}
                        ref={this.inputKey}
                        onChangeText={(e) => {
                            this.setState({
                                showError: parseInt(e) > parseInt(this.passportAccount?.depositMoney ?? '0'),
                                money: e,
                            });
                        }}
                    />
                </View>
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text
                        style={{
                            fontSize: 15,
                            color: '#333',
                        }}>{`可提现余额 ${this.passportAccount?.depositMoneyStr}`}</Text>
                    <UITouchableOpacity
                        onPress={() => {
                            this.inputKey.current?.setNativeProps({text: this.passportAccount?.depositMoney ?? ''});
                        }}>
                        <Text style={{fontSize: 15, color: '#3F7BFF', marginLeft: 14}}>全部提现</Text>
                    </UITouchableOpacity>
                </View>
            </View>
        );
    }

    /**
     * 注释: 显示绑定银行卡提示框
     * 时间: 2025/4/7 星期一 9:20
     * <AUTHOR>
     */
    showBindBankTipDialog() {
        this.showDialogFromParams({
            title: '提示',
            views: (
                <Text style={{fontSize: 15, color: '#333', marginHorizontal: 15, marginBottom: 20}}>
                    为支持提现收款，系统需将您当前选定的收款银行卡申请绑定至<Text style={{color: '#FC8312'}}>招行监管账户</Text>，请您确认是否同意此绑定操作？
                </Text>
            ),
            leftTxt: '放弃提现',
            rightTxt: '确认，继续提现',
            rightOnClick: () => {
                this.bindBankPassport();
            },
        });
    }
}

const styles = StyleSheet.create({
    tipStyle: {
        marginVertical: 8,
        marginHorizontal: 10,
        backgroundColor: '#fff',
        paddingHorizontal: 10,
        paddingVertical: 15,
        borderRadius: 4,
    },
    inputStyle: {
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 0.5,
        borderColor: '#eee',
        margin: 10,
    },
    bankItemStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottomWidth: 0.5,
        borderColor: '#eee',
        minHeight: 50,
    },
});
