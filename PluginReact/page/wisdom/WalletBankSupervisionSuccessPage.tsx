import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {Method} from '../util/NativeModulesTools';

interface State extends BaseState {}

/**
 *  desc: 监管账户绑定成功
 *  user: 宋双朋
 *  time: 2025/4/11 11:45
 */
export default class WalletBankSupervisionSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    constructor(props) {
        super(props);
        this.state = {};
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#fff'}}>
                <UITitleView title={'绑定成功'} />
                <View style={{flex: 1, alignItems: 'center', marginHorizontal: 25}}>
                    <UIImage source={'wallet_bank_icon_1'} style={{width: 32, height: 32}} />
                    <Text
                        style={{
                            fontSize: 16,
                            color: '#333333',
                            marginTop: 15,
                            fontWeight: 'bold',
                        }}>
                        {'监管账户绑定成功'}
                    </Text>
                </View>
                {/*底部按钮*/}
                <View style={styles.buttonStyle}>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openLineServer();
                        }}>
                        <UIImage source={'img_online_service'} style={{width: 103, height: 31, marginBottom: 35}} />
                    </UITouchableOpacity>
                    <UIButton
                        text={'关闭'}
                        style={{width: 340, height: 44}}
                        onPress={() => {
                            RouterUtils.skipPop();
                        }}
                    />
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    itemStyle: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderTopWidth: 0.5,
        borderColor: '#eee',
        height: 50,
    },
    buttonStyle: {
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 15,
        marginBottom: 30,
    },
    subTitleStyle: {
        fontSize: 14,
        color: '#999',
        marginTop: 2,
        marginBottom: 20,
    },
});
