export class PaymentRecord {
    orderId: string; // 运单号
    paymentBizImei: string; // 申请流水号
    createdTimeStr: string; // 支付申请时间
    paymentPostingTimeStr: string; // 入账时间
    paymentPostingStatus: string; // 账状态数字
    paymentPostingStatusStr: string; // 账状态中文
    consignorName: string; // 货主名称
    carrierName: string; // 承运方名称
    plateNumber: string; // 车牌号
    cargoName: string; // 货物名称
    earnestType: string; // 订金类型数字 1.可退 2.不可退
    earnestTypeStr: string; // 订金类型中文
    paymentStatus: string; // 支付状态数字
    paymentStatusStr: string; // 支付状态中文
    payChannelStr: string; // 支付方式中文
    actualPaymentMoneyStr: string; // 支付金额(带￥符号)
    earnestMoneyStr: string; // 订金金额(带￥符号)
    commissionMoneyStr: string; // 佣金金额(带￥符号)
    couponMoneyStr: string; // 优惠金额(带￥符号)
    refundMoneyStr: string; // 退款金额
    refundStatusStr: string; // 退款状态数字
    refundTypeStr: string; // 退款类型中文
    refundRemark: string; // 退款原因
    refundTimeStr: string; // 退款时间
    bizStatus: string; // 订金状态数字
    bizStatusStr: string; // 订金状态中文
    despatchPlaceStr: string; // 启运地详细地址
    deliverPlaceStr: string; // 目的地详细地址
}
