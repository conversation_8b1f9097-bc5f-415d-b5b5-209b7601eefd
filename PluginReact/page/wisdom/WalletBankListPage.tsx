import {StyleSheet, Text, View} from 'react-native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ReqBankList, RsqBankList} from './requests/ReqBankList';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import EventBus from '../util/EventBus';
import {Constant} from '../base/Constant';
import {ArrayUtils} from '../util/ArrayUtils';
import {Method} from '../util/NativeModulesTools';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import WalletBankItemView from './views/WalletBankItemView';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import UITitleView from '../widget/UITitleView';
import WalletBankVerifyDialog from './views/WalletBankVerifyDialog';
import UIListView from '../widget/UIListView';

interface State extends BaseState {
    rightMargin: number;
    showVerifyDialog: boolean;
    showAddBank: boolean;
    item?: RsqBankList;
    listData?: RsqBankList[];
}

/**
 *  desc: 银行卡列表
 *  user: 宋双朋
 *  time: 2025/4/9 10:04
 */
export default class WalletBankListPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    nowPage: number = 1;

    constructor(props) {
        super(props);
        this.state = {
            rightMargin: 15,
            showVerifyDialog: false,
            showAddBank: false,
        };
    }

    refreshListListener() {
        this.nowPage = 1;
        this.queryBankList();
    }

    componentDidMount() {
        super.componentDidMount();
        EventBus.getInstance().addListener(Constant.event_wallet_bank_list, this.refreshListListener);
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        EventBus.getInstance().removeListener(this.refreshListListener);
    }

    // 加载数据
    queryBankList() {
        let request = new ReqBankList();
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (ArrayUtils.isNoEmpty(res.data?.rootArray)) {
                    this.setState({showAddBank: true});
                    if (this.nowPage == 1) {
                        this.setState({listData: res.data?.rootArray});
                    } else {
                        this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                    }
                } else {
                    this.setState({showAddBank: false});
                }
            }
        });
    }

    addBank = () => {
        let login = Method.getLogin();
        switch (login?.consignorTypeFlag) {
            case '1':
            case '3':
                RouterUtils.skipRouter(RouterUrl.WisdomAddPublicBankRouter);
                break;
            case '2':
                RouterUtils.skipRouter(RouterUrl.WisdomAddPersonBankRouter);
                break;
            default:
                break;
        }
    };

    renderEmptyView() {
        return (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
                <UIImage source={'wallet_bank_icon_3'} style={{width: 180, height: 180}} />
                <Text style={{color: '#333333', fontSize: 18, fontWeight: 'bold'}}>您还没有添加银行卡</Text>
                <UIButton
                    text={'添加银行卡'}
                    borderRadius={25}
                    style={{paddingLeft: 25, paddingRight: 25, paddingTop: 5, paddingBottom: 5, marginTop: 15}}
                    onPress={() => {
                        this.addBank();
                    }}
                />
            </View>
        );
    }

    renderAddBankView() {
        return (
            this.state.showAddBank && (
                <View style={{backgroundColor: '#FFFFFF', padding: 10}}>
                    <UIButton
                        text={'添加银行卡'}
                        borderRadius={6}
                        textColor={'#5086FC'}
                        backgroundColor={'#FFFFFF'}
                        onPress={() => {
                            this.addBank();
                        }}
                    />
                </View>
            )
        );
    }

    /**
     * 注释：银行卡view
     * 时间：2025/4/9 10:31
     * @author：宋双朋
     * @param item
     * @param index
     * @returns {JSX.Element}
     */
    renderItemView = (item: RsqBankList) => {
        return (
            <WalletBankItemView
                item={item}
                callBack={() => {
                    this.refreshListListener();
                }}
                verifyCallBack={(item: RsqBankList) => {
                    //打款验证
                    this.setState({showVerifyDialog: true, item: item}, () => {});
                }}
                serviceCallBack={() => {
                    Method.openLineServer();
                }}
            />
        );
    };

    render() {
        return (
            <View style={styles.container}>
                <UITitleView title={'银行卡管理'} />
                {ArrayUtils.isEmpty(this.state.listData) && this.renderEmptyView()}
                {ArrayUtils.isNoEmpty(this.state.listData) && (
                    <GestureHandlerRootView style={{flex: 1}}>
                        <UIListView
                            renderItem={({item}) => {
                                return this.renderItemView(item);
                            }}
                            dataList={this.state.listData}
                            onRefresh={() => {
                                this.refreshListListener();
                            }}
                            onLoadMore={() => {
                                this.queryBankList();
                            }}
                        />
                    </GestureHandlerRootView>
                )}
                {this.renderAddBankView()}
                {this.state.showVerifyDialog && (
                    <WalletBankVerifyDialog
                        callBack={() => {
                            this.setState({showVerifyDialog: false});
                        }}
                        closeCallBack={() => {
                            this.setState({showVerifyDialog: false});
                        }}
                        item={this.state.item}
                    />
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    container: {flex: 1, backgroundColor: '#EFF0F3'},
});
