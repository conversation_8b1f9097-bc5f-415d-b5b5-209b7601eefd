import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {RsqBankList} from './requests/ReqBankList';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {Method} from '../util/NativeModulesTools';
import UITitleView from '../widget/UITitleView';

interface State extends BaseState {}

/**
 * 注释: 提现成功页
 * 时间: 2025/4/3 星期四 10:55
 * <AUTHOR>
 */
export default class WithdrawalSuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    //银行卡信息
    bankInfo: RsqBankList;
    //提现金额
    money: string;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.bankInfo = this.pageParams.bankInfo;
        this.money = this.pageParams.money;
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#fff'}}>
                <UITitleView title={'提现结果'} />
                {/*结果视图*/}
                <View style={{marginTop: 28, alignItems: 'center', flex: 1}}>
                    <UIImage source={'icon_success'} style={{width: 32, height: 32}} />
                    <Text style={{fontSize: 16, color: '#333', marginTop: 10}}>提现申请成功</Text>
                    <Text style={styles.subTitleStyle}>预计24小时内到账，节假日顺延</Text>
                    <View style={styles.itemStyle}>
                        <Text style={{fontSize: 17, color: '#333'}}>{this.bankInfo.cardType}</Text>
                        <View style={{flexDirection: 'row'}}>
                            <Text style={{fontSize: 17, color: '#666', marginRight: 7}}>{this.bankInfo.bankName}</Text>
                            <Text
                                style={{
                                    fontSize: 17,
                                    color: '#333',
                                }}>{`尾号${this.bankInfo.bankCardNo?.slice(-4)}`}</Text>
                        </View>
                    </View>
                    <View style={[styles.itemStyle, {borderBottomWidth: 0.5}]}>
                        <Text style={{fontSize: 17, color: '#333'}}>转出金额</Text>
                        <Text style={{fontSize: 17, color: '#333'}}>{`${this.money}元`}</Text>
                    </View>
                </View>
                {/*底部按钮*/}
                <View style={styles.buttonStyle}>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openLineServer();
                        }}>
                        <UIImage source={'img_online_service'} style={{width: 103, height: 31, marginBottom: 35}} />
                    </UITouchableOpacity>
                    <UIButton
                        text={'完成'}
                        style={{width: 340, height: 44}}
                        onPress={() => {
                            RouterUtils.skipPop();
                        }}
                    />
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    itemStyle: {
        width: '100%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderTopWidth: 0.5,
        borderColor: '#eee',
        height: 50,
    },
    buttonStyle: {
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 15,
        marginBottom: 30,
    },
    subTitleStyle: {
        fontSize: 14,
        color: '#999',
        marginTop: 2,
        marginBottom: 20,
    },
});
