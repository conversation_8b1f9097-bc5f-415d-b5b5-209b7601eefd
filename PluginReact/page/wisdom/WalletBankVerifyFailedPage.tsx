import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';

interface State extends BaseState {}

/**
 * 注释: 银行卡认证-失败
 * 时间: 2025/4/9 15:55
 * <AUTHOR>
 */
export default class WalletBankVerifyFailedPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    reason?: string;

    constructor(props) {
        super(props);
        this.state = {};
    }

    addBank = () => {
        let login = Method.getLogin();
        switch (login?.consignorTypeFlag) {
            case '1':
            case '3':
                RouterUtils.skipPop();
                RouterUtils.skipRouter(RouterUrl.WisdomAddPublicBankRouter);
                break;
            case '2':
                RouterUtils.skipPop();
                RouterUtils.skipRouter(RouterUrl.WisdomAddPersonBankRouter);
                break;
            default:
                break;
        }
    };

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#FFFFFF'}}>
                <UITitleView title={'认证失败'} />
                <View style={{flex: 1, flexDirection: 'column', marginTop: 28, alignItems: 'center'}}>
                    <UIImage source={'icon_failed'} style={{width: 32, height: 32}} />
                    <Text style={{fontSize: 16, color: '#333', marginTop: 10}}>认证失败请您重新绑卡！</Text>
                    <Text style={{fontSize: 17, color: '#666', marginHorizontal: 35, marginTop: 24}}>{this.reason}</Text>
                </View>
                <View style={styles.buttonStyle}>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openLineServer();
                        }}>
                        <UIImage source={'img_online_service'} style={{width: 103, height: 31, marginBottom: 35}} />
                    </UITouchableOpacity>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                        <UIButton
                            text={'关闭页面'}
                            borderColor={'#5086FC'}
                            backgroundColor={'#fff'}
                            fontSize={16}
                            textColor={'#5086FC'}
                            style={{flex: 1, marginRight: 17}}
                            onPress={() => {
                                RouterUtils.skipPop();
                            }}
                        />
                        <UIButton
                            text={'重新绑定'}
                            style={{flex: 1}}
                            onPress={() => {
                                this.addBank();
                            }}
                        />
                    </View>
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    buttonStyle: {
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 15,
        marginBottom: 30,
    },
});
