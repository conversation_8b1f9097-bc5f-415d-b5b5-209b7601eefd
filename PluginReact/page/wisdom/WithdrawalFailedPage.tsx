import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {RspGetPassportAccount} from './requests/ReqGetPassportAccount';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {Method} from '../util/NativeModulesTools';

interface State extends BaseState {}

/**
 * 注释:提现失败页
 * 时间: 2025/4/3 星期四 10:56
 * <AUTHOR>
 */
export default class WithdrawalFailedPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    reason: string;
    passportAccount?: RspGetPassportAccount;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.reason = this.pageParams.reason;
        this.passportAccount = this.pageParams.passportAccount;
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#fff'}}>
                <UITitleView title={'提现结果'} />
                <View style={{flex: 1, flexDirection: 'column', marginTop: 28, alignItems: 'center'}}>
                    <UIImage source={'icon_failed'} style={{width: 32, height: 32}} />
                    <Text style={{fontSize: 16, color: '#333', marginTop: 10}}>提现申请失败</Text>
                    <Text style={{fontSize: 17, color: '#666', marginHorizontal: 35, marginTop: 24}}>{this.reason}</Text>
                </View>
                <View style={styles.buttonStyle}>
                    <UITouchableOpacity
                        onPress={() => {
                            Method.openLineServer();
                        }}>
                        <UIImage source={'img_online_service'} style={{width: 103, height: 31, marginBottom: 35}} />
                    </UITouchableOpacity>
                    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
                        <UIButton
                            text={'关闭页面'}
                            borderColor={'#5086FC'}
                            backgroundColor={'#fff'}
                            fontSize={16}
                            textColor={'#5086FC'}
                            style={{flex: 1, marginRight: 17}}
                            onPress={() => {
                                RouterUtils.skipPop();
                            }}
                        />
                        <UIButton
                            text={'重新提现'}
                            style={{flex: 1}}
                            onPress={() => {
                                RouterUtils.skipPop();
                                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                                    page: 'WithdrawalPage',
                                    data: {
                                        passportAccount: this.passportAccount,
                                    },
                                });
                            }}
                        />
                    </View>
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    buttonStyle: {
        flexDirection: 'column',
        alignItems: 'center',
        marginHorizontal: 15,
        marginBottom: 30,
    },
});
