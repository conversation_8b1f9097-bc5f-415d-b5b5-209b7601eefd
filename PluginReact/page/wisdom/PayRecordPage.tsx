import {Dimensions, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import {SceneMap, TabView} from 'react-native-tab-view';
import PayRecordPageView from './views/PayRecordPageView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import {gScreen_width} from '../util/scaled-style';

interface State extends BaseState {
    tabIndex: number;
}

/**
 * 注释: 支付记录
 * 时间: 2025/4/11 星期五 14:06
 * <AUTHOR>
 */
export default class PayRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    // 运单号
    orderId: string;
    //1-支付中/已关闭 2-已支付/已退款 3-订金收入
    listType: string;
    //0-交易通 1-摘单
    source?: number;
    //回调
    callBack?: Function;

    constructor(props) {
        super(props);
        this.state = {
            tabIndex: 0,
        };
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.listType = this.pageParams.listType;
        this.source = this.pageParams.source ?? 0;
        this.callBack = this.getCallBack();
    }

    /**
     * 注释: 绘制TabBar
     * 时间: 2025/4/1 星期二 9:52
     * <AUTHOR>
     * @param index
     * @returns {React.JSX.Element}
     */
    renderTabBar(index: number) {
        return (
            <View style={styles.tabBarStyle}>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 0});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>支付中/已关闭</Text>
                    {index == 0 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
                <UITouchableOpacity
                    style={styles.tabItemStyle}
                    onPress={() => {
                        this.setState({tabIndex: 1});
                    }}>
                    <Text style={{fontSize: 15, color: '#333'}}>已支付/已退款</Text>
                    {index == 1 && <View style={{width: 73, height: 3, backgroundColor: '#5086FC', position: 'absolute', bottom: 0}} />}
                </UITouchableOpacity>
            </View>
        );
    }

    /**
     * 注释: 绘制主视图
     * 时间: 2025/4/1 星期二 9:52
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderMainView() {
        //界面title
        let routes: {key: string; title: string}[];
        //tabs页面
        let renderScene: any;
        routes = [
            {key: 'first', title: '支付中/已关闭'},
            {key: 'second', title: '已支付/已退款'},
        ];
        renderScene = SceneMap({
            first: () => {
                return <PayRecordPageView listType={'1'} orderId={this.orderId} source={this.source} callBack={this.callBack} />;
            },
            second: () => {
                return <PayRecordPageView listType={'2'} orderId={this.orderId} source={this.source} />;
            },
        });
        return (
            <TabView
                navigationState={{index: this.state.tabIndex, routes}}
                renderScene={renderScene}
                onIndexChange={(tabIndex) => {
                    this.setState({tabIndex: tabIndex});
                }}
                renderTabBar={() => this.renderTabBar(this.state.tabIndex)}
                initialLayout={{width: Dimensions.get('window').width}}
            />
        );
    }

    render() {
        return (
            <View style={{flex: 1}}>
                <UITitleView title={'支付记录'} />
                {this.renderMainView()}
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    tabBarStyle: {
        width: gScreen_width,
        height: (gScreen_width / 375) * 42,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tabItemStyle: {
        flex: 1,
        height: (gScreen_width / 375) * 42,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
