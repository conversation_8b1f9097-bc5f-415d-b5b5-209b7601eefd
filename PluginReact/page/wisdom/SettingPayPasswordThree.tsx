import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ReqSetPayPwd} from './requests/ReqSetPayPwd';
import {RouterUtils} from '../util/RouterUtils';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import UITitleView from '../widget/UITitleView';
import SmsCodeInput from './views/SmsCodeInput';
import TextUtils from '../util/TextUtils';

interface State extends BaseState {
    showError?: boolean;
}

/**
 * 注释: 设置支付密码第三页
 * 时间: 2025/4/3 星期四 8:57
 * <AUTHOR>
 */
export default class SettingPayPasswordThree extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    code: string;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.code = this.pageParams.code;
    }

    /**
     * 注释: 设置支付密码
     * 时间: 2025/4/9 星期三 14:32
     * <AUTHOR>
     */
    setPayPassword(userPwd: string) {
        let request = new ReqSetPayPwd();
        request.userPwd = userPwd;
        request.token = this.pageParams.token;
        request.request().then((res) => {
            if (res.isSuccess()) {
                RouterUtils.skipPop();
            } else {
                Method.showToast(res.getMsg());
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'设置支付密码'} />
                <View style={{flexDirection: 'column', marginTop: 120, alignItems: 'center'}}>
                    <Text style={{fontSize: 16, color: '#999'}}>请再次输入</Text>
                    <SmsCodeInput
                        handleSubmit={(code) => {
                            this.setState({showError: this.code != code});
                            if (this.code == code) {
                                this.setPayPassword(TextUtils.md5(code));
                            }
                        }}
                        maxNum={6}
                    />
                    {this.state.showError && <Text style={{fontSize: 14, color: '#FF5959', marginTop: 11}}>两次输入密码不一致，请重新输入</Text>}
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
