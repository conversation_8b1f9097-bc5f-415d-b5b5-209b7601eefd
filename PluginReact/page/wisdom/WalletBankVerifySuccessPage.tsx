import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {RsqBankList} from './requests/ReqBankList';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import UITitleView from '../widget/UITitleView';
import UIImage from '../widget/UIImage';
import UIButton from '../widget/UIButton';
import {RouterUtils} from '../util/RouterUtils';

interface State extends BaseState {}

/**
 * 注释: 银行卡认证-成功
 * 时间: 2025/4/9 15:55
 * <AUTHOR>
 */
export default class WalletBankVerifySuccessPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    item?: RsqBankList;

    constructor(props) {
        super(props);
        this.item = this.getParams().item;
        this.state = {};
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#FFFFFF'}}>
                <UITitleView title={'认证成功'} />
                <View style={{alignItems: 'center', flex: 1, marginLeft: 25, marginRight: 25}}>
                    <UIImage source={'wallet_bank_icon_1'} style={{width: 32, height: 32}} />
                    <Text style={{fontSize: 16, color: '#333333', marginTop: 15}}>{'银行卡已通过认证！'}</Text>
                    <Text
                        style={{
                            fontSize: 16,
                            color: '#333333',
                            marginTop: 15,
                        }}>
                        {'为了保障资金安全并'}
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#F39324',
                                marginTop: 15,
                            }}>
                            {'支持后续提现/充值'}
                        </Text>
                        {'操作，请将此卡与监管账户关联'}
                    </Text>
                    <View style={{width: '100%', flexDirection: 'row', alignItems: 'center', marginTop: 15}}>
                        <UIImage source={'wallet_bank_icon_2'} style={{width: 14, height: 14}} />
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#333333',
                                fontWeight: 'bold',
                                marginLeft: 5,
                            }}>
                            {'充值说明'}
                        </Text>
                    </View>
                    <Text
                        style={{
                            fontSize: 15,
                            color: '#666666',
                            marginTop: 15,
                        }}>
                        {'请使用企业网银或去银行柜台，使用您绑定的银行卡，通 过对公打款至'}
                        <Text
                            style={{
                                fontSize: 15,
                                color: '#F39324',
                                marginTop: 15,
                            }}>
                            {'“招商银行第三方资金平台账户”'}
                        </Text>
                        {'进行交易通钱包充值，充值账号信息如下：'}
                    </Text>
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#666666',
                            marginTop: 15,
                            marginLeft: 15,
                            width: '100%',
                        }}>
                        {'账户名称:'}
                        <Text
                            style={{
                                fontSize: 13,
                                color: '#333333',
                                marginTop: 15,
                                fontWeight: 'bold',
                            }}>
                            {'      招商银行第三方平台资金'}
                        </Text>
                    </Text>
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#666666',
                            marginTop: 15,
                            marginLeft: 15,
                            width: '100%',
                        }}>
                        {'充值账户:'}
                        <Text
                            style={{
                                fontSize: 13,
                                color: '#333333',
                                marginTop: 15,
                                fontWeight: 'bold',
                            }}>
                            {this.item?.bankCardNo}
                        </Text>
                    </Text>
                    <Text
                        style={{
                            fontSize: 13,
                            color: '#666666',
                            marginTop: 15,
                            marginLeft: 15,
                            width: '100%',
                        }}>
                        {'开户网点:'}
                        <Text
                            style={{
                                fontSize: 13,
                                color: '#333333',
                                marginTop: 15,
                                fontWeight: 'bold',
                            }}>
                            {this.item?.bankName}
                        </Text>
                    </Text>
                    <View style={{flex: 1}} />
                    <UIButton
                        text={'关闭'}
                        style={{
                            marginLeft: 20,
                            marginRight: 20,
                            marginTop: 25,
                            width: '100%',
                            marginBottom: 25,
                        }}
                        onPress={() => {
                            RouterUtils.skipPop();
                        }}
                    />
                </View>
            </View>
        );
    }
}

const styles = StyleSheet.create({
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    leftTxt: {
        fontSize: 15,
        color: '#1F2937',
    },
});
