import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ArrayUtils} from '../util/ArrayUtils';
import UITitleView from '../widget/UITitleView';
import UIListView from '../widget/UIListView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import TextUtils from '../util/TextUtils';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import {PaymentRecord} from './models/PaymentRecord';
import {ReqQueryEarnestPayment} from './requests/ReqQueryEarnestPayment';
import UISearchView from '../widget/UISearchView';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import UICommonFilterPopup from '../widget/filter/UICommonFilterPopup';
import {Child, FilterPopupBean} from '../widget/filter/FilterPopupBean';
import dayjs from 'dayjs';
import {subtractDaysFromTime} from '../util/DateUtil';
import OrderPickReactExtension from '../pick/utils/OrderPickReactExtension';

interface State extends BaseState {
    startDate?: string;
    endDate?: string;
    showFilterDialog?: boolean;
    filterData: FilterPopupBean[];
    listData?: PaymentRecord[];
}

/**
 * 注释: 订金记录
 * 时间: 2025/3/31 星期一 13:59
 * <AUTHOR>
 */
export default class DepositIncomeRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    nowPage: number = 1;
    orderId: string;
    initFilterData: FilterPopupBean[] = [];

    constructor(props) {
        super(props);
        //货主名称
        let inputHzName = new FilterPopupBean();
        inputHzName.title = '货主名称';
        inputHzName.placeholder = '请输入货主名称';
        inputHzName.filterType = 'input';
        //支付方式过滤
        let payTypeFilter = new FilterPopupBean();
        payTypeFilter.title = '支付方式';
        payTypeFilter.isSingle = true;
        payTypeFilter.children = [new Child(false, '微信', 1), new Child(false, '支付宝', 2), new Child(false, '银行卡', 3), new Child(false, '交易通', 4)];
        //订金状态
        let statusFilter = new FilterPopupBean();
        statusFilter.title = '订金状态';
        statusFilter.isSingle = true;
        statusFilter.children = [new Child(false, '待退回', 1), new Child(false, '已退回', 2), new Child(false, '已支付货主', 3)];
        //退款原因：运输完成退回订金、抢单失败、运单取消、违约终止
        let reasonFilter = new FilterPopupBean();
        reasonFilter.title = '退款原因';
        reasonFilter.isSingle = true;
        reasonFilter.children = [new Child(false, '运输完成退回订金', 5), new Child(false, '抢单失败', 1), new Child(false, '运单取消', 2), new Child(false, '违约终止', 4)];
        this.state = {
            filterData: [inputHzName, payTypeFilter, statusFilter, reasonFilter],
            showFilterDialog: false,
            startDate: dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD'),
            endDate: dayjs(new Date()).format('YYYY-MM-DD'),
        };
        this.pageParams = this.getParams();
        this.orderId = this.pageParams.orderId;
        this.initFilterData = ArrayUtils.deepCopy(this.state.filterData, FilterPopupBean);
    }

    componentDidMount() {
        this.queryEarnestPayment();
    }

    /**
     * 注释: 查询订金收入记录
     * 时间: 2025/4/10 星期四 11:00
     * <AUTHOR>
     */
    queryEarnestPayment() {
        let request = new ReqQueryEarnestPayment();
        request.nowPage = this.nowPage;
        request.pageSize = 10;
        request.orderId = this.orderId;
        request.startTime = this.state.startDate;
        request.endTime = this.state.endDate;
        request.listType = 3;
        request.shipperName = this.state.filterData[0].inputValue;
        //支付方式
        let payTypeItem = this.state.filterData[1].children.filter((item) => {
            return item.isCheck;
        });
        request.paymentType = payTypeItem.length > 0 ? payTypeItem[0].value : undefined;
        //订金状态
        let statusItem = this.state.filterData[2].children.filter((item) => {
            return item.isCheck;
        });
        request.earnestRecordBizStatus = statusItem.length > 0 ? statusItem[0].value : undefined;
        //退回原因
        let refundItem = this.state.filterData[3].children.filter((item) => {
            return item.isCheck;
        });
        request.refundReason = refundItem.length > 0 ? refundItem[0].value : undefined;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.nowPage == 1) {
                    this.setState({listData: res.data?.rootArray});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
                this.nowPage++;
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'订金记录'} />
                {this.renderHeadView()}
                <UIListView
                    contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                    renderItem={({item}) => this.renderItemView(item)}
                    dataList={this.state.listData}
                    onRefresh={() => {
                        this.nowPage = 1;
                        this.queryEarnestPayment();
                    }}
                    onLoadMore={() => {
                        this.queryEarnestPayment();
                    }}
                />
                {/*过滤弹窗*/}
                {this.state.showFilterDialog && this.renderFilterDialog()}
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制过滤弹窗
     * 时间: 2025/4/14 星期一 13:58
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    renderFilterDialog() {
        return (
            <UICommonFilterPopup
                filterData={this.state.filterData}
                onReset={() => {
                    this.setState(
                        {
                            filterData: ArrayUtils.deepCopy(this.initFilterData, FilterPopupBean),
                            showFilterDialog: false,
                        },
                        () => {
                            this.queryEarnestPayment();
                        },
                    );
                }}
                onClose={(data: FilterPopupBean[]) => {
                    this.setState({filterData: data, showFilterDialog: false});
                }}
                onSubmit={(data: FilterPopupBean[]) => {
                    this.setState({filterData: data, showFilterDialog: false}, () => {
                        this.queryEarnestPayment();
                    });
                }}
            />
        );
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UISearchView
                    placeholder={'请输入运单号搜索'}
                    searchButton={true}
                    value={this.orderId}
                    searchView={
                        <UITouchableOpacity
                            onPress={() => {
                                this.nowPage = 1;
                                this.queryEarnestPayment();
                            }}>
                            <Text style={{fontSize: 15, color: '#5086FC'}}>搜索</Text>
                        </UITouchableOpacity>
                    }
                    backGroundStyle={{
                        backgroundColor: '#EFF0F3',
                        borderRadius: 4,
                        borderWidth: 0,
                        marginHorizontal: 0,
                        paddingHorizontal: 10,
                    }}
                    onChangeText={(e) => {
                        this.orderId = e;
                    }}
                    onSubmitEditing={(text) => {
                        this.nowPage = 1;
                        this.orderId = text;
                        this.queryEarnestPayment();
                    }}
                />
                <View style={{marginTop: 13, flexDirection: 'row'}}>
                    {/*时间选择*/}
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            Method.showStartEndTimePicker().then((res) => {
                                let startTime = res['startTime'];
                                let endTime = res['endTime'];
                                this.setState(
                                    {
                                        startDate: startTime.split(' ')[0],
                                        endDate: endTime.split(' ')[0],
                                    },
                                    () => {
                                        this.queryEarnestPayment();
                                    },
                                );
                            });
                        }}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#1F66FF',
                            }}>
                            {this.state.startDate ? `${this.state.startDate}至 ${this.state.endDate}` : '请选择时间'}
                        </Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                    {/*更多*/}
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center', marginLeft: 50}}
                        onPress={() => {
                            this.setState({showFilterDialog: true});
                        }}>
                        <Text style={{fontSize: 14, color: '#1F66FF'}}>更多</Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                </View>
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 9:47
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderItemView(item: PaymentRecord) {
        return (
            <View
                style={{
                    flexDirection: 'column',
                    backgroundColor: '#fff',
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingBottom: 10,
                }}>
                {this.renderItem('支付申请时间：', item.createdTimeStr)}
                {this.renderItem('申请流水号：', item.paymentBizImei)}
                {this.renderItem('运单号：', item.orderId, item.orderId)}
                {this.renderItem('车牌号：', item.plateNumber)}
                {this.renderItem('货主：', item.consignorName)}
                {this.renderItem('启运地：', item.despatchPlaceStr)}
                {this.renderItem('目的地：', item.deliverPlaceStr)}
                {this.renderItem('货物名称：', item.cargoName)}
                {this.renderItem(`订金：`, item.earnestMoneyStr, '', `${item.earnestType ?? ''}`)}
                {this.renderItem(`支付方式：`, item.payChannelStr)}
                {this.renderItem(`订金状态：`, item.bizStatusStr)}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:02
     * <AUTHOR>
     * @param label
     * @param value
     * @returns {Element}
     * @param orderId
     */
    renderItem(label: string, value: string, orderId?: string, earnestTypeStr?: string) {
        return (
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                <Text style={{fontSize: 16, color: '#666', width: 120}}>{label}</Text>
                <Text style={{fontSize: 16, color: '#333', flex: 1}}>{value}</Text>
                {TextUtils.isNoEmpty(orderId) && (
                    <UITouchableOpacity
                        onPress={() => {
                            //跳转运单详情
                            OrderPickReactExtension.openWaybillDetails(orderId ?? '', '1');
                        }}>
                        <Text style={{fontSize: 14, color: '#1F66FF', marginLeft: 10}}>详情</Text>
                    </UITouchableOpacity>
                )}
                {TextUtils.isNoEmpty(earnestTypeStr) && <UIImage source={TextUtils.equals(earnestTypeStr, '1') ? 'icon_refundable' : 'icon_non_refundable'} style={{width: 44, height: 22}} />}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    searchStyle: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: 30,
        backgroundColor: '#EFF0F3',
        paddingHorizontal: 10,
        borderRadius: 4,
    },
});
