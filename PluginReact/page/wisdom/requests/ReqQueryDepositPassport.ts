import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import PageList from '../../http/PageList';
import {WithdrawalRecord} from '../models/WithdrawalRecord';

/**
 * 注释: 提现记录列表
 * 时间: 2025/4/8 星期二 10:15
 * <AUTHOR>
 */
export class ReqQueryDepositPassport extends BaseRequest {
    nowPage: number;
    pageSize: number;
    startDate?: string;
    endDate?: string;
    state: string;

    async request(): Promise<BaseResponse<PageList<WithdrawalRecord>>> {
        this.params = {
            nowPage: this.nowPage,
            pageSize: this.pageSize,
            startDate: this.startDate,
            endDate: this.endDate,
            state: this.state,
        };
        return super.postList('pps-app/passport/deposit/queryDepositPassportPage', WithdrawalRecord);
    }
}
