import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 提现接口
 * 时间: 2025/4/9 星期三 20:01
 * <AUTHOR>
 */
export class ReqCashConfirmDeposit extends BaseRequest {
    cardId: string;
    // 1 已绑定 0 待绑定
    bindFlag: number;
    //提现金额
    depositMoney: string;
    //提现密码
    depositPwd: string;
    //平台ID
    subsidiaryId: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            cardId: this.cardId,
            bindFlag: `${this.bindFlag}`,
            depositMoney: this.depositMoney,
            depositPwd: this.depositPwd,
            subsidiaryId: this.subsidiaryId,
        };
        return super.postCashEncryptStringWithBase64('pps-app/passport/deposit/applyDeposit', ResultData);
    }
}
