import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 校验设置支付密码
 * 时间: 2025/4/8 星期二 11:14
 * <AUTHOR>
 */
export class ReqCheckSetPassword extends BaseRequest {
    async request(): Promise<BaseResponse<RsqCheckSetPassword>> {
        this.params = {};
        return super.post('pps-app/setting/pwd/queryIsSettingBookPwd', RsqCheckSetPassword);
    }
}

export class RsqCheckSetPassword extends ResultData {
    //  1-设置过 2-未设置
    isInfo: number;
}
