import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 *  desc: 解绑招商交易通
 *  user: 宋双朋
 *  time: 2025/4/10 19:10
 */
export class ReqRemoveBankPassport extends BaseRequest {
    cardId: string;
    subsidiaryId: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            cardId: this.cardId,
            subsidiaryId: this.subsidiaryId,
        };
        return super.post('pps-app/account/bankCard/removeBankPassport', ResultData);
    }
}
