import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 关闭订金支付
 * 时间: 2025/4/15 星期二 10:44
 * <AUTHOR>
 */
export class ReqEarnestPaymentClose extends BaseRequest {
    orderId: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            orderId: this.orderId,
        };
        return super.post('ams-app/process/earnestPaymentClose', ResultData);
    }
}
