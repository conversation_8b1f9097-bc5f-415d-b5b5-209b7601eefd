import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 *  desc: 删除银行卡
 *  user: 宋双朋
 *  time: 2025/4/11 17:14
 */
export class ReqChangeBankCardState extends BaseRequest {
    //银行卡id
    cardId: string;
    //1-设置为默认银行卡 2-取消默认密码 3-删除银行卡
    state: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            cardId: this.cardId,
            state: this.state,
        };
        return super.post('pps-app/account/bankCard/changeBankCardState', ResultData);
    }
}
