import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 设置支付密码
 * 时间: 2025/4/8 星期二 11:30
 * <AUTHOR>
 */
export class ReqSetPayPwd extends BaseRequest {
    userPwd: string;
    token: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            userPwd: this.userPwd,
            token: this.token,
        };
        return super.post('pps-app/account/setPasswordWithToken', ResultData);
    }
}
