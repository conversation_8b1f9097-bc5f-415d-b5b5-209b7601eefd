import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import PageList from '../../http/PageList';
import {TradeRecord} from '../models/TradeRecord';

/**
 * 注释: 收支明细列表
 * 时间: 2025/4/8 星期二 10:33
 * <AUTHOR>
 */
export class ReqQueryRevenueRecord extends BaseRequest {
    bookNo?: string;
    orderId?: string;
    nowPage: number;
    pageSize: number;
    accountDateS?: string;
    accountDateE?: string;

    async request(): Promise<BaseResponse<PageList<TradeRecord>>> {
        this.params = {
            nowPage: this.nowPage,
            pageSize: this.pageSize,
            accountDateS: this.accountDateS,
            accountDateE: this.accountDateE,
            bookNo: this.bookNo,
            orderId: this.orderId,
        };
        return super.postList('pps-app/passport/account/ordList', TradeRecord);
    }
}
