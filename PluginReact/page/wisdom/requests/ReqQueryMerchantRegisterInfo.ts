import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 查询商户进件信息
 * 时间: 2025/4/8 星期二 9:49
 * <AUTHOR>
 */
export class ReqQueryMerchantRegisterInfo extends BaseRequest {
    async request(): Promise<BaseResponse<RspQueryMerchantRegisterInfo>> {
        this.params = {
            subsidiaryId: '',
        };
        return super.post('pps-app/passport/merchantRegister/queryMerchantRegisterInfo', RspQueryMerchantRegisterInfo);
    }
}

export class RspQueryMerchantRegisterInfo extends ResultData {
    public merchantRegisterInfo?: MerchantRegisterInfo;
}

export class MerchantRegisterInfo {
    //是否已申请开户：1-是 2-否
    public applyRegisterFlag: string;
    //开户状态：1-受理中 2-成功 3-失败
    public status: string;
}
