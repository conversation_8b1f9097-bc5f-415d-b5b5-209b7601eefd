import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 查询交易通账号信息
 * 时间: 2025/4/7 星期一 15:58
 * <AUTHOR>
 */
export class ReqGetPassportAccount extends BaseRequest {
    async request(): Promise<BaseResponse<RspGetPassportAccount>> {
        this.params = {
            subsidiaryId: '',
        };
        return super.post('pps-app/passport/account/getPassportAccount', RspGetPassportAccount);
    }
}

export class RspGetPassportAccount extends ResultData {
    bookInfo: BookInfo;
}

export class BookInfo {
    //银行卡号
    public bookNo: string;
    //银行名称
    public bookName: string;
    //1.个人 2.企业
    public accountType: string;
    //可用余额
    public depositMoney: string;
    public depositMoneyStr: string;
    //冻结金额
    public freezeMoney: string;
    public freezeMoneyStr: string;
    public subsidiaryId: string;
}
