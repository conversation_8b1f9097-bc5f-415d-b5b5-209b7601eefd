import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';

/**
 * 注释:   绑定招商交易通
 * 时间: 2025/4/9 星期三 19:02
 * <AUTHOR>
 */
export class ReqBindBankPassport extends BaseRequest {
    cardId: string;
    subsidiaryId: string;
    accMobile?: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            cardId: this.cardId,
            accMobile: this.accMobile,
            subsidiaryId: this.subsidiaryId,
        };
        return super.post('pps-app/account/bankCard/bindBankPassport', ResultData);
    }
}
