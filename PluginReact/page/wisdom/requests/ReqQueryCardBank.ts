import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 输入卡号查询归属银行
 * 时间: 2025/4/8 星期二 16:20
 * <AUTHOR>
 */
export class ReqQueryCardBank extends BaseRequest {
    bankCardNumber?: string;

    async request(): Promise<BaseResponse<RsqQueryCardBank>> {
        this.params = {
            bankCardNumber: this.bankCardNumber,
        };
        return super.post('pps-app/account/bankCard/queryCardBank', RsqQueryCardBank);
    }
}

export class RsqQueryCardBank extends ResultData {
    bankSimpleName: string; //归属银行
    bankid: string; //归属银行
}
