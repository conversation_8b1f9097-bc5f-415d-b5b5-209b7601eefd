import {Platform} from 'react-native';
import {BaseResponse} from '../../http/BaseResponse';
import {BaseRequest} from '../../http/BaseRequest';
import {ResultData} from '../../http/ResultData';

/**
 * http://wiki.zczy56.com/pages/viewpage.action?pageId=8290911
 * 发送验证码接口
 * 对接人：钱程
 */
export class ReqSendVerifyCode extends BaseRequest {
    public mobile: string; //手机号
    // moduleType=0：通用
    // moduleType=1：账户注册
    // moduleType=2：修改服务密码
    // moduleType=3：忘记密码修改密码
    // moduleType=4：修改手机号
    // moduleType=5：重置支付密码
    // moduleType=6：登录密码修改
    // moduleType=7：HUE绑定设备
    // moduleType=8：摘单
    // moduleType=9：验证码登录
    // moduleType=10：设置支付密码
    // moduleType=11：找回密码
    // moduleType=12：设备验证
    // moduleType=15：活动报名验证码
    // * moduleType=16：扫码绑定
    // * moduleType=17：扫码解绑
    // * moduleType=18：切换账号绑定
    public moduleType: string; // 模块类型

    // plateFormType=0：非平台会员
    // plateFormType=1：汽运、船会员
    // plateFormType=2：加盟运力会员
    // plateFormType=3：所有平台会员
    // plateFormType=-1：所有手机号码（包含平台和非平台会员）
    public plateFormType: string; // 模块类型
    // userClientType=1：pc前台
    // userClientType=2：ios
    // userClientType=3：android
    // userClientType=4：后台
    // userClientType=5：微信
    // userClientType=6：微信小程序
    // userClientType=7：加盟运力ios
    // userClientType=8：加盟运力android
    // userClientType=9：终审加盟
    public imageIdentifyCode?: string; // 图片验证码
    public type?: string; // 验证码类型(1：短信验证码 2：语音验证码)
    public async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            mobile: this.mobile,
            type: this.type,
            moduleType: this.moduleType,
            plateFormType: this.plateFormType,
            userClientType: Platform.OS === 'ios' ? '2' : '3',
            imageIdentifyCode: this.imageIdentifyCode,
        };
        return super.post('/mms-app/mms/verifyCode/sendVerifyCode', ResultData);
    }
}
