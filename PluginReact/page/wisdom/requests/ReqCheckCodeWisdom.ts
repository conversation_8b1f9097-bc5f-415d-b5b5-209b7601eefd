import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 校验密码
 * 时间: 2025/4/8 星期二 11:17
 * <AUTHOR>
 */
export class ReqCheckCodeWisdom extends BaseRequest {
    moduleType: string;
    mobile: string;
    verifyCode: string;
    verifyCodeType: string;

    async request(): Promise<BaseResponse<RspMessageToken>> {
        this.params = {
            moduleType: this.moduleType,
            mobile: this.mobile,
            verifyCode: this.verifyCode,
            verifyCodeType: this.verifyCodeType,
        };
        return super.post('pps-app/verifycode/checkVerifyCode', RspMessageToken);
    }
}

export class RspMessageToken extends ResultData {
    token: string;
}
