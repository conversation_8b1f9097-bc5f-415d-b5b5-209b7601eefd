import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 *  desc: 查看银行卡号
 *  user: 宋双朋
 *  time: 2025/4/14 16:51
 */
export class ReqQueryBankCardNumber extends BaseRequest {
    cardId: string;

    async request(): Promise<BaseResponse<RspQueryBankCardNumber>> {
        this.params = {
            cardId: this.cardId,
        };
        return super.post('pps-app/account/bankCard/queryBankCardNumber', RspQueryBankCardNumber);
    }
}

export class RspQueryBankCardNumber extends ResultData {
    bankCardNumber: string; // 银行卡号
}
