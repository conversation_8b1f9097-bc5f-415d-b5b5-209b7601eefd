import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import PageList from '../../http/PageList';
import {PaymentRecord} from '../models/PaymentRecord';

/**
 * 注释: 查询订金收入记录
 * 时间: 2025/4/8 星期二 14:03
 * <AUTHOR>
 */
export class ReqQueryEarnestPayment extends BaseRequest {
    nowPage: number;
    pageSize: number;
    orderId?: string;
    startTime?: string;
    endTime?: string;
    //入账状态：1未入账 2已入账
    paymentPostingStatus?: number;
    //货主名称
    shipperName?: string;
    //支付方式 0-初始化 1-微信 2-支付宝 3-云闪付 4-交易通余额
    paymentType?: number;
    //支付状态 0.无效 1.支付中 2.支付成功 3-支付失败 4.支付关闭
    paymentState?: number;
    //退款原因：0-无 1-抢单失败、2-运单取消、3-运单失效、4-违约终止 5-确认收货
    refundReason?: number;
    //司机APP端 承运方订金记录-订金状态：1-待退回 2-已退回 3-已支付货主
    earnestRecordBizStatus?: number;
    listType: number;
    //是否已退款 0.无效 1.是 2.否
    isRefund?: number;

    async request(): Promise<BaseResponse<PageList<PaymentRecord>>> {
        this.params = {
            nowPage: this.nowPage,
            pageSize: this.pageSize,
            orderId: this.orderId,
            startTime: this.startTime,
            endTime: this.endTime,
            paymentPostingStatus: this.paymentPostingStatus,
            listType: this.listType,
            shipperName: this.shipperName,
            paymentType: this.paymentType,
            refundReason: this.refundReason,
            earnestRecordBizStatus: this.earnestRecordBizStatus,
            paymentState: this.paymentState,
            isRefund: this.isRefund,
        };
        return super.postList('ams-app/process/queryEarnestPayment', PaymentRecord);
    }
}
