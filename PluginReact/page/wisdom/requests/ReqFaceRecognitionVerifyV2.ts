import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 *  desc: 添加银行卡-人脸识别
 *  user: 宋双朋
 *  time: 2025/4/9 19:30
 */
export class ReqFaceRecognitionVerifyV2 extends BaseRequest {
    orderNo?: string = '';
    from?: string = 'consignor';

    async request(): Promise<BaseResponse<RspAddBankCard>> {
        this.params = {
            orderNo: this.orderNo,
            from: this.from,
        };
        return super.post('mms-app/mms/upgrade/faceRecognitionVerifyV2', RspAddBankCard);
    }
}

export class RspAddBankCard extends ResultData {
    /** 1-提醒app前往进行身份实名*/
    checkState?: string = '';
}
