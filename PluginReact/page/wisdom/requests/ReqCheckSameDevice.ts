import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';

/**
 * 注释: 校验用户有没有更换设备
 * 时间: 2025/4/8 星期二 11:11
 * <AUTHOR>
 */
export class ReqCheckSameDevice extends BaseRequest {
    macAddress?: string;
    udid?: string;

    async request(): Promise<BaseResponse<ResultData>> {
        this.params = {
            macAddress: this.macAddress,
            udid: this.udid,
        };
        return super.post('pps-app/account/deposit/checkSameDevice', ResultData);
    }
}
