import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {ResultData} from '../../http/ResultData';
import PageList from '../../http/PageList';
import {SettledListInfo} from '../models/SettledListInfo';

/**
 * 注释: 查询已结算运费
 * 时间: 2025/4/14 星期一 19:19
 * <AUTHOR>
 */
export class ReqQuerySettledList extends BaseRequest {
    nowPage: number;
    pageSize: number;
    orderId?: string;
    startTime?: string;
    endTime?: string;
    plateNumber?: string;
    shipperName?: string;
    cargoName?: string;

    async request(): Promise<BaseResponse<PageList<SettledListInfo>>> {
        this.params = {
            nowPage: this.nowPage,
            pageSize: this.pageSize,
            orderId: this.orderId,
            startTime: this.startTime,
            endTime: this.endTime,
            plateNumber: this.plateNumber,
            shipperName: this.shipperName,
            cargoName: this.cargoName,
        };
        return super.postList('ams-app/process/querySettledList', SettledListInfo);
    }
}
