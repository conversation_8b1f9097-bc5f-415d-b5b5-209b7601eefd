import TextUtils from '../../util/TextUtils';
import {ResultData} from '../../http/ResultData';
import {BaseRequest} from '../../http/BaseRequest';
import {BaseResponse} from '../../http/BaseResponse';
import {Method} from '../../util/NativeModulesTools';

/**
 *  desc: 获取用户简明信息
 *  user: 宋双朋
 *  time: 2025/2/14 16:07
 */
export class RspUserBriefInfo extends ResultData {
    heavyTruckFlag?: string;
    isBangApple?: string;
    personalInfoSwitch?: string;
    idCardNo?: string;
    headUrl?: string;
    memberName?: string;
    lockType?: string;
    verifyStatus?: number;
    verifyExamineStatus?: string;
    cityName?: string;
    areaName?: string;
    userHeadPic?: string;
    customerId?: string;
    openAccount?: string;
    authType?: string;
    vehicleRiskAudit?: string;
    isBangWeiXi?: string;
    businessLicenseName?: string;
    nickName?: string;
    mobile?: string;
    examineType?: number;
    materialFlag?: number;
    userName?: string;
    userId?: string;
    riskAudit?: string;
    isSalesman?: number;
    nooType?: string;
    examineNewType?: string;
    upgradeType?: string;
    subType?: string;
    userType?: number;
    provinceName?: string;
    customerNm?: string;
    completePercent?: string; //资料完成进度
    //货主类型标记  1企业类货主  2个人类货主  3个体工商户
    consignorTypeFlag?: string;
    //自然人是否升级中  0否  1是
    naturalPersonUpgrade?: string;
    // 1 男 2 女
    sex?: string;
}

export enum ViewStatus {
    CHECK = '审核中',
    UNAUTO = '初级会员 去认证',
    PERSON2 = '高级会员',
    PERSON3 = '审核不通过，重新认证',
    PERSON4 = '升级个体工商户',
    PERSON5 = '认证审核中',
}

/**
 * 注释：认证状态
 * 时间：2025/2/17 9:04
 * 作者：宋双朋
 */
export const userStatus = (data: RspUserBriefInfo) => {
    let viewStatus;
    switch (data.examineType) {
        case 3:
            viewStatus = ViewStatus.UNAUTO;
            break;
        case 0:
            viewStatus = ViewStatus.CHECK;
            break;
        case 1:
            if (TextUtils.equals('2', data.consignorTypeFlag)) {
                switch (data.naturalPersonUpgrade) {
                    case '0':
                        viewStatus = ViewStatus.PERSON4;
                        break;
                    case '1':
                        viewStatus = ViewStatus.PERSON5;
                        break;
                    default:
                        viewStatus = ViewStatus.PERSON2;
                        break;
                }
            }
            break;
        case 2:
            viewStatus = ViewStatus.PERSON3;
            break;
    }
    return viewStatus;
};

export const isCompletePercent = (data: RspUserBriefInfo) => {
    if (TextUtils.isEmpty(data.completePercent)) {
        return true;
    } else {
        return TextUtils.equals('100%', data.completePercent);
    }
};

export class ReqQueryUserBriefInfo extends BaseRequest {
    userId?: string;

    public async request(): Promise<BaseResponse<RspUserBriefInfo>> {
        this.params = {};
        let resp = await super.post('mms-app/mms/upgrade/queryUserBriefInfo', RspUserBriefInfo);
        if (resp.isSuccess()) {
            let login = Method.getLogin();
            if (login) {
                let change = false;
                if (!TextUtils.equals(login?.examineType, resp.data?.examineType)) {
                    //认证状态不相同，更新登录信息数据
                    login.examineType = `${resp.data?.examineType}`;
                    change = true;
                }
                if (!TextUtils.equals(login?.consignorTypeFlag, resp.data?.consignorTypeFlag)) {
                    //类型状态不相同
                    login.consignorTypeFlag = `${resp.data?.consignorTypeFlag}`;
                    change = true;
                }
            }
        }
        return resp;
    }
}

export class RspQueryAvatarBorder extends ResultData {
    picUrl?: string;
}

export class ReqQueryAvatarBorder extends BaseRequest {
    public async request(): Promise<BaseResponse<RspQueryAvatarBorder>> {
        this.params = {};
        return super.post('mms-app/member/queryAvatarBorder', RspQueryAvatarBorder);
    }
}

export class RspQueryAccount extends ResultData {
    balanceMoney?: string;
}

export class ReqQueryAccount extends BaseRequest {
    subsidiaryId: string = '2'; // 所属平台

    public async request(): Promise<BaseResponse<RspQueryAccount>> {
        this.params = {
            subsidiaryId: this.subsidiaryId,
        };
        return super.post('pps-app/account/proxysettle/queryAccount', RspQueryAccount);
    }
}
