import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import UITitleView from '../widget/UITitleView';
import SmsCodeInput from './views/SmsCodeInput';
import {RouterUtils} from '../util/RouterUtils';
import {RouterUrl} from '../base/RouterUrl';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';

interface State extends BaseState {}

/**
 * 注释: 设置支付密码第二步
 * 时间: 2025/4/3 星期四 8:56
 * <AUTHOR>
 */
export default class SettingPayPasswordTwo extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    token: string;

    constructor(props) {
        super(props);
        this.state = {};
        this.pageParams = this.getParams();
        this.token = this.pageParams.token;
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'设置支付密码'} />
                <View style={{flexDirection: 'column', marginTop: 120, alignItems: 'center'}}>
                    <Text style={{fontSize: 16, color: '#999'}}>请输入支付密码，用于交易资金转出</Text>
                    <SmsCodeInput
                        handleSubmit={(code) => {
                            RouterUtils.skipPop();
                            RouterUtils.skipRouter(RouterUrl.ReactPage, {
                                page: 'SettingPayPasswordThree',
                                data: {
                                    code: code,
                                    token: this.token,
                                },
                            });
                        }}
                        maxNum={6}
                    />
                </View>
                {this.initCommView()}
            </View>
        );
    }
}

const styles = StyleSheet.create({});
