import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {TradeRecord} from './models/TradeRecord';
import BaseCommPage, {BaseState} from '../base/BaseCommPage';
import {ReqQueryRevenueRecord} from './requests/ReqQueryRevenueRecord';
import {ArrayUtils} from '../util/ArrayUtils';
import UITitleView from '../widget/UITitleView';
import UIListView from '../widget/UIListView';
import UISearchView from '../widget/UISearchView';
import UITouchableOpacity from '../widget/UITouchableOpacity';
import UIImage from '../widget/UIImage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ParamListBase} from '@react-navigation/native';
import {Method} from '../util/NativeModulesTools';
import dayjs from 'dayjs';
import {subtractDaysFromTime} from '../util/DateUtil';

interface State extends BaseState {
    startDate?: string;
    endDate?: string;
    listData?: TradeRecord[];
}

/**
 * 注释: 收支记录
 * 时间: 2025/3/31 星期一 14:31
 * <AUTHOR>
 */
export default class RevenueRecordPage extends BaseCommPage<NativeStackScreenProps<ParamListBase>, State> {
    pageParams: any;
    bookNo?: string;
    nowPage: number = 1;
    orderId?: string;

    constructor(props) {
        super(props);
        this.state = {
            startDate: dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD'),
            endDate: dayjs(new Date()).format('YYYY-MM-DD'),
        };
        this.pageParams = this.getParams();
        this.bookNo = this.pageParams.bookNo;
    }

    componentDidMount() {
        super.componentDidMount();
        this.queryRevenueRecord();
    }

    /**
     * 注释: 查询收支记录
     * 时间: 2025/4/10 星期四 9:58
     * <AUTHOR>
     */
    queryRevenueRecord() {
        let request = new ReqQueryRevenueRecord();
        request.nowPage = this.nowPage;
        request.bookNo = this.bookNo;
        request.pageSize = 10;
        request.accountDateS = this.state.startDate;
        request.accountDateE = this.state.endDate;
        request.orderId = this.orderId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (this.nowPage == 1) {
                    this.setState({listData: res.data?.rootArray});
                } else {
                    this.setState({listData: [...(this.state.listData ?? []), ...(res.data?.rootArray ?? [])]});
                }
                this.nowPage++;
            }
        });
    }

    render() {
        return (
            <View style={{flex: 1, backgroundColor: '#EFF0F3'}}>
                <UITitleView title={'收支记录'} />
                {this.renderHeadView()}
                <UIListView
                    contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                    renderItem={({item}) => this.renderItemView(item)}
                    dataList={this.state.listData}
                    onRefresh={() => {
                        this.nowPage = 1;
                        this.queryRevenueRecord();
                    }}
                    onLoadMore={() => {
                        this.queryRevenueRecord();
                    }}
                />
                {this.initCommView()}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:30
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderItemView(item: TradeRecord) {
        return (
            <View
                style={{
                    flexDirection: 'column',
                    backgroundColor: '#fff',
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingBottom: 10,
                }}>
                {this.renderItem('交易时间：', item.tradeTime)}
                {this.renderItem('交易流水号：', item.imei)}
                {this.renderItem('交易方向：', item.financeTypeDesc)}
                {this.renderItem('交易类型：', item.operateRemark)}
                {this.renderItem('交易金额：', item.moneyStr)}
                {this.renderItem('运单号：', item.orderId)}
                {this.renderItem('摘要：', item.abstractRemark)}
            </View>
        );
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:02
     * <AUTHOR>
     * @param label
     * @param value
     * @param seeDetail
     * @returns {React.JSX.Element}
     */
    renderItem(label: string, value: string, seeDetail: boolean = false) {
        return (
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                <Text style={{fontSize: 16, color: '#666', width: 100}}>{label}</Text>
                <Text style={{fontSize: 16, color: '#333', flex: 1}}>{value}</Text>
                {seeDetail && <Text style={{fontSize: 14, color: '#1F66FF', marginLeft: 10}}>详情</Text>}
            </View>
        );
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UISearchView
                    placeholder={'请输入运单号搜索'}
                    searchButton={true}
                    searchView={
                        <UITouchableOpacity
                            onPress={() => {
                                this.nowPage = 1;
                                this.queryRevenueRecord();
                            }}>
                            <Text style={{fontSize: 15, color: '#5086FC'}}>搜索</Text>
                        </UITouchableOpacity>
                    }
                    backGroundStyle={{
                        backgroundColor: '#EFF0F3',
                        borderRadius: 4,
                        borderWidth: 0,
                        marginHorizontal: 0,
                        paddingHorizontal: 10,
                    }}
                    onChangeText={(e) => {
                        this.orderId = e;
                    }}
                    onSubmitEditing={(e) => {
                        this.orderId = e;
                        this.nowPage = 1;
                        this.queryRevenueRecord();
                    }}
                />
                <UITouchableOpacity
                    style={{flexDirection: 'row', alignItems: 'center', marginTop: 13}}
                    onPress={() => {
                        Method.showStartEndTimePicker().then((res) => {
                            let startTime = res['startTime'];
                            let endTime = res['endTime'];
                            this.setState({
                                startDate: startTime.split(' ')[0],
                                endDate: endTime.split(' ')[0],
                            });
                        });
                    }}>
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#1F66FF',
                        }}>
                        {this.state.startDate ? `${this.state.startDate} 至 ${this.state.endDate}` : '请选择时间'}
                    </Text>
                    <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                </UITouchableOpacity>
            </View>
        );
    }
}

const styles = StyleSheet.create({});
