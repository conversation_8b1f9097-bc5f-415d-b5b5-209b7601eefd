import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect} from 'react';
import showText, {ReqQueryList, RspQueryList} from '../requests/ReqQueryList';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIImage from '../../widget/UIImage';
import {http} from '../../const.global';
import {gScreen_height} from '../../util/scaled-style';
import UIListView from '../../widget/UIListView';
import UIPopup from '../../widget/UIPopup';

interface Props {
    callBack?: Function;
}

/**
 * 注释: 选择归属银行
 * 时间: 2025/4/9 17:05
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function WalletSelectBankChanelDialog(props: Props) {
    const [dataList, setDataList] = React.useState<RspQueryList[]>([]);
    useEffect(() => {
        loadData();
    }, []);
    const loadData = () => {
        let req = new ReqQueryList();
        req.request().then((res) => {
            if (res.isSuccess()) {
                setDataList(res.data?.rootArray ?? []);
            }
        });
    };
    const renderItemView = (item: RspQueryList) => {
        return (
            <UITouchableOpacity
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: '#F2F8FF',
                    paddingLeft: 10,
                    paddingRight: 10,
                    marginTop: 7,
                    paddingTop: 5,
                    paddingBottom: 5,
                    borderRadius: 5,
                }}
                onPress={() => {
                    props.callBack && props.callBack(item);
                }}>
                <UIImage source={http.imagUrl(item.logoPic ?? '')} style={{width: 28, height: 28}} />
                <Text style={{fontSize: 14, color: '#333', marginLeft: 10}}>{showText(item)}</Text>
            </UITouchableOpacity>
        );
    };

    const renderChildrenView = () => {
        return (
            <View
                style={{
                    paddingBottom: 13,
                    borderBottomRightRadius: 5,
                    borderBottomLeftRadius: 5,
                    backgroundColor: '#FFFFFF',
                    paddingLeft: 10,
                    paddingEnd: 10,
                    height: gScreen_height / 2,
                }}>
                <UIListView renderItem={({item}) => renderItemView(item)} dataList={dataList} />
            </View>
        );
    };
    return (
        <UIPopup
            children={renderChildrenView()}
            title={'请选择'}
            showSubTitle={false}
            subTitle={'确定'}
            onClose={() => {
                props.callBack && props.callBack();
            }}
            onSubCallBack={() => {
                props.callBack && props.callBack();
            }}
            style={{justifyContent: 'flex-end', padding: 0}}
        />
    );
}

const styles = StyleSheet.create({});
