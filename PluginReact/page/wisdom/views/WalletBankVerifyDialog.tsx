import {StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useEffect} from 'react';
import {RsqBankList} from '../requests/ReqBankList';
import {Method} from '../../util/NativeModulesTools';
import {ReqVerifyPaymentResult} from '../requests/ReqVerifyPaymentResult';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import TextUtils from '../../util/TextUtils';
import {DialogBuilder} from '../../base/BaseCommPage';
import UIImage from '../../widget/UIImage';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import UIButton from '../../widget/UIButton';
import UIPopup from '../../widget/UIPopup';

interface Props {
    callBack?: Function;
    closeCallBack?: Function;
    item?: RsqBankList;
}

/**
 * 注释: 添加对公银行卡验证
 * 时间: 2025/4/9 17:05
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function WalletBankVerifyDialog(props: Props) {
    const [validateAmt, setValidateAmt] = React.useState('');
    const [errorMsg, setErrorMsg] = React.useState('');
    const [showErrorMsg, setShowErrorMsg] = React.useState(false);

    const verifyMoney = () => {
        let login = Method.getLogin();
        let request = new ReqVerifyPaymentResult();
        request.cardId = props.item?.cid ?? '';
        request.validateAmt = validateAmt;
        request.realName = login?.memberName ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //认证成功
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'WalletBankVerifySuccessPage',
                    data: {
                        item: props.item,
                    },
                });
                props.callBack && props.callBack();
            } else {
                switch (res.data?.resultCode) {
                    case '0001':
                        //认证失败
                        RouterUtils.skipRouter(RouterUrl.ReactPage, {
                            page: 'WalletBankVerifyFailedPage',
                            data: {
                                item: props.item,
                            },
                        });
                        props.closeCallBack && props.closeCallBack();
                        break;
                    default:
                        if (TextUtils.isEmpty(res.data?.resultMsg)) {
                            Method.showDialogToast(res.getMsg());
                            setShowErrorMsg(false);
                        } else {
                            setShowErrorMsg(true);
                            setErrorMsg(res.data?.resultMsg ?? '');
                        }
                        break;
                }
            }
        });
    };
    const warningView = () => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: 5,
                }}>
                <UIImage
                    source={'invoice_icon_5'}
                    style={{
                        width: 14,
                        height: 14,
                    }}
                />
                <Text
                    style={{
                        color: '#FF7520',
                        fontSize: 12,
                        marginLeft: 5,
                    }}>
                    {errorMsg}
                </Text>
            </View>
        );
    };
    const renderHeadView = () => {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    borderTopRightRadius: 5,
                    borderTopLeftRadius: 5,
                    backgroundColor: '#FFFFFF',
                    justifyContent: 'space-between',
                }}>
                <UITouchableOpacity
                    onPress={() => {
                        props.closeCallBack && props.closeCallBack();
                    }}>
                    <UIImage source={'com_close_icon'} style={{width: 18, height: 18, marginLeft: 15}} />
                </UITouchableOpacity>
                <Text
                    style={{
                        fontSize: 17,
                        color: '#333333',
                        fontWeight: 'bold',
                    }}>
                    {'金额校验'}
                </Text>
                <UIButton
                    text={'确定'}
                    backgroundColor={'#FFFFFF'}
                    borderColor={'#FFFFFF'}
                    textColor={'#5086FC'}
                    style={{marginRight: 15}}
                    onPress={() => {
                        verifyMoney();
                    }}
                />
            </View>
        );
    };
    const renderChildrenView = () => {
        return (
            <View
                style={{
                    paddingTop: 13,
                    paddingBottom: 13,
                    borderBottomRightRadius: 5,
                    borderBottomLeftRadius: 5,
                    backgroundColor: '#FFFFFF',
                    paddingLeft: 15,
                    paddingEnd: 10,
                }}>
                <Text
                    style={{
                        fontSize: 14,
                        color: '#333333',
                        marginTop: 10,
                    }}>
                    {'请输入您收到的金额数字（如：0.01元，则输入“0.01” ），您有 2 次验证机会，若金额输入错误超过 2 次须重新认证'}
                </Text>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: 10,
                    }}>
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#333333',
                        }}>
                        {'金额验证：'}
                    </Text>
                    <TextInput
                        style={{
                            backgroundColor: '#EFEFEF',
                            fontSize: 14,
                            borderRadius: 5,
                            paddingLeft: 10,
                            marginLeft: 10,
                            marginRight: 10,
                            paddingTop: 0,
                            flex: 1,
                            paddingBottom: 0,
                        }}
                        placeholder={'请输入金额数字'}
                        keyboardType={'numeric'}
                        value={validateAmt}
                        onChangeText={(text) => {
                            const filtered = text
                                .replace(/[^0-9.]/g, '') // 移除非数字和小数点
                                .replace(/(\..*)\./g, '$1') // 禁止多个小数点
                                .replace(/^0+(\d)/, '$1') // 禁止前导零
                                .replace(/^\./, '0.') // 自动补全小数点前的零
                                .substring(0, 5);
                            setValidateAmt(filtered);
                        }}
                    />
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#333333',
                        }}>
                        {'元'}
                    </Text>
                </View>
                {showErrorMsg && warningView()}
            </View>
        );
    };
    return (
        <UIPopup
            children={renderChildrenView()}
            headView={renderHeadView()}
            style={{
                justifyContent: 'flex-end',
                padding: 0,
            }}
        />
    );
}

const styles = StyleSheet.create({});
