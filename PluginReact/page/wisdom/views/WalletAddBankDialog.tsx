import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Method} from '../../util/NativeModulesTools';
import UIImage from '../../widget/UIImage';
import UIButton from '../../widget/UIButton';
import UIPopup from '../../widget/UIPopup';

interface Props {
    bankNo?: string;
    callBack?: Function;
}

/**
 * 注释: 添加对公银行卡验证提醒
 * 时间: 2025/4/9 17:05
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function WalletAddBankDialog(props: Props) {
    const renderHeadView = () => {
        return (
            <Text
                style={{
                    paddingTop: 13,
                    paddingBottom: 13,
                    fontSize: 18,
                    fontWeight: 'bold',
                    borderTopLeftRadius: 5,
                    borderTopRightRadius: 5,
                    backgroundColor: '#ECF5FF',
                    color: '#5086FC',
                    textAlign: 'center',
                }}>
                您离绑卡成功还差最后一步
            </Text>
        );
    };
    const renderChildrenView = () => {
        let login = Method.getLogin();
        return (
            <View
                style={{
                    paddingTop: 13,
                    paddingBottom: 13,
                    borderBottomRightRadius: 5,
                    borderBottomLeftRadius: 5,
                    backgroundColor: '#FFFFFF',
                    paddingLeft: 10,
                    paddingEnd: 10,
                }}>
                <Text
                    style={{
                        fontSize: 14,
                        textAlign: 'left',
                        letterSpacing: 1.5,
                    }}>
                    {'若银行卡信息无误，中储南京智慧物流科技有限公司将会在'}
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#FF5F1E',
                        }}>
                        {'2小时'}
                    </Text>
                    {'内向账户'}
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#5086FC',
                        }}>
                        {props.bankNo}
                    </Text>
                    {'转入0.01～0.99，请至'}
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#333333',
                            fontWeight: 'bold',
                        }}>
                        {'【银行卡管理】'}
                    </Text>
                    <Text
                        style={{
                            fontSize: 14,
                            color: '#FF5F1E',
                        }}>
                        {'完成收款码认证，输入您收到的金额数字（如：0.01 元，则输入“0.01”），完成认证。'}
                    </Text>
                </Text>
                <UIImage source={'wallet_icon_5'} style={{width: 255, height: 112, marginTop: 10, marginBottom: 5}} />
                <Text style={{fontSize: 14, color: '#333333'}}>
                    {'若超过2小时未收到打款，请您联系客服协助'}
                    <Text style={{fontSize: 14, color: '#5086FC'}}>{'400-088-5566'} </Text>
                </Text>
                <UIButton
                    text={'我知道了'}
                    style={{marginLeft: 25, marginRight: 25, marginTop: 25}}
                    onPress={() => {
                        props.callBack && props.callBack();
                    }}
                />
            </View>
        );
    };
    return <UIPopup children={renderChildrenView()} headView={renderHeadView()} style={{justifyContent: 'center', padding: 0, margin: 37}} />;
}

const styles = StyleSheet.create({});
