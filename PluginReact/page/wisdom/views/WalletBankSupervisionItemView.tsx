import {StyleSheet, Text, TextInput, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {bindStateImg, bindStateStr, formatCardNumberV1, RsqBankList} from '../requests/ReqBankList';
import {ReqBindBankPassport} from '../requests/ReqBindBankPassport';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import {DialogBuilder} from '../../base/BaseCommPage';
import {ReqRemoveBankPassport} from '../requests/ReqRemoveBankPassport';
import UIImage from '../../widget/UIImage';
import {Method} from '../../util/NativeModulesTools';
import {http} from '../../const.global';
import UIButton from '../../widget/UIButton';
import ELogin from '../../user/personalsafety/models/ELogin';
import Modal from 'react-native-modal';

interface Props {
    item: RsqBankList;
    callBack: Function;
    subsidiaryId?: string;
}

/**
 * 注释: 银行卡条目
 * 时间: 2025/4/9 14:31
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function WalletBankSupervisionItemView(props: Props) {
    const [rightMargin, setRightMargin] = useState(15);
    const [mobile, setMobile] = useState('');
    const [mobileMemo, setMobileMemo] = useState('');
    const [isShowDialog, setIsShowDialog] = useState(false);
    const [rightBorderRadius, setRightBorderRadius] = useState(5);

    useEffect(() => {
        let login = Method.getLogin();
        setMobile(login.mobile);
        setMobileMemo(login.mobile);
    }, []);

    const bind = () => {
        let request = new ReqBindBankPassport();
        request.cardId = props.item?.cid ?? '';
        request.accMobile = mobile;
        request.subsidiaryId = props?.subsidiaryId ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //刷新列表
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: 'WalletBankSupervisionSuccessPage',
                });
                props.callBack() && props.callBack();
                Method.showDialogToast('绑卡成功！您可使用该绑定银行卡进行余额提现。');
            } else {
                Method.showDialogToast('绑卡失败！银行账户核验失败|信息认证未通过。建议您核实手机号与银行预留手机号是否一致，重新操作绑定');
            }
        });
    };
    const unBind = () => {
        let request = new ReqRemoveBankPassport();
        request.cardId = props.item?.cid ?? '';
        request.subsidiaryId = props?.subsidiaryId ?? '';
        request.request().then((res) => {
            if (res.isSuccess()) {
                //刷新列表
                props.callBack() && props.callBack();
            } else {
                Method.showDialogToast(res.getMsg());
            }
        });
    };
    return (
        <>
            <View style={[styles.itemContainer, {marginBottom: 0, marginRight: rightMargin}]}>
                <View style={{height: 7, backgroundColor: '#EFF0F3'}} />
                <LinearGradient
                    style={[
                        styles.itemViewContainer,
                        {
                            borderTopRightRadius: rightBorderRadius,
                            borderBottomRightRadius: rightBorderRadius,
                        },
                    ]}
                    colors={['#71A1EE', '#316CCF']}
                    useAngle={true}
                    angle={90}>
                    <View style={{flexDirection: 'row', alignItems: 'center'}}>
                        <UIImage source={http.imagUrl(props.item.logo)} style={{width: 40, height: 40}} />
                        <View style={{flexDirection: 'column'}}>
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Text style={styles.itemNameText} ellipsizeMode={'tail'} numberOfLines={1}>
                                    {props.item.bankName}
                                </Text>
                                <UIImage
                                    source={bindStateImg(props.item)}
                                    style={{
                                        width: 76,
                                        height: 23,
                                        marginTop: 5,
                                    }}
                                />
                                <UIButton
                                    text={bindStateStr(props.item)}
                                    style={{
                                        height: 24,
                                        width: 80,
                                        marginTop: 5,
                                        marginLeft: 10,
                                    }}
                                    backgroundColor={'#FFFFFF'}
                                    textColor={'#5086FC'}
                                    onPress={() => {
                                        switch (props.item.bindState) {
                                            case '2':
                                                unBind();
                                                break;
                                            case '1':
                                                setIsShowDialog(true);
                                                break;
                                            default:
                                                break;
                                        }
                                    }}
                                />
                            </View>
                            <Text style={styles.itemTypeText}>{'借记卡'}</Text>
                            <View style={{flexDirection: 'row', alignItems: 'center'}}>
                                <Text style={styles.itemBankNoText}>{formatCardNumberV1(props.item.bankCardNo)} </Text>
                            </View>
                        </View>
                    </View>
                </LinearGradient>
            </View>
            <Modal
                style={{}}
                animationIn={'slideInUp'}
                backdropOpacity={0.5}
                useNativeDriver={true}
                isVisible={isShowDialog}
                onBackButtonPress={() => {
                    setIsShowDialog(false);
                }}
                onBackdropPress={() => {
                    setIsShowDialog(false);
                }}>
                <View
                    style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'white',
                        borderRadius: 10,
                        padding: 20,
                    }}>
                    <Text style={{color: 'black', fontSize: 18, fontWeight: 'bold'}}>提示</Text>
                    <Text>请核实以下手机号与银行预留手机号是否一致，如果不一致，请在下方进行修改，以确保能够成功绑定：</Text>
                    <View style={{flexDirection: 'row', alignItems: 'center', justifyContent: 'center'}}>
                        <Text style={{color: 'black', marginTop: 10}}>银行预留手机号:</Text>
                        <TextInput
                            value={mobile}
                            onChangeText={(text) => {
                                setMobile(text);
                            }}
                            style={{borderWidth: 1, borderColor: '#999', textAlign: 'right', marginTop: 10, padding: 5}}
                        />
                    </View>
                    <UIButton
                        text={'确定'}
                        style={{marginTop: 20, width: 100}}
                        onPress={() => {
                            bind();
                            setIsShowDialog(false);
                            setMobile(mobileMemo);
                        }}
                    />
                </View>
            </Modal>
        </>
    );
}

const styles = StyleSheet.create({
    itemContainer: {
        flex: 1,
        marginLeft: 15,
        marginRight: 15,
    },
    itemViewContainer: {
        padding: 10,
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        borderBottomLeftRadius: 5,
        borderBottomRightRadius: 5,
    },
    itemNameText: {fontSize: 18, fontWeight: 'normal', marginStart: 20, width: 90, color: '#FFFFFF'},
    itemTypeText: {fontSize: 12, fontWeight: 'normal', color: '#FFFFFF', marginStart: 20, marginTop: 5},
    itemBankNoText: {fontSize: 12, fontWeight: 'normal', color: '#FFFFFF', marginStart: 20, marginTop: 5},
    deleteButton: {
        backgroundColor: 'red',
        justifyContent: 'center',
        alignItems: 'center',
        width: 80,
    },
    deleteText: {
        color: 'white',
        fontWeight: 'bold',
    },
});
