import {StyleSheet, Text, View} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {ReqQueryEarnestPayment} from '../requests/ReqQueryEarnestPayment';
import {PaymentRecord} from '../models/PaymentRecord';
import UISearchView from '../../widget/UISearchView';
import UITouchableOpacity from '../../widget/UITouchableOpacity';
import {Method} from '../../util/NativeModulesTools';
import dayjs from 'dayjs';
import UIImage from '../../widget/UIImage';
import {Child, FilterPopupBean} from '../../widget/filter/FilterPopupBean';
import UIListView from '../../widget/UIListView';
import {ArrayUtils} from '../../util/ArrayUtils';
import UICommonFilterPopup from '../../widget/filter/UICommonFilterPopup';
import TextUtils from '../../util/TextUtils';
import {RouterUtils} from '../../util/RouterUtils';
import {RouterUrl} from '../../base/RouterUrl';
import UIButton from '../../widget/UIButton';
import {ReqEarnestPaymentClose} from '../requests/ReqEarnestPaymentClose';
import {subtractDaysFromTime} from '../../util/DateUtil';

interface Props {
    orderId?: string;
    //1-支付中/已关闭 2-已支付/已退款
    listType: string;
    //0-交易通 1-摘单
    source?: number;
    //回调
    callBack?: Function;
}

/**
 * 注释: 支付记录页面
 * 时间: 2025/4/14 星期一 16:07
 * <AUTHOR>
 * @param props
 * @constructor
 */
export default function PayRecordPageView(props: Props) {
    const nowPage = useRef<number>(1);
    const startDateRef = useRef<string>();
    const endDateRef = useRef<string>();
    const orderId = useRef<string>(props.orderId ?? '');
    const initFilterDataRef = useRef<FilterPopupBean[]>([]);
    const filterDataRef = useRef<FilterPopupBean[]>([]);

    const [listData, setListData] = useState<PaymentRecord[]>([]);
    const [startDate, setStartDate] = useState<string>();
    const [endDate, setEndDate] = useState<string>();
    const [showFilter, setShowFilter] = useState<boolean>(false);
    const [filterData, setFilterData] = useState<FilterPopupBean[]>([]);

    // 页面初始化
    useEffect(() => {
        startDateRef.current = dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD');
        endDateRef.current = dayjs(new Date()).format('YYYY-MM-DD');
        queryPaymentRecoed();
        initFilterData();
        setStartDate(dayjs(subtractDaysFromTime(new Date(), 30)).format('YYYY-MM-DD'));
        setEndDate(dayjs(new Date()).format('YYYY-MM-DD'));
    }, []);

    /**
     * 注释: 关闭订单
     * 时间: 2025/4/15 星期二 10:46
     * <AUTHOR>
     * @param orderId
     */
    function closeOrder(orderId: string) {
        let request = new ReqEarnestPaymentClose();
        request.orderId = orderId;
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (props.source == 0) {
                    Method.showDialogToast('关闭成功');
                    queryPaymentRecoed();
                } else {
                    Method.showDialogFormParams({
                        title: '提示',
                        message: '订单已关闭，您可继续前往抢单页面发起操作。',
                        cancelable: false,
                        cancelText: '关闭',
                        okText: '继续抢单',
                        okAction: () => {
                            RouterUtils.skipPop();
                        },
                    });
                }
            } else {
                if (props.source == 0) {
                    Method.showDialogToast(res.getMsg());
                } else {
                    Method.showDialogFormParams({
                        title: '提示',
                        message: '系统查询您的订单现已支付成功，无需关闭支付，您可继续前往抢单页面发起操作。',
                        cancelable: false,
                        cancelText: '关闭',
                        okText: '继续抢单',
                        okAction: () => {
                            RouterUtils.skipPop();
                            props.callBack && props.callBack();
                        },
                    });
                }
            }
        });
    }

    /**
     * 注释: 初始化过滤数据
     * 时间: 2025/4/14 星期一 16:48
     * <AUTHOR>
     */
    function initFilterData() {
        if (props.listType == '1') {
            //货主名称
            let inputHzName = new FilterPopupBean();
            inputHzName.title = '货主名称';
            inputHzName.placeholder = '请输入货主名称';
            inputHzName.filterType = 'input';
            //支付方式过滤
            let payTypeFilter = new FilterPopupBean();
            payTypeFilter.title = '支付方式';
            payTypeFilter.isSingle = true;
            payTypeFilter.children = [new Child(false, '微信', 1), new Child(false, '支付宝', 2), new Child(false, '银行卡', 3), new Child(false, '交易通', 4)];
            //支付状态
            let statusFilter = new FilterPopupBean();
            statusFilter.title = '支付状态';
            statusFilter.isSingle = true;
            statusFilter.children = [new Child(false, '支付中', 1), new Child(false, '已关闭', 4)];
            setFilterData([inputHzName, payTypeFilter, statusFilter]);
            initFilterDataRef.current = ArrayUtils.deepCopy([inputHzName, payTypeFilter, statusFilter], FilterPopupBean);
        } else {
            //货主名称
            let inputHzName = new FilterPopupBean();
            inputHzName.title = '货主名称';
            inputHzName.placeholder = '请输入货主名称';
            inputHzName.filterType = 'input';
            //支付方式过滤
            let payTypeFilter = new FilterPopupBean();
            payTypeFilter.title = '支付方式';
            payTypeFilter.isSingle = true;
            payTypeFilter.children = [new Child(false, '微信', 1), new Child(false, '支付宝', 2), new Child(false, '银行卡', 3), new Child(false, '交易通', 4)];
            //是否已退款
            let statusFilter = new FilterPopupBean();
            statusFilter.title = '是否已退款';
            statusFilter.isSingle = true;
            statusFilter.children = [new Child(false, '是', 1), new Child(false, '否', 2)];
            //退款原因：运输完成退回订金、抢单失败、运单取消、违约终止
            let reasonFilter = new FilterPopupBean();
            reasonFilter.title = '退款原因';
            reasonFilter.isSingle = true;
            reasonFilter.children = [new Child(false, '运输完成退回订金', 5), new Child(false, '抢单失败', 1), new Child(false, '运单取消', 2), new Child(false, '违约终止', 4)];
            setFilterData([inputHzName, payTypeFilter, statusFilter, reasonFilter]);
            initFilterDataRef.current = ArrayUtils.deepCopy([inputHzName, payTypeFilter, statusFilter, reasonFilter], FilterPopupBean);
        }
    }

    /**
     * 注释: 绘制头部视图
     * 时间: 2025/3/31 星期一 15:11
     * <AUTHOR>
     * @returns {React.JSX.Element}
     */
    function renderHeadView() {
        return (
            <View style={{backgroundColor: '#fff', padding: 15}}>
                <UISearchView
                    placeholder={'请输入运单号搜索'}
                    searchButton={true}
                    value={orderId.current}
                    searchView={
                        <UITouchableOpacity
                            onPress={() => {
                                nowPage.current == 1;
                                queryPaymentRecoed();
                            }}>
                            <Text style={{fontSize: 15, color: '#5086FC'}}>搜索</Text>
                        </UITouchableOpacity>
                    }
                    backGroundStyle={{
                        backgroundColor: '#EFF0F3',
                        borderRadius: 4,
                        borderWidth: 0,
                        marginHorizontal: 0,
                        paddingHorizontal: 10,
                    }}
                    onChangeText={(e: string) => {
                        orderId.current = e;
                    }}
                    onSubmitEditing={(e: string) => {
                        nowPage.current == 1;
                        orderId.current = e;
                        queryPaymentRecoed();
                    }}
                />
                <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 13}}>
                    {/*时间选择*/}
                    <UITouchableOpacity
                        style={{flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            Method.showStartEndTimePicker().then((res) => {
                                let startTime = res['startTime'];
                                let endTime = res['endTime'];
                                startDateRef.current = startTime.split(' ')[0];
                                endDateRef.current = endTime.split(' ')[0];
                                nowPage.current == 1;
                                setStartDate(startTime.split(' ')[0]);
                                setEndDate(endTime.split(' ')[0]);
                                queryPaymentRecoed();
                            });
                        }}>
                        <Text
                            style={{
                                fontSize: 14,
                                color: '#1F66FF',
                            }}>
                            {startDate ? `${dayjs(startDate).format('YYYY-MM-DD')} 至 ${dayjs(endDate).format('YYYY-MM-DD')}` : '请选择时间'}
                        </Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                    {/*更多*/}
                    <UITouchableOpacity
                        style={{marginLeft: 50, flexDirection: 'row', alignItems: 'center'}}
                        onPress={() => {
                            setShowFilter(true);
                        }}>
                        <Text style={{fontSize: 14, color: '#1F66FF'}}>更多</Text>
                        <UIImage source={'icon_use_login_down'} style={{width: 16, height: 10}} />
                    </UITouchableOpacity>
                </View>
            </View>
        );
    }

    /**
     * 注释: 查询支付记录
     * 时间: 2025/4/10 星期四 11:00
     * <AUTHOR>
     */
    function queryPaymentRecoed() {
        let request = new ReqQueryEarnestPayment();
        request.nowPage = nowPage.current;
        request.pageSize = 10;
        request.orderId = orderId.current;
        request.startTime = startDateRef.current;
        request.endTime = endDateRef.current;
        request.listType = parseInt(props.listType);
        //货主名称
        let inputHzName = filterDataRef.current.find((item) => {
            return item.title == '货主名称';
        });
        if (inputHzName) {
            request.shipperName = inputHzName.inputValue;
        }
        //支付方式
        let payTypeItem = filterDataRef.current.find((item) => {
            return item.title == '支付方式';
        });
        if (payTypeItem) {
            let payTypeSelectItem = payTypeItem?.children?.find((item) => {
                return item.isCheck;
            });
            request.paymentType = payTypeSelectItem?.value;
        }
        //支付状态
        let statusItem = filterDataRef.current.find((item) => {
            return item.title == '支付状态';
        });
        if (statusItem) {
            let statusSelectItem = statusItem?.children?.find((item) => {
                return item.isCheck;
            });
            request.paymentState = statusSelectItem?.value;
        }
        //是否已退款
        let refundItem = filterDataRef.current.find((item) => {
            return item.title == '是否已退款';
        });
        if (refundItem) {
            let refundSelectItem = refundItem?.children?.find((item) => {
                return item.isCheck;
            });
            request.isRefund = refundSelectItem?.value;
        }
        //退款原因
        let refundReasonItem = filterDataRef.current.find((item) => {
            return item.title == '退款原因';
        });
        if (refundReasonItem) {
            let refundReasonSelectItem = refundReasonItem?.children?.find((item) => {
                return item.isCheck;
            });
            request.refundReason = refundReasonSelectItem?.value;
        }
        request.request().then((res) => {
            if (res.isSuccess()) {
                if (nowPage.current == 1) {
                    setListData(res.data?.rootArray ?? []);
                } else {
                    setListData([...(listData ?? []), ...(res.data?.rootArray ?? [])]);
                }
                nowPage.current++;
            }
        });
    }

    /**
     * 注释: 绘制Item
     * 时间: 2025/4/1 星期二 11:02
     * <AUTHOR>
     * @param label
     * @param value
     * @returns {JSX.Element}
     * @param color
     */
    function renderItem(label: string, value: string, color?: string) {
        return (
            <View style={{flexDirection: 'row', alignItems: 'center', marginTop: 10}}>
                <Text style={{fontSize: 16, color: '#666', width: 100}}>{label}</Text>
                <Text style={{fontSize: 16, color: color ?? '#333', flex: 1}}>{value}</Text>
            </View>
        );
    }

    /**
     * 注释: 绘制关闭ItemView
     * 时间: 2025/4/15 星期二 8:54
     * <AUTHOR>
     * @returns {JSX.Element}
     */
    function renderCloseItemView(item: PaymentRecord) {
        return (
            <View
                style={{
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingVertical: 12,
                    backgroundColor: '#fff',
                }}>
                {renderItem('申请时间：', item.createdTimeStr)}
                {renderItem('申请流水号：', item.paymentBizImei)}
                {renderItem('运单号：', item.orderId)}
                {renderItem('车牌号：', item.plateNumber)}
                {renderItem('货主：', item.consignorName)}
                {renderItem('支付状态：', item.paymentStatusStr)}
                {renderItem('支付方式：', item.payChannelStr)}
                <View style={{borderRadius: 6, padding: 10, marginVertical: 10, backgroundColor: '#FAFAFD'}}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingBottom: 10,
                            borderBottomWidth: 0.5,
                            borderColor: '#eee',
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>支付金额：</Text>
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#333',
                                fontWeight: 'bold',
                            }}>
                            {item.actualPaymentMoneyStr}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            paddingVertical: 5,
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>其中，订金：</Text>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            {item.bizStatusStr == '可退' && <UIImage source={'ic_ketui'} style={{width: 44, height: 22}} />}
                            {item.bizStatusStr == '不可退' && <UIImage source={'ic_noketui'} style={{width: 50, height: 22}} />}
                            <Text style={{fontSize: 16, color: '#333'}}>{item.earnestMoneyStr}</Text>
                        </View>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            paddingVertical: 5,
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>技术服务费：</Text>
                        <Text style={{fontSize: 16, color: '#333'}}>{item.commissionMoneyStr}</Text>
                    </View>
                    {TextUtils.isNoEmpty(item.couponMoneyStr) && (
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                paddingVertical: 5,
                            }}>
                            <Text style={{fontSize: 16, color: '#666'}}>服务费抵扣券：</Text>
                            <Text style={{fontSize: 16, color: '#FF8916'}}>{`减 ${item.couponMoneyStr}`}</Text>
                        </View>
                    )}
                </View>
                {TextUtils.isNoEmpty(item.refundTimeStr) && renderItem('退款时间：', item.refundTimeStr)}
                {TextUtils.isNoEmpty(item.refundTypeStr) && renderItem('退款方式：', item.refundTypeStr)}
                {TextUtils.isNoEmpty(item.refundMoneyStr) && renderItem('退款金额：', `¥${item.refundMoneyStr}`)}
                {TextUtils.isNoEmpty(item.refundRemark) && renderItem('退款原因：', item.refundRemark)}
            </View>
        );
    }

    /**
     * 注释: 绘制支付中ItemView
     * 时间: 2025/4/15 星期二 8:51
     * <AUTHOR>
     * @param item
     * @return {JSX.Element}
     */
    function renderPayingItemView(item: PaymentRecord) {
        return (
            <View
                style={{
                    marginTop: 8,
                    paddingHorizontal: 15,
                    paddingVertical: 12,
                    backgroundColor: '#fff',
                }}>
                {renderItem('申请时间：', item.createdTimeStr)}
                {renderItem('申请流水号：', item.paymentBizImei)}
                {renderItem('运单号：', item.orderId)}
                {renderItem('车牌号：', item.plateNumber)}
                {renderItem('货主：', item.consignorName)}
                {renderItem('支付状态：', item.paymentStatusStr, item.paymentStatus == '1' ? '#f00' : '#333')}
                {renderItem('支付方式：', item.payChannelStr)}
                <View style={{borderRadius: 6, padding: 10, marginVertical: 10, backgroundColor: '#FAFAFD'}}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            paddingBottom: 10,
                            borderBottomWidth: 0.5,
                            borderColor: '#eee',
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>支付金额：</Text>
                        <Text
                            style={{
                                fontSize: 16,
                                color: '#333',
                                fontWeight: 'bold',
                            }}>
                            {item.actualPaymentMoneyStr}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            paddingVertical: 5,
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>其中，订金：</Text>
                        <View style={{flexDirection: 'row', alignItems: 'center'}}>
                            <UIImage source={item.earnestType == '1' ? 'ic_ketui' : 'ic_noketui'} style={{width: 44, height: 22, marginRight: 5}} />
                            <Text style={{fontSize: 16, color: '#333'}}>{item.earnestMoneyStr}</Text>
                        </View>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            paddingVertical: 5,
                        }}>
                        <Text style={{fontSize: 16, color: '#666'}}>技术服务费：</Text>
                        <Text style={{fontSize: 16, color: '#333'}}>{item.commissionMoneyStr}</Text>
                    </View>
                    {TextUtils.isNoEmpty(item.couponMoneyStr) && (
                        <View
                            style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                paddingVertical: 5,
                            }}>
                            <Text style={{fontSize: 16, color: '#666'}}>服务费抵扣券：</Text>
                            <Text style={{fontSize: 16, color: '#FF8916'}}>{`减 ${item.couponMoneyStr}`}</Text>
                        </View>
                    )}
                </View>
                {!TextUtils.equals('交易通', item.payChannelStr) && item.paymentStatus == '1' && (
                    <UIButton
                        text={'关闭支付'}
                        style={{
                            backgroundColor: '#fff',
                            borderColor: '#5086FC',
                            borderWidth: 0.5,
                            width: 110,
                            alignSelf: 'flex-end',
                        }}
                        textColor={'#5086FC'}
                        onPress={() => {
                            closeOrder(item.orderId);
                        }}
                    />
                )}
            </View>
        );
    }

    return (
        <View style={{backgroundColor: '#EFF0F3', flex: 1}}>
            {renderHeadView()}
            <UIListView
                contentContainerStyle={{backgroundColor: '#EFF0F3'}}
                renderItem={({item}) => {
                    return props.listType == '1' ? renderPayingItemView(item) : renderCloseItemView(item);
                }}
                dataList={listData}
                onRefresh={() => {
                    nowPage.current = 1;
                    queryPaymentRecoed();
                }}
                onLoadMore={() => {
                    queryPaymentRecoed();
                }}
            />
            {/*筛选弹窗*/}
            {showFilter && (
                <UICommonFilterPopup
                    filterData={filterData}
                    onReset={() => {
                        setFilterData(ArrayUtils.deepCopy(initFilterDataRef.current, FilterPopupBean));
                        filterDataRef.current = ArrayUtils.deepCopy(initFilterDataRef.current, FilterPopupBean);
                        setShowFilter(false);
                        nowPage.current = 1;
                        queryPaymentRecoed();
                    }}
                    onClose={(data: FilterPopupBean[]) => {
                        setShowFilter(false);
                        setFilterData(data);
                        filterDataRef.current = data;
                    }}
                    onSubmit={(data: FilterPopupBean[]) => {
                        setShowFilter(false);
                        setFilterData(data);
                        nowPage.current = 1;
                        filterDataRef.current = data;
                        queryPaymentRecoed();
                    }}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({});
