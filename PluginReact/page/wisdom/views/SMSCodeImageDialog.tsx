import Modal from 'react-native-modal';
import {Text, TextInput, TouchableOpacity, View} from 'react-native';
import {useState} from 'react';
import {http} from '../../const.global';
import UIImage from '../../widget/UIImage';

export const SMSCodeImageDialog = (pros: {mobile: string; cancel: () => void; confirm: (imageCode) => void}) => {
    const [imageCode, setImageCode] = useState<string>('');
    const [url, setUrl] = useState<string>(`${http.url()}/mms-app/mms/verifyCode/getImageVerifyCode?mobile=${pros.mobile}&time=${new Date().getTime()}`);
    const checkImageVerifyCode = async () => {
        pros.confirm(imageCode);
    };

    const updateUrl = () => {
        setUrl(`${http.url()}/mms-app/mms/verifyCode/getImageVerifyCode?mobile=${pros.mobile}&time=${new Date().getTime()}`);
    };
    return (
        <Modal isVisible={true} onBackdropPress={pros.cancel} onBackButtonPress={pros.cancel}>
            <View style={{backgroundColor: '#fff', margin: 15, borderRadius: 8}}>
                <Text style={{marginTop: 10, fontSize: 14, color: '#333', alignSelf: 'center'}}>请输入图形验证码</Text>
                <View style={{marginTop: 17, marginLeft: 10, marginRight: 10, flexDirection: 'row', alignItems: 'center'}}>
                    <TextInput
                        placeholder={'请输入'}
                        style={{
                            height: 55,
                            padding: 0,
                            flex: 1,
                            borderWidth: 0.5,
                            color: '#333',
                            borderColor: '#ccc',
                            borderRadius: 5,
                            paddingLeft: 10,
                        }}
                        keyboardType={'numeric'}
                        onChangeText={(text) => {
                            setImageCode(text);
                        }}
                    />
                    <TouchableOpacity onPress={updateUrl}>
                        <UIImage source={url} style={{width: 100, height: 40, marginLeft: 10}} resizeMode={'stretch'} />
                    </TouchableOpacity>
                </View>
                <View style={{backgroundColor: '#e3e3e3', height: 0.5, marginTop: 17}} />
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                    <Text style={{color: '#5086FC', fontSize: 14, flex: 1, padding: 11, textAlign: 'center'}} onPress={pros.cancel}>
                        取消
                    </Text>
                    <View style={{backgroundColor: '#e3e3e3', width: 0.5, height: 42}} />
                    <Text style={{color: '#5086FC', fontSize: 14, flex: 1, padding: 11, textAlign: 'center'}} onPress={checkImageVerifyCode}>
                        确定
                    </Text>
                </View>
            </View>
        </Modal>
    );
};
