import React from 'react';
import {AppRegistry, DeviceEventEmitter, LogBox, ScrollView, TextInput, Text, Platform, StyleSheet} from 'react-native';
import * as Sentry from '@sentry/react-native';

import {createNavigationContainerRef, NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {SafeAreaProvider} from 'react-native-safe-area-context';

import './page/const.global'; //全局常量声明不能删除 ，否则会导致页面崩溃
import Routers from './page/routers/router.configs';
import EventBus from './page/util/EventBus';
import LanguageType from './page/util/language/LanguageType';
import TextUtils from './page/util/TextUtils';
import {RouterUtils} from './page/util/RouterUtils';
import {RouterUrl} from './page/base/RouterUrl';
import {Method} from './page/util/NativeModulesTools';
import UIWaitLoading from './page/widget/UIWaitLoading';
import UINewDilaog from './page/widget/UINewDilaog';
import {Constant} from './page/base/Constant';

const Stack = createNativeStackNavigator();

//全局Navigation Ref
export const NavigationRef = createNavigationContainerRef();

class App extends React.Component {
    running = true;

    constructor(props) {
        super(props);
        this.deviceEventEmitter = null;
        this.baseEventBusListener = null;
        this.state = {
            waitShow: false,
        };
        running = true;
    }

    /**
     * 注释: 设置Sentry
     * 时间: 2024/2/26 0026 11:34
     * <AUTHOR>
     */
    setSentry() {
        Sentry.init({
            dsn: __DEV__ ? 'http://039ee338186f4fdfb82e4c7bacec721a@172.20.22.165:9000/4' : 'https://<EMAIL>/7',
            environment: __DEV__ ? 'DEBUG' : 'RELEASE',
            tracesSampleRate: 0.3,
            //面包屑过滤
            beforeBreadcrumb: (event) => {
                if (event.category === 'console' || event.category === 'touch') {
                    return null;
                }
                return event;
            },
            //性能采样设置用户信息
            beforeSendTransaction: (event) => {
                this.setSentryUserInfo(event);
                return event;
            },
            //上报采样设置用户信息
            beforeSend: (event) => {
                this.setSentryUserInfo(event);
                return event;
            },
        });
    }

    /**
     * 注释: 设置Sentry上报用户信息
     * 时间: 2024/5/8 0008 16:14
     * <AUTHOR>
     * @param event
     */
    setSentryUserInfo(event) {
        let login = Method.getLogin();
        if (login != null) {
            event.user = {
                username: login.mobile,
                phone: login.mobile,
                versionCode: login.systemVersion,
                deviceId: login.mac,
                versionName: Method.getHostVersionName(),
            };
        }
    }

    /**
     * 注释: 设置RN默认属性
     * 时间: 2023/11/30 0030 17:12
     * <AUTHOR>
     */
    setRnDefaultProps() {
        //控件默认属性
        Text.defaultProps = {...(Text.defaultProps || {}), allowFontScaling: false};
        ScrollView.defaultProps = {...(ScrollView.defaultProps || {}), showsVerticalScrollIndicator: false};
        TextInput.defaultProps = {
            ...(TextInput.defaultProps || {}),
            allowFontScaling: false,
            autoCorrect: false,
        };
        //解决部分手机Text展示不全问题
        const defaultFontFamily = {
            ...Platform.select({
                android: {fontFamily: ''},
            }),
        };
        const __render = Text.render;
        Text.render = function (props, ref) {
            if (Platform.OS === 'ios') {
                return __render.call(this, props, ref);
            }
            const {style, ..._props} = props;
            const _style = StyleSheet.flatten(style) || {};
            return __render.call(this, {..._props, style: {...defaultFontFamily, ..._style}}, ref);
        };
    }

    componentDidMount() {
        running = true;
        //检测RN更新
        Method.onCheckVersion();
        //设置EventBus监听原生事件
        this.deviceEventEmitter = DeviceEventEmitter.addListener('NativeEventBus', (params) => {
            console.log('NativeEventBus', JSON.stringify(params));
            let eventName = params.eventName;
            if (TextUtils.equals('pageToJumpKey', eventName)) {
                //跳转到指定页面
                let data = params.data;
                if (TextUtils.isNoEmpty(data)) {
                    let JSONBigString = require('json-bigint')({storeAsString: true});
                    data = JSONBigString.parse(data);
                }
                //  单独判断页面
                if (TextUtils.equals('CyrAuthAllPage', params.page)) {
                    return;
                }
                RouterUtils.skipRouter(RouterUrl.ReactPage, {
                    page: params.page,
                    data: data,
                });
            } else {
                EventBus.getInstance().fireEvent(eventName, params);
            }
        });
        //忽略报错
        LogBox.ignoreLogs(['Non-serializable values were found in the navigation state', 'componentWillReceiveProps has been renamed', 'Each child in a list should have a unique "key" prop', 'VirtualizedLists should never be nested']);
        //语言切换初始化
        LanguageType.init();
        //设置控件默认属性
        this.setRnDefaultProps();
        //Sentry初始设置
        this.setSentry();
        //EventBus监听(多和ReactNative 界面出现多个弹窗问题)
        this.baseEventBusListener = (key, value) => {
            switch (key) {
                case 'showWaitDialog':
                    this.setState({waitShow: true});
                    break;
                case 'hideWaitDialog':
                    this.setState({waitShow: false});
                    break;
                default:
                    break;
            }
        };
        EventBus.getInstance().addListener(Constant.event_base_page, this.baseEventBusListener);
    }

    componentWillUnmount() {
        running = false;
        //移除监听
        if (this.deviceEventEmitter != null) {
            this.deviceEventEmitter.remove();
        }
        if (this.baseEventBusListener != null) {
            EventBus.getInstance().removeListener(this.baseEventBusListener);
        }
        //Can't perform a React state update on an unmounted component.
        this.setState = (state, callback) => {
            return;
        };
    }

    _showWaitDialog = () => {
        if (running) {
            this.setState({waitShow: true});
        }
    };

    _dismissWait = () => {
        if (running) {
            this.setState({waitShow: false});
        }
    };

    render() {
        //原生传值
        const data = this.props.data;
        //指定界面
        const page = this.props.page;

        return (
            <>
                <SafeAreaProvider>
                    <NavigationContainer ref={NavigationRef}>
                        <Stack.Navigator
                            initialRouteName={page}
                            screenOptions={{
                                headerShown: false,
                                backgroundColor: '#ffffff',
                                animation: 'slide_from_right',
                            }}>
                            {Routers.map((item, index) => {
                                //遍历所有页面
                                return (
                                    <Stack.Screen key={item.name} name={item.name}>
                                        {(props) => <item.component {...props} exreaData={data} />}
                                    </Stack.Screen>
                                );
                            })}
                        </Stack.Navigator>
                    </NavigationContainer>
                </SafeAreaProvider>
                <UIWaitLoading visible={this.state.waitShow} dismiss={this._dismissWait} />
                <UINewDilaog />
            </>
        );
    }
}

AppRegistry.registerComponent("ReactHost", () => Sentry.wrap(App));
