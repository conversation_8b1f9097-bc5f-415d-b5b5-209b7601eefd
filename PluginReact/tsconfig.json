{
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    "jsx": "preserve",
    "strict": false,
    "noImplicitAny": false,
    "moduleResolution": "node",
    "baseUrl": "./page",
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "removeComments": false,
    "isolatedModules": true,
    //开启严格空安全检查
    "strictNullChecks": true,
    "allowJs": true,
    "checkJs": false
  },
  "exclude": [
    "./node_modules",
    "PluginReact/babel.config.js",
    "PluginReact/metro.config.js",
    "PluginReact/index.android.js"
  ]
}
