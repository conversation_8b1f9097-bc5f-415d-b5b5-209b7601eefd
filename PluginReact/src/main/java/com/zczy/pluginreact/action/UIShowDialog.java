package com.zczy.pluginreact.action;

import android.app.Activity;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.ui.dialog.ToastDialog;
import com.sfh.lib.ui.dialog.WaitDialog;
import com.zczy.comm.pluginserver.AMainServer;
import com.zczy.comm.ui.SafeWaitDialog;
import com.zczy.comm.utils.JsonUtil;
import com.zczy.version.sdk.ZVersionManager;

public class UIShowDialog extends UIAction  {

    public static final int DIALOG = 0x11;
    public static final int WAIT = 0x12;

    String data;

    Callback onLeft;
    Callback onRight;
    ToastDialog dialog;

    //等待对话框
    SafeWaitDialog waitDialog;

    public UIShowDialog(ReactApplicationContext context) {
        super(context, 0);
    }

    public UIShowDialog setData(String data) {
        this.data = data;
        return this;
    }

    public UIShowDialog setOnLeft(Callback onLeft) {
        this.onLeft = onLeft;
        return this;
    }

    public UIShowDialog setOnRight(Callback onRight) {
        this.onRight = onRight;
        return this;
    }

    @Override
    public void action() {

        Activity activity = this.getCurrentActivity();
        if (activity != null && !activity.isDestroyed()) {
            switch (this.requestCode) {
                case DIALOG: {
                    this.dialog = ToastDialog.newToastDialog(activity);
                    DilaogStyle style = JsonUtil.toJsonObject(this.data, DilaogStyle.class);
                    if (style != null) {
                        DialogBuilder dialogBuilder = new DialogBuilder();
                        dialogBuilder.setCancelable(style.cancelable);
                        dialogBuilder.setHideCancel(style.hideCancel);
                        dialogBuilder.setTitle(style.title);
                        dialogBuilder.setMessage(style.message);
                        dialogBuilder.setCancelText(style.cancelText);
                        dialogBuilder.setOKText(style.okText);
                        dialogBuilder.setViewListener(new DialogBuilder.DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogBuilder.DialogInterface dialog, int which) {
                                if (style.message.contains("在线客服")){
                                    AMainServer mainServer = AMainServer.getPluginServer();
                                    if (mainServer != null) {
                                        mainServer.openLineServer(activity);
                                    }
                                }
                            }
                        });
                        dialogBuilder.setCancelListener((dialog, which) -> {
                            dialog.dismiss();
                            if (onLeft != null) {
                                onLeft.invoke();
                                onLeft = null;
                            }
                        });
                        dialogBuilder.setOkListener((dialog, which) -> {
                            dialog.dismiss();
                            if (onRight != null) {
                                onRight.invoke();
                                onRight = null;
                            }
                        });
                        dialog.show(dialogBuilder);
                    }
                    break;
                }

                case WAIT: {
                    this.waitDialog = SafeWaitDialog.newToastDialog(activity);
                    this.waitDialog.show();
                    break;
                }
            }
        }
    }

    /**
     * 隐藏等待对话框
     */
    public void hideWaitDialog() {
        if (waitDialog != null && waitDialog.isShowing()) {
            waitDialog.dismiss();
            waitDialog = null;
        }
    }


    @Override
    public void onHostResume() {

    }

    @Override
    public void onHostPause() {

    }

    @Override
    public void onHostDestroy() {

        super.onHostDestroy();

        if (this.dialog != null && this.dialog.isShowing()) {
            this.dialog.dismiss();
        }
        this.dialog = null;
        if (this.waitDialog != null && this.waitDialog.isShowing()) {
            this.waitDialog.dismiss();
        }
        this.waitDialog = null;
    }

    public void dismissWait() {
        if (this.waitDialog != null && this.waitDialog.isShowing()) {
            this.waitDialog.dismiss();
        }
    }

    //提示对话框
    class DilaogStyle {
        String title = "提示";
        String message;
        String cancelText = "取消";
        String okText = "确定";
        boolean hideCancel = false;
        boolean cancelable = true;
    }
}
