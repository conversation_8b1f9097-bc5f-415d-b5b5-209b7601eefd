module.exports = {
    root: true,
    extends: '@react-native-community',
    rules: {
        'prettier/prettier': [
            'error',
            {
                endOfLine: 'auto',
            },
        ],
        'no-console': 'off', // 允许使用 console 语句
        'no-unused-vars': 'warn', // 允许未使用的变量，但会发出警告
        'no-undef': 'error', // 不允许使用未定义的变量
        'no-var': 'error', // 不允许使用 var 声明变量
        'no-extra-semi': 'error', // 不允许使用多余的分号
    },
    parserOptions: {
        ecmaVersion: 6,
    },
    globals: {
        myGlobalVar: 'readonly', // 可以使用myGlobalVar变量，但不允许重新赋值
    },
    ignorePatterns: ['node_modules/', 'build/'], // 忽略 node_modules 目录和 build 目录下的文件
};
