{"name": "ReactNative_CYF", "private": true, "scripts": {"adb-reverse": "adb reverse tcp:8081 tcp:8081", "start": "react-native start", "clean": "npm cache clean --force", "prettier": "prettier --write page/**/*.ts page/**/*.tsx page/**/*.js", "update-version": "node scripts/updateVersion.js", "bundle-debug": "react-native bundle --platform android --dev true --reset-cache --entry-file index.js --bundle-output ./src/main/assets/index.android.bundle --assets-dest ./src/main/res/", "bundle-release": "react-native bundle --platform android --dev false --reset-cache --entry-file index.js --bundle-output ./src/main/assets/index.android.bundle --assets-dest ./src/main/res/ --sourcemap-output ./src/main/assets/index.android.bundle.map && npm run sentry-upload", "bundle-release-only": "react-native bundle --platform android --dev false --reset-cache --entry-file index.js --bundle-output ./src/main/assets/index.android.bundle", "sentry-upload": "node scripts/uploadSentry.js", "lint": "eslint .", "lint-fix": "eslint . --fix"}, "dependencies": {"@react-native/assets": "1.0.0", "@react-navigation/bottom-tabs": "6.5.7", "@react-navigation/material-top-tabs": "6.6.2", "@react-navigation/native": "6.1.6", "@react-navigation/native-stack": "6.9.12", "@sentry/react-native": "5.22.1", "@sentry/react": "7.110.1", "@sentry-internal/replay-canvas": "7.110.1", "@sentry-internal/feedback": "7.110.1", "@sentry-internal/tracing": "7.110.1", "@sentry/utils": "7.110.1", "@sentry/types": "7.110.1", "@sentry/replay": "7.110.1", "@shopify/flash-list": "1.5.0", "class-transformer": "0.5.1", "date-fns": "2.30.0", "json-bigint": "1.0.0", "lodash": "^4.17.21", "lottie-react-native": "5.1.6", "react": "17.0.2", "react-native": "0.65.3", "crypto-js": "4.2.0", "react-native-fast-image": "8.6.3", "react-native-gesture-handler": "2.9.0", "react-native-linear-gradient": "2.8.2", "react-native-modal": "13.0.1", "react-native-pager-view": "6.0.1", "react-native-picker": "4.3.7", "react-native-ratings": "^8.1.0", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.5.1", "react-native-safe-modules": "1.0.3", "react-native-screens": "3.20.0", "react-native-tab-view": "3.5.1", "react-native-webview": "11.26.1", "reflect-metadata": "0.1.13", "rmc-date-picker": "6.0.10", "react-native-image-gallery": "2.1.5", "react-native-super-grid": "5.1.0", "@react-native/normalize-color": "1.0.0", "use-subscription": ">=1.0.0 <1.6.0", "stacktrace-parser": "0.1.10", "scheduler": "0.20.2", "@react-native/polyfills": "1.0.0", "anser": "1.4.0", "react-freeze": "1.0.3", "tslib": "2.4.0", "recyclerlistview": "4.2.0", "xdate": "^0.8.0", "react-native-swipe-gestures": "^1.0.5", "memoize-one": "^5.2.1"}, "devDependencies": {"@babel/core": "7.22.9", "@babel/plugin-proposal-decorators": "7.22.7", "@babel/runtime": "7.22.6", "@react-native-community/cli": "6.4.0", "@react-native-community/cli-hermes": "6.3.1", "@react-native-community/cli-platform-android": "6.3.1", "@react-native-community/cli-platform-ios": "6.2.1", "@react-native-community/cli-plugin-metro": "6.4.0", "@tsconfig/react-native": "2.0.3", "@types/react": "17.0.2", "@types/react-native": "0.65.3", "metro": "0.66.2", "https-proxy-agent": "5.0.1", "metro-react-native-babel-preset": "0.66.0", "prettier": "2.8.8", "progress": "2.0.3", "typescript": "4.9.5", "proxy-from-env": "1.1.0", "hoist-non-react-statics": "3.3.2", "localforage": "1.10.0", "@react-native-community/eslint-config": "^2.0.0", "eslint": "7.14.0", "@jridgewell/gen-mapping": "0.3.5"}, "engines": {"node": "16.14.0"}}