apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'//插件编译
apply plugin: 'kotlin-kapt'

android {

    compileSdkVersion config.compileSdkVersion
    defaultConfig {
        minSdkVersion config.minSdkVersion
        targetSdkVersion config.targetSdkVersion
        versionCode 1
        versionName '1.0.0'
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildTypes {
        debug {
            debuggable true  //启用debug的buildType配置
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            assets.srcDirs = ['./assets',"./src/main/assets"]
        }
    }
}

dependencies {
    implementation project(path: ':LibComm')

    //react-native
    api "com.facebook.react:react-native:0.65.3"
    implementation project(path: ':PluginOrder') // From node_modules
    debugImplementation(name: 'hermes-debug', ext: 'aar')
    releaseImplementation(name: 'hermes-release', ext: 'aar')

    implementation project(':react-native-screens')
    implementation project(':react-native-gesture-handler')
    implementation project(':react-native-safe-area-context')
    implementation project(':react-native-pager-view')
    implementation project(':react-native-picker')
    implementation project(':react-native-fast-image')
    implementation project(':react-native-webview')
    implementation project(':lottie-react-native')
    implementation project(':react-native-linear-gradient')
    implementation project(':react-native-flash-list')

    //ARouter
    kapt libs.tool_arouter_kapt

    //flipper调试
    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}") {
        exclude group: 'com.facebook.fbjni'
    }
    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group: 'com.facebook.flipper'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}") {
        exclude group: 'com.facebook.flipper'
    }

    implementation project(path: ':appLibCertificate')
}