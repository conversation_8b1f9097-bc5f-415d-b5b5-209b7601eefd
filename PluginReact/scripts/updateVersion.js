const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);

updateRnVersionCode(args[0]).then((value) => {});

/**
 * 注释: 更新RN Bundle版本号
 * 时间: 2024/5/17 0017 11:18
 * <AUTHOR>
 * @returns {Promise<void>}
 */
async function updateRnVersionCode(versionNum) {
    const configPath = path.join(__dirname, '../page/const.global.js');
    const regex = /versionCode:\s+\d+/g;
    let result = await findMatchesInFile(configPath, regex);
    let versionCode = result[0].split(':')[1].trim();
    console.log(`更新前RN版本号:${versionCode}`);
    if (versionCode != null) {
        const number = Number(versionCode);
        // 读取文件内容
        const fileContent = fs.readFileSync(configPath, 'utf8');
        const newContent = fileContent.replace(versionCode, `${versionNum ?? number + 1}`);
        fs.writeFileSync(configPath, newContent, 'utf8');
        console.log(`版本号更新成功，现在版本号为：${versionNum ?? number + 1}`);
    }
}

/**
 * 注释: 查找指定文件中匹配的正则结果
 * 时间: 2024/5/13 0013 16:47
 * <AUTHOR>
 * @param filePath
 * @param regex
 * @returns {Promise<unknown>}
 */
function findMatchesInFile(filePath, regex) {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                reject(err);
                return;
            }
            const matches = data.match(regex);
            resolve(matches);
        });
    });
}
