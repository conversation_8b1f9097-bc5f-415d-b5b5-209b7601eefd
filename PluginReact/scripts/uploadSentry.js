const {exec} = require('child_process');
const sentryCli = require.resolve('../node_modules/@sentry/cli/bin/sentry-cli');
const path = require('path');
const bundlePath = path.join(__dirname, '../src/main/assets/index.android.bundle');
const sourceMapPath = path.join(__dirname, '../src/main/assets/index.android.bundle.map');
const fs = require('fs');

uploadBundle().then((r) => {});

/**
 * 注释: 上传Bundle
 * 时间: 2024/5/13 0013 17:14
 * <AUTHOR>
 * @returns {Promise<void>}
 */
async function uploadBundle() {
    let releaseName = await getReleaseName();
    let rnVersionCode = await getRNVersionCode();
    // 要执行的命令
    let command = `node ${sentryCli} sourcemaps upload ${bundlePath} ${sourceMapPath} --release ${releaseName} --dist ${rnVersionCode}`;

    // 执行命令
    exec(command, (error, stdout, stderr) => {
        if (error) {
            console.error(`执行命令时出错: ${error.message}`);
            return;
        }
        // 命令输出
        console.log('命令输出:');
        console.log(stdout);
    });
}

/**
 * 注释: 获取Release名称
 * 时间: 2024/5/13 0013 17:08
 * <AUTHOR>
 * @returns {Promise<string>}
 */
async function getReleaseName() {
    const configPath = path.join(__dirname, '../../build.gradle');
    const regex = /versionCode\s+:\s\d+/g;
    let result = await findMatchesInFile(configPath, regex);
    let versionCode = result[0].split(':')[1].trim();

    const regexName = /versionName\s+:\s+'\S+'/g;
    let resultName = await findMatchesInFile(configPath, regexName);
    let versionName = resultName[0].split(':')[1].trim().replaceAll("'", '');
    let releaseName = `com.tiema.zhwl_android@${versionName}+${versionCode}`;
    console.log('绑定的Release:', releaseName);
    return releaseName;
}

/**
 * 注释: 获取RN版本号
 * 时间: 2024/5/13 0013 17:09
 * <AUTHOR>
 * @returns {Promise<void>}
 */
async function getRNVersionCode() {
    const configPath = path.join(__dirname, '../page/const.global.js');
    const regex = /versionCode:\s+\d+/g;
    let result = await findMatchesInFile(configPath, regex);
    let versionCode = result[0].split(':')[1].trim();
    console.log('RN版本号:', versionCode);
    return versionCode;
}

/**
 * 注释: 查找指定文件中匹配的正则结果
 * 时间: 2024/5/13 0013 16:47
 * <AUTHOR>
 * @param filePath
 * @param regex
 * @returns {Promise<unknown>}
 */
function findMatchesInFile(filePath, regex) {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                reject(err);
                return;
            }
            const matches = data.match(regex);
            resolve(matches);
        });
    });
}
