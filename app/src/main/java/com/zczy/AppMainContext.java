package com.zczy;

import android.content.Context;
import android.content.Intent;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.facebook.react.ReactApplication;
import com.facebook.soloader.SoLoader;
import com.ijiami.AlgorithmType;
import com.ijiami.JMEncryptBox;
import com.ijiami.whitebox.Sm4EncryptBox;
import com.ijiami.whitebox.WhiteEncryptBox;
import com.sfh.lib.AppCacheManager;
import com.sfh.lib.http.transaction.OutreachRequest;
import com.sfh.lib.rx.EmptyResult;
import com.sfh.lib.rx.RetrofitManager;
import com.sfh.lib.ui.AbstractLifecycleActivity;
import com.sfh.lib.ui.dialog.DialogBuilder;
import com.sfh.lib.utils.UtilTool;
import com.zczy.certificate.Constant;
import com.zczy.comm.CommServer;
import com.zczy.comm.Const;
import com.zczy.comm.ZczyApplication;
import com.zczy.comm.config.HttpURLConfig;
import com.zczy.comm.data.entity.ELogin;
import com.zczy.comm.data.request.ReqCityList;
import com.zczy.comm.file.oss.OssALServer;
import com.zczy.comm.http.entity.IDecheck;
import com.zczy.comm.socket.UriMatcherType;
import com.zczy.comm.utils.SentryUploadLog;
import com.zczy.comm.utils.ThreadSafeExceptionHandler;
import com.zczy.location.LocationManager;
import com.zczy.plugin.order.supervise.SuperviseManager;
import com.zczy.pluginreact.ZczyReactNativeHost;
import com.zczy.startup.CacheInitializer;
import com.zczy.user.message.PushIntentService;
import com.zczy.user.setting.UserEditPasswordctivity;
import com.zczy.utils.AppUtils;
import com.zczy.utils.TtsHelper;
import com.zczy.version.sdk.ZVersionManager;
import com.zczy.version.sdk.rn.RNVersion;
import com.zczy.zlog.ZLog;
import com.zczy.zlog.ZLogConfig;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.functions.Predicate;
import io.sentry.ITransaction;

/**
 * <AUTHOR> 孙飞虎
 * @company 中储智运科技股份有限公司
 * @copyright （版权）中储智运科技股份有限公司所有
 * @date 2018/9/27
 */
public class AppMainContext extends ZczyApplication implements ReactApplication, LifecycleObserver {

    //正在加载的Bundle 版本号
    private RNVersion rnVersion;

    //全局统计维度事件
    private ITransaction transaction;

    //页面统计维度事件
    private ITransaction pageTransaction;

    public RNVersion getRnVersion() {
        if (rnVersion == null) {
            rnVersion = new RNVersion();
        }
        return rnVersion;
    }

    public void setRnVersion(RNVersion rnVersion) {
        this.rnVersion = rnVersion;
    }

    //应用级别日志监控
    public ITransaction getTransaction() {
        return transaction;
    }

    public void setTransaction(ITransaction transaction) {
        this.transaction = transaction;
    }

    @Override
    public void onCreate() {
        super.onCreate();

        // 安装线程安全异常处理器，防止 Lottie 等组件的线程异常导致应用崩溃
        ThreadSafeExceptionHandler.install();

        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
        //爱加密
        JMEncryptBox.setLicenseKey("6FE413FF18F21E05932BC31F9169488841F6CA799EB24FF332600AC5A118DF93E8A1576654577970BBF2535318F199A2229768AF8456ECA35CF098799CDC3D206E7AFFFE93454872BF44F8FDCD30AE10A6ED79F61E9534031401AEA807736C88E6BF");
        WhiteEncryptBox.init();
        //RN初始化
        SoLoader.init(this, false);
        getReactNativeHost();
        //设置OSS 上传空间
        OssALServer.setBucketName(HttpURLConfig.isDebug());

        RetrofitManager.executeSigin(Observable.just(this).filter(new Predicate<Context>() {
            @Override
            public boolean test(Context context) throws Exception {
                String process = UtilTool.getProcessName(context, android.os.Process.myPid());
                return !TextUtils.equals("com.tiema.zhwl_android", process);
            }
        }).delay(500, TimeUnit.MILLISECONDS), new EmptyResult<AppMainContext>() {
            @Override
            public void onSuccess(AppMainContext context) throws Exception {
                //其他进程先初始化
                new CacheInitializer().create(context);
            }
        });
    }


    @Override
    public IDecheck getDecheck() {
        //加解密
        return new IDecheck() {
            @Override
            public String checkcode(OutreachRequest outreachRequest, String data) {
                //数据加密 爱加密
                try {
                    ZLog.i("Http", "请求参数:" + data);
                    String content = JMEncryptBox.encryptToBase64(data, AlgorithmType.AES);
                    return TextUtils.isEmpty(content) ? data : content;
                } catch (Exception e) {
                }
                return data;
            }

            @Override
            public String decheckcode(OutreachRequest outreachRequest, String data) {
                //数据解密 爱加密
                try {
                    if (!(outreachRequest instanceof ReqCityList)) {
                        //不是城市接口
                        ZLog.i("Http", "返回参数:" + data);
                    }
                    String content = JMEncryptBox.decryptFromBase64(data, AlgorithmType.AES).getText();
                    return TextUtils.isEmpty(content) ? data : content;
                } catch (Exception e) {
                }
                return data;
            }

            @Override
            public String aesEncryptStringWithBase64(OutreachRequest request, String data, String en, byte[] bytes) {
                //白盒=爱加密
                try {
                    ZLog.i("Http", "请求参数:" + data);
                    return Sm4EncryptBox.encryptToBase64_CBC(data, Sm4EncryptBox.pkcs7padding);
                } catch (Exception e) {
                }
                return data;
            }

            @Override
            public String aesDecryptStringWithBase64(OutreachRequest request, String data, String en, byte[] bytes) {
                //白盒=爱加密
                try {
                    ZLog.i("Http", "返回参数:" + data);
                    return Sm4EncryptBox.decryptFromBase64_CBC(data, Sm4EncryptBox.pkcs7padding);
                } catch (Exception e) {
                }
                return data;

            }
        };
    }

    private long lastTime = 0L;

    @Override
    public synchronized void onLoseToken(String code, String exit) {
        if (TextUtils.equals("COM99997", code)) {
            //重新设置密码
            UserEditPasswordctivity.startUI(this);
            return;
        }

        long nowTime = System.currentTimeMillis();
        if (nowTime - lastTime < 3000) {
            lastTime = nowTime;
            return;
        }
        lastTime = nowTime;
        ELogin login = CommServer.getUserServer().getLogin();
        if (login != null && !TextUtils.isEmpty(login.getUserId())) {
            //1 JPush 消息推送别名 账号时别名【userId_childId】
            String alias = HttpURLConfig.getPushAlias() + login.getUserId() + "_" + (TextUtils.isEmpty(login.getChildId()) ? "" : login.getChildId());
            PushIntentService.initJPushAlias(this, false, alias);

        }
        // 清空缓存临时数据
        AppCacheManager.removeCache(Const.LOGIN_KEY);
        //清除展示导航的临时数据
        AppCacheManager.removeCache(Constant.HOME_SHOW_NAVIGATE);
        if (!TextUtils.isEmpty(exit) && Looper.myLooper() == Looper.getMainLooper()) {
            Toast.makeText(this, exit, Toast.LENGTH_SHORT).show();
        }

        //清除省平台任务与数据
        SuperviseManager.getInstance().logout();
        //停止任务
        LocationManager.stop(this);
        //清除所有缓存websoket 数据
        UriMatcherType.CLEARALL.clearAll(this);
        // 打开应用首个打开界面
        Intent i = this.getPackageManager().getLaunchIntentForPackage(this.getPackageName());
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        this.startActivity(i);

    }


    /***
     * 语音播报
     */
    public static void readText(Context context, String msgText) {
        if (CommServer.getUserServer().isLogin()) {
            //ZCZY-14368  取消未登陆时语音播报
            TtsHelper.create(context).readText(context, msgText);
        }
    }

    private ZczyReactNativeHost reactNativeHost;

    @Override
    public ZczyReactNativeHost getReactNativeHost() {
        if (reactNativeHost == null) {
            reactNativeHost = new ZczyReactNativeHost(this);
        }
        return reactNativeHost;
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    public void onForeground() {
        if (AppUtils.isProxy() && !HttpURLConfig.isDebug()) {
            DialogBuilder builder = new DialogBuilder();
            AbstractLifecycleActivity activity = ((AbstractLifecycleActivity) ZVersionManager.getInstance().getCurrentActivity());
            builder.setTitle("提示");
            builder.setMessage("您设置了网络代理，不允许访问，请检查网络设置");
            builder.setCancelable(false);
            builder.setOkListener((dialog, which) -> {
                dialog.dismiss();
                activity.finish();
            });
            builder.setHideCancel(true);
            activity.runOnUiThread(() -> activity.showDialog(builder));
            return;
        }
        ZLog.i("应用进入前台", "onForeground");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    public void onBackground() {
        ZLog.i("应用进入后台", "onBackground");
        ZLog.e("zlog 开发者选项", "isDeveloperOptionsEnabledGlobal = " + ZLogConfig.isDeveloperOptionsEnabledGlobal(this)
                + "  isDeveloperOptionsEnabledSecure = " + ZLogConfig.isDeveloperOptionsEnabledSecure(this));
        SentryUploadLog.uploadLogs(this);
    }
}
